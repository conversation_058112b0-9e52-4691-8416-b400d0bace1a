<?xml version="1.0" encoding="UTF-8"?>
<lint>
    <!--
        Use this file to configure which <PERSON><PERSON> checks should be ignored
        https://developer.android.com/studio/write/lint.html#pref
        An example lint xml file can be found here:
        https://github.com/wireapp/wire-android/blob/master/app/config/lint-config.xml
     -->

    <issue id="StringFormatCount" severity="error" />
    <issue id="MissingTranslation">
        <ignore path="*/**"/>
    </issue>
    <issue id="ExtraTranslation">
        <ignore path="*/**"/>
    </issue>
    <issue id="InvalidPackage">
        <ignore regexp="/caches/modules-2/files-2.1/com.amersports.formatter/format/"/>
    </issue>
    <issue id="TypographyEllipsis" severity="ignore"/>
    <!--  Added since we don't provide drawables in all densities  -->
    <issue id="IconMissingDensityFolder" severity="ignore"/>
    <issue id="IconDensities" severity="ignore"/>
    <issue id="InvalidPackage">
        <ignore path="**/byte-buddy-agent-*.jar"/>
        <ignore path="**/mockito-core-*.jar"/>
    </issue>
    <!--
        Apparently this inspection is not that bad. After investigation, I found out
        that the system actually does optimizations to avoid overdraw so it is safe to
        lower its severity.
    -->
    <issue id="Overdraw" severity="informational"/>
    <!--
        The limit set by this inspection is 800. According to designers, our SVGs
        are highly optimized so there's nothing we can do to lower this paths count.
        Using Avocado command line tool can reduce some paths and is recommended.
      -->
    <issue id="VectorPath" severity="informational"/>
    <issue id="GradleDependency" severity="ignore"/>
    <issue id="UnusedResources" severity="ignore">
        <ignore regexp=".*Braze.*"/>
    </issue>

    <issue id="TimberExceptionLogging" severity="informational"/>

    <!-- can be removed when Butterknife is removed and epoxy is not using resource ids in annotations anymore-->
    <issue id="NonConstantResourceId" severity="ignore"/>

    <!-- no planned support on chrome OS so this can be ignored -->
    <issue id="LockedOrientationActivity" severity="ignore"/>

    <!-- TODO We are are using the native Switch component in many places. This Lint rule gives us warnings about that.
    The now recommended SwitchCompat and SwitchMaterial classes do not derive from the platform Switch class so
    they are not compatible. AppCompatLayoutInflater also won't map references to Switch to the new classes.
    At some point we may want to migrate to SwitchMaterial, but let's ignore this for now. -->
    <issue id="UseSwitchCompatOrMaterialCode" severity="ignore"/>
    <issue id="UseSwitchCompatOrMaterialXml" severity="ignore"/>

    <issue id="UseAppTint" severity="informational"/>
    <issue id="UseCompatLoadingForDrawables" severity="informational"/>
    <issue id="UseCompatTextViewDrawableXml" severity="informational"/>

    <!-- ignore typos in all string.xml files-->
    <issue id="Typos">
        <ignore path="res/values/strings.xml" />
        <ignore path="res/values-cs/strings.xml" />
        <ignore path="res/values-da/strings.xml" />
        <ignore path="res/values-de/strings.xml" />
        <ignore path="res/values-es/strings.xml" />
        <ignore path="res/values-fi/strings.xml" />
        <ignore path="res/values-fr/strings.xml" />
        <ignore path="res/values-it/strings.xml" />
        <ignore path="res/values-ja/strings.xml" />
        <ignore path="res/values-ko/strings.xml" />
        <ignore path="res/values-nb/strings.xml" />
        <ignore path="res/values-nl/strings.xml" />
        <ignore path="res/values-pl/strings.xml" />
        <ignore path="res/values-pt/strings.xml" />
        <ignore path="res/values-ru/strings.xml" />
        <ignore path="res/values-sv/strings.xml" />
        <ignore path="res/values-th/strings.xml" />
        <ignore path="res/values-tr/strings.xml" />
        <ignore path="res/values-zh-rCN/strings.xml" />
        <ignore path="res/values-zh-rTW/strings.xml" />
        <ignore path="res/values/strings_non_localized.xml" />
        <ignore path="res/values/third_party_keys.xml" />
    </issue>

    <issue id="MissingQuantity" severity="informational"/>
    <issue id="IconDipSize" severity="informational"/>
    <issue id="SetTextI18n" severity="informational"/>
    <issue id="PluralsCandidate" severity="informational"/>
    <issue id="ContentDescription" severity="warning"/>

    <!-- this rule is just broken, we remove it correctly but lint reports error -->
    <issue id="RemoveWorkManagerInitializer" severity="ignore"/>

    <!-- since updating com.google.android.support:wearable to 2.9.0 lot's of classes are annotated with @VisibleForTesting-->
    <!-- TODO need to migrate to wear OS jetpack library -->
    <issue id="VisibleForTests" severity="ignore">
        <ignore regexp=".*STTAndroidCore.*"/>
        <ignore regexp=".*DataLayerUtil.*"/>
        <ignore regexp=".*Wearable.*"/>
    </issue>

    <issue id="ChromeOsAbiSupport" severity="informational"/>

    <issue id="KaptUsageInsteadOfKsp" severity="informational" />

    <issue id="SelectedPhotoAccess" severity="informational" />
</lint>
