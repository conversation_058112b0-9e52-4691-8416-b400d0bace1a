/**
 * This configuration file holds values used by Gradle build scripts.
 * Please do not add any Gradle dependency nor version definitions here.
 * Instead add those to /buildSrc/src/main/kotlin/Deps.kt
 */
ext {
    sourceJavaVersion = JavaVersion.VERSION_17
    targetJavaVersion = JavaVersion.VERSION_17
    kotlinJvmVersion = "17"

    sourceLanguage = "en"
    supportedLocales = ["cs", "da", "de", "es", "fi", "fr", "it","ja", "ko", "nl", "nb","pl", "pt", "ru","sv", "tr", "th", "zh-rCN", "vi", "zh-rTW"]
}

allprojects {
    ext.sally = { isRelease ->
        def repoUrl
        if (isRelease) {
            repoUrl = "https://sally01.jfrog.io/sally01/amersports-release-local"
        } else {
            repoUrl = "https://sally01.jfrog.io/sally01/amersports-snapshot-local"
        }
        repositories.maven {
            url = repoUrl
            credentials {
                username = artifactory_user
                password = artifactory_password
            }
            content {
                if (isRelease) {
                    releasesOnly()
                } else {
                    snapshotsOnly()
                }
            }
            mavenContent {
                includeGroupByRegex "com\\.asoy.*"
                includeGroupByRegex "com\\.soy.*"
                includeGroupByRegex "com\\.suunto.*"
                includeGroupByRegex "com\\.amersports.*"
                includeGroupByRegex "com\\.stt.*"
            }
            authentication {
                basic(BasicAuthentication)
            }
        }
    }
    ext.sallySnapshot = { sally(false) }
    ext.sallyRelease = { sally(true) }
    ext.sallyJcenter = {
        repositories.maven {
            url = "https://sally01.jfrog.io/artifactory/jcenter/"
            credentials {
                username = artifactory_user
                password = artifactory_password
            }
            authentication {
                basic(BasicAuthentication)
            }
        }
    }
}
