#!/bin/bash

if [[ $# -ne 2 ]]; then
	echo "Usage: $0 keystore keyalias"
	echo "You can check the keyalis with command:"
	echo "  keytool -list -v -keystore keystore_file"
	exit 1
fi

if [[ ! -f $1 ]]; then
	echo "$1 doesn't exist or isn't a keystore"
	exit 1
fi

read -p "Is 'ormlite_config.txt' file up-to-date? [y/n] " -n 1 -r
echo
if [[ $REPLY =~ ^[Nn]$ ]]; then
    echo "See file DatabaseConfigUtil.java for more details"
	exit 1
fi

read -r -s -p "Keystore Password: " STORE_PASS
echo
read -r -s -p "Key Password: " KEY_PASS
echo

export ORG_GRADLE_PROJECT_keyStore="$1"
export ORG_GRADLE_PROJECT_storePass="$STORE_PASS"
export ORG_GRADLE_PROJECT_alias="$2"
export ORG_GRADLE_PROJECT_keyPass="$KEY_PASS"

./gradlew signingReport

read -p "Is this correct? [y/n] " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
	./gradlew -q clean assembleSuuntoPlaystoreRelease --stacktrace
fi
