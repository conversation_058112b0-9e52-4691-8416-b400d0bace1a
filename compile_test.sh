#!/bin/bash

# You can add this script to Android Studio:
# 1. Open Preferences -> Tools -> External tools
# 2. Type "$ProjectFileDir$/compile_test.sh" (without quotes) as value for Program
# 3. Type "$ProjectFileDir$" (without quotes) as value for Working directory
# 4. You can assign your favourite shortcut to it as with any other command in AS (Preferences -> Keymap -> External tools)

GRADLE_TASK=":STTAndroid:testSportstrackerPlaystoreReleaseUnitTest"
TEST_PACKAGE="com.stt.android.test.ui.testcases"
GRADLE_COMMAND="-Pandroid.testInstrumentationRunnerArguments.package=${TEST_PACKAGE} ${GRADLE_TASK}"
echo "Executing ${GRADLE_COMMAND} ..."
./gradlew ${GRADLE_COMMAND} || exit $?
command -v say >/dev/null 2>&1 && say "Build complete and tested"
