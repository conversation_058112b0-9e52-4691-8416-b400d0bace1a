<?xml version="1.0" encoding="UTF-8"?>
<suppressions xmlns="https://jeremylong.github.io/DependencyCheck/dependency-suppression.1.3.xsd">
    <suppress>
        <notes><![CDATA[
            This suppresses all CVE entries that have a score below CVSS 7.
        ]]></notes>
        <cvssBelow>7</cvssBelow>
    </suppress>
    <suppress>
        <notes><![CDATA[
        https://github.com/facebook/facebook-android-sdk/issues/1237

        file name: gson-2.8.8.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.google\.code\.gson/gson@.*$</packageUrl>
        <cve>CVE-2022-25647</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. The vulnerability is in the Facebook messenger app

        file name: facebook-messenger-17.0.1.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.facebook\.android/facebook-messenger@.*$
        </packageUrl>
        <cve>CVE-2020-20093</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        Firebase does not use affected items fom guava
        https://github.com/firebase/firebase-android-sdk/issues/5673

        file name: guava-31.1-android.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.google\.guava/guava@.*$</packageUrl>
        <cve>CVE-2023-2976</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        Firebase does not use affected items fom guava
        https://github.com/firebase/firebase-android-sdk/issues/5673

        file name: guava-31.1-android.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.google\.guava/guava@.*$</packageUrl>
        <cve>CVE-2020-8908</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. The vulnerability is in the Tasks.org app

        file name: kotlinx-coroutines-play-services-1.8.1.jar
   ]]></notes>
        <packageUrl regex="true">
            ^pkg:maven/org\.jetbrains\.kotlinx/kotlinx-coroutines-play-services@.*$
        </packageUrl>
        <cve>CVE-2020-22475</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. The vulnerability is in the Tasks.org app

        file name: kotlinx-coroutines-play-services-1.8.1.jar
   ]]></notes>
        <packageUrl regex="true">
            ^pkg:maven/org\.jetbrains\.kotlinx/kotlinx-coroutines-play-services@.*$
        </packageUrl>
        <cve>CVE-2022-39349</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        We do not parse Protocol Buffers data from untrusted sources.

   file name: datastore-preferences-core-1.0.0.jar (shaded: com.google.protobuf:protobuf-javalite:3.10.0)
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.google\.protobuf/protobuf-javalite@.*$</packageUrl>
        <vulnerabilityName>CVE-2024-7254</vulnerabilityName>
    </suppress>
    <suppress>
        <notes><![CDATA[
        Inspector.jar is used for Android Studio app inspection. The resulting app apk does not
        contain it.

        file name: ui-1.0.1.aar: inspector.jar (shaded: com.google.protobuf:protobuf-javalite:3.10.0)
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.google\.protobuf/protobuf-javalite@.*$</packageUrl>
        <cve>CVE-2022-3171</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        Inspector.jar is used for Android Studio app inspection. The resulting app apk does not
        contain it.

        file name: ui-1.0.1.aar: inspector.jar (shaded: com.google.protobuf:protobuf-javalite:3.10.0)
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.google\.protobuf/protobuf-javalite@.*$</packageUrl>
        <vulnerabilityName>CVE-2022-3510</vulnerabilityName>
    </suppress>
    <suppress>
        <notes><![CDATA[
        Inspector.jar is used for Android Studio app inspection. The resulting app apk does not
        contain it.

        file name: ui-1.0.1.aar: inspector.jar (shaded: com.google.protobuf:protobuf-javalite:3.10.0)
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.google\.protobuf/protobuf-javalite@.*$</packageUrl>
        <cve>CVE-2021-22569</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        We override okhttp to version 4.12.0

        file name: okhttp-3.14.7.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.squareup\.okhttp3/okhttp@.*$</packageUrl>
        <vulnerabilityName>CVE-2021-0341</vulnerabilityName>
    </suppress>
    <suppress>
        <notes><![CDATA[
        We override okhttp to version 4.12.0

        file name: okhttp-3.14.9.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.squareup\.okhttp3/okhttp@.*$</packageUrl>
        <vulnerabilityName>CVE-2021-0341</vulnerabilityName>
    </suppress>
    <suppress>
        <notes><![CDATA[
        Not used by our app.

        file name: okio-1.17.2.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.squareup\.okio/okio@.*$</packageUrl>
        <vulnerabilityName>CVE-2023-3635</vulnerabilityName>
    </suppress>
    <suppress>
        <notes><![CDATA[
        Overridden with version 3.7.0

        file name: okio-metadata-3.3.0-all.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.squareup\.okio/okio@.*$</packageUrl>
        <vulnerabilityName>CVE-2023-3635</vulnerabilityName>
    </suppress>
    <suppress>
        <notes><![CDATA[
        Resolved in Play Services 18.0.2. We use 18.0.4

        file name: play-services-basement-16.0.1.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.google\.android\.gms/play-services-basement@.*$
        </packageUrl>
        <vulnerabilityName>CVE-2022-1799</vulnerabilityName>
    </suppress>
    <suppress>
        <notes><![CDATA[
        Resolved in Play Services 18.0.2. We use 18.0.4

        file name: play-services-basement-16.0.1.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.google\.android\.gms/play-services-basement@.*$
        </packageUrl>
        <vulnerabilityName>CVE-2022-2390</vulnerabilityName>
    </suppress>
    <suppress>
        <notes><![CDATA[
        Resolved in Play Services 18.0.2. We use 18.0.4

        file name: play-services-basement-18.0.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.google\.android\.gms/play-services-basement@.*$
        </packageUrl>
        <vulnerabilityName>CVE-2022-1799</vulnerabilityName>
    </suppress>
    <suppress>
        <notes><![CDATA[
        Resolved in Play Services 18.0.2. We use 18.0.4

        file name: play-services-basement-18.0.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.google\.android\.gms/play-services-basement@.*$
        </packageUrl>
        <vulnerabilityName>CVE-2022-2390</vulnerabilityName>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. https://github.com/jeremylong/DependencyCheck/issues/1727

        file name: sqlite-2.4.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\.sqlite/sqlite@.*$</packageUrl>
        <cve>CVE-2017-10989</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. https://github.com/jeremylong/DependencyCheck/issues/1727

        file name: sqlite-2.4.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\.sqlite/sqlite@.*$</packageUrl>
        <cve>CVE-2019-19646</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. https://github.com/jeremylong/DependencyCheck/issues/1727

        file name: sqlite-2.4.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\.sqlite/sqlite@.*$</packageUrl>
        <cve>CVE-2020-11656</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. https://github.com/jeremylong/DependencyCheck/issues/1727

        file name: sqlite-2.4.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\.sqlite/sqlite@.*$</packageUrl>
        <cve>CVE-2015-5895</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. https://github.com/jeremylong/DependencyCheck/issues/1727

        file name: sqlite-2.4.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\.sqlite/sqlite@.*$</packageUrl>
        <cve>CVE-2015-3414</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. https://github.com/jeremylong/DependencyCheck/issues/1727

        file name: sqlite-2.4.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\.sqlite/sqlite@.*$</packageUrl>
        <cve>CVE-2015-3415</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. https://github.com/jeremylong/DependencyCheck/issues/1727

        file name: sqlite-2.4.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\.sqlite/sqlite@.*$</packageUrl>
        <cve>CVE-2015-3416</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. https://github.com/jeremylong/DependencyCheck/issues/1727

        file name: sqlite-2.4.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\.sqlite/sqlite@.*$</packageUrl>
        <cve>CVE-2015-3717</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. https://github.com/jeremylong/DependencyCheck/issues/1727

        file name: sqlite-2.4.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\.sqlite/sqlite@.*$</packageUrl>
        <cve>CVE-2022-35737</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. https://github.com/jeremylong/DependencyCheck/issues/1727

        file name: sqlite-framework-2.4.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\.sqlite/sqlite-framework@.*$</packageUrl>
        <cve>CVE-2017-10989</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. https://github.com/jeremylong/DependencyCheck/issues/1727

        file name: sqlite-framework-2.4.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\.sqlite/sqlite-framework@.*$</packageUrl>
        <cve>CVE-2019-19646</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. https://github.com/jeremylong/DependencyCheck/issues/1727

        file name: sqlite-framework-2.4.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\.sqlite/sqlite-framework@.*$</packageUrl>
        <cve>CVE-2020-11656</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. https://github.com/jeremylong/DependencyCheck/issues/1727

        file name: sqlite-framework-2.4.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\.sqlite/sqlite-framework@.*$</packageUrl>
        <cve>CVE-2015-5895</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. https://github.com/jeremylong/DependencyCheck/issues/1727

        file name: sqlite-framework-2.4.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\.sqlite/sqlite-framework@.*$</packageUrl>
        <cve>CVE-2015-3414</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. https://github.com/jeremylong/DependencyCheck/issues/1727

        file name: sqlite-framework-2.4.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\.sqlite/sqlite-framework@.*$</packageUrl>
        <cve>CVE-2015-3415</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. https://github.com/jeremylong/DependencyCheck/issues/1727

        file name: sqlite-framework-2.4.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\.sqlite/sqlite-framework@.*$</packageUrl>
        <cve>CVE-2015-3416</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. https://github.com/jeremylong/DependencyCheck/issues/1727

        file name: sqlite-framework-2.4.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\.sqlite/sqlite-framework@.*$</packageUrl>
        <cve>CVE-2015-3717</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. https://github.com/jeremylong/DependencyCheck/issues/1727

        file name: sqlite-framework-2.4.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\.sqlite/sqlite-framework@.*$</packageUrl>
        <cve>CVE-2022-35737</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. https://github.com/jeremylong/DependencyCheck/issues/1727

        file name: sqlite-ktx-2.4.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\.sqlite/sqlite-ktx@.*$</packageUrl>
        <cve>CVE-2017-10989</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. https://github.com/jeremylong/DependencyCheck/issues/1727

        file name: sqlite-ktx-2.4.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\.sqlite/sqlite-ktx@.*$</packageUrl>
        <cve>CVE-2019-19646</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. https://github.com/jeremylong/DependencyCheck/issues/1727

        file name: sqlite-ktx-2.4.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\.sqlite/sqlite-ktx@.*$</packageUrl>
        <cve>CVE-2020-11656</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. https://github.com/jeremylong/DependencyCheck/issues/1727

        file name: sqlite-ktx-2.4.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\.sqlite/sqlite-ktx@.*$</packageUrl>
        <cve>CVE-2015-5895</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. https://github.com/jeremylong/DependencyCheck/issues/1727

        file name: sqlite-ktx-2.4.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\.sqlite/sqlite-ktx@.*$</packageUrl>
        <cve>CVE-2015-3414</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. https://github.com/jeremylong/DependencyCheck/issues/1727

        file name: sqlite-ktx-2.4.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\.sqlite/sqlite-ktx@.*$</packageUrl>
        <cve>CVE-2015-3415</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. https://github.com/jeremylong/DependencyCheck/issues/1727

        file name: sqlite-ktx-2.4.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\.sqlite/sqlite-ktx@.*$</packageUrl>
        <cve>CVE-2015-3416</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. https://github.com/jeremylong/DependencyCheck/issues/1727

        file name: sqlite-ktx-2.4.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\.sqlite/sqlite-ktx@.*$</packageUrl>
        <cve>CVE-2015-3717</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        False positive. https://github.com/jeremylong/DependencyCheck/issues/1727

        file name: sqlite-ktx-2.4.0.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/androidx\.sqlite/sqlite-ktx@.*$</packageUrl>
        <cve>CVE-2022-35737</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
        Might be valid still. Could not find anything from emarsys change log about this being
        addressed.

        file name: emarsys-sdk-3.7.6.aar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.emarsys/emarsys-sdk@.*$</packageUrl>
        <vulnerabilityName>CVE-2023-6542</vulnerabilityName>
    </suppress>
</suppressions>
