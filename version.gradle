import com.stt.gradle.github.GithubPlugin
import com.stt.gradle.github.PullRequestTask

apply plugin: GithubPlugin

def props = new Properties()
def propsFile = file("version.properties")

def execOperations = services.get(ExecOperations)

propsFile.withInputStream { stream ->
    props.load(stream)
    int versionCode = props['versionCode'].toInteger()
    project.ext.set('versionCode', versionCode)
    project.ext.set('versionName', getVersionName(props))
}

tasks.register('pullRemoteBranchesAndCheckoutRelease') {
    description "Pull all remote branches and checkout latest release branch"
    doLast {
        execOperations.exec {
            commandLine 'git', 'pull', '--all'
        }
        execOperations.exec {
            commandLine 'git', 'checkout', "release-${-> project.versionName}"
        }
    }
}

ext.setVersionCode = { major, minor, hotfix ->
    // Version code format: 3,045,001 (major, minor, hotfix)
    def versionCode = major * 1000000 + minor * 1000 + hotfix
    props['versionCode'] = String.valueOf(versionCode)
    props.store(propsFile.newWriter(), null)
    project.ext.set('versionCode', versionCode)
    print "Version code was set to ${project.versionCode}"
}

ext.getVersionCode = { ->
    return props['versionCode'].toInteger()
}

ext.getMajor = { ->
    return props['major'].toInteger()
}

ext.getMinor = { ->
    return props['minor'].toInteger()
}

ext.getHotfix = { ->
    return props['hotfix'].toInteger()
}

ext.getLatestTag = {
    file("ReleaseNotes.md").withReader('UTF-8') {
        def matcher = it.readLine() =~ /\d+\.\d+\.\d+/
        return "v${matcher[0]}"
    }
}

tasks.register('incrementMajorVersion') {
    description "Increment major version"
    doLast {
        def major = ++(getMajor())
        props['major'] = String.valueOf(major)
        props['minor'] = '0'
        props['hotfix'] = '0'
        props.store(propsFile.newWriter(), null)
        project.ext.set('versionName', getVersionName(props))
        print "Increased major version to ${project.versionName}"
        setVersionCode(major, 0, 0)
    }
}

tasks.register('incrementMinorVersion') {
    description "Increment minor version"
    doLast {
        def minor = ++(getMinor())
        props['minor'] = String.valueOf(minor)
        props['hotfix'] = '0'
        props.store(propsFile.newWriter(), null)
        project.ext.set('versionName', getVersionName(props))
        print "Increased minor version to ${project.versionName}"
        setVersionCode(getMajor(), minor, 0)
    }
}

tasks.register('incrementHotfixVersion') {
    description "Increment hotfix version"
    doLast {
        def hotfix = ++(getHotfix())
        props['hotfix'] = String.valueOf(hotfix)
        props.store(propsFile.newWriter(), null)
        project.ext.set('versionName', getVersionName(props))
        print "Increased hotfix version to ${project.versionName}"
        setVersionCode(getMajor(), getMinor(), hotfix)
    }
}

tasks.register('commitVersionIncrease', Exec) {
    description "Commit version change"
    commandLine 'git', 'commit', '-am', "Increased to version ${-> project.versionName} v${-> project.versionCode}"
    doFirst {
        println "Committing changes with message 'Increased to version ${project.versionName} v${project.versionCode}'"
    }
    doLast {
        props.store(propsFile.newWriter(), null)
        project.ext.set('versionCode', versionCode)
        project.ext.set('versionName', getVersionName(props))
    }
}

tasks.register('deleteOldReleaseBranch', Exec) {
    description "Delete latest release branch on remote"
    commandLine 'git', 'push', 'origin', ":release-${-> project.versionName}"
}

tasks.register('createReleaseBranch', Exec) {
    description "Create a new release branch based on current version name"
    commandLine 'git', 'checkout', '-b', "release-${-> project.versionName}"
}

tasks.register('createReleaseTag') {
    description "Create a release tag"
    doFirst {
        println "Creating release tag 'v${project.versionName}'"
    }
    doLast {
        execOperations.exec {
            commandLine 'git', 'tag', '-a', "v${-> project.versionName}", '-m', "Release ${-> project.versionName} v${-> project.versionCode}"
            ignoreExitValue true
        }
        execOperations.exec {
            commandLine('git', 'push', 'origin', '--tags')
        }
    }
}

github {
    token = "61688e99eaa489ea4f0c67b811dafb28d7af61a9"
    user = "SportsTrackingTechnologies"
    repo = "STTAndroid"
}

tasks.register('createPullRequestToDevelop', PullRequestTask) {
    title = "Merge release-${-> project.versionName} to develop"
    message = "Merge me please"
    head = "release-${-> project.versionName}"
    base = "develop"
}

tasks.register('createPullRequestToMaster', PullRequestTask) {
    title = "Merge release-${-> project.versionName} to master"
    message = "Merge me please"
    head = "release-${-> project.versionName}"
    base = "master"
}

tasks.register('validateReleaseBranch', Exec) {
    description "Validate that the current branch is a release branch"
    commandLine 'git', 'rev-parse', '--abbrev-ref', 'HEAD'
    standardOutput = new ByteArrayOutputStream()
    doLast {
        if (standardOutput.toString().trim() != "release-${-> project.versionName}") {
            throw new GradleException("The current branch is not a release branch. Should be 'release-${-> project.versionName}'.")
        }
    }
}

tasks.register('pushVersionCommit', Exec) {
    dependsOn validateReleaseBranch
    description "Push version increment commit to remote server"
    commandLine 'git', 'push', 'origin', "release-${-> project.versionName}"
}

tasks.register('releaseNotes') {
    doLast {
        new ByteArrayOutputStream().withStream { os ->
            def tag = getLatestTag()
            println "Executing get merge commit messages and auther name since tag: $tag"
            execOperations.exec {
                commandLine 'git', 'log', '--merges', '--format=%b by author:%an', "$tag..HEAD"
                standardOutput os
            }
            println "Command result:\n ${os.toString()}"
            def messages = os.toString().replace('"', '').split('\n')
                .collect { it.trim() }.findAll { !it.isEmpty() }
            def fixes = []
            def features = []
            def techs = []
            def translations = []
            messages.each { message ->
                if (message =~ /^[fF]ix/) {
                    fixes.add(message.replaceFirst(/^[fF]ix[:\/]\s*/, ''))
                }
                else if (message =~ /^[fF]eature/) {
                    features.add(message.replaceFirst(/^[fF]eature[:\/]\s*/, ''))
                }
                else if (message =~ /^[tT]echnical/) {
                    techs.add(message.replaceFirst(/^[tT]echnical[:\/]\s*/, ''))
                }
                else if (message =~ /^[tT]ranslation(s?)/) {
                    translations.add(message.replaceFirst(/^[tT]ranslation(s?)[:\/]\s*/, ''))
                }
            }
            def UTF8 = 'UTF-8'
            File notes = file('ReleaseNotes.md')
            notes.createNewFile()
            notes.withWriter(UTF8) { writer ->
                writer.write ""
                writer.writeLine "#### ${-> project.versionName} (${new Date().format('yyyy-MM-dd')})"
                if (!fixes.isEmpty()) {
                    writer.writeLine " - Fixes"
                    fixes.each { fix ->
                        writer.writeLine "  - $fix"
                    }
                }
                if (!features.isEmpty()) {
                    writer.writeLine "\n - Features"
                    features.each { feature ->
                        writer.writeLine "  - $feature"
                    }
                }
                if (!techs.isEmpty()) {
                    writer.writeLine "\n - Technical"
                    techs.each { tech ->
                        writer.writeLine "  - $tech"
                    }
                }
                if (!translations.isEmpty()) {
                    writer.writeLine "\n - Translations"
                    translations.each { translation ->
                        writer.writeLine "  - $translation"
                    }
                }
            }

            File changelog = file('CHANGELOG.md')
            changelog.setText("${notes.getText(UTF8)}\n${changelog.getText(UTF8)}", UTF8)
        }
    }
}

static def getVersionName(props) {
    int major = props['major'].toInteger()
    int minor = props['minor'].toInteger()
    int hotfix = props['hotfix'].toInteger()
    return "$major.$minor.$hotfix"
}
