package com.stt.android.device.remote.suuntoplusguide

import android.content.Context
import android.content.pm.ServiceInfo
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.work.Constraints
import androidx.work.CoroutineWorker
import androidx.work.Data
import androidx.work.ExistingWorkPolicy
import androidx.work.ForegroundInfo
import androidx.work.ListenableWorker
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.OutOfQuotaPolicy
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import com.stt.android.R
import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.coroutines.runSuspendCatchingWithTimeout
import com.stt.android.device.domain.GetWatchCapabilitiesUseCase
import com.stt.android.notifications.CHANNEL_ID_FOREGROUND_SYNC
import com.stt.android.utils.STTConstants
import com.stt.android.watch.background.SuuntoPlusGuideRemoteSyncJobLauncher
import com.stt.android.watch.suuntoplusguide.SuuntoPlusGuideWatchSyncProvider
import com.stt.android.worker.ifNotAlreadyScheduled
import com.suunto.connectivity.suuntoplusguide.SuuntoPlusGuideSyncLogicResult
import timber.log.Timber
import javax.inject.Inject
import kotlin.time.Duration.Companion.minutes

class SuuntoPlusGuideRemoteSyncJob(
    private val context: Context,
    private val params: WorkerParameters,
    private val remoteSyncLogic: SuuntoPlusGuideRemoteSyncLogic,
    private val watchSyncProvider: SuuntoPlusGuideWatchSyncProvider,
    private val watchIndependentSyncLogic: SuuntoPlusGuideWatchIndependentRemoteSyncLogic,
    private val watchCapabilitiesUseCase: GetWatchCapabilitiesUseCase,
) : CoroutineWorker(context, params) {

    override suspend fun doWork(): Result {
        val (serial, watchCapabilities) = watchCapabilitiesUseCase.getCurrentCapabilities()

        return if (serial == null || watchCapabilities == null || !watchCapabilities.areSuuntoPlusGuidesSupported) {
            syncRemoteWithoutWatch()
        } else {
            syncRemoteWithCompatibleWatch(serial)
        }
    }

    private suspend fun syncRemoteWithoutWatch(): Result =
        runSuspendCatching {
            watchIndependentSyncLogic.sync()
            Result.success()
        }.getOrElse { e ->
            Timber.w(e, "Watch independent remote sync failed")
            Result.failure()
        }

    private suspend fun syncRemoteWithCompatibleWatch(
        serial: String,
    ): Result {
        val alwaysTriggerWatchSync =
            params.inputData.getBoolean(KEY_ALWAYS_TRIGGER_WATCH_SYNC, false)

        val result = remoteSyncLogic.syncWithBackend(serial)
        val triggerWatchSync = result.triggerWatchSync || alwaysTriggerWatchSync

        if (triggerWatchSync) {
            runSuspendCatchingWithTimeout(5.minutes.inWholeMilliseconds) {
                watchSyncProvider.sendSuuntoPlusGuideWatchSyncQuery()
            }.onFailure { e ->
                Timber.w(e, "Unable to trigger watch SuuntoPlus guides sync")
            }
        }

        return when (result) {
            is SuuntoPlusGuideSyncLogicResult.Failure -> Result.failure()
            SuuntoPlusGuideSyncLogicResult.NoNewData -> Result.success()
            is SuuntoPlusGuideSyncLogicResult.Success -> Result.success()
        }
    }

    override suspend fun getForegroundInfo(): ForegroundInfo {
        val notificationId = STTConstants.NotificationIds.EXPEDITED_SUUNTO_PLUS_SYNC
        val notification = createNotification()

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            ForegroundInfo(
                notificationId,
                notification,
                ServiceInfo.FOREGROUND_SERVICE_TYPE_DATA_SYNC
            )
        } else {
            ForegroundInfo(notificationId, notification)
        }
    }

    private fun createNotification() =
        NotificationCompat.Builder(context, CHANNEL_ID_FOREGROUND_SYNC)
            .setSmallIcon(R.drawable.icon_notification)
            .setVisibility(NotificationCompat.VISIBILITY_PRIVATE)
            .setContentTitle(context.getString(R.string.notification_channel_foreground_sync))
            .setContentText(context.getString(R.string.notification_foreground_sync_content))
            .build()

    class Factory
    @Inject constructor(
        private val remoteSyncLogic: SuuntoPlusGuideRemoteSyncLogic,
        private val watchSyncProvider: SuuntoPlusGuideWatchSyncProvider,
        private val watchCapabilitiesUseCase: GetWatchCapabilitiesUseCase,
        private val watchIndependentSyncLogic: SuuntoPlusGuideWatchIndependentRemoteSyncLogic,
    ) : CoroutineWorkerAssistedFactory {
        override fun create(context: Context, params: WorkerParameters): ListenableWorker {
            return SuuntoPlusGuideRemoteSyncJob(
                context = context,
                params = params,
                remoteSyncLogic = remoteSyncLogic,
                watchSyncProvider = watchSyncProvider,
                watchCapabilitiesUseCase = watchCapabilitiesUseCase,
                watchIndependentSyncLogic = watchIndependentSyncLogic,
            )
        }
    }

    companion object {
        private const val TAG = "SuuntoPlusGuideRemoteSyncJob"
        private const val KEY_ALWAYS_TRIGGER_WATCH_SYNC = "KEY_ALWAYS_TRIGGER_WATCH_SYNC"

        @JvmStatic
        fun enqueue(
            workManager: WorkManager,
            alwaysTriggerWatchSync: Boolean = false,
        ) {
            workManager.ifNotAlreadyScheduled(TAG) {
                workManager.enqueueUniqueWork(
                    TAG,
                    ExistingWorkPolicy.APPEND_OR_REPLACE,
                    OneTimeWorkRequestBuilder<SuuntoPlusGuideRemoteSyncJob>()
                        .apply {
                            // Don't require network if alwaysTriggerWatchSync is set. This allows the
                            // job to trigger the watch sync even if remote is won't succeed.
                            //
                            // If alwaysTriggerWatchSync is not set, then it makes no sense to run the
                            // job without a network connection.
                            if (!alwaysTriggerWatchSync) {
                                setConstraints(
                                    Constraints.Builder()
                                        .setRequiredNetworkType(NetworkType.CONNECTED)
                                        .build()
                                )
                            }
                        }
                        .setInputData(
                            Data.Builder()
                                .putBoolean(
                                    KEY_ALWAYS_TRIGGER_WATCH_SYNC,
                                    alwaysTriggerWatchSync
                                )
                                .build()
                        )
                        .setExpedited(OutOfQuotaPolicy.RUN_AS_NON_EXPEDITED_WORK_REQUEST)
                        .build()
                )
            }
        }
    }
}

class SuuntoPlusGuideRemoteSyncJobLauncherImpl @Inject constructor(
    private val workManager: WorkManager
) : SuuntoPlusGuideRemoteSyncJobLauncher {
    override fun enqueueRemoteSyncJob(alwaysTriggerWatchSync: Boolean) {
        SuuntoPlusGuideRemoteSyncJob.enqueue(workManager, alwaysTriggerWatchSync)
    }
}
