package com.stt.android.device.suuntoplusfeature.details

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import com.stt.android.common.viewstate.LoadingStateViewModel
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusSyncState
import com.stt.android.device.domain.GetCurrentWatchfaceIdUseCase
import com.stt.android.device.domain.SetCurrentWatchfaceUseCase
import com.stt.android.device.domain.suuntoplusfeature.FeatureAndWatchStatus
import com.stt.android.device.domain.suuntoplusfeature.GetSuuntoPlusFeatureUseCase
import com.stt.android.device.domain.suuntoplusfeature.NumberOfEnabledFeaturesUseCase
import com.stt.android.device.domain.suuntoplusfeature.RemoveFeatureFromLibraryUseCase
import com.stt.android.device.domain.suuntoplusfeature.SetEnabledStateForFeatureUseCase
import com.stt.android.device.domain.suuntoplusfeature.isWatchface
import com.stt.android.device.domain.suuntoplusfeature.settings.DoesSportsAppSupportSettingsUseCase
import com.stt.android.device.domain.suuntoplusguide.IsSuuntoPlusGuideSyncOngoingUseCase
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusPluginStatus
import com.stt.android.device.remote.suuntoplusguide.DelayedSuuntoPlusGuideRemoteSyncTrigger
import com.stt.android.device.remote.suuntoplusguide.DelayedSuuntoPlusGuideRemoteSyncTriggerImpl
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.watch.IsWatchBusyUseCase
import com.stt.android.domain.watch.IsWatchConnectedUseCase
import com.stt.android.watch.background.SuuntoPlusGuideRemoteSyncJobLauncher
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import timber.log.Timber
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.format.FormatStyle
import javax.inject.Inject

@HiltViewModel
class SuuntoPlusFeatureDetailsViewModel @Inject constructor(
    private val getSuuntoPlusFeatureUseCase: GetSuuntoPlusFeatureUseCase,
    private val removeFeatureFromLibraryUseCase: RemoveFeatureFromLibraryUseCase,
    private val isSyncOngoingUseCase: IsSuuntoPlusGuideSyncOngoingUseCase,
    private val doesSportsAppSupportSettingsUseCase: DoesSportsAppSupportSettingsUseCase,
    private val setEnabledStateForFeatureUseCase: SetEnabledStateForFeatureUseCase,
    private val numberOfEnabledFeaturesUseCase: NumberOfEnabledFeaturesUseCase,
    private val watchConnectedUseCase: IsWatchConnectedUseCase,
    private val isWatchBusyUseCase: IsWatchBusyUseCase,
    remoteSyncJobLauncher: SuuntoPlusGuideRemoteSyncJobLauncher,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    private val getCurrentWatchfaceIdUseCase: GetCurrentWatchfaceIdUseCase,
    private val setCurrentWatchfaceUseCase: SetCurrentWatchfaceUseCase,
) : LoadingStateViewModel<SuuntoPlusFeatureDetailsViewState>(
    ioThread = ioThread,
    mainThread = mainThread
),
    DelayedSuuntoPlusGuideRemoteSyncTrigger by DelayedSuuntoPlusGuideRemoteSyncTriggerImpl(
        remoteSyncJobLauncher
    ) {

    private val modificationTimeFormatter = DateTimeFormatter.ofLocalizedDateTime(
        FormatStyle.SHORT,
        FormatStyle.SHORT
    )

    var currentDescriptionText by mutableStateOf<String?>(null)
        private set

    var isTranslated by mutableStateOf(false)

    var removedFromLibrary by mutableStateOf(false)
        private set

    private fun Long.formatEpochMillisAsDateTime(): String =
        modificationTimeFormatter.format(
            LocalDateTime.ofInstant(
                Instant.ofEpochMilli(this),
                ZoneId.systemDefault()
            )
        )

    var pluginId: String? = null

    private data class CombinedState(
        val featureAndWatchStatus: FeatureAndWatchStatus?,
        val suuntoPlusSyncState: SuuntoPlusSyncState,
        val watchConnected: Boolean,
        val watchBusy: Boolean,
        val currentWatchfaceId: String?,
    )

    fun loadFeatureDetails(id: String) {
        launch {
            runSuspendCatching {
                removedFromLibrary = false
                notifyLoading()
                combine(
                    getSuuntoPlusFeatureUseCase.getFeatureAndWatchStatusById(id),
                    isSyncOngoingUseCase.getSyncStateFlow(),
                    watchConnectedUseCase.isWatchConnected(),
                    isWatchBusyUseCase.isWatchBusy(),
                    getCurrentWatchfaceIdUseCase(),
                    ::CombinedState
                )
                    .catch {
                        Timber.w(it, "Failed to query for feature and watch status")
                        notifyError(it, viewState.value?.data)
                    }
                    .collect { (featureAndStatus, syncOngoingStatus, watchConnected, watchBusy, currentWatchfaceId) ->
                        val feature = featureAndStatus?.feature
                        if (feature == null) {
                            notifyError(NullPointerException(), viewState.value?.data)
                        } else {
                            val supportsSettings = runSuspendCatching {
                                doesSportsAppSupportSettingsUseCase.doesSportsAppSupportSettings(id)
                            }
                                .onFailure { Timber.w(it, "Failed to check for settings support") }
                                .getOrDefault(false)

                            pluginId = feature.pluginId
                            val syncing = syncOngoingStatus.isSyncOngoing
                            val supported = featureAndStatus.watchStatus != SuuntoPlusPluginStatus.NOT_SUPPORTED

                            val haveSpaceForInstalling =
                                numberOfEnabledFeaturesUseCase.canOneMoreFeatureBeEnabled(feature.isWatchface())

                            if (currentDescriptionText.isNullOrEmpty()) { // It‘s only used for initialization and doesn’t change the display state
                                feature.localizedRichText?.let {
                                    if (it.isEmpty()) {
                                        currentDescriptionText = feature.richDescription
                                    } else {
                                        currentDescriptionText = it
                                        isTranslated = true
                                    }
                                }
                            }
                            val isWatchface = feature.getFeatureType().isWatchface()
                            val isCurrentWatchface = currentWatchfaceId != null && feature.id == currentWatchfaceId

                            notifyDataLoaded(
                                SuuntoPlusFeatureDetailsViewState(
                                    title = feature.name,
                                    description = feature.description,
                                    richDescription = feature.richDescription,
                                    ownerLogoUrl = null,
                                    url = feature.url,
                                    owner = feature.owner,
                                    watchStatus = featureAndStatus.watchStatus,
                                    bannerImageUrl = feature.bannerUrl,
                                    detailScreenImageUrl = feature.iconUrl,
                                    installOnWatch = feature.enabled && supported,
                                    enableInstallButton = !syncing && supported && haveSpaceForInstalling,
                                    enableUninstallButton = !syncing,
                                    syncOngoing = syncing,
                                    enabled = feature.enabled && featureAndStatus.watchStatus != SuuntoPlusPluginStatus.NOT_SUPPORTED,
                                    modificationTime = feature.modifiedMillis.formatEpochMillisAsDateTime(),
                                    supportsSettings = supportsSettings,
                                    showWatchNotConnectedMessage = supportsSettings && !watchConnected,
                                    showWatchBusyMessage = supportsSettings && watchBusy,
                                    localizedRichTextAutomatically = feature.localizedRichTextAutomatically,
                                    localizedRichText = feature.localizedRichText,
                                    type = feature.getFeatureType(),
                                    isCurrentWatchface = isCurrentWatchface,
                                    watchfaceId = if (isWatchface) feature.id else null,
                                    showSetAsCurrentWatchfaceButton = watchConnected && isWatchface && !isCurrentWatchface && featureAndStatus.watchStatus == SuuntoPlusPluginStatus.IN_WATCH && feature.enabled,
                                    haveSpaceForInstalling = haveSpaceForInstalling,
                                )
                            )
                        }
                    }
            }.onFailure { e ->
                Timber.w(e, "Failed to load feature details for id $id")
                notifyError(e, viewState.value?.data)
            }
        }
    }

    fun removeFeatureFromLibrary(featureId: String) =
        launch {
            runSuspendCatching {
                removeFeatureFromLibraryUseCase.removeFeatureFromLibrary(featureId)
                removedFromLibrary = true
                syncNow(alwaysTriggerWatchSync = false)
            }.onFailure { e ->
                Timber.w(e, "Failed to remove feature $featureId")
                notifyError(e, viewState.value?.data)
            }
        }

    fun updateEnabledState(featureId: String, enabled: Boolean) {
        launch {
            runSuspendCatching {
                setEnabledStateForFeatureUseCase.updateEnabledState(featureId, enabled)
                syncNow(alwaysTriggerWatchSync = true)
            }.onFailure {
                Timber.w(it, "Failed to update enabled state for feature ID $featureId")
            }
        }
    }

    fun changeLanguage(translated: Boolean) {
        isTranslated = translated
        currentDescriptionText = if (translated) {
            viewState.value?.data?.localizedRichText ?: ""
        } else {
            viewState.value?.data?.richDescription ?: ""
        }
    }

    override fun retryLoading() = Unit

    fun setAsCurrentWatchface() {
        launch {
            viewState.value?.data?.watchfaceId?.let {
                setCurrentWatchfaceUseCase(it)
            }
        }
    }
}
