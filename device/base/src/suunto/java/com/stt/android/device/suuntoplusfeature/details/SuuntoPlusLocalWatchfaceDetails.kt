package com.stt.android.device.suuntoplusfeature.details

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.confirmation
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.device.R
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusPluginStatus
import com.stt.android.device.suuntoplusdetails.SuuntoPlusItemDetailsHeader
import com.stt.android.device.suuntoplusdetails.note.SuuntoPlusDetailsNote
import com.stt.android.device.suuntoplusdetails.note.SuuntoPlusItemDetailNote
import com.stt.android.suuntoplus.SuuntoPlusItemLabel
import com.stt.android.suuntoplus.ui.SuuntoPlusItemDetailsScreenLabels
import kotlinx.collections.immutable.toPersistentList
import java.util.Locale
import com.stt.android.R as BR

@Composable
fun SuuntoPlusLocalWatchfaceDetails(
    watchStatus: SuuntoPlusPluginStatus,
    isCurrentWatchface: Boolean,
    watchPreviewImageUrl: String?,
    title: String,
    onSetAsCurrentWatchfaceClick: () -> Unit,
    showSetAsCurrentWatchfaceButton: Boolean,
    installOnWatch: Boolean,
    enableInstallButton: Boolean,
    onInstallClick: () -> Unit,
    enableUninstallButton: Boolean,
    onUninstallClick: () -> Unit,
    haveSpaceForInstalling: Boolean,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
    ) {
        SuuntoPlusItemDetailsHeader(
            bannerImageUrl = null,
            bannerImageScale = ContentScale.Crop,
            watchPreviewImageUrl = watchPreviewImageUrl,
            title = title,
            showWatchPreviewOnly = true,
        )

        SuuntoPlusItemDetailsScreenLabels(
            labels = buildList {
                if (isCurrentWatchface) {
                    add(
                        SuuntoPlusItemLabel(
                            text = stringResource(id = com.stt.android.device.R.string.suunto_plus_store_watch_faces_in_use_label),
                            color = MaterialTheme.colors.confirmation
                        )
                    )
                }
                add(
                    SuuntoPlusItemLabel(
                        text = stringResource(id = com.stt.android.device.R.string.suunto_plus_store_watch_faces_in_my_watch_faces_label),
                        color = MaterialTheme.colors.primary
                    )
                )

                if (watchStatus == SuuntoPlusPluginStatus.IN_WATCH) {
                    add(
                        SuuntoPlusItemLabel(
                            text = stringResource(id = com.stt.android.device.R.string.suunto_plus_status_synced_to_watch),
                            color = MaterialTheme.colors.confirmation
                        )
                    )
                }
            }.toPersistentList(),
            modifier = Modifier.padding(MaterialTheme.spacing.medium)
        )

        if (!haveSpaceForInstalling) {
            SuuntoPlusItemDetailNote(
                note = SuuntoPlusDetailsNote.MaxFeatureLimitReachedNote(true),
                onNoteAction = null,
                modifier = Modifier.padding(MaterialTheme.spacing.medium)
            )
        }

        if (watchStatus == SuuntoPlusPluginStatus.NOT_SUPPORTED) {
            SuuntoPlusItemDetailNote(
                note = SuuntoPlusDetailsNote.GenericTextResourceNote(
                    R.string.suunto_plus_watch_not_compatible_with_sports_app
                ),
                onNoteAction = {},
                modifier = Modifier.padding(MaterialTheme.spacing.medium)
            )
        } else {
            Spacer(modifier = Modifier.weight(1f))

            WatchfaceButtons(
                installOnWatch = installOnWatch,
                enableInstallButton = enableInstallButton,
                onInstallClick = onInstallClick,
                enableUninstallButton = enableUninstallButton,
                onUninstallClick = onUninstallClick,
                showSetAsCurrentWatchfaceButton = showSetAsCurrentWatchfaceButton,
                onSetAsCurrentWatchfaceClick = onSetAsCurrentWatchfaceClick,
            )
        }
    }
}

@Composable
private fun WatchfaceButtons(
    installOnWatch: Boolean,
    enableInstallButton: Boolean,
    onInstallClick: () -> Unit,
    enableUninstallButton: Boolean,
    onUninstallClick: () -> Unit,
    showSetAsCurrentWatchfaceButton: Boolean,
    onSetAsCurrentWatchfaceClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        if (showSetAsCurrentWatchfaceButton) {
            PrimaryButton(
                text = stringResource(id = BR.string.suunto_plus_set_as_current_watch_face),
                onClick = { onSetAsCurrentWatchfaceClick() },
                enabled = enableUninstallButton,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(MaterialTheme.spacing.medium)
            )
        }

        if (installOnWatch) {
            TextButton(
                onClick = onUninstallClick,
                enabled = enableUninstallButton,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = MaterialTheme.colors.error,
                    disabledContentColor = MaterialTheme.colors.error.copy(alpha = 0.6f)
                ),
                modifier = Modifier
                    .align(Alignment.CenterHorizontally)
                    .padding(bottom = MaterialTheme.spacing.medium)
            ) {
                Text(
                    text = stringResource(id = R.string.suunto_plus_floating_action_button_uninstall_from_watch).uppercase(
                        Locale.getDefault()
                    ),
                )
            }
        } else {
            PrimaryButton(
                text = stringResource(id = R.string.suunto_plus_floating_action_button_install_on_watch).uppercase(Locale.getDefault()),
                enabled = enableInstallButton,
                onClick = onInstallClick,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(MaterialTheme.spacing.medium)
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun SuuntoPlusLocalWatchfaceDetailsPreview() {
    AppTheme {
        SuuntoPlusLocalWatchfaceDetails(
            watchStatus = SuuntoPlusPluginStatus.IN_WATCH,
            isCurrentWatchface = false,
            watchPreviewImageUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-icons/zzcaco01.png",
            title = "Cadence coach",
            onSetAsCurrentWatchfaceClick = {},
            showSetAsCurrentWatchfaceButton = true,
            installOnWatch = true,
            enableInstallButton = false,
            onInstallClick = {},
            enableUninstallButton = true,
            haveSpaceForInstalling = true,
            onUninstallClick = {},
        )
    }
}
