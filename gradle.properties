# ---------- JVM ----------
# G<PERSON><PERSON>'s own JVM: 6 GB heap, ParallelGC (faster startup, simpler GC for dev)
org.gradle.jvmargs=-Dfile.encoding=UTF-8 -Xmx6g -Xms2g \
                   -XX:+UseParallelGC

# Kotlin compiler daemons: 4 GB heap each, ParallelGC
kotlin.daemon.jvmargs=-Xmx4g -XX:+UseParallelGC -Dfile.encoding=UTF-8

# ---------- Worker limits ----------
org.gradle.workers.max=4

# ---------- Performance ----------
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configuration-cache=true
org.gradle.configuration-cache.problems=warn
org.gradle.configuration-cache.parallel=true
org.gradle.configuration-cache.test-warmup=false

# ---------- Android ----------
android.useAndroidX=true
android.enableJetifier=true
android.enableAppCompileTimeRClass=true
android.enableR8.fullMode=false
android.lint.useK2Uast=true

# ---------- Project flags ----------
uploadMappingFiles=false
exclude32bit=false
VERSION_NAME=1.0.0-SNAPSHOT
