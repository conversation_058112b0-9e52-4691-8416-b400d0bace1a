#!/bin/bash

# You can add this script to Android Studio:
# 1. Open Preferences -> Tools -> External tools
# 2. Type "$ProjectFileDir$/compile_run.sh" (without quotes) as value for Program
# 3. Type "$ProjectFileDir$" (without quotes) as value for Working directory
# 4. (Optional) You can pass the min SDK as parameter by adding "-PminSdk=23" in the Parameters field
# 5. You can assign your favourite shortcut to it as with any other command in AS (Preferences -> Keymap -> External tools)

GRADLE_TASK=:"STTAndroid:assembleSportstrackerPlaystoreDebug"
echo Executing task ${GRADLE_TASK} ...
command -v osascript >/dev/null 2>&1 && osascript -e 'display notification with title "Building APK" sound name "Purr"'
time GRADLE_OUTPUT="$(./gradlew -quiet $1 ${GRADLE_TASK} 2>&1)"
GRADLE_RESULT=$?
if [ $GRADLE_RESULT -ne 0 ]
  then
    echo "$GRADLE_OUTPUT" 1>&2
    exit $GRADLE_RESULT
fi
command -v adb >/dev/null 2>&1 || { echo >&2 "I require adb but it's not in your PATH.  Aborting."; exit 1; }
# get connected devices ID
DEVICES=`adb devices | tail -n +2 | cut -f1`
echo "`adb devices -l`"
ME=`whoami`
for i in ${DEVICES}
do
  echo "Firing intent for device $i"
  # Notice below two lines are only one command run in the background (see last '&')
  adb -s $i install -r ./STTAndroid/build/outputs/apk/STTAndroid-komposti-sportstracker-debug.apk >/dev/null 2>&1 && \
  adb -s $i shell am start -n "com.stt.android.$ME/com.stt.android.launcher.ProxyActivity" -a android.intent.action.MAIN -c android.intent.category.LAUNCHER >/dev/null 2>&1 &
done
# Wait for above commands to finish
wait
# If possible show a notification
command -v osascript >/dev/null 2>&1 && osascript -e 'display notification with title "APK Deployed" sound name "Purr"'
