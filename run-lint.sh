#!/bin/bash
set -e

ST="./gradlew :app:lintSpoPlaRel"
SUUNTO_PLAYSTORE="./gradlew :app:lintSuuPlayRel"
SUUNTO_CHINA="./gradlew :app:lintSuuChiRe"

COMMAND=$1  # Save the first argument to decide the command
shift       # Shift the arguments so that $@ no longer includes the first one

case "$COMMAND" in
    --sportstracker) $ST "$@" ;;
    --suuntoPlaystore) $SUUNTO_PLAYSTORE "$@" ;;
    --suuntoChina) $SUUNTO_CHINA "$@" ;;
    *) $ST "$@" && $SUUNTO_PLAYSTORE "$@" && $SUUNTO_CHINA "$@" ;;
esac
