# EditorConfig is awesome: http://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
# Prevent Git (and Github) "No new line at end of file" warning
[*]
insert_final_newline = true

# 4 space indentation for Java and Kotlin source files
[*.{java,kt}]
indent_style = space
indent_size = 4
ktlint_code_style = android_studio

[*.{kt,kts}]
compose_allowed_composition_locals = LocalIsEdit,LocalPlayer,LocalIsMaterial3,LocalHyperLinkHandler,LocalExtraPalette,LocalExtraShapes,LocalSpacing,LocalExtraTextStyles,LocalElevation,LocalIconSizes,LocalShowBottomSheet,LocalTrainingHubDimens,LocalTrainingHubColors,LocalHomeWidgetScale
# Upgraded kotlinter to 5.0.1, it used ktlint 1.5.0, and then there were a lot of standard and compose lint errors, so I disabled it, don't know any good solutions.
ktlint_compose_modifier-composed-check = disabled # Should investigate if we can replace calls to composed()
ktlint_compose_parameter-naming = disabled # Requires renaming a lot of params to enable
ktlint_compose_lambda-param-in-effect = disabled # Requires refactoring/suppressing a few features

ktlint_standard_function-signature = disabled
ktlint_standard_trailing-comma-on-call-site = disabled
ktlint_standard_trailing-comma-on-declaration-site = disabled
ktlint_standard_chain-method-continuation = disabled
ktlint_standard_parameter-list-wrapping = disabled
ktlint_standard_import-ordering = disabled
ktlint_standard_multiline-expression-wrapping = disabled
ktlint_standard_no-empty-first-line-in-class-body = disabled
ktlint_standard_max-line-length = disabled
ktlint_standard_function-expression-body = disabled
ktlint_standard_property-naming = disabled
ktlint_standard_class-signature = disabled
ktlint_standard_no-blank-line-in-list = disabled
ktlint_standard_string-template-indent = disabled
ktlint_standard_blank-line-before-declaration = disabled
ktlint_standard_chain-wrapping = disabled
ktlint_standard_function-naming = disabled
ktlint_standard_condition-wrapping = disabled
ktlint_standard_argument-list-wrapping = disabled
ktlint_standard_wrapping = disabled
ktlint_standard_annotation = disabled
ktlint_standard_colon-spacing = disabled
ktlint_standard_no-blank-line-before-rbrace = disabled
ktlint_standard_no-consecutive-blank-lines = disabled
ktlint_standard_paren-spacing = disabled
ktlint_standard_spacing-between-declarations-with-annotations = disabled
ktlint_standard_no-unused-imports = disabled
ktlint_standard_no-single-line-block-comment = disabled
ktlint_standard_parameter-list-spacing = disabled
ktlint_standard_no-multi-spaces = disabled
ktlint_standard_value-parameter-comment = disabled
ktlint_standard_no-consecutive-comments = disabled
ktlint_standard_backing-property-naming = disabled
ktlint_standard_unnecessary-parentheses-before-trailing-lambda = disabled
ktlint_standard_comma-spacing = disabled
ktlint_standard_function-literal = disabled
ktlint_standard_no-empty-first-line-in-method-block = disabled
ktlint_standard_curly-spacing = disabled
ktlint_standard_op-spacing = disabled
ktlint_standard_spacing-between-function-name-and-opening-parenthesis = disabled
ktlint_standard_filename = disabled
ktlint_standard_if-else-wrapping = disabled
ktlint_standard_if-else-bracing = disabled
ktlint_standard_multiline-if-else = disabled
ktlint_standard_range-spacing = disabled
ktlint_standard_enum-wrapping = disabled
ktlint_standard_kdoc-wrapping = disabled
ktlint_standard_statement-wrapping = disabled
ktlint_standard_string-template = disabled
ktlint_standard_type-parameter-list-spacing = disabled
ktlint_standard_spacing-between-declarations-with-comments = disabled
ktlint_standard_function-start-of-body-spacing = disabled
ktlint_standard_function-return-type-spacing = disabled
ktlint_standard_no-trailing-spaces = disabled
ktlint_standard_no-semi = disabled
ktlint_standard_comment-wrapping = disabled
ktlint_standard_no-wildcard-imports = disabled
ktlint_standard_package-name = disabled
ktlint_standard_no-unit-return = disabled # Gets confused by our own enum class Unit
ktlint_standard_indent = disabled

# Disable all compose rules from divemodecustomization folder
[divecustomization/**.{kt,kts}]
ktlint_compose_lambda-param-event-trailing = disabled
ktlint_compose_mutable-state-autoboxing = disabled
