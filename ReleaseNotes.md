#### 5.4.4 (2025-06-26)
 - Fixes
  - 186898 187189 187284 Fix TP bugs by author:wang<PERSON><PERSON><PERSON>-suunto
  - 187104 Put paired headset to backend, for headset view in user profile by author:wang<PERSON><PERSON><PERSON>-suunto
  - 176972 187239 187278 Fix TP bugs by author:wangxia<PERSON>un-suunto
  - 187309 Revert settings lock to avoid dead lock by author:wangxia<PERSON>un-suunto
  - 184362,186811,186970,186977,186981,187212,187250 bug by author:shua<PERSON><PERSON>chen
  - 186246 187098 187187 187188 187245 187248 Fix TP bugs by author:wangxia<PERSON>un-suunto
  - 186950 186937 UI adjustments for device page by author:JessieeLi
  - 185927 Make sure minDate is never before today by author:<PERSON>
  - 187398 Add Back to current button in training by author:Jiang<PERSON>ianming2
  - 187364 Support faded color for training volume chart by author:Jiang<PERSON>ianming2
  - Remove feature toggles(KEY_ENABLE_NEW_FRIENDS/KEY_ENABLE_NEW_USER_PROFILE/KEY_ENABLE_NEW_ACCOUNT_DELETION) by author:wang<PERSON><PERSON><PERSON>-suunto
  - Show error tips for new friends related views by author:wang<PERSON><PERSON><PERSON>-suunto
  - 186996 Coach output into UI reorganisation by author:<PERSON><PERSON>ianming2
  - 186167 Add top route filter above carousel card by author:moon
  - Update top route filter UI by author:moon
  - Optimize top route carousel item loading ui by author:moon
  - Add some MapDelegatePublic apis by author:moon
  - Update TopRouteFilterChip ui style by author:moon

 - Technical
  - Double check the sleepDurationAvg meets coach requirement by author:JiangXianming2
  - Remove RxJava from several places by author:Xizhi Zhu (Steven)
  - Add test tileServerUrl for TEST_CHINA by author:JiangXianming2
