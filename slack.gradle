import com.stt.gradle.slack.SlackTask

tasks.register('publishReleaseNotes', SlackTask) {
    messageText = file('ReleaseNotes.md').getText('UTF-8')
    // This webhook URL points to #mobile-app-releases channel
    webhookUrl = "*****************************************************************************"
}

tasks.register('publishIntermediateReleaseNotes', SlackTask) {
    messageText = file('ReleaseNotes.md').getText('UTF-8')
    // This webhook URL points to #symbio-compatibility channel
    webhookUrl = "*****************************************************************************"
}

tasks.register('announceReleaseBranch', SlackTask) {
    messageText = "Release branch ${-> project.versionName} has been created"
    // This webhook URL points to #mobile-android-dev channel
    webhookUrl = "*****************************************************************************"
}
