package com.stt.android.workoutdetail.location.select

import android.location.Location
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.distinctUntilChanged
import com.google.android.gms.maps.model.LatLng
import com.stt.android.R
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.domain.mapbox.FetchLocationNameUseCase
import com.stt.android.maps.OnMapReadyCallback
import com.stt.android.maps.SuuntoMap
import com.stt.android.maps.location.SuuntoLocationListener
import com.stt.android.maps.location.SuuntoLocationSource
import com.stt.android.maps.newLatLngZoom
import com.stt.android.models.MapSelectionModel
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.utils.STTConstants
import com.stt.android.workoutdetail.location.base.BaseWorkoutLocationViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class WorkoutSelectLocationViewModel
@Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    mapSelectionModel: MapSelectionModel,
    fetchLocationNameUseCase: FetchLocationNameUseCase,
    private val locationSource: SuuntoLocationSource,
    coroutinesDispatchers: CoroutinesDispatchers,
) : BaseWorkoutLocationViewModel(
    latLng = savedStateHandle[WorkoutSelectLocationFragment.ARGUMENT_COORDINATE],
    mapSelectionModel = mapSelectionModel,
    fetchLocationNameUseCase = fetchLocationNameUseCase,
    coroutinesDispatchers = coroutinesDispatchers,
),
    OnMapReadyCallback,
    SuuntoLocationListener {

    private var latLng: LatLng?
        get() = savedStateHandle[WorkoutSelectLocationFragment.ARGUMENT_COORDINATE]
        set(value) { savedStateHandle[WorkoutSelectLocationFragment.ARGUMENT_COORDINATE] = value }

    private var map: SuuntoMap? = null

    private val _title = MutableLiveData<Int>().apply { value = R.string.add_location }

    val title: LiveData<Int>
        get() = _title

    private val _isLocationCentered = MutableLiveData<Boolean?>()
    val isLocationCentered
        get() = _isLocationCentered.distinctUntilChanged()

    val checkLocationPermissionEvent: LiveData<Unit>
        get() = _checkLocationPermissionEvent
    private val _checkLocationPermissionEvent = SingleLiveEvent<Unit>()

    val onLocationButtonClickedEvent: LiveData<Unit>
        get() = _onLocationButtonClickedEvent
    private val _onLocationButtonClickedEvent = SingleLiveEvent<Unit>()

    val noLocationInfoEvent: LiveData<Unit>
        get() = _noLocationInfoEvent
    private val _noLocationInfoEvent = SingleLiveEvent<Unit>()

    private val onMapMoveListener = object : SuuntoMap.OnMapMoveListener {
        override fun onMapMoveBegin() {
            _isLocationCentered.postValue(false)
        }

        override fun onMapMoveEnd() {
            // No action
        }
    }

    override fun onMapReady(map: SuuntoMap) {
        this.map = map
        map.addOnMapMoveListener(onMapMoveListener)
        setLocationIfAvailable()
    }

    private fun setLocationIfAvailable() {
        val latLng = this.latLng
        if (latLng != null) {
            _title.postValue(R.string.edit_location)
            setLocation(latLng, animated = false)
        } else {
            _title.postValue(R.string.add_location)
            _checkLocationPermissionEvent.call()
        }
    }

    fun onLocationButtonClicked() {
        _onLocationButtonClickedEvent.call()
    }

    // TODO There is also a setLocation() function in BaseWorkoutLocationViewModel. Figure out if
    //  we can merge these two.
    fun setLocation(latLng: LatLng, animated: Boolean) {
        this.latLng = latLng
        setCamera(latLng, animated)
    }

    private fun setCamera(latLng: LatLng, animated: Boolean = false) {
        val latLngZoom = newLatLngZoom(
            latLng,
            STTConstants.MapPreferences.DEFAULT_ZOOM_LEVEL
        )
        if (animated) {
            map?.animateCamera(latLngZoom)
        } else {
            map?.moveCamera(latLngZoom)
        }
    }

    fun locationPermissionGranted() {
        val locationSource = locationSource
        setLocationSource(locationSource)
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun setLocationSource(locationSource: SuuntoLocationSource) {
        map?.let { map ->
            map.setLocationSource(locationSource)
            map.setMyLocationEnabled(true)
            locationSource.getLastKnownLocation(
                { location ->
                    onLocationChanged(location, locationSource)
                },
                { error ->
                    Timber.w(error, "Error getting location")
                    _noLocationInfoEvent.call()
                }
            )
        }
    }

    override fun onLocationChanged(location: Location, source: SuuntoLocationSource) {
        setCamera(LatLng(location.latitude, location.longitude))
        _isLocationCentered.postValue(true)
    }

    override fun onLocationAvailability(locationAvailable: Boolean, source: SuuntoLocationSource) {
        // do nothing
    }
}
