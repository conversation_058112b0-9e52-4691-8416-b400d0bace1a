package com.stt.android.workoutdetail.recentsummary

import android.content.Context
import android.content.res.Resources
import com.amersports.formatter.Unit
import com.amersports.formatter.unit.jscience.JScienceUnitConverter
import com.github.mikephil.charting.charts.CombinedChart
import com.github.mikephil.charting.components.LimitLine
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.data.BarDataSet
import com.github.mikephil.charting.data.CombinedData
import com.stt.android.ThemeColors.primaryTextColor
import com.stt.android.core.R
import com.stt.android.domain.user.workout.RecentWorkoutSummary
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.components.charts.RecentWorkoutMarkerView
import com.stt.android.ui.components.charts.RecentWorkoutSummaryChart
import com.stt.android.ui.utils.TextFormatter
import java.util.Locale
import com.stt.android.R as BaseR

/**
 * help build recentWorkoutSummary item graph
 */
object RecentWorkoutItemHelper {
    const val PAGE_DURATION = 0
    const val PAGE_DISTANCE = 1
    const val PAGE_SPEED = 2
    const val PAGE_PACE = 3
    const val PAGE_ENERGY = 4
    const val PAGE_AVERAGE_HEART_RATE = 5
    const val PAGE_AVERAGE_CADENCE = 6

    fun formatValue(
        value: Float,
        itemPosition: Int,
        resources: Resources,
        activityType: ActivityType,
        infoModelFormatter: InfoModelFormatter,
        unitConverter: JScienceUnitConverter,
    ): String? = when (itemPosition) {
        PAGE_DURATION -> infoModelFormatter.formatValue(SummaryItem.DURATION, value).value

        PAGE_DISTANCE -> if (activityType.usesNauticalUnits) {
            val distance = infoModelFormatter.formatValue(SummaryItem.NAUTICALDISTANCE, value)
            "${distance.value} ${resources.getString(distance.unit ?: R.string.nautical_mile)}"
        } else {
            val distance = infoModelFormatter.formatValue(SummaryItem.DISTANCE, value)
            "${distance.value} ${resources.getString(distance.unit ?: infoModelFormatter.unit.distanceUnit)}"
        }

        PAGE_SPEED -> if (activityType.usesNauticalUnits) {
            String.format(
                Locale.getDefault(),
                "%s %s",
                TextFormatter.formatSpeed(infoModelFormatter.unit.toKnots(value.toDouble())),
                resources.getString(R.string.knots)
            )
        } else {
            val distance = infoModelFormatter.formatValue(SummaryItem.AVGSPEED, value)
            "${distance.value} ${resources.getString(distance.unit ?: infoModelFormatter.unit.speedUnit)}"
        }

        PAGE_PACE -> {
            "${infoModelFormatter.formatDurationAsPace(value * 60.0)} ${resources.getString(infoModelFormatter.unit.paceUnit)}"
        }

        PAGE_ENERGY -> {
            val joules = unitConverter.convert(value.toDouble(), Unit.KCAL, Unit.J)
            val energy = infoModelFormatter.formatValue(SummaryItem.ENERGY, joules)
            "${energy.value} ${resources.getString(energy.unit ?: R.string.kcal)}"
        }

        PAGE_AVERAGE_HEART_RATE -> {
            val hz = unitConverter.convert(value.toDouble(), Unit.RPM, Unit.HZ)
            val heartRate = infoModelFormatter.formatValue(SummaryItem.AVGHEARTRATE, hz)
            "${heartRate.value} ${resources.getString(heartRate.unit ?: R.string.bpm)}"
        }

        PAGE_AVERAGE_CADENCE -> {
            val cadence = infoModelFormatter.formatValue(SummaryItem.AVGCADENCE, value)
            "${cadence.value} ${resources.getString(cadence.unit ?: BaseR.string.rpm)}"
        }

        else -> throw IllegalArgumentException("Unsupported page: $itemPosition")
    }

    /**
     * for indoor activities, we hide distance, speed, and pace
     * for activities other than cycling and mountain biking, we hide cadence
     */
    fun positionToPage(activityType: ActivityType, position: Int): Int = if (activityType.isIndoor) {
        if (position == 0) {
            PAGE_DURATION
        } else {
            position + 3
        }
    } else {
        position
    }

    fun initChart(
        context: Context,
        referenceWorkout: WorkoutHeader,
        recentWorkoutSummary: RecentWorkoutSummary,
        chart: RecentWorkoutSummaryChart,
        position: Int,
        infoModelFormatter: InfoModelFormatter,
        unitConverter: JScienceUnitConverter,
    ) {
        val itemPosition = positionToPage(referenceWorkout.activityType, position)
        chart.marker = RecentWorkoutMarkerView(
            context,
            itemPosition,
            referenceWorkout.activityType,
            infoModelFormatter,
            unitConverter,
        )
        chart.setTouchEnabled(false)
        chart.axisRight.isEnabled = false
        val xAxis = chart.xAxis
        xAxis.setDrawLabels(false)
        xAxis.position = XAxis.XAxisPosition.BOTTOM_INSIDE
        val combinedData = when (itemPosition) {
            PAGE_DURATION -> recentWorkoutSummary.duration
            PAGE_DISTANCE -> recentWorkoutSummary.distance
            PAGE_SPEED -> recentWorkoutSummary.speed
            PAGE_PACE -> recentWorkoutSummary.pace
            PAGE_ENERGY -> recentWorkoutSummary.energy
            PAGE_AVERAGE_HEART_RATE -> recentWorkoutSummary.averageHeartRate
            PAGE_AVERAGE_CADENCE -> recentWorkoutSummary.averageCadence
            else -> throw java.lang.IllegalArgumentException("Unsupported page: $itemPosition")
        }
        setChart(context, referenceWorkout, combinedData, chart, itemPosition, infoModelFormatter, unitConverter)
    }

    private fun setChart(
        context: Context,
        referenceWorkout: WorkoutHeader,
        data: CombinedData,
        chart: CombinedChart,
        itemPosition: Int,
        infoModelFormatter: InfoModelFormatter,
        unitConverter: JScienceUnitConverter,
    ) {
        val labelColor = primaryTextColor(context)
        val leftAxis = chart.axisLeft
        leftAxis.removeAllLimitLines()
        val barData = data.barData
        val barDataSet = barData.getDataSetByIndex(0) as BarDataSet
        val xMax = barData.xMax
        var xAxisMaximum = Math.max(xMax, 28f) // 28 means number of days
        xAxisMaximum++ // Prevents last point to be cropped in the chart
        val xAxis = chart.xAxis
        xAxis.axisMinimum = -1f // -1f prevents first point to be cropped in the chart
        xAxis.axisMaximum = xAxisMaximum
        val yValCount = barData.entryCount
        var count = yValCount
        // Remove zero values from average calculations
        for (i in 0 until yValCount) {
            if (barDataSet.getEntryForIndex(i).y.toDouble() == 0.0) {
                count--
            }
        }
        var yMax = 0f
        if (count > 1) {
            var total = 0.0f
            for (i in 0 until yValCount) {
                val y = barDataSet.getEntryForIndex(i).y
                total += y
                yMax = Math.max(y, yMax)
            }
            val yAverage = total / count
            val avgLineColor = barDataSet.color
            val average = LimitLine(
                yAverage,
                formatValue(
                    yAverage,
                    itemPosition,
                    context.resources,
                    referenceWorkout.activityType,
                    infoModelFormatter,
                    unitConverter,
                )
            )
            average.labelPosition = LimitLine.LimitLabelPosition.LEFT_TOP
            average.textColor = labelColor
            average.textSize = 9.0f
            average.lineWidth = 0.5f
            average.lineColor = avgLineColor
            average.enableDashedLine(5.0f, 15.0f, 0.0f)
            leftAxis.addLimitLine(average)
        } else {
            yMax = barDataSet.getEntryForIndex(0).y
        }
        if (yMax > 0f) {
            leftAxis.axisMaximum = yMax * 1.2f
        } else {
            leftAxis.resetAxisMaximum()
        }
        chart.data = data
        // must call these after setData()
        chart.legend.isEnabled = false
        val density: Float = context.resources.displayMetrics.density
        chart.setViewPortOffsets(15.0f * density, 20.0f * density, 15.0f * density, 5.0f * density)
        chart.animateY(750)
    }

    fun getTitle(resources: Resources, workoutHeader: WorkoutHeader?): List<String> {
        val titles = arrayListOf<String>()
        val activityType: ActivityType = workoutHeader?.activityType ?: ActivityType.DEFAULT
        val isIndoor = activityType.isIndoor
        val isSnorkeling = ActivityType.SNORKELING == activityType
        val isCycling = !isIndoor &&
            (
                ActivityType.CYCLING == activityType ||
                ActivityType.MOUNTAIN_BIKING == activityType ||
                ActivityType.GRAVEL_CYCLING == activityType
            )
        titles.add(resources.getString(BaseR.string.duration))
        if (!isIndoor && !isSnorkeling) {
            titles.add(resources.getString(BaseR.string.distance))
            titles.add(resources.getString(R.string.speed))
            titles.add(resources.getString(BaseR.string.pace))
        }
        if (isSnorkeling) {
            titles.add(resources.getString(BaseR.string.distance))
        }
        titles.add(resources.getString(BaseR.string.energy))
        titles.add(resources.getString(BaseR.string.avg_hr))
        if (isCycling) {
            titles.add(resources.getString(BaseR.string.avg_cadence))
        }
        return titles
    }
}
