package com.stt.android.workoutdetail.tags

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import androidx.compose.foundation.Image
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.airbnb.epoxy.ModelView
import com.stt.android.R
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodySmallBold
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.tags.UserTag
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.newfeed.OnTagClicked
import com.stt.android.ui.components.tags.DefaultTag
import com.suunto.connectivity.suuntoconnectivity.device.ProductType
import timber.log.Timber

@ModelView(autoLayout = ModelView.Size.MATCH_WIDTH_MATCH_HEIGHT)
class TagsCarouselView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private var composeView: ComposeView = ComposeView(context)

    init {
        addView(composeView)
    }

    fun addTags(
        tagsData: TagsData?,
        isSubscribedToPremium: Boolean,
        onTagClicked: OnTagClicked?,
    ) {
        if (tagsData == null) {
            visibility = View.GONE
            return
        }
        composeView.setContent {
            TagsView(
                tagsData = tagsData,
                isSubscribedToPremium = isSubscribedToPremium,
                onTagClicked = onTagClicked,
                modifier = Modifier.padding(
                    horizontal = MaterialTheme.spacing.xsmaller,
                    vertical = MaterialTheme.spacing.small,
                ),
            )
        }
    }
}

@Composable
fun TagsView(
    tagsData: TagsData,
    isSubscribedToPremium: Boolean,
    onTagClicked: OnTagClicked?,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .horizontalScroll(rememberScrollState()),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small)
    ) {
        tagsData.deviceTag?.let { deviceTag ->
            DeviceModel(
                displayName = deviceTag.displayName,
                productType = deviceTag.productType
            )
        }

        val premiumTags = tagsData.suuntoTags
            .filter { !it.editable }.takeIf { !isSubscribedToPremium } ?: emptyList()
        val suuntoTags = tagsData.suuntoTags - premiumTags.toSet()
        buildList {
            addAll(suuntoTags)
            premiumTags.firstOrNull()?.let { add(it) }
        }.forEach { suuntoTag ->
            val tagName = stringResource(suuntoTag.nameRes).let {
                if (!suuntoTag.editable && !isSubscribedToPremium) {
                    stringResource(
                        id = R.string.tags_impact_non_premium,
                        stringResource(R.string.premium)
                    )
                } else {
                    it
                }
            }
            DefaultTag(
                text = tagName,
                onChecked = if (tagsData.isOwnWorkout) {
                    { onTagClicked?.invoke(tagName, suuntoTag.editable) }
                } else null,
                isUserTag = false
            )
        }

        tagsData.userTags.forEach { userTag ->
            val tagName = userTag.name
            DefaultTag(
                text = tagName,
                onChecked = if (tagsData.isOwnWorkout) {
                    { onTagClicked?.invoke(tagName, true) }
                } else null,
                isUserTag = true,
                backgroundColor = userTag.backgroundColor?.run { colorResource(this) }
                    ?: MaterialTheme.colors.surface,
            )
        }
    }
}

@Composable
private fun DeviceModel(
    displayName: String?,
    productType: String?
) {
    if (!displayName.isNullOrBlank() && !productType.isNullOrBlank()) {
        val deviceIcon: Int = try {
            ProductType.valueOf(productType).deviceIconFromProductType()
        } catch (e: IllegalArgumentException) {
            Timber.e(e, "productType is not recognized")
            null
        } ?: return

        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .wrapContentWidth()
                .heightIn(dimensionResource(R.dimen.tag_default_chip_min_height))
        ) {
            Image(
                painter = painterResource(deviceIcon),
                contentDescription = stringResource(R.string.device_icon),
                contentScale = ContentScale.Fit,
                modifier = Modifier.size(MaterialTheme.iconSizes.small)
            )
            Spacer(modifier = Modifier.width(MaterialTheme.spacing.xxsmall))
            Text(
                text = displayName,
                style = MaterialTheme.typography.bodySmallBold,
                color = MaterialTheme.colors.nearBlack
            )
        }
    }
}

private fun ProductType.deviceIconFromProductType(): Int = when (this) {
    ProductType.SPORT_WATCH,
    ProductType.DIVE_WATCH -> R.drawable.ic_device_model_watch

    ProductType.DIVE_COMPUTER -> R.drawable.ic_device_model_eon
    ProductType.BIKE_COMPUTER -> R.drawable.ic_device_model_bike
    ProductType.SPORT_EARPHONE -> R.drawable.icon_device_headset
}

@Preview(showBackground = true)
@Composable
private fun DeviceModelPreviewSportWatch() {
    AppTheme {
        DeviceModel(
            displayName = "Suunto 9 Peak",
            productType = "SPORT_WATCH"
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun DeviceModelPreviewDiveComputer() {
    AppTheme {
        DeviceModel(
            displayName = "Suunto EON Steel Black",
            productType = "DIVE_COMPUTER"
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun DeviceModelPreviewBikeComputer() {
    AppTheme {
        DeviceModel(
            displayName = "Karoo 2",
            productType = "BIKE_COMPUTER"
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun TagsViewPreview() {
    AppTheme {
        TagsView(
            tagsData = TagsData(
                deviceTag = DeviceTag(
                    displayName = "Suunto 9 Peak",
                    productType = "SPORT_WATCH",
                ),
                suuntoTags = listOf(SuuntoTag.COMMUTE, SuuntoTag.IMPACT_FLEXIBILITY),
                userTags = listOf(
                    UserTag.empty("Custom"),
                    UserTag.empty("Custom 2"),
                    UserTag.empty("Custom long name")
                ),
                isOwnWorkout = true,
            ),
            isSubscribedToPremium = false,
            onTagClicked = null,
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun OtherUsersTagsPreview() {
    AppTheme {
        TagsView(
            tagsData = TagsData(
                deviceTag = DeviceTag(
                    displayName = "Suunto 9 Peak",
                    productType = "SPORT_WATCH",
                ),
                suuntoTags = listOf(SuuntoTag.COMMUTE, SuuntoTag.IMPACT_FLEXIBILITY),
                userTags = listOf(
                    UserTag.empty("Custom"),
                    UserTag.empty("Custom 2"),
                    UserTag.empty("Custom long name")
                ),
                isOwnWorkout = false,
            ),
            isSubscribedToPremium = false,
            onTagClicked = null,
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun NonPremiumTagsViewPreview() {
    AppTheme {
        TagsView(
            tagsData = TagsData(
                deviceTag = null,
                suuntoTags = listOf(
                    SuuntoTag.IMPACT_ABOVE_THRESHOLD_VO2MAX,
                    SuuntoTag.IMPACT_FLEXIBILITY
                ),
                userTags = emptyList(),
                isOwnWorkout = true,
            ),
            isSubscribedToPremium = false,
            onTagClicked = null,
        )
    }
}
