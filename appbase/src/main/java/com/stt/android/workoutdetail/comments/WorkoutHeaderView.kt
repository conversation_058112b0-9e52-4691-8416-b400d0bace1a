package com.stt.android.workoutdetail.comments

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import coil3.imageLoader
import coil3.request.ImageRequest
import coil3.request.target
import coil3.request.transformations
import coil3.transform.CircleCropTransformation
import com.stt.android.coil.placeholderWithFallback
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.core.R
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.databinding.WorkoutHeaderViewBinding
import com.stt.android.domain.user.GetUserByUsernameUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.social.userprofile.UserProfileActivity
import com.stt.android.ui.utils.TextFormatter
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.cancelChildren
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class WorkoutHeaderView : ConstraintLayout, View.OnClickListener {
    interface OnCloseListener {
        fun onClose()
    }

    @Inject
    lateinit var getUserByUsernameUseCase: GetUserByUsernameUseCase

    @Inject
    lateinit var coroutinesDispatchers: CoroutinesDispatchers

    private val coroutineScope = CoroutineScope(SupervisorJob() + coroutinesDispatchers.main)

    private val binding = WorkoutHeaderViewBinding.inflate(LayoutInflater.from(context), this)
        .apply {
            userImage.setOnClickListener(this@WorkoutHeaderView)
            close.setOnClickListener(this@WorkoutHeaderView)
        }

    var onCloseListener: OnCloseListener? = null

    private var workoutUserName: String? = null

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int)
        : super(context, attrs, defStyleAttr)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int, defStyleRes: Int)
        : super(context, attrs, defStyleAttr, defStyleRes)

    override fun onDetachedFromWindow() {
        coroutineScope.cancel()
        super.onDetachedFromWindow()
    }

    override fun onClick(v: View) {
        when (v) {
            binding.close -> onCloseListener?.onClose()

            binding.userImage -> workoutUserName?.let {
                context.startActivity(UserProfileActivity.newStartIntent(context, it, false))
            }
        }
    }

    fun setWorkoutHeader(workoutHeader: WorkoutHeader) {
        workoutUserName = workoutHeader.username

        binding.activity.text = workoutHeader.activityTypeAndTime()

        coroutineScope.coroutineContext.cancelChildren()
        coroutineScope.launch {
            val (username, profileImageUrl) = runSuspendCatching {
                getUserByUsernameUseCase.getUserByUsername(workoutHeader.username, true)
                    .let { user ->
                        user.realNameOrUsername to user.profileImageUrl
                    }
            }.getOrElse { e ->
                Timber.w(e, "Failed to get user")
                workoutHeader.username to null
            }

            binding.userName.text = username

            val request = ImageRequest.Builder(context)
                .data(profileImageUrl)
                .placeholderWithFallback(context, R.drawable.ic_default_profile_image_light)
                .transformations(CircleCropTransformation())
                .target(binding.userImage)
                .build()
            context.imageLoader.enqueue(request)
        }
    }

    private fun WorkoutHeader.activityTypeAndTime(): String =
        "${activityType.getLocalizedName(resources)}, ${TextFormatter.formatRelativeDateSpan(resources, startTime)}"
}
