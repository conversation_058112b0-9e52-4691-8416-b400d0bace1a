package com.stt.android.workoutdetail.comments;

import androidx.annotation.NonNull;
import android.text.TextUtils;
import androidx.annotation.Nullable;
import com.j256.ormlite.field.DataType;
import com.j256.ormlite.field.DatabaseField;
import com.j256.ormlite.table.DatabaseTable;

@DatabaseTable(tableName = WorkoutComment.TABLE_NAME)
public class WorkoutComment {
    public static final String TABLE_NAME = "workoutComment";
    @DatabaseField(id = true, dataType = DataType.INTEGER, columnName = DbFields.ID)
    private final int id;
    @DatabaseField(dataType = DataType.STRING, columnName = DbFields.KEY)
    @Nullable
    private final String key;
    @DatabaseField(dataType = DataType.STRING, columnName = DbFields.WORKOUT_KEY)
    private final String workoutKey;
    @DatabaseField(dataType = DataType.STRING, columnName = DbFields.USER_NAME)
    private final String userName;
    @DatabaseField(dataType = DataType.STRING, columnName = DbFields.REAL_NAME)
    private final String realName;
    @DatabaseField(dataType = DataType.STRING, columnName = DbFields.MESSAGE)
    private final String message;
    @DatabaseField(dataType = DataType.LONG, columnName = DbFields.TIMESTAMP)
    private final long timestamp;
    @DatabaseField(dataType = DataType.STRING, columnName = DbFields.PROFILE_PICTURE_URL)
    private final String profilePictureUrl;

    /**
     * Only used by OrmLite.
     */
    WorkoutComment() {
        this(null, null, null, null, null, null, 0L);
    }

    public WorkoutComment(@Nullable String key, String workoutKey, String message, String username, String realName,
        String profilePictureUrl) {
        this(key, workoutKey, message, username, realName, profilePictureUrl,
            System.currentTimeMillis());
    }

    public WorkoutComment(@Nullable String key, String workoutKey, String message, String userName, String realName,
        String profilePictureUrl, long timestamp) {
        int id = 31 + (int) timestamp;
        id += 31 * id + (TextUtils.isEmpty(key) ? 0 : key.hashCode());
        id += 31 * id + (TextUtils.isEmpty(workoutKey) ? 0 : workoutKey.hashCode());
        id += 31 * id + (TextUtils.isEmpty(message) ? 0 : message.hashCode());
        id += 31 * id + (TextUtils.isEmpty(userName) ? 0 : userName.hashCode());
        this.id = id;

        this.key = key;
        this.workoutKey = workoutKey;
        this.message = message;
        this.userName = userName;
        this.realName = realName;
        this.timestamp = timestamp;
        this.profilePictureUrl = profilePictureUrl;
    }

    @Nullable
    public String getKey() {
        return key;
    }

    public String getWorkoutKey() {
        return workoutKey;
    }

    public String getMessage() {
        return message;
    }

    public String getUsername() {
        return userName;
    }

    public String getRealName() {
        return realName;
    }

    /**
     * @return the real name if available otherwise fallback to username
     */
    @NonNull
    public String getRealNameOrUsername() {
        return realName == null || realName.trim().length() == 0 ? userName : realName;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public String getProfilePictureUrl() {
        return profilePictureUrl;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        WorkoutComment that = (WorkoutComment) o;

        if (id != that.id) return false;
        if (timestamp != that.timestamp) return false;
        if (key != null ? !key.equals(that.key) : that.key != null) {
            return false;
        }
        if (workoutKey != null ? !workoutKey.equals(that.workoutKey) : that.workoutKey != null) {
            return false;
        }
        if (userName != null ? !userName.equals(that.userName) : that.userName != null) {
            return false;
        }
        if (realName != null ? !realName.equals(that.realName) : that.realName != null) {
            return false;
        }
        if (message != null ? !message.equals(that.message) : that.message != null) return false;
        return profilePictureUrl != null ? profilePictureUrl.equals(that.profilePictureUrl)
            : that.profilePictureUrl == null;
    }

    @Override
    public int hashCode() {
        int result = id;
        result = 31 * result + (key != null ? key.hashCode() : 0);
        result = 31 * result + (workoutKey != null ? workoutKey.hashCode() : 0);
        result = 31 * result + (userName != null ? userName.hashCode() : 0);
        result = 31 * result + (realName != null ? realName.hashCode() : 0);
        result = 31 * result + (message != null ? message.hashCode() : 0);
        result = 31 * result + (int) (timestamp ^ (timestamp >>> 32));
        result = 31 * result + (profilePictureUrl != null ? profilePictureUrl.hashCode() : 0);
        return result;
    }

    public static abstract class DbFields {
        public static final String ID = "id";
        public static final String KEY = "key";
        public static final String WORKOUT_KEY = "workoutKey";
        public static final String USER_NAME = "userName";
        public static final String REAL_NAME = "realName";
        public static final String MESSAGE = "message";
        public static final String TIMESTAMP = "timestamp";
        public static final String PROFILE_PICTURE_URL = "profilePictureUrl";
    }
}
