package com.stt.android.workoutdetail.comments;

import android.annotation.SuppressLint;
import android.graphics.Typeface;
import android.os.Build;
import android.text.Layout;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.style.ClickableSpan;
import android.text.style.TextAppearanceSpan;
import android.view.MotionEvent;
import android.view.View;
import android.widget.TextView;
import androidx.annotation.StyleRes;
import androidx.core.content.res.ResourcesCompat;
import com.stt.android.FontRefs;
import com.stt.android.R;
import com.stt.android.utils.CustomFontStyleSpan;

@SuppressLint("ClickableViewAccessibility")
public class CommentUtils {
    // TODO: Remove this method and use WorkoutCommentUserName and WorkoutCommentBody consistently everywhere once
    // WorkoutDetailsActivity and CommentsDialogFragment have been whitened
    public static void styleCommentsTextViewFont(
            TextView textView,
            String realNameOrUsername,
            String comment,
            ClickableSpan clickableSpan
    ) {
        styleCommentsTextViewFont(
                textView,
                realNameOrUsername,
                comment,
                R.style.WorkoutCommentUserName,
                R.style.WorkoutCommentBody,
                clickableSpan);
    }

    public static void styleCommentsTextViewFontWorkoutCard(
            TextView textView,
            String realNameOrUsername,
            String comment
    ) {
        styleCommentsTextViewFont(
            textView,
            realNameOrUsername,
            comment,
            R.style.WorkoutCommentUserName,
            R.style.WorkoutCommentBody,
            null);
    }

    private static void styleCommentsTextViewFont(
        TextView textView,
        String realNameOrUsername,
        String comment,
        @StyleRes int userNameTextStyle,
        @StyleRes int bodyTextStyle,
        ClickableSpan clickableSpan
    ) {
        String nameAndComment = realNameOrUsername + ' ' + comment;

        SpannableStringBuilder result = new SpannableStringBuilder(nameAndComment);

        // set clickable span to username
        if (clickableSpan != null) {
            result.setSpan(clickableSpan, 0, realNameOrUsername.length(),
                Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
            textView.setOnTouchListener(new SpanClickTouchListener());
        }

        // User name
        result.setSpan(
                new TextAppearanceSpan(textView.getContext(), userNameTextStyle),
                0,
                realNameOrUsername.length(),
                Spannable.SPAN_INCLUSIVE_EXCLUSIVE);

        // On API level 26 and below, setting TextAppearanceSpan does not affect font family
        if (Build.VERSION.SDK_INT < 27) {
            Typeface userTypeface = ResourcesCompat.getFont(
                    textView.getContext(),
                    FontRefs.COMMENT_USER_FONT_REF);

            if (userTypeface != null) {
                result.setSpan(
                        new CustomFontStyleSpan(userTypeface),
                        0,
                        realNameOrUsername.length(),
                        Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
            }
        }

        // Comment body
        result.setSpan(
                new TextAppearanceSpan(textView.getContext(), bodyTextStyle),
                realNameOrUsername.length() + 1,
                nameAndComment.length(),
                Spannable.SPAN_INCLUSIVE_EXCLUSIVE);

        // On API level 26 and below, setting TextAppearanceSpan does not affect font family
        if (Build.VERSION.SDK_INT < 27) {
            Typeface bodyTypeface = ResourcesCompat.getFont(
                    textView.getContext(),
                    FontRefs.COMMENT_BODY_FONT_REF);

            if (bodyTypeface != null) {
                result.setSpan(
                        new CustomFontStyleSpan(bodyTypeface),
                        realNameOrUsername.length() + 1,
                        nameAndComment.length(),
                        Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
            }
        }

        textView.setText(result, TextView.BufferType.SPANNABLE);
    }

    private static class SpanClickTouchListener implements View.OnTouchListener {
        @Override
        public boolean onTouch(View v, MotionEvent event) {
            if (event.getAction() == MotionEvent.ACTION_UP ||
                event.getAction() == MotionEvent.ACTION_DOWN) {

                TextView tv = (TextView) v;
                CharSequence text = tv.getText();
                if (text instanceof Spannable spannable) {
                    int x = (int) event.getX();
                    int y = (int) event.getY();

                    x -= tv.getTotalPaddingLeft();
                    y -= tv.getTotalPaddingTop();

                    x += tv.getScrollX();
                    y += tv.getScrollY();

                    Layout layout = tv.getLayout();
                    int line = layout.getLineForVertical(y);
                    int off = layout.getOffsetForHorizontal(line, x);

                    ClickableSpan[] spans = spannable.getSpans(off, off, ClickableSpan.class);
                    if (spans.length > 0) {
                        if (event.getAction() == MotionEvent.ACTION_UP) {
                            spans[0].onClick(tv);
                        }
                        return true;
                    }
                }
            }
            return false;
        }
    }
}
