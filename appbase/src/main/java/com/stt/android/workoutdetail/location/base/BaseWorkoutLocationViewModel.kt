package com.stt.android.workoutdetail.location.base

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.google.android.gms.maps.model.LatLng
import com.stt.android.common.coroutines.CoroutineViewModel
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.domain.mapbox.FetchLocationNameUseCase
import com.stt.android.maps.MapType
import com.stt.android.models.MapSelectionModel
import com.stt.android.ui.extensions.formatCoordinates
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber

abstract class BaseWorkoutLocationViewModel(
    latLng: LatLng?,
    private val mapSelectionModel: MapSelectionModel,
    private val fetchLocationNameUseCase: FetchLocationNameUseCase,
    coroutinesDispatchers: CoroutinesDispatchers,
) : CoroutineViewModel(coroutinesDispatchers) {

    private val _pendingLatLng = MutableLiveData<LatLng?>()
    private val _storedLatLng = MutableLiveData<LatLng?>()
    private val _locationName = MutableLiveData<String>()
    private val _coordinate = MutableLiveData<String>()
    private val _isLoading = MutableLiveData<Boolean>()

    init {
        _storedLatLng.value = latLng
        latLng?.let(::setLocation)
    }

    val selectedMapType: MapType get() = mapSelectionModel.selectedMapType

    val pendingLatLng: LiveData<LatLng?>
        get() = _pendingLatLng
    val storedLatLng: LiveData<LatLng?>
        get() = _storedLatLng
    val locationName: LiveData<String>
        get() = _locationName
    val coordinate: LiveData<String>
        get() = _coordinate
    val isLoading: LiveData<Boolean>
        get() = _isLoading

    fun setLocation(latLng: LatLng) {
        _pendingLatLng.value = latLng
        viewModelScope.launch {
            _isLoading.value = true
            val locationName = fetchLocationName(latLng)
            val coordinatesDeferred = async(computation) { latLng.formatCoordinates() }
            val coordinatesFormatted = coordinatesDeferred.await()
            _locationName.value = locationName
            _coordinate.value = coordinatesFormatted
            _isLoading.value = false
        }
    }

    private suspend fun fetchLocationName(latLng: LatLng): String {
        return try {
            withContext(io) {
                fetchLocationNameUseCase(
                    FetchLocationNameUseCase.Params(
                        latLng.latitude,
                        latLng.longitude,
                    )
                )?.fullAddress ?: ""
            }
        } catch (throwable: Throwable) {
            Timber.w(throwable)
            ""
        }
    }
}
