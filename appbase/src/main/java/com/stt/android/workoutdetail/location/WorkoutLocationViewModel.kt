package com.stt.android.workoutdetail.location

import android.location.Location
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import com.google.android.gms.maps.model.LatLng
import com.stt.android.R
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.domain.mapbox.FetchLocationNameUseCase
import com.stt.android.maps.OnMapReadyCallback
import com.stt.android.maps.SuuntoMap
import com.stt.android.maps.location.SuuntoLocationListener
import com.stt.android.maps.location.SuuntoLocationSource
import com.stt.android.maps.newLatLngZoom
import com.stt.android.models.MapSelectionModel
import com.stt.android.utils.STTConstants.MapPreferences.DEFAULT_ZOOM_LEVEL
import com.stt.android.workoutdetail.location.base.BaseWorkoutLocationViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import java.time.Clock
import java.time.Instant
import javax.inject.Inject
import javax.inject.Named

@HiltViewModel
class WorkoutLocationViewModel
@Inject constructor(
    savedStateHandle: SavedStateHandle,
    private val locationSource: SuuntoLocationSource,
    @Named(HAS_LOCATION_PERMISSION) private val hasLocationPermission: Boolean,
    mapSelectionModel: MapSelectionModel,
    fetchLocationNameUseCase: FetchLocationNameUseCase,
    private val clock: Clock,
    coroutinesDispatchers: CoroutinesDispatchers,
) : BaseWorkoutLocationViewModel(
    latLng = savedStateHandle[WorkoutLocationFragment.ARGUMENT_COORDINATE],
    mapSelectionModel = mapSelectionModel,
    fetchLocationNameUseCase = fetchLocationNameUseCase,
    coroutinesDispatchers = coroutinesDispatchers
),
    OnMapReadyCallback,
    SuuntoLocationListener {

    private val requiresUserConfirmation: Boolean =
        savedStateHandle[WorkoutLocationFragment.ARGUMENT_REQUIRES_USER_CONFIRMATION] ?: false

    private val workoutStopTime: Long? =
        savedStateHandle[WorkoutLocationFragment.ARGUMENT_WORKOUT_STOP_TIME]

    private var map: SuuntoMap? = null

    private val _showLocationInfo = MediatorLiveData<Boolean>()
    private val _showDimmingOverlay = MutableLiveData<Boolean>()
    private val _buttonText = MediatorLiveData<Int>()

    val showLocationInfo: LiveData<Boolean> = _showLocationInfo
    val showDimmingOverlay: LiveData<Boolean> = _showDimmingOverlay
    val buttonText: LiveData<Int> = _buttonText

    init {
        _showLocationInfo.addSource(coordinate) { value ->
            _showLocationInfo.value = !value.isNullOrBlank() && !requiresUserConfirmation
        }
        _buttonText.apply {
            addSource(showLocationInfo) { updateButtonText() }
            addSource(isLoading) { updateButtonText() }
            value = getButtonTextResIntValue()
        }
    }

    private fun updateButtonText() {
        _buttonText.value = getButtonTextResIntValue()
    }

    private fun getButtonTextResIntValue(): Int {
        val nowEpochSecond = Instant.now(clock).toEpochMilli()
        val stopTimeInSecond = workoutStopTime ?: 0
        val secondsSinceWorkoutStopped = (nowEpochSecond - stopTimeInSecond) / 1000
        val isRecentWorkout =
            secondsSinceWorkoutStopped in RANGE_INTERVAL_TO_SHOW_CONFIRMATION_IN_SECONDS

        return when {
            requiresUserConfirmation && isRecentWorkout -> R.string.tap_to_confirm_location
            showLocationInfo.value == true -> R.string.empty
            isLoading.value == true -> R.string.empty
            else -> R.string.tap_to_add_location
        }
    }

    private fun setLocationIfAvailable() {
        val latLng = this.pendingLatLng.value

        if (requiresUserConfirmation && latLng != null) {
            setCamera(latLng)
            _showDimmingOverlay.value = true
        } else if (latLng != null) {
            setCamera(latLng)
            setLocation(latLng)
            _showDimmingOverlay.value = false
        } else if (hasLocationPermission) {
            setLocationSource(locationSource)
            _showDimmingOverlay.value = true
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun setCamera(latLng: LatLng) {
        map?.moveCamera(
            newLatLngZoom(
                latLng,
                DEFAULT_ZOOM_LEVEL
            )
        )
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun setLocationSource(locationSource: SuuntoLocationSource) {
        map?.let { map ->
            map.setLocationSource(locationSource)
            locationSource.getLastKnownLocation { location ->
                onLocationChanged(location, locationSource)
            }
        }
    }

    override fun onMapReady(map: SuuntoMap) {
        this.map = map
        setLocationIfAvailable()
    }

    override fun onLocationChanged(location: Location, source: SuuntoLocationSource) {
        setCamera(LatLng(location.latitude, location.longitude))
    }

    override fun onLocationAvailability(locationAvailable: Boolean, source: SuuntoLocationSource) {
        // do nothing
    }

    companion object {
        const val HAS_LOCATION_PERMISSION = "workoutLocationHasLocationPermission"

        // confirm location just appears when the dive time exceeds the current time of 30
        // minutes but less than 1 hour. (30min < dive time < 1 hour).
        val RANGE_INTERVAL_TO_SHOW_CONFIRMATION_IN_SECONDS: IntRange = 1801..3599
    }
}
