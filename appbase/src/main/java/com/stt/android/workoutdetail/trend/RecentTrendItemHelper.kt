package com.stt.android.workoutdetail.trend

import android.content.res.Resources
import android.widget.TextView
import com.amersports.formatter.unit.jscience.JScienceUnitConverter
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.data.LineData
import com.stt.android.R
import com.stt.android.domain.user.workout.RecentWorkoutTrend
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.components.charts.RecentWorkoutTrendChart
import com.stt.android.workoutdetail.recentsummary.RecentWorkoutItemHelper
import kotlin.math.abs

/**
 * help build recentTrend item graph
 */

object RecentTrendItemHelper {

    fun setCurrentData(
        position: Int,
        recentWorkoutTrend: RecentWorkoutTrend,
        chart: RecentWorkoutTrendChart,
        resources: Resources
    ) {
        val page = RecentWorkoutItemHelper.positionToPage(
            recentWorkoutTrend.currentWorkout.activityType,
            position
        )
        when (page) {
            RecentWorkoutItemHelper.PAGE_DURATION -> setChart(
                recentWorkoutTrend.duration,
                chart,
                resources
            )

            RecentWorkoutItemHelper.PAGE_DISTANCE -> setChart(
                recentWorkoutTrend.distance,
                chart,
                resources
            )

            RecentWorkoutItemHelper.PAGE_SPEED -> setChart(
                recentWorkoutTrend.speed,
                chart,
                resources
            )

            RecentWorkoutItemHelper.PAGE_PACE -> setChart(recentWorkoutTrend.pace, chart, resources)
            RecentWorkoutItemHelper.PAGE_ENERGY -> setChart(
                recentWorkoutTrend.energy,
                chart,
                resources
            )

            RecentWorkoutItemHelper.PAGE_AVERAGE_HEART_RATE -> setChart(
                recentWorkoutTrend.averageHeartRate,
                chart,
                resources
            )

            RecentWorkoutItemHelper.PAGE_AVERAGE_CADENCE -> setChart(
                recentWorkoutTrend.averageCadence,
                chart,
                resources
            )

            else -> throw IllegalArgumentException("Unsupported position: $position")
        }
    }

    fun setPreviousData(
        position: Int,
        recentWorkoutTrend: RecentWorkoutTrend,
        resources: Resources,
        similarWorkout: Boolean,
        chart: RecentWorkoutTrendChart,
        comparisonValue: TextView,
        comparisonTitle: TextView,
        infoModelFormatter: InfoModelFormatter,
        unitConverter: JScienceUnitConverter,
    ) {
        val value: Double
        val page = RecentWorkoutItemHelper.positionToPage(
            recentWorkoutTrend.currentWorkout.activityType,
            position
        )
        when (page) {
            RecentWorkoutItemHelper.PAGE_DURATION -> {
                setChart(recentWorkoutTrend.duration, chart, resources)
                value = (
                    recentWorkoutTrend.currentWorkout.totalTime - recentWorkoutTrend.previousWorkout.totalTime
                    )
                if (similarWorkout) {
                    if (value <= 0.0) {
                        comparisonTitle.setText(R.string.faster_than_previous)
                    } else {
                        comparisonTitle.setText(R.string.slower_than_previous)
                    }
                } else {
                    if (value >= 0.0) {
                        comparisonTitle.setText(R.string.longer_than_previous)
                    } else {
                        comparisonTitle.setText(R.string.shorter_than_previous)
                    }
                }
            }

            RecentWorkoutItemHelper.PAGE_DISTANCE -> {
                setChart(recentWorkoutTrend.distance, chart, resources)
                value = (
                    recentWorkoutTrend.currentWorkout.totalDistance - recentWorkoutTrend.previousWorkout.totalDistance
                    )
                if (value >= 0.0) {
                    comparisonTitle.setText(R.string.longer_than_previous)
                } else {
                    comparisonTitle.setText(R.string.shorter_than_previous)
                }
            }

            RecentWorkoutItemHelper.PAGE_SPEED -> {
                setChart(recentWorkoutTrend.speed, chart, resources)
                value = (
                    recentWorkoutTrend.currentWorkout.avgSpeed - recentWorkoutTrend.previousWorkout.avgSpeed
                    )
                if (value >= 0.0) {
                    comparisonTitle.setText(R.string.faster_than_previous)
                } else {
                    comparisonTitle.setText(R.string.slower_than_previous)
                }
            }

            RecentWorkoutItemHelper.PAGE_PACE -> {
                setChart(recentWorkoutTrend.pace, chart, resources)
                value = infoModelFormatter.unit.toPaceUnit(recentWorkoutTrend.previousWorkout.avgSpeed) -
                    infoModelFormatter.unit.toPaceUnit(recentWorkoutTrend.currentWorkout.avgSpeed)
                if (value >= 0.0) {
                    comparisonTitle.setText(R.string.faster_than_previous)
                } else {
                    comparisonTitle.setText(R.string.slower_than_previous)
                }
            }

            RecentWorkoutItemHelper.PAGE_ENERGY -> {
                setChart(recentWorkoutTrend.energy, chart, resources)
                value = (
                    recentWorkoutTrend.currentWorkout.energyConsumption - recentWorkoutTrend.previousWorkout.energyConsumption
                    )
                if (value >= 0.0) {
                    comparisonTitle.setText(R.string.more_than_previous)
                } else {
                    comparisonTitle.setText(R.string.less_than_previous)
                }
            }

            RecentWorkoutItemHelper.PAGE_AVERAGE_HEART_RATE -> {
                setChart(recentWorkoutTrend.averageHeartRate, chart, resources)
                value = (
                    recentWorkoutTrend.currentWorkout.heartRateAverage - recentWorkoutTrend.previousWorkout.heartRateAverage
                    )
                if (value >= 0.0) {
                    comparisonTitle.setText(R.string.more_than_previous)
                } else {
                    comparisonTitle.setText(R.string.less_than_previous)
                }
            }

            RecentWorkoutItemHelper.PAGE_AVERAGE_CADENCE -> {
                setChart(recentWorkoutTrend.averageCadence, chart, resources)
                value = (
                    recentWorkoutTrend.currentWorkout.averageCadence - recentWorkoutTrend.previousWorkout.averageCadence
                    ).toDouble()
                if (value >= 0.0) {
                    comparisonTitle.setText(R.string.more_than_previous)
                } else {
                    comparisonTitle.setText(R.string.less_than_previous)
                }
            }

            else -> throw IllegalArgumentException("Unsupported page: $page")
        }
        comparisonValue.text =
            RecentWorkoutItemHelper.formatValue(
                abs(value).toFloat(),
                page,
                resources,
                recentWorkoutTrend.currentWorkout.activityType,
                infoModelFormatter,
                unitConverter,
            )
        if ((page == RecentWorkoutItemHelper.PAGE_DURATION || page == RecentWorkoutItemHelper.PAGE_AVERAGE_HEART_RATE) && similarWorkout) {
            comparisonValue.setTextColor(
                if (value <= 0.0) {
                    resources.getColor(com.stt.android.core.R.color.comparison_color_increase)
                } else {
                    resources.getColor(
                        com.stt.android.core.R.color.comparison_color_decrease
                    )
                }
            )
        } else {
            comparisonValue.setTextColor(
                if (value >= 0.0) {
                    resources.getColor(com.stt.android.core.R.color.comparison_color_increase)
                } else {
                    resources.getColor(
                        com.stt.android.core.R.color.comparison_color_decrease
                    )
                }
            )
        }
    }

    private fun setChart(lineData: LineData, chart: LineChart, resources: Resources) {
        chart.data = lineData
        // must call these after setData()
        chart.legend.isEnabled = false
        chart.highlightValue((lineData.getDataSetByIndex(0).entryCount - 1).toFloat(), 0)
        val density = resources.displayMetrics.density
        chart.setViewPortOffsets(
            25.0f * density,
            20.0f * density,
            25.0f * density,
            20.0f * density
        )
        chart.animateY(300)
    }
}
