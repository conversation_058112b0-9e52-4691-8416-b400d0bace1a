package com.stt.android.workoutdetail.location.select

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.IntentCompat
import androidx.databinding.DataBindingUtil
import com.google.android.gms.maps.model.LatLng
import com.stt.android.R
import com.stt.android.databinding.ActivityWorkoutSelectLocationBinding
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class WorkoutSelectLocationActivity : AppCompatActivity() {

    private lateinit var binding: ActivityWorkoutSelectLocationBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_workout_select_location)
        showWorkoutSelectLocation()
    }

    private fun showWorkoutSelectLocation() {
        var fragment =
            supportFragmentManager.findFragmentByTag(WorkoutSelectLocationFragment.FRAGMENT_TAG)
        if (fragment == null || !fragment.isAdded) {
            val latLng = IntentCompat.getParcelableExtra(intent, EXTRA_LAT_LNG, LatLng::class.java)
            fragment = WorkoutSelectLocationFragment.newInstance(latLng)
            supportFragmentManager
                .beginTransaction()
                .replace(
                    R.id.workoutSelectLocationContainer,
                    fragment,
                    WorkoutSelectLocationFragment.FRAGMENT_TAG
                )
                .commit()
        }
    }

    companion object {
        private const val EXTRA_LAT_LNG = "extra_lat_lng"

        @JvmStatic
        fun newStartIntent(
            context: Context,
            latLng: LatLng?
        ): Intent {
            return Intent(context, WorkoutSelectLocationActivity::class.java)
                .putExtra(EXTRA_LAT_LNG, latLng)
        }
    }
}
