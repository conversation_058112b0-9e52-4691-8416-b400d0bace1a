package com.stt.android.workoutdetail.workoutvalues

import com.stt.android.infomodel.SummaryItem
import com.stt.android.workoutdetail.workoutvalues.composables.WorkoutValuesGridType
import com.stt.android.workouts.details.values.WorkoutValue

data class WorkoutValuesGridData(
    val showHeader: <PERSON>olean,
    val activityName: String,
    val activityIcon: Int,
    val addTopMargin: Boolean,
    val workoutValues: List<WorkoutValuesGridItemData>,
    val workoutValueGroups: List<WorkoutValueGroupData>,
    val workoutValuesGridType: WorkoutValuesGridType,
    val showDetailsButton: Boolean,
    val onValueClicked: (WorkoutValue) -> Unit,
    val onMultisportDetailsClicked: () -> Unit,
    val onViewMoreClicked: (() -> Unit)? = null
)

data class WorkoutValuesGridItemData(
    val name: String,
    val value: String,
    val showMoreInfoIcon: Boolean = false,
    val workoutValue: WorkoutValue = WorkoutValue(),
)

data class WorkoutValueGroupData(
    val name: String,
    val items: List<SummaryItem>,
    val highlight: List<SummaryItem>,
    val workoutValues: List<WorkoutValuesGridItemData>,
)
