package com.stt.android.workoutdetail.comments

sealed class CommentsDialogUiState {
    data object Loading : CommentsDialogUiState()

    data class Loaded(val items: List<Item>) : CommentsDialogUiState() {
        sealed class Item {
            data class Description(val description: String) : Item()

            data class Comment(
                val comment: WorkoutComment,
                val deletable: Boolean,
            ) : Item()
        }
    }

    data object Error : CommentsDialogUiState()
}
