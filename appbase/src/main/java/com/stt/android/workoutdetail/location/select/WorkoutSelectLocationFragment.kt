package com.stt.android.workoutdetail.location.select

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.content.ContextCompat
import androidx.core.os.BundleCompat
import androidx.core.os.postDelayed
import androidx.fragment.app.viewModels
import com.google.android.gms.maps.model.LatLng
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.snackbar.Snackbar
import com.stt.android.R
import com.stt.android.common.ui.SimpleDialogFragment
import com.stt.android.common.ui.ViewModelFragment2
import com.stt.android.common.ui.observeK
import com.stt.android.databinding.FragmentWorkoutSelectLocationBinding
import com.stt.android.maps.MarkerZPriority
import com.stt.android.maps.OnMapReadyCallback
import com.stt.android.maps.SuuntoBitmapDescriptorFactory
import com.stt.android.maps.SuuntoMap
import com.stt.android.maps.SuuntoMapOptions
import com.stt.android.maps.SuuntoMarker
import com.stt.android.maps.SuuntoMarkerOptions
import com.stt.android.maps.SuuntoSupportMapFragment
import com.stt.android.ui.map.MapHelper
import com.stt.android.workoutdetail.location.bottomsheet.WorkoutLocationBottomSheetFragment
import com.stt.android.workoutdetail.location.bottomsheet.WorkoutLocationBottomSheetViewModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class WorkoutSelectLocationFragment :
    ViewModelFragment2(),
    SuuntoMap.OnMapClickListener,
    OnMapReadyCallback {
    override val viewModel: WorkoutSelectLocationViewModel by viewModels()

    private val viewDataBinding: FragmentWorkoutSelectLocationBinding get() = requireBinding()

    override fun getLayoutResId(): Int = R.layout.fragment_workout_select_location

    private val bottomSheetViewModel by viewModels<WorkoutLocationBottomSheetViewModel>()
    private var map: SuuntoMap? = null
    private lateinit var bottomSheetBehavior: BottomSheetBehavior<*>
    private var locationMarker: SuuntoMarker? = null
    private val expandBottomSheetHandler = Handler(Looper.getMainLooper())

    private val bottomSheetCallback = object : BottomSheetBehavior.BottomSheetCallback() {
        override fun onSlide(bottomSheet: View, slideOffset: Float) {
            setMapPadding()
        }

        override fun onStateChanged(bottomSheet: View, newState: Int) {
            setMapPadding()
        }
    }

    private val requestPermissionLauncher =
        registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { permissions ->
            when {
                permissions[Manifest.permission.ACCESS_FINE_LOCATION] ?: false ||
                    permissions[Manifest.permission.ACCESS_COARSE_LOCATION] ?: false -> {
                    viewModel.locationPermissionGranted()
                }

                else -> {
                    // Explain to the user that the feature is unavailable because
                    // the features requires a permission that the user has denied.
                    // At the same time, respect the user's decision. Don't link to
                    // system settings in an effort to convince the user to change
                    // their decision.
                    Snackbar.make(
                        viewDataBinding.root,
                        R.string.location_permission_required,
                        Snackbar.LENGTH_LONG
                    ).show()
                }
            }
        }

    private fun getCoordinateArgument() = BundleCompat.getParcelable(
        requireArguments(), ARGUMENT_COORDINATE, LatLng::class.java
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setHasOptionsMenu(true)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val view = super.onCreateView(inflater, container, savedInstanceState)
        initBottomSheet()
        return view
    }

    private fun initBottomSheet() {
        bottomSheetBehavior =
            BottomSheetBehavior.from(viewDataBinding.workoutSelectLocationBottomSheet)
        bottomSheetBehavior.addBottomSheetCallback(bottomSheetCallback)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        showBottomSheet()
        initLiveDataObservers()
    }

    private fun showBottomSheet() {
        val fm = childFragmentManager
        if (fm.findFragmentByTag(WorkoutLocationBottomSheetFragment.FRAGMENT_TAG) == null) {
            fm.beginTransaction()
                .add(
                    R.id.workout_select_location_bottom_sheet_container,
                    WorkoutLocationBottomSheetFragment(),
                    WorkoutLocationBottomSheetFragment.FRAGMENT_TAG
                )
                .commit()
        }
    }

    private fun initLiveDataObservers() {
        bottomSheetViewModel.storedLatLng.observeK(viewLifecycleOwner) { latLng ->
            activity?.invalidateOptionsMenu()
            if (latLng == null) removeLocationMarker()
        }

        viewModel.isLocationCentered.observeK(viewLifecycleOwner) { isLocationCentered ->
            context?.apply {
                viewDataBinding.locationFab.setImageDrawable(
                    AppCompatResources.getDrawable(
                        this,
                        if (isLocationCentered != null && isLocationCentered) R.drawable.ic_location_arrow_fill else R.drawable.ic_location_arrow
                    )
                )
            }
        }

        viewModel.checkLocationPermissionEvent.observeK(viewLifecycleOwner) {
            if (isLocationPermissionGranted()) {
                viewModel.locationPermissionGranted()
            }
        }

        viewModel.noLocationInfoEvent.observeK(viewLifecycleOwner) {
            showNoLocationError()
        }

        viewModel.onLocationButtonClickedEvent.observeK(viewLifecycleOwner) {
            when {
                isLocationPermissionGranted() -> {
                    viewModel.locationPermissionGranted()
                }

                shouldShowRequestPermissionRationale(Manifest.permission.ACCESS_FINE_LOCATION) -> {
                    showRequestPermissionRationaleUi()
                }

                else -> {
                    // We can request location permissions
                    requestLocationPermissions()
                }
            }
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        setupToolbar()
        loadMap()
    }

    private fun showRequestPermissionRationaleUi() {
        AlertDialog.Builder(requireContext())
            .setMessage(R.string.location_permission_rationale_for_location)
            .setPositiveButton(R.string.ok) { _, _ -> requestLocationPermissions() }
            .setNegativeButton(R.string.cancel) { dialog, _ -> dialog.cancel() }
            .show()
    }

    private fun isLocationPermissionGranted(): Boolean {
        val fineLocationGranted = ContextCompat.checkSelfPermission(
            requireContext(),
            Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED

        val coarseLocationGranted = ContextCompat.checkSelfPermission(
            requireContext(),
            Manifest.permission.ACCESS_COARSE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED

        return fineLocationGranted || coarseLocationGranted
    }

    private fun requestLocationPermissions() {
        // The registered ActivityResultCallback gets the result of this request.
        requestPermissionLauncher.launch(
            arrayOf(
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION
            )
        )
    }

    private fun setupToolbar() {
        val activity = activity as AppCompatActivity
        activity.setSupportActionBar(viewDataBinding.toolbar)
        activity.supportActionBar?.run {
            setDisplayShowHomeEnabled(true)
            setDisplayHomeAsUpEnabled(true)
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        super.onCreateOptionsMenu(menu, inflater)
        if (bottomSheetViewModel.storedLatLng.value != null) {
            inflater.inflate(R.menu.workout_select_location, menu)
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                activity?.run {
                    this.setResult(Activity.RESULT_CANCELED)
                }
                requireActivity().onBackPressed()
                true
            }

            R.id.delete -> {
                confirmDelete()
                true
            }

            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun confirmDelete() {
        if (!isAdded) return
        val simpleDialogFragment = SimpleDialogFragment.newInstance(
            getString(R.string.delete_location_query),
            null,
            getString(R.string.delete),
            getString(R.string.cancel)
        )
        simpleDialogFragment.setTargetFragment(this, CONFIRM_DELETE_LOCATION_REQUEST)
        simpleDialogFragment.show(parentFragmentManager, CONFIRM_DELETE_LOCATION_TAG)
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == CONFIRM_DELETE_LOCATION_REQUEST &&
            resultCode == SimpleDialogFragment.RESULT_POSITIVE
        ) {
            setResult(null)
        } else {
            super.onActivityResult(requestCode, resultCode, data)
        }
    }

    private fun setResult(latLng: LatLng?) {
        activity?.run {
            val intent = Intent().putExtra(EXTRA_LOCATION_RESULT, latLng)
            this.setResult(Activity.RESULT_OK, intent)
            this.finish()
        }
    }

    private fun loadMap() {
        map?.clear()
        val fm = childFragmentManager
        var mapFragment =
            fm.findFragmentByTag(MAP_FRAGMENT_TAG) as SuuntoSupportMapFragment?
        if (mapFragment == null) {
            val options = SuuntoMapOptions()
                .mapType(viewModel.selectedMapType.name)
                .compassEnabled(true)
                .rotateGesturesEnabled(true)
                .scrollGesturesEnabled(true)
                .tiltGesturesEnabled(true)
                .zoomControlsEnabled(false)
                .zoomGesturesEnabled(true)
                .attributionEnabled(resources.getBoolean(R.bool.maps_logo_enabled))
                .logoEnabled(resources.getBoolean(R.bool.maps_logo_enabled))
            mapFragment = SuuntoSupportMapFragment.newInstance(options)
            fm.beginTransaction().add(
                R.id.mapContainer,
                mapFragment,
                MAP_FRAGMENT_TAG
            ).commitNow()
        }
        mapFragment.getMapAsync(this)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        map?.clear()
        expandBottomSheetHandler.removeCallbacksAndMessages(null)
        bottomSheetBehavior.removeBottomSheetCallback(bottomSheetCallback)
    }

    override fun onMapReady(map: SuuntoMap) {
        this.map = map
        getCoordinateArgument()?.run {
            setLocationMarker(this)
        }
        map.addOnMapClickListener(this)
        map.getUiSettings().setMyLocationButtonEnabled(false)
        viewModel.onMapReady(map)
        expandBottomSheet()
    }

    override fun onMapClick(latLng: LatLng, placeName: String?) = setLocation(latLng)

    private fun setLocation(latLng: LatLng) {
        if (!isAdded) return

        arguments?.putParcelable(ARGUMENT_COORDINATE, latLng)
        viewModel.setLocation(latLng, animated = true)
        bottomSheetViewModel.setLocation(latLng)
        setLocationMarker(latLng)
    }

    private fun setLocationMarker(latLng: LatLng) {
        removeLocationMarker()

        val startMarker = SuuntoMarkerOptions()
            .position(latLng)
            .icon(
                SuuntoBitmapDescriptorFactory(requireContext())
                    .fromResource(R.drawable.map_pin)
            )
            .anchor(0.5f, 1.0f)
            .zPriority(MarkerZPriority.START_POINT)

        locationMarker = map?.addMarker(startMarker)
        setMapPaddingOnPreDraw()
        expandBottomSheet()
    }

    private fun showNoLocationError() {
        Snackbar.make(
            viewDataBinding.root,
            R.string.location_not_available,
            Snackbar.LENGTH_LONG
        ).show()
    }

    private fun removeLocationMarker() {
        locationMarker?.remove()
        locationMarker = null
    }

    private fun setMapPaddingOnPreDraw() {
        val viewTreeObserver = viewDataBinding.workoutSelectLocationBottomSheet.viewTreeObserver
        viewTreeObserver.addOnPreDrawListener(object : ViewTreeObserver.OnPreDrawListener {
            override fun onPreDraw(): Boolean {
                return if (!viewTreeObserver.isAlive || !isAdded) {
                    true
                } else {
                    viewTreeObserver.removeOnPreDrawListener(this)
                    setMapPadding()
                    true
                }
            }
        })
    }

    private fun setMapPadding() {
        map?.let { map ->
            MapHelper.updateMapPaddingWithDefaults(
                resources = resources,
                map = map,
                paddingTop = 0,
                paddingBottom = viewDataBinding.root.height - viewDataBinding.workoutSelectLocationBottomSheet.top,
                compassTopMargin = resources.getDimensionPixelOffset(R.dimen.size_spacing_medium),
                mapboxLogoLeftMargin = 0,
                mapCreditTextView = null,
                addCreditHeightToBottomPadding = false
            )
        }
    }

    private fun expandBottomSheet() {
        expandBottomSheetHandler.postDelayed(200) {
            bottomSheetBehavior.state = BottomSheetBehavior.STATE_EXPANDED
        }
    }

    companion object {
        const val ARGUMENT_COORDINATE = "argument_coordinate"
        private const val MAP_FRAGMENT_TAG = "WorkoutSelectLocationMapFragment"
        private const val CONFIRM_DELETE_LOCATION_TAG = "confirm_delete_location"
        private const val CONFIRM_DELETE_LOCATION_REQUEST = 1
        const val FRAGMENT_TAG = "WorkoutSelectLocationFragment"
        const val EXTRA_LOCATION_RESULT = "extra_location_result"

        @JvmStatic
        fun newInstance(
            latLng: LatLng?
        ): WorkoutSelectLocationFragment {
            return WorkoutSelectLocationFragment().apply {
                arguments = Bundle().apply {
                    putParcelable(ARGUMENT_COORDINATE, latLng)
                }
            }
        }
    }
}
