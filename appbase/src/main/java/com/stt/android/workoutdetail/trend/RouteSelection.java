package com.stt.android.workoutdetail.trend;

import android.content.res.Resources;
import androidx.annotation.NonNull;
import com.stt.android.R;
import com.stt.android.domain.localization.Localizable;

public enum RouteSelection implements Localizable {
    ON_THIS_ROUTE(R.string.on_this_route_capital),
    ON_ALL_ROUTE(R.string.on_all_route_capital);

    public static final RouteSelection DEFAULT = ON_THIS_ROUTE;

    private final int stringId;

    RouteSelection(int stringId) {
        this.stringId = stringId;
    }

    @Override
    public String toString(@NonNull Resources resources) {
        return resources.getString(stringId);
    }
}
