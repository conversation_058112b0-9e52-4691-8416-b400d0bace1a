package com.stt.android.workoutdetail.workoutvalues.composables

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TopAppBar
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.component.SuuntoFullscreenDialog
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyLargeBold
import com.stt.android.compose.theme.bodyMegaBold
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.spacing
import com.stt.android.infomodel.SummaryItem
import com.stt.android.workoutdetail.workoutvalues.WorkoutValueGroupData
import com.stt.android.workoutdetail.workoutvalues.WorkoutValuesGridItemData
import com.stt.android.workouts.details.values.WorkoutValue
import com.stt.android.core.R as CR

@Composable
fun WorkoutValuesContainer(
    showHeader: Boolean,
    activityName: String,
    activityIcon: Int,
    workoutValues: List<WorkoutValuesGridItemData>,
    workoutValueGroups: List<WorkoutValueGroupData>,
    workoutValuesGridType: WorkoutValuesGridType,
    showDetailsButton: Boolean,
    onValueClicked: (WorkoutValue) -> Unit,
    onMultisportDetailsClicked: () -> Unit,
    modifier: Modifier = Modifier,
    onViewMoreClicked: (() -> Unit)? = null,
    enableWorkoutValueGroups: Boolean = false,
) {
    Surface(
        modifier = modifier.fillMaxWidth(),
        color = MaterialTheme.colors.surface
    ) {
        // Total number of rows when fully expanded
        val totalRows = (workoutValues.size + 1) / 2
        // Show all items if expanding would add only one more row
        val shouldShowAllItemsByDefault = totalRows - 5 <= 1

        var showAllValues by rememberSaveable { mutableStateOf(false) }
        val maxItems = when (workoutValuesGridType) {
            WorkoutValuesGridType.MULTISPORT_PART_COMPACT -> 4
            WorkoutValuesGridType.ANALYSIS -> 8
            WorkoutValuesGridType.MULTISPORT_PART_FULL,
            WorkoutValuesGridType.NORMAL,
            WorkoutValuesGridType.LONG_SCREENSHOT -> if ((showAllValues && !enableWorkoutValueGroups) || shouldShowAllItemsByDefault) {
                workoutValues.size
            } else 10
        }
        val items = workoutValues.take(maxItems)
        val rows = (items.size + 1) / 2

        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.wrapContentHeight()
        ) {
            if (showHeader) {
                WorkoutValuesHeader(
                    activityName = activityName,
                    activityIcon = activityIcon,
                    showDetailsButton = showDetailsButton,
                    onMultisportDetailsClicked = onMultisportDetailsClicked,
                    modifier = Modifier.fillMaxWidth()
                )
            }
            WorkoutValuesGrid(
                items = items,
                rows = rows,
                onValueClicked = onValueClicked
            )
            onViewMoreClicked?.let {
                if (!shouldShowAllItemsByDefault && workoutValuesGridType.isExpandSupported() && workoutValues.size > 10) {
                    if (enableWorkoutValueGroups) {
                        // todo replace with 'Show all' button
                        WorkoutValuesFooter(
                            isExpanded = false,
                            onToggleShowAllItems = {
                                showAllValues = true
                                onViewMoreClicked.invoke()
                            }
                        )
                    } else {
                        WorkoutValuesFooter(
                            isExpanded = showAllValues,
                            onToggleShowAllItems = {
                                showAllValues = !showAllValues
                                if (showAllValues) {
                                    onViewMoreClicked.invoke()
                                }
                            }
                        )
                    }
                }
            }
            if (enableWorkoutValueGroups) {
                WorkoutValueGroups(
                    show = showAllValues,
                    onDismissRequest = { showAllValues = false },
                    workoutValueGroups = workoutValueGroups,
                    onValueClicked = onValueClicked
                )
            }
        }
    }
}

@Composable
private fun WorkoutValueGroups(
    show: Boolean,
    onDismissRequest: () -> Unit,
    workoutValueGroups: List<WorkoutValueGroupData>,
    onValueClicked: (WorkoutValue) -> Unit
) {
    SuuntoFullscreenDialog(
        showDialog = show,
        onDismissRequest = onDismissRequest,
        disableScrim = true,
        useSystemBarTheme = false,
        content = { onDismiss ->
            Column {
                TopAppBar(
                    title = {
                        Text(
                            text = stringResource(R.string.workout_values_stats).uppercase()
                        )
                    },
                    navigationIcon = {
                        IconButton(onClick = onDismiss) {
                            Icon(Icons.Default.ArrowBack, contentDescription = "Back")
                        }
                    },
                    backgroundColor = MaterialTheme.colors.surface,
                    elevation = 4.dp
                )
                SummaryValueGroups(
                    workoutValueGroups = workoutValueGroups,
                    onValueClicked = onValueClicked,
                    modifier = Modifier.weight(1f)
                )
            }
        }
    )
}

@Composable
private fun SummaryValueGroups(
    workoutValueGroups: List<WorkoutValueGroupData>,
    onValueClicked: (WorkoutValue) -> Unit,
    modifier: Modifier = Modifier,
    enableHighlight: Boolean = false, // todo: Enable when updating the workout details to the new design.
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colors.surface)
            .verticalScroll(rememberScrollState()),
    ) {
        workoutValueGroups.forEachIndexed { index, group ->
            if (index > 0) {
                Spacer(modifier = Modifier.height(MaterialTheme.spacing.large))
            }
            Text(
                text = group.name,
                modifier = Modifier.padding(all = MaterialTheme.spacing.medium),
                style = MaterialTheme.typography.bodyLargeBold,
                color = MaterialTheme.colors.onSurface,
            )
            val gridItems = group.workoutValues.filter {
                !enableHighlight || !group.highlight.contains(it.workoutValue.item)
            }
            val rows = (gridItems.size + 1) / 2
            group.workoutValues.filter { enableHighlight && group.highlight.contains(it.workoutValue.item) }
                .forEach { value ->
                    Divider(
                        modifier = Modifier.fillMaxWidth(),
                        color = MaterialTheme.colors.dividerColor
                    )
                    WorkoutValuesGridItem(
                        workoutValueGridItem = value,
                        onValueClicked = onValueClicked,
                        valueTextStyle = MaterialTheme.typography.bodyMegaBold
                    )
                }
            WorkoutValuesGrid(
                items = gridItems,
                rows = rows,
                onValueClicked = onValueClicked
            )
            Divider(color = MaterialTheme.colors.dividerColor)
        }
    }
}

@Preview
@Composable
private fun WorkoutValuesContainerPreview() {
    AppTheme {
        WorkoutValuesContainer(
            showHeader = true,
            activityName = "Scubadiving",
            activityIcon = CR.drawable.ic_activity_scuba,
            workoutValues = WorkoutValueGridDummyData.gridItems,
            workoutValueGroups = emptyList(),
            workoutValuesGridType = WorkoutValuesGridType.NORMAL,
            showDetailsButton = false,
            onValueClicked = {},
            onMultisportDetailsClicked = {},
            onViewMoreClicked = {}
        )
    }
}

@Preview
@Composable
private fun WorkoutValuesContainerMultiSportPartPreview() {
    AppTheme {
        WorkoutValuesContainer(
            showHeader = true,
            activityName = "Scubadiving",
            activityIcon = CR.drawable.ic_activity_scuba,
            workoutValues = WorkoutValueGridDummyData.gridItems.take(4),
            workoutValueGroups = emptyList(),
            workoutValuesGridType = WorkoutValuesGridType.MULTISPORT_PART_COMPACT,
            showDetailsButton = true,
            onValueClicked = {},
            onMultisportDetailsClicked = {},
            onViewMoreClicked = {}
        )
    }
}

@Preview
@Composable
private fun SummaryGroupsPreview() {
    AppTheme {
        SummaryValueGroups(
            workoutValueGroups = listOf(
                WorkoutValueGroupData(
                    name = "Duration",
                    items = listOf(
                        SummaryItem.DURATION,
                        SummaryItem.ASCENTTIME,
                        SummaryItem.DESCENTTIME,
                    ),
                    highlight = listOf(
                        SummaryItem.DURATION,
                        SummaryItem.ASCENTTIME,
                    ),
                    workoutValues = listOf(
                        WorkoutValuesGridItemData(
                            name = "Duration",
                            value = "42’00.5",
                            showMoreInfoIcon = false,
                            workoutValue = WorkoutValue.createEmpty()
                                .copy(item = SummaryItem.DURATION),
                        ),
                        WorkoutValuesGridItemData(
                            name = "Ascent duration",
                            value = "22'25.0",
                            showMoreInfoIcon = false,
                            workoutValue = WorkoutValue.createEmpty()
                                .copy(item = SummaryItem.ASCENTTIME),
                        ),
                        WorkoutValuesGridItemData(
                            name = "Descent duration",
                            value = "19'15.0",
                            showMoreInfoIcon = false,
                            workoutValue = WorkoutValue.createEmpty(),
                        ),
                    )
                ),
                WorkoutValueGroupData(
                    name = "Pace",
                    items = listOf(
                        SummaryItem.AVGPACE,
                        SummaryItem.MAXPACE,
                        SummaryItem.NORMALIZEDGRADEDPACE,
                        SummaryItem.PEAKPACE30S,
                    ),
                    highlight = listOf(
                    ),
                    workoutValues = listOf(
                        WorkoutValuesGridItemData(
                            name = "Average pace",
                            value = "05'02 /km",
                            showMoreInfoIcon = false,
                            workoutValue = WorkoutValue.createEmpty()
                                .copy(item = SummaryItem.AVGPACE),
                        ),
                        WorkoutValuesGridItemData(
                            name = "Max pace",
                            value = "03'21 &km",
                            showMoreInfoIcon = false,
                            workoutValue = WorkoutValue.createEmpty(),
                        ),
                        WorkoutValuesGridItemData(
                            name = "Normalized Graded Pace",
                            value = "04'42 /km",
                            showMoreInfoIcon = false,
                            workoutValue = WorkoutValue.createEmpty(),
                        ),
                        WorkoutValuesGridItemData(
                            name = "Peak pace, 30 s",
                            value = "03'23 /km",
                            showMoreInfoIcon = false,
                            workoutValue = WorkoutValue.createEmpty(),
                        ),
                    )
                ),
            ),
            onValueClicked = {}
        )
    }
}

@Preview
@Composable
private fun WorkoutValueGroupsPreview() {
    AppTheme {
        WorkoutValueGroups(
            show = true,
            onDismissRequest = {},
            workoutValueGroups = listOf(
                WorkoutValueGroupData(
                    name = "Duration",
                    items = listOf(
                        SummaryItem.DURATION,
                        SummaryItem.ASCENTTIME,
                        SummaryItem.DESCENTTIME,
                    ),
                    highlight = listOf(
                        SummaryItem.DURATION,
                        SummaryItem.ASCENTTIME,
                    ),
                    workoutValues = listOf(
                        WorkoutValuesGridItemData(
                            name = "Duration",
                            value = "42'00.5",
                            showMoreInfoIcon = false,
                            workoutValue = WorkoutValue.createEmpty()
                                .copy(item = SummaryItem.DURATION),
                        ),
                        WorkoutValuesGridItemData(
                            name = "Ascent duration",
                            value = "22'25.0",
                            showMoreInfoIcon = false,
                            workoutValue = WorkoutValue.createEmpty()
                                .copy(item = SummaryItem.ASCENTTIME),
                        ),
                        WorkoutValuesGridItemData(
                            name = "Descent duration",
                            value = "19'15.0",
                            showMoreInfoIcon = false,
                            workoutValue = WorkoutValue.createEmpty(),
                        ),
                    )
                ),
                WorkoutValueGroupData(
                    name = "Pace",
                    items = listOf(
                        SummaryItem.AVGPACE,
                        SummaryItem.MAXPACE,
                        SummaryItem.NORMALIZEDGRADEDPACE,
                        SummaryItem.PEAKPACE30S,
                    ),
                    highlight = listOf(),
                    workoutValues = listOf(
                        WorkoutValuesGridItemData(
                            name = "Average pace",
                            value = "05'02 /km",
                            showMoreInfoIcon = false,
                            workoutValue = WorkoutValue.createEmpty()
                                .copy(item = SummaryItem.AVGPACE),
                        ),
                        WorkoutValuesGridItemData(
                            name = "Max pace",
                            value = "03'21 /km",
                            showMoreInfoIcon = false,
                            workoutValue = WorkoutValue.createEmpty(),
                        ),
                        WorkoutValuesGridItemData(
                            name = "Normalized Graded Pace",
                            value = "04'42 /km",
                            showMoreInfoIcon = false,
                            workoutValue = WorkoutValue.createEmpty(),
                        ),
                        WorkoutValuesGridItemData(
                            name = "Peak pace, 30 s",
                            value = "03'23 /km",
                            showMoreInfoIcon = false,
                            workoutValue = WorkoutValue.createEmpty(),
                        ),
                    )
                ),
            ),
            onValueClicked = {}
        )
    }
}

object WorkoutValueGridDummyData {
    val gridItems = listOf(
        WorkoutValuesGridItemData("Dive time", "45'54.6"),
        WorkoutValuesGridItemData("Max Depth", "30,7m"),
        WorkoutValuesGridItemData("Avg depth", "12,1"),
        WorkoutValuesGridItemData("Bottom temp.", "4 C"),
        WorkoutValuesGridItemData("In series", "2"),
        WorkoutValuesGridItemData("Surface time", "1:40"),
        WorkoutValuesGridItemData(
            "Algorithm",
            "The Suunto Fused RGBM Algorithm optimizes safe diving practices by integrating two decompression models for enhanced depth and risk management."
        ),
        WorkoutValuesGridItemData("Dive mode", "CCR Trimix"),
        WorkoutValuesGridItemData("Gas type", "CC Oxygen"),
        WorkoutValuesGridItemData("Gas type", "NX5"),
        WorkoutValuesGridItemData("Gas type", "TX21/4"),
        WorkoutValuesGridItemData("Gas type", "NX99"),
        WorkoutValuesGridItemData("Start pressure", "163 bar"),
        WorkoutValuesGridItemData("CNS", "27 %")
    )
}

enum class WorkoutValuesGridType {
    NORMAL,
    MULTISPORT_PART_COMPACT,
    MULTISPORT_PART_FULL,
    ANALYSIS,
    LONG_SCREENSHOT;

    fun isExpandSupported() =
        this == NORMAL || this == MULTISPORT_PART_FULL || this == LONG_SCREENSHOT
}
