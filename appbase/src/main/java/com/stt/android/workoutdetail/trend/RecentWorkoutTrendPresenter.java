package com.stt.android.workoutdetail.trend;

import android.content.Context;
import android.content.SharedPreferences;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.data.LineDataSet;
import com.github.mikephil.charting.utils.EntryXComparator;
import com.stt.android.R;
import com.stt.android.controllers.CurrentUserController;
import com.stt.android.controllers.UserSettingsController;
import com.stt.android.controllers.WorkoutHeaderController;
import com.stt.android.domain.user.MeasurementUnit;
import com.stt.android.domain.user.workout.RecentWorkoutTrend;
import com.stt.android.domain.workout.ActivityType;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.models.SimilarWorkoutModel;
import com.stt.android.presenters.MVPPresenter;
import com.stt.android.utils.STTConstants;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import javax.inject.Inject;
import rx.Observable;
import rx.Single;
import rx.Subscriber;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;
import timber.log.Timber;

public class RecentWorkoutTrendPresenter extends MVPPresenter<RecentWorkoutTrendView> {
    private final Context applicationContext;
    private final SharedPreferences sharedPreferences;
    @SuppressWarnings("WeakerAccess")
    final CurrentUserController currentUserController;
    @SuppressWarnings("WeakerAccess")
    final UserSettingsController userSettingsController;
    private final SimilarWorkoutModel similarWorkoutModel;

    @SuppressWarnings("WeakerAccess")
    int limit;
    final WorkoutHeaderController workoutHeaderController;
    @SuppressWarnings("WeakerAccess")
    WorkoutHeader referenceWorkout;

    @Nullable
    private Subscription loadRecentWorkoutTrendSubscription;
    @SuppressWarnings("WeakerAccess")
    boolean noWorkoutsWithSimilarRouteFound;
    boolean hideRouteSelection;

    @Inject
    RecentWorkoutTrendPresenter(
        Context applicationContext,
        SharedPreferences sharedPreferences,
        CurrentUserController currentUserController,
        UserSettingsController userSettingsController,
        SimilarWorkoutModel similarWorkoutModel,
        WorkoutHeaderController workoutHeaderController
    ) {
        this.applicationContext = applicationContext;
        this.sharedPreferences = sharedPreferences;
        this.currentUserController = currentUserController;
        this.userSettingsController = userSettingsController;
        this.similarWorkoutModel = similarWorkoutModel;
        this.workoutHeaderController = workoutHeaderController;
    }

    void setReferenceWorkout(WorkoutHeader referenceWorkout) {
        this.referenceWorkout = referenceWorkout;
    }

    void setLimit(int limit) {
        this.limit = limit;
    }

    @Override
    protected void onViewTaken() {
        super.onViewTaken();
        loadRecentWorkoutTrend(getRouteSelection());
    }

    private void loadRecentWorkoutTrend(final RouteSelection routeSelection) {
        if (loadRecentWorkoutTrendSubscription != null) {
            loadRecentWorkoutTrendSubscription.unsubscribe();
        }

        // if asked to load workouts on all routes, just do that without any fallbacks
        // if asked to load workouts on same route, do it; falls back to backend if no
        // match is found, and reference workout is not own; and eventually falls back to all routes
        noWorkoutsWithSimilarRouteFound = false;
        Observable<List<WorkoutHeader>> workoutsObservable = Observable.fromCallable(
            () -> {
                if (routeSelection == RouteSelection.ON_ALL_ROUTE) {
                    noWorkoutsWithSimilarRouteFound = true;
                    return similarWorkoutModel.findRecentWorkout(referenceWorkout, limit);
                }

                List<WorkoutHeader> similarRoute = similarWorkoutModel.findRecentWorkoutsOnSimilarRoute(referenceWorkout);
                if (!similarRoute.isEmpty()) {
                    return similarRoute;
                }

                noWorkoutsWithSimilarRouteFound = true;
                hideRouteSelection = true;
                return similarWorkoutModel.findRecentWorkout(referenceWorkout, limit);
            });
        Observable<List<WorkoutHeader>> finalWorkoutsObservable = workoutsObservable
            // Sort the workouts by start time and limit the amount to "limit"
            .map(workouts -> {
                workouts = new ArrayList<>(workouts);
                workouts.sort(
                    (left, right) -> Long.compare(right.getStartTime(), left.getStartTime()));

                if (limit > 0 && workouts.size() > limit) {
                    workouts = workouts.subList(0, limit);
                }

                return workouts;
            });

        loadRecentWorkoutTrendSubscription =
            // Original referenceWorkout from the bundle has polyline=null to prevent
            // TransactionTooLargeException. Fetch the full header from db.
            loadWorkoutHeader(referenceWorkout.getId())
                .map(header -> this.referenceWorkout = header)
                .flatMapObservable(header -> finalWorkoutsObservable.map(this::buildRecentTrend))
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Subscriber<>() {
                    @Override
                    public void onCompleted() {
                        // do nothing
                    }

                    @Override
                    public void onError(Throwable e) {
                        RecentWorkoutTrendView v = getView();
                        if (v != null) {
                            v.onTrendLoadFailed();
                        }
                    }

                    @Override
                    public void onNext(RecentWorkoutTrend result) {
                        RecentWorkoutTrendView v = getView();
                        if (v != null) {
                            if (result == null || (!currentUserController.getUsername()
                                .equals(referenceWorkout.getUsername())
                                && result.previousWorkout == null)) {
                                // don't show it, if there's only one workout for friends or others
                                v.onNoTrendDataAvailable();
                            } else {
                                if (hideRouteSelection && noWorkoutsWithSimilarRouteFound) {
                                    v.onNoTrendOnSameRouteAvailable();
                                }
                                v.onTrendLoaded(result);
                            }
                        }
                    }
                });
        // Add to the compositeSubscription to delegate unsubscription to presenter
        subscription.add(loadRecentWorkoutTrendSubscription);
    }

    private Single<WorkoutHeader> loadWorkoutHeader(int workoutId) {
        return Single.fromCallable(() -> {
            try {
                List<WorkoutHeader> workoutHeaders = workoutHeaderController.find(Collections.singletonList(workoutId));
                return workoutHeaders.isEmpty() ? null : workoutHeaders.get(0);
            } catch (Exception e) {
                Timber.w(e, "An error has occurred while trying to find workout by id");
                return null;
            }
        });
    }

    void onWorkoutSummaryViewClicked(@NonNull WorkoutHeader selectedWorkoutHeader) {
        if (view != null) {
            view.openDetailsForWorkout(
                selectedWorkoutHeader.getUsername(),
                selectedWorkoutHeader.getId(),
                selectedWorkoutHeader.getKey()
            );
        }
    }

    private RecentWorkoutTrend buildRecentTrend(List<WorkoutHeader> workoutHeaders) {
        int workoutHeaderCount = workoutHeaders != null ? workoutHeaders.size() : 0;
        if (workoutHeaderCount == 0) {
            return null;
        }

        ActivityType activityType = referenceWorkout.getActivityType();
        boolean shouldAddCadence = ActivityType.CYCLING.equals(activityType) ||
            ActivityType.MOUNTAIN_BIKING.equals(activityType) ||
            ActivityType.GRAVEL_CYCLING.equals(activityType);

        int xIndex = 0;
        ArrayList<Integer> colors = new ArrayList<>(workoutHeaderCount);

        ArrayList<Entry> durationEntries = new ArrayList<>(workoutHeaderCount);
        ArrayList<Entry> distanceEntries = new ArrayList<>(workoutHeaderCount);
        ArrayList<Entry> speedEntries = new ArrayList<>(workoutHeaderCount);
        ArrayList<Entry> paceEntries = new ArrayList<>(workoutHeaderCount);
        ArrayList<Entry> energyEntries = new ArrayList<>(workoutHeaderCount);
        ArrayList<Entry> averageHeartRateEntries = new ArrayList<>(workoutHeaderCount);
        ArrayList<Entry> averageCadenceEntries =
            shouldAddCadence ? new ArrayList<>(workoutHeaderCount) : null;

        int color = ContextCompat.getColor(applicationContext, R.color.blue);
        int highLightColor = ContextCompat.getColor(applicationContext, R.color.accent);
        MeasurementUnit measurementUnit = userSettingsController.getSettings().getMeasurementUnit();
        long referenceTime = referenceWorkout.getStartTime();

        for (int i = workoutHeaderCount - 1; i >= 0; --i) {
            WorkoutHeader recentWorkoutHeader = workoutHeaders.get(i);

            long workoutStartTime = recentWorkoutHeader.getStartTime();

            durationEntries.add(
                new Entry(xIndex, (float) recentWorkoutHeader.getTotalTime(), recentWorkoutHeader));
            distanceEntries.add(new Entry(xIndex, (float) recentWorkoutHeader.getTotalDistance(),
                recentWorkoutHeader));
            float speed = (float) recentWorkoutHeader.getAvgSpeed();
            speedEntries.add(new Entry(xIndex, speed, recentWorkoutHeader));
            paceEntries.add(
                new Entry(xIndex, (float) measurementUnit.toPaceUnit(speed), recentWorkoutHeader));
            energyEntries.add(new Entry(xIndex, (float) recentWorkoutHeader.getEnergyConsumption(),
                recentWorkoutHeader));
            averageHeartRateEntries.add(
                new Entry(xIndex, (float) recentWorkoutHeader.getHeartRateAverage(),
                    recentWorkoutHeader));

            if (shouldAddCadence) {
                averageCadenceEntries.add(new Entry(xIndex, recentWorkoutHeader.getAverageCadence(),
                    recentWorkoutHeader));
            }

            colors.add(referenceTime == workoutStartTime ? highLightColor : color);
            ++xIndex;
        }
        WorkoutHeader previousWorkout = workoutHeaderCount > 1 ? workoutHeaders.get(1) : null;
        LineData durationLineData = createLineData(durationEntries, color, colors);
        LineData distanceLineData = createLineData(distanceEntries, color, colors);
        LineData speedLineData = createLineData(speedEntries, color, colors);
        LineData paceLineData = createLineData(paceEntries, color, colors);
        LineData energyLineData = createLineData(energyEntries, color, colors);
        LineData averageHeartRateLineData =
            createLineData(averageHeartRateEntries, color, colors);
        LineData averageCadenceLineData =
            shouldAddCadence ? createLineData(averageCadenceEntries, color, colors) : null;
        return new RecentWorkoutTrend(referenceWorkout, previousWorkout, durationLineData,
            distanceLineData, speedLineData, paceLineData, energyLineData, averageHeartRateLineData,
            averageCadenceLineData);
    }

    private static LineData createLineData(ArrayList<Entry> entries, int color, ArrayList<Integer> colors) {
        // Data should be order based on x-axis values
        // https://github.com/PhilJay/MPAndroidChart/wiki/Setting-Data#the-order-of-entries
        entries.sort(new EntryXComparator());
        LineData lineData = new LineData();

        LineDataSet lineDataSet = new LineDataSet(entries, "");
        lineDataSet.setLineWidth(1.5f);
        lineDataSet.setColors(colors);
        lineDataSet.setDrawFilled(true);
        lineDataSet.setFillColor(color);
        lineDataSet.setFillAlpha(64);
        lineDataSet.setDrawValues(false);
        lineDataSet.setDrawHighlightIndicators(false);
        lineDataSet.setDrawCircles(true);
        lineDataSet.setDrawCircleHole(false);
        lineDataSet.setCircleColors(colors);
        lineDataSet.setCircleRadius(3f);
        lineData.addDataSet(lineDataSet);

        return lineData;
    }

    RouteSelection getRouteSelection() {
        if (noWorkoutsWithSimilarRouteFound) {
            return RouteSelection.ON_ALL_ROUTE;
        }
        String routeSelectionStr = sharedPreferences.getString(
            STTConstants.DefaultPreferences.KEY_WORKOUT_TREND_ROUTE_SELECTION,
            RouteSelection.DEFAULT.name());
        return RouteSelection.valueOf(routeSelectionStr);
    }

    void updateRouteSelection(RouteSelection routeSelection) {
        sharedPreferences.edit()
            .putString(STTConstants.DefaultPreferences.KEY_WORKOUT_TREND_ROUTE_SELECTION,
                routeSelection.name())
            .apply();
        loadRecentWorkoutTrend(routeSelection);
    }

    WorkoutHeader getReferenceWorkout() {
        return referenceWorkout;
    }

    void updateReferenceWorkout(WorkoutHeader referenceWorkout) {
        this.referenceWorkout = referenceWorkout;
        loadRecentWorkoutTrend(getRouteSelection());
    }
}
