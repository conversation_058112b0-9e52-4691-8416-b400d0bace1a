package com.stt.android.workoutdetail.comments

import android.animation.Animator
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.google.android.material.snackbar.Snackbar
import com.stt.android.AppConfig
import com.stt.android.R
import com.stt.android.common.ui.SimpleDialogFragment
import com.stt.android.common.ui.SimpleDialogFragment.Companion.newInstance
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutCommentController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.databinding.DialogWorkoutCommentsBinding
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.ui.utils.AnimationHelper
import com.stt.android.ui.utils.DialogHelper
import com.stt.android.utils.KeyboardUtil
import com.stt.android.utils.STTConstants
import com.stt.android.workoutdetail.comments.DragToDismissFrameLayout.DragDismissCallback
import com.stt.android.workouts.edit.SaveWorkoutHeaderService
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import kotlin.math.min

@AndroidEntryPoint
class CommentsDialogFragment : DialogFragment(), DragDismissCallback,
    CommentTextForm.OnSubmitListener, WorkoutHeaderView.OnCloseListener {
    @Inject
    lateinit var workoutCommentController: WorkoutCommentController

    @Inject
    lateinit var currentUserController: CurrentUserController

    private val viewModel: CommentsDialogViewModel by viewModels()
    private val binding: DialogWorkoutCommentsBinding by lazy {
        DialogWorkoutCommentsBinding.bind(requireView())
    }
    private val adapter: CommentsAdapter = CommentsAdapter(
        onDeleteCommentRequested = ::showDeleteDialogFragment,
    )

    private var deleteCommentKey: String? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        val binding = DialogWorkoutCommentsBinding.inflate(inflater, container, false)

        requireDialog().window?.requestFeature(Window.FEATURE_NO_TITLE)

        val shouldShowKeyboard = requireArguments().getBoolean(STTConstants.ExtraKeys.COMMENTS_SHOW_KEYBOARD)
        if (shouldShowKeyboard) {
            binding.commentForm.requestTextFocus()
            KeyboardUtil.showKeyboardFrom(requireDialog(), requireContext(), binding.root)
        }

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog?.window?.attributes?.windowAnimations = R.style.EnterUpExitDownDialogAnimation

        binding.commentsList.isNestedScrollingEnabled = true
        binding.commentsList.adapter = adapter
        binding.elasticDismissFrameLayout.addListener(this)
        binding.commentForm.onSubmitListener = this
        binding.workoutHeader.setWorkoutHeader(viewModel.workoutHeader)
        binding.workoutHeader.onCloseListener = this

        observeCommentsUiState()
    }

    private fun observeCommentsUiState() {
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.commentsUiState
                    .collect { state ->
                        when (state) {
                            is CommentsDialogUiState.Loading -> binding.loadingSpinner.isVisible = true

                            is CommentsDialogUiState.Loaded -> {
                                binding.loadingSpinner.isVisible = false
                                binding.commentsList.isVisible = true

                                adapter.submitList(state.items) {
                                    binding.noComments.isVisible = state.items.isEmpty()
                                    binding.commentsList.scrollToPosition(state.items.size - 1)
                                }
                            }

                            is CommentsDialogUiState.Error -> {
                                binding.loadingSpinner.isVisible = false
                                context?.let { DialogHelper.showDialog(it, R.string.network_disabled_enable) }
                            }
                        }
                    }
            }
        }
    }

    override fun onStart() {
        super.onStart()

        val window = dialog?.window ?: return

        val width = min(
            resources.displayMetrics.widthPixels.toDouble(),
            resources.getDimensionPixelSize(com.stt.android.core.R.dimen.content_max_width).toDouble(),
        ).toInt()
        val height = ViewGroup.LayoutParams.MATCH_PARENT
        window.setLayout(width, height)

        window.decorView.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.transparent))
        window.decorView.setPadding(0, 0, 0, 0)
    }

    override fun onDestroyView() {
        hideKeyboard()
        super.onDestroyView()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        outState.putString(DELETE_COMMENT_KEY, deleteCommentKey)
        super.onSaveInstanceState(outState)
    }

    override fun onViewStateRestored(savedInstanceState: Bundle?) {
        super.onViewStateRestored(savedInstanceState)

        if (savedInstanceState != null) {
            deleteCommentKey = savedInstanceState.getString(DELETE_COMMENT_KEY)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == DELETE_DIALOG_CODE &&
            resultCode == SimpleDialogFragment.RESULT_POSITIVE &&
            deleteCommentKey != null) {
            deleteComment(requireNotNull(deleteCommentKey))
            deleteCommentKey = null
        }
    }

    override fun onClose() {
        hideKeyboard()
        safeDismiss(false)
    }

    private fun hideKeyboard() {
        KeyboardUtil.hideKeyboardFrom(requireContext(), binding.root)
    }

    override fun onDragDismissed(up: Boolean) {
        hideKeyboard()
        safeDismiss(up)
    }

    private fun safeDismiss(up: Boolean) {
        if (up) {
            AnimationHelper.move(
                binding.elasticDismissFrameLayout,
                0f,
                0f,
                binding.elasticDismissFrameLayout.translationY,
                binding.elasticDismissFrameLayout.translationY - binding.elasticDismissFrameLayout.height,
            ).setListener(
                object : Animator.AnimatorListener {
                    override fun onAnimationStart(animator: Animator) = Unit

                    override fun onAnimationEnd(animator: Animator) {
                        dismissAllowingStateLoss()
                    }

                    override fun onAnimationCancel(animator: Animator) = Unit

                    override fun onAnimationRepeat(animator: Animator) = Unit
                }
            )
        } else {
            dismissAllowingStateLoss()
        }
    }

    private fun deleteComment(commentKey: String) {
        lifecycleScope.launch {
            runSuspendCatching {
                viewModel.deleteComment(commentKey)
            }.onSuccess {
                // This triggers reloading of comments.
                changeWorkoutHeaderCommentCount(-1)
            }.onFailure { e ->
                Timber.w(e, "Failed to delete comment")

                Snackbar.make(
                    binding.commentsList,
                    R.string.error_generic_try_again,
                    Snackbar.LENGTH_SHORT,
                ).show()
            }
        }
    }

    private fun showDeleteDialogFragment(commentKey: String?) {
        deleteCommentKey = commentKey

        if (parentFragmentManager.findFragmentByTag(DELETE_DIALOG_TAG) == null) {
            val dialog = newInstance(
                getString(R.string.delete_comment),
                null,
                getString(R.string.delete),
                getString(R.string.cancel)
            )
            dialog.setTargetFragment(this@CommentsDialogFragment, DELETE_DIALOG_CODE)
            dialog.show(parentFragmentManager, DELETE_DIALOG_TAG)
        }
    }

    override fun onSubmit(text: String) {
        binding.loadingSpinner.isVisible = true
        binding.commentForm.isEnabled = false

        lifecycleScope.launch {
            runSuspendCatching {
                viewModel.sendComment(text)
            }.onSuccess {
                binding.loadingSpinner.isVisible = false
                binding.commentForm.isEnabled = true
                binding.commentForm.clearText()

                // This triggers reloading of comments.
                changeWorkoutHeaderCommentCount(1)
            }.onFailure { e ->
                Timber.w(e, "Failed to send comment")

                binding.loadingSpinner.isVisible = false
                binding.commentForm.isEnabled = true
                DialogHelper.showDialog(requireContext(), R.string.network_disabled_enable)
            }
        }
    }

    private fun changeWorkoutHeaderCommentCount(commentChangeCount: Int) {
        SaveWorkoutHeaderService.enqueueWork(
            context = requireContext(),
            workoutHeader = viewModel.workoutHeader.toBuilder()
                .commentCount(viewModel.workoutHeader.commentCount + commentChangeCount)
                .locallyChanged(true)
                .build(),
            shouldSync = false,
        )
    }

    companion object {
        const val FRAGMENT_TAG: String =
            "com.stt.android.ui.fragments.workout.details.comments.CommentsDialogFragment"

        private const val DELETE_DIALOG_TAG = AppConfig.APPLICATION_ID + "DELETE_DIALOG_TAG"
        private const val DELETE_DIALOG_CODE = 10

        private const val DELETE_COMMENT_KEY = "DELETE_COMMENT_KEY"

        fun newInstance(
            workoutHeader: WorkoutHeader,
            showKeyboard: Boolean,
        ): CommentsDialogFragment = CommentsDialogFragment().apply {
            val bundle = Bundle()
            bundle.putParcelable(STTConstants.ExtraKeys.WORKOUT_HEADER, workoutHeader)
            bundle.putBoolean(STTConstants.ExtraKeys.COMMENTS_SHOW_KEYBOARD, showKeyboard)
            arguments = bundle
        }
    }
}
