package com.stt.android.workoutdetail.workoutvalues.composables

import androidx.annotation.DrawableRes
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.R
import com.stt.android.compose.component.SuuntoActivityIcon
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.spacing
import com.stt.android.core.R as CR

@Composable
fun WorkoutValuesHeader(
    activityName: String,
    @DrawableRes activityIcon: Int,
    showDetailsButton: Boolean,
    onMultisportDetailsClicked: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.padding(
            start = MaterialTheme.spacing.medium,
            end = MaterialTheme.spacing.small,
            top = if (showDetailsButton) MaterialTheme.spacing.xsmall else MaterialTheme.spacing.smaller,
            bottom = if (showDetailsButton) MaterialTheme.spacing.xsmall else MaterialTheme.spacing.smaller
        ),
        verticalAlignment = Alignment.CenterVertically
    ) {
        SuuntoActivityIcon(
            iconRes = activityIcon,
            tint = Color.Black,
            background = Color.Transparent,
        )

        Text(
            modifier = Modifier.padding(start = MaterialTheme.spacing.smaller),
            text = activityName.uppercase(),
            style = MaterialTheme.typography.bodyBold
        )

        Spacer(modifier = Modifier.weight(1f))
        if (showDetailsButton) {
            TextButton(onClick = onMultisportDetailsClicked) {
                Text(
                    text = stringResource(id = R.string.multisport_details).uppercase(),
                    style = MaterialTheme.typography.bodyBold
                )
            }
        }
    }
}

@Preview(showBackground = true, widthDp = 380)
@Composable
private fun WorkoutValuesHeaderPreview() {
    AppTheme {
        WorkoutValuesHeader(
            activityName = "Running",
            activityIcon = CR.drawable.ic_activity_running,
            showDetailsButton = true,
            onMultisportDetailsClicked = {}
        )
    }
}
