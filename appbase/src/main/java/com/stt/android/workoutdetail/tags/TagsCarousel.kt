package com.stt.android.workoutdetail.tags

import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.stt.android.R
import com.stt.android.common.KotlinEpoxyHolder
import com.stt.android.newfeed.OnTagClicked

// The way tags are shown in feed and workout details are different;
// Feed: one epoxy model with multiple data binding
// Workout details: multiple epoxy models;
// for that reason, it is not possible (At least for now) to reuse the same component between the two;
// and for that we have this view kinda a wrapper for TagsCarouselView
// this view get TagsData as params and then call addDats, and TagsCarouselView in feeds uses BindingAdapter that calls `addTags`
@EpoxyModelClass
abstract class TagsCarousel : EpoxyModelWithHolder<Holder>() {
    override fun getDefaultLayout() = R.layout.tags_carousel

    @EpoxyAttribute
    lateinit var tagsData: TagsData

    @JvmField
    @EpoxyAttribute
    var isSubscribedToPremium: Boolean = false

    @EpoxyAttribute
    lateinit var onTagClicked: OnTagClicked

    override fun bind(holder: Holder) {
        holder.tagsCarousel.addTags(tagsData, isSubscribedToPremium, onTagClicked)
    }
}

class Holder : KotlinEpoxyHolder() {
    val tagsCarousel by bind<TagsCarouselView>(R.id.tags_carousel)
}
