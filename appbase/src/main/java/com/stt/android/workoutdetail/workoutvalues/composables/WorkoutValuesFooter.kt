package com.stt.android.workoutdetail.workoutvalues.composables

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.material.Divider
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.R
import com.stt.android.compose.theme.AppTheme
import com.stt.android.core.R as CR

@Composable
fun WorkoutValuesFooter(
    isExpanded: Boolean,
    onToggleShowAllItems: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(MaterialTheme.colors.surface)
    ) {
        Divider(
            modifier = Modifier.fillMaxWidth(),
            color = colorResource(id = CR.color.very_light_gray)
        )
        TextButton(
            onClick = onToggleShowAllItems,
            modifier = Modifier
                .fillMaxWidth()
                .height(IntrinsicSize.Min)
        ) {
            val buttonTextId = if (isExpanded) {
                R.string.workout_values_grid_view_less
            } else {
                R.string.workout_values_grid_view_more
            }
            Text(text = stringResource(id = buttonTextId).uppercase())
        }
    }
}

@Preview
@Composable
private fun WorkoutValuesFooterPreview() {
    AppTheme {
        WorkoutValuesFooter(
            isExpanded = false,
            onToggleShowAllItems = {}
        )
    }
}
