package com.stt.android.workoutdetail.location.bottomsheet

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import com.google.android.gms.maps.model.LatLng
import com.stt.android.R
import com.stt.android.common.ui.ViewModelFragment2
import com.stt.android.databinding.FragmentWorkoutLocationBottomSheetBinding
import com.stt.android.workoutdetail.location.select.WorkoutSelectLocationFragment.Companion.EXTRA_LOCATION_RESULT
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class WorkoutLocationBottomSheetFragment : ViewModelFragment2() {

    override val viewModel: WorkoutLocationBottomSheetViewModel by viewModels(
        ownerProducer = { requireParentFragment() }
    )

    private val viewDataBinding: FragmentWorkoutLocationBottomSheetBinding get() = requireBinding()

    override fun getLayoutResId() = R.layout.fragment_workout_location_bottom_sheet

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val view = super.onCreateView(inflater, container, savedInstanceState)
        viewDataBinding.workoutLocationBottomSheetSaveLocation.setOnClickListener {
            setResult(viewModel.pendingLatLng.value)
        }
        return view
    }

    private fun setResult(latLng: LatLng?) {
        activity?.run {
            val intent = Intent().putExtra(EXTRA_LOCATION_RESULT, latLng)
            this.setResult(Activity.RESULT_OK, intent)
            this.finish()
        }
    }

    companion object {
        const val FRAGMENT_TAG = "WorkoutLocationBottomSheetFragment"
    }
}
