package com.stt.android.workoutdetail.trend;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewpager.widget.ViewPager;
import com.amersports.formatter.unit.jscience.JScienceUnitConverter;
import com.stt.android.R;
import com.stt.android.databinding.RecentWorkoutTrendFragmentBinding;
import com.stt.android.domain.user.workout.RecentWorkoutTrend;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.mapping.InfoModelFormatter;
import com.stt.android.ui.adapters.RecentWorkoutTrendPagerAdapter;
import com.stt.android.ui.fragments.summaries.CustomDropdownArrayAdapter;
import com.stt.android.ui.fragments.workout.BaseWorkoutHeaderFragment;
import com.stt.android.ui.utils.AnimationHelper;
import com.stt.android.ui.utils.PagerBulletStripUtility;
import com.stt.android.utils.STTConstants;
import dagger.hilt.android.AndroidEntryPoint;
import javax.inject.Inject;

@AndroidEntryPoint
public class RecentWorkoutTrendFragment extends BaseWorkoutHeaderFragment
    implements RecentWorkoutTrendView, ViewPager.OnPageChangeListener,
    AdapterView.OnItemSelectedListener {
    public static final String FRAGMENT_TAG =
        "com.stt.android.workoutdetail.trend.RecentWorkoutTrendFragment.FRAGMENT_TAG";

    public static RecentWorkoutTrendFragment newInstance(WorkoutHeader workoutHeader) {
        RecentWorkoutTrendFragment fragment = new RecentWorkoutTrendFragment();

        Bundle args = new Bundle();
        // We don't need the polyline, so remove it to avoid TransactionTooLargeException
        args.putParcelable(STTConstants.ExtraKeys.WORKOUT_HEADER,
            workoutHeader.toBuilder().polyline(null).build());
        fragment.setArguments(args);

        return fragment;
    }

    private static final int RECENT_WORKOUT_LIMIT = 7;

    @Inject
    RecentWorkoutTrendPresenter recentWorkoutTrendPresenter;

    @Inject
    InfoModelFormatter infoModelFormatter;

    @Inject
    JScienceUnitConverter unitConverter;

    private RecentWorkoutTrendFragmentBinding binding;

    private RecentWorkoutTrendPagerAdapter recentWorkoutTrendPagerAdapter;
    private int lastKnownPage;
    private ImageView[] bullets;

    private boolean initialSelection;
    private static final String PAGER_STATE = "pager_state";

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
        Bundle savedInstanceState) {
        binding = RecentWorkoutTrendFragmentBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

        recentWorkoutTrendPresenter.setReferenceWorkout(getWorkoutHeader());
        recentWorkoutTrendPresenter.setLimit(RECENT_WORKOUT_LIMIT);

        binding.routeSelection.setAdapter(
            new CustomDropdownArrayAdapter<>(requireContext(), RouteSelection.values()));
        binding.routeSelection.setSelection(
            recentWorkoutTrendPresenter.getRouteSelection() == RouteSelection.ON_THIS_ROUTE ? 0
                : 1);
        binding.routeSelection.setOnItemSelectedListener(this);

        initialSelection = true;

        binding.trendViewPager.addOnPageChangeListener(this);
        if (savedInstanceState != null) {
            lastKnownPage = savedInstanceState.getInt(PAGER_STATE);
            onPageSelected(lastKnownPage);
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        recentWorkoutTrendPresenter.takeView(this);
    }

    @Override
    public void onStop() {
        recentWorkoutTrendPresenter.dropView();
        super.onStop();
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
        outState.putInt(PAGER_STATE, lastKnownPage);
        super.onSaveInstanceState(outState);
    }

    @Override
    protected void onWorkoutHeaderUpdated(WorkoutHeader updateWorkoutHeader) {
        WorkoutHeader current = getWorkoutHeader();
        if (!current.getActivityType().equals(updateWorkoutHeader.getActivityType())
            || current.getStartTime() != updateWorkoutHeader.getStartTime()
            || Double.compare(current.getTotalDistance(), updateWorkoutHeader.getTotalDistance())
            != 0) {
            recentWorkoutTrendPresenter.updateReferenceWorkout(updateWorkoutHeader);
        }
    }

    @Override
    public void onTrendLoaded(RecentWorkoutTrend trend) {
        if (binding == null) {
            return;
        }

        AnimationHelper.fadeOut(binding.loadingSpinner);

        recentWorkoutTrendPagerAdapter =
            new RecentWorkoutTrendPagerAdapter(getActivity(), trend,
                recentWorkoutTrendPresenter.getRouteSelection() == RouteSelection.ON_THIS_ROUTE,
                infoModelFormatter, unitConverter);
        binding. trendViewPager.setAdapter(recentWorkoutTrendPagerAdapter);
        binding.bulletStrip.removeAllViews();
        bullets =
            PagerBulletStripUtility.updateBulletStrip(recentWorkoutTrendPagerAdapter.getCount(),
                binding.bulletStrip, binding.trendViewPager);
        binding.trendViewPager.setCurrentItem(lastKnownPage);
        onPageSelected(lastKnownPage);
    }

    @Override
    public void onNoTrendOnSameRouteAvailable() {
        if (binding != null) {
            binding.title.setText(R.string.previous_on_all_route_capital);
            binding.routeSelection.setVisibility(View.GONE);
        }
    }

    @Override
    public void onNoTrendDataAvailable() {
        View root = getView();
        if (root != null) {
            root.setVisibility(View.GONE);
        }
    }

    @Override
    public void onTrendLoadFailed() {
        View root = getView();
        if (root != null) {
            root.setVisibility(View.GONE);
        }
    }

    @Override
    public void openDetailsForWorkout(
        @NonNull String username,
        @Nullable Integer workoutId,
        @Nullable String workoutKey
    ) {
        // do nothing
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
        // do nothing
    }

    @Override
    public void onPageSelected(int position) {
        if (recentWorkoutTrendPagerAdapter != null) {
            binding.dataType.setText(recentWorkoutTrendPagerAdapter.getPageTitle(position));
        }

        if (bullets != null) {
            for (int i = 0; i < bullets.length; i++) {
                bullets[i].setImageLevel(0);
            }
            bullets[position].setImageLevel(1);
            lastKnownPage = position;
        }
    }

    @Override
    public void onPageScrollStateChanged(int state) {
        // do nothing
    }

    @Override
    public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
        if (initialSelection) {
            initialSelection = false;
            return;
        }

        boolean shouldShowSimilarRoute = position == 0;
        RouteSelection routeSelection =
            shouldShowSimilarRoute ? RouteSelection.ON_THIS_ROUTE : RouteSelection.ON_ALL_ROUTE;
        recentWorkoutTrendPresenter.updateRouteSelection(routeSelection);
    }

    @Override
    public void onNothingSelected(AdapterView<?> parent) {
        // do nothing
    }
}
