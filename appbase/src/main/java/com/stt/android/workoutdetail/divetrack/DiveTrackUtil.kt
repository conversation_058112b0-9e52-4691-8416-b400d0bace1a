package com.stt.android.workoutdetail.divetrack

import com.stt.android.divetrack.DiveTrack
import com.stt.android.domain.sml.Sml
import kotlin.math.max
import kotlin.math.min

object DiveTrackUtil {
    fun diveTrackFromSml(sml: Sml?): DiveTrack? {
        var minX = Double.MAX_VALUE
        var maxX = -Double.MAX_VALUE
        var minY = Double.MAX_VALUE
        var maxY = -Double.MAX_VALUE
        var minZ = Double.MAX_VALUE
        var maxZ = -Double.MAX_VALUE
        val points = sml?.streamData?.diveTrack?.map {
            minX = min(minX, it.x)
            maxX = max(maxX, it.x)
            minY = min(minY, it.y)
            maxY = max(maxY, it.y)
            minZ = min(minZ, it.z)
            maxZ = max(maxZ, it.z)
            DiveTrack.Point(
                x = it.x,
                y = it.y,
                z = it.z
            )
        }
        return points?.run {
            DiveTrack(
                points = this,
                xRange = minX.rangeTo(maxX),
                yRange = minY.rangeTo(maxY),
                zRange = minZ.rangeTo(maxZ)
            ).takeIf { this.isNotEmpty() } // return null if there are no points
        }
    }
}
