package com.stt.android.workoutdetail.location.bottomsheet

import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.SavedStateHandle
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.domain.mapbox.FetchLocationNameUseCase
import com.stt.android.models.MapSelectionModel
import com.stt.android.workoutdetail.location.WorkoutLocationFragment
import com.stt.android.workoutdetail.location.base.BaseWorkoutLocationViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class WorkoutLocationBottomSheetViewModel
@Inject constructor(
    savedStateHandle: SavedStateHandle,
    mapSelectionModel: MapSelectionModel,
    fetchLocationNameUseCase: FetchLocationNameUseCase,
    coroutinesDispatchers: CoroutinesDispatchers,
) : BaseWorkoutLocationViewModel(
    latLng = savedStateHandle[WorkoutLocationFragment.ARGUMENT_COORDINATE],
    mapSelectionModel = mapSelectionModel,
    fetchLocationNameUseCase = fetchLocationNameUseCase,
    coroutinesDispatchers = coroutinesDispatchers,
) {

    enum class SheetState {
        INSTRUCTIONS,
        LOCATION_INFO,
        LOADING
    }

    private val _viewState = MediatorLiveData<SheetState>()

    init {
        _viewState.apply {
            addSource(pendingLatLng) { updateState() }
            addSource(isLoading) { updateState() }
            value = SheetState.INSTRUCTIONS
        }
    }

    val viewState: LiveData<SheetState>
        get() = _viewState

    private fun updateState() {
        val state = when {
            isLoading.value == true -> SheetState.LOADING
            pendingLatLng.value != null -> SheetState.LOCATION_INFO
            else -> SheetState.INSTRUCTIONS
        }
        if (_viewState.value != state) {
            _viewState.value = state
        }
    }
}
