package com.stt.android.workoutdetail.trend;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.stt.android.domain.user.workout.RecentWorkoutTrend;
import com.stt.android.views.MVPView;

interface RecentWorkoutTrendView extends MVPView {
    void onTrendLoaded(RecentWorkoutTrend trend);

    void onNoTrendOnSameRouteAvailable();

    void onNoTrendDataAvailable();

    void onTrendLoadFailed();

    void openDetailsForWorkout(@NonNull String username, @Nullable Integer workoutId, @Nullable String workoutKey);
}
