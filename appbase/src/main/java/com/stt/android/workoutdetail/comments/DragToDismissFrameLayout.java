package com.stt.android.workoutdetail.comments;

import android.content.Context;
import androidx.core.view.NestedScrollingParent;
import androidx.core.view.ViewCompat;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.widget.FrameLayout;
import java.util.ArrayList;
import java.util.List;

public class DragToDismissFrameLayout extends FrameLayout implements NestedScrollingParent {

    private float dragDismissDistance = -1f;
    private float dragDismissFraction = 0.2f;

    // state
    private boolean draggingDown = false;
    private boolean draggingUp = false;

    private List<DragDismissCallback> callbacks;

    public DragToDismissFrameLayout(Context context) {
        super(context);
    }

    public DragToDismissFrameLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public DragToDismissFrameLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public DragToDismissFrameLayout(Context context, AttributeSet attrs, int defStyleAttr,
        int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        return super.onTouchEvent(event);
    }

    @Override
    public boolean onStartNestedScroll(View child, View target, int nestedScrollAxes) {
        return true;
    }

    @Override
    public void onNestedScrollAccepted(View child, View target, int nestedScrollAxes) {

    }

    @Override
    public boolean onNestedPreFling(View target, float velocityX, float velocityY) {
        return false;
    }

    @Override
    public int getNestedScrollAxes() {
        return ViewCompat.SCROLL_AXIS_VERTICAL;
    }

    @Override
    public void onNestedPreScroll(View target, int dx, int dy, int[] consumed) {
        // if we're in a drag gesture and the user reverses up the we should take those events
        if ((draggingDown && (dy > 0)) || (draggingUp && (dy < 0))) {
            elasticDrag(dy);
            consumed[1] = dy;
        }
    }

    @Override
    public boolean onNestedFling(View target, float velocityX, float velocityY, boolean consumed) {
        return false;
    }

    @Override
    public void onNestedScroll(View target, int dxConsumed, int dyConsumed, int dxUnconsumed,
        int dyUnconsumed) {
        elasticDrag(dyUnconsumed);
    }

    @Override
    public void onStopNestedScroll(View child) {
        if (Math.abs(getTranslationY()) >= dragDismissDistance) {
            dispatchDismissCallback(getTranslationY()<0);
        } else { // settle back to natural position
            animate().translationY(0f)
                .setDuration(200L)
                .setInterpolator(new AccelerateDecelerateInterpolator())
                .setListener(null)
                .start();
            draggingDown = draggingUp = false;
        }
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        if (dragDismissFraction > 0f) {
            dragDismissDistance = h * dragDismissFraction;
        }
    }

    public void addListener(DragDismissCallback listener) {
        if (callbacks == null) {
            callbacks = new ArrayList<>();
        }
        callbacks.add(listener);
    }

    public void removeListener(DragDismissCallback listener) {
        if (callbacks != null && callbacks.size() > 0) {
            callbacks.remove(listener);
        }
    }

    private void elasticDrag(int scroll) {
        if (scroll == 0) return;

        // track the direction & set the pivot point for scaling
        // don't double track i.e. if start dragging down and then reverse, keep tracking as
        // dragging down until they reach the 'natural' position
        if (scroll < 0 && !draggingUp && !draggingDown) {
            draggingDown = true;
        } else if (scroll > 0 && !draggingDown && !draggingUp) {
            draggingUp = true;
        }

        float translationY = getTranslationY() - scroll;
        setTranslationY(translationY);

        // if we've reversed direction and gone past the settle point then clear the flags to
        // allow the list to get the scroll events & reset any transforms
        if ((draggingDown && translationY <= 0) || (draggingUp && translationY >= 0)) {
            //totalDrag = dragTo = dragFraction = 0;
            draggingDown = draggingUp = false;
            setTranslationY(0f);
        }
    }

    private void dispatchDismissCallback(boolean up) {
        if (callbacks != null && !callbacks.isEmpty()) {
            for (DragDismissCallback callback : callbacks) {
                callback.onDragDismissed(up);
            }
        }
    }

    public interface DragDismissCallback {
        void onDragDismissed(boolean up);
    }
}
