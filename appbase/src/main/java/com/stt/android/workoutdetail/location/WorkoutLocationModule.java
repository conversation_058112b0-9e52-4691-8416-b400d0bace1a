package com.stt.android.workoutdetail.location;

import android.content.Context;
import androidx.fragment.app.Fragment;
import com.stt.android.utils.PermissionUtils;
import dagger.Module;
import dagger.Provides;
import dagger.hilt.InstallIn;
import dagger.hilt.android.components.FragmentComponent;
import dagger.hilt.components.SingletonComponent;
import javax.inject.Named;
import pub.devrel.easypermissions.EasyPermissions;

@Module
@InstallIn(SingletonComponent.class)
public abstract class WorkoutLocationModule {

    @Provides
    @Named(WorkoutLocationViewModel.HAS_LOCATION_PERMISSION)
    static boolean provideHasLocationPermission(Context appContext) {
        return EasyPermissions.hasPermissions(appContext, PermissionUtils.LOCATION_PERMISSIONS);
    }
}
