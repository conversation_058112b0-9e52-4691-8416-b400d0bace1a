package com.stt.android.appversion

import android.content.Context

object AppStorePackageUtils {

    private val brandPackageMap = mutableMapOf<String, String>()

    init {
        brandPackageMap["XIAOMI"] = "com.xiaomi.market"
        brandPackageMap["HUAWEI"] = "com.huawei.appmarket"
        brandPackageMap["HONOR"] = "com.huawei.appmarket"
        brandPackageMap["OPPO"] = "com.heytap.market"
        brandPackageMap["REALME"] = "com.heytap.market"
        brandPackageMap["ONEPLUS"] = "com.heytap.market"
        brandPackageMap["VIVO"] = "com.bbk.appstore"
        brandPackageMap["MEIZU"] = "com.meizu.mstore"
        brandPackageMap["LENOVO"] = "com.lenovo.leos.appstore"
        brandPackageMap["SAMSUNG"] = "com.sec.android.app.samsungapps"
    }

    fun getAppStorePkgByPhoneBrand(context: Context, brand: String): String {
        if (brand.isBlank()) return ""
        val appStorePackage = brandPackageMap[brand.uppercase()] ?: ""
        return if (hasAppStoreInstalled(context, appStorePackage)) {
            appStorePackage
        } else {
            ""
        }
    }

    private fun hasAppStoreInstalled(context: Context, appStorePackage: String): Boolean {
        return try {
            context.packageManager.getPackageInfo(appStorePackage, 0)
            true
        } catch (_: Exception) {
            false
        }
    }
}
