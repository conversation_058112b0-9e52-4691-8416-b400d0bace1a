package com.stt.android.appversion

import android.app.Application
import android.os.Build
import com.stt.android.data.appversion.AppVersionRepository
import com.stt.android.di.VersionCode
import com.stt.android.di.VersionName
import com.stt.android.remote.BrandForAsko
import com.stt.android.remote.appversion.AppUpgradeInfo
import com.stt.android.remote.appversion.AppVersionInfo
import com.stt.android.remote.appversion.CheckAppVersionRemoteApi
import com.stt.android.utils.FlavorUtils
import javax.inject.Inject

class AppVersionRepositoryImpl @Inject constructor(
    val application: Application,
    private val appVersionRemoteApi: CheckAppVersionRemoteApi,
    @VersionCode private val currentVersion: Int,
    @VersionName private val appVersionName: String,
    @BrandForAsko private val brandType: String
) : AppVersionRepository {

    override suspend fun checkAppVersion(): AppUpgradeInfo? {
        val chinaFlavor = FlavorUtils.isSuuntoAppChina
        // global version will return google play link only, no need for phone brand and package name
        val phoneBrand = if (chinaFlavor) {
            Build.BRAND.lowercase()
        } else "ANDROID"
        val packageName = if (chinaFlavor) {
            AppStorePackageUtils.getAppStorePkgByPhoneBrand(application, phoneBrand)
        } else null
        return appVersionRemoteApi.checkAppVersion(
            appVersionName,
            currentVersion,
            Build.VERSION.SDK_INT.toString(),
            phoneBrand,
            packageName,
            brandType.uppercase(),
        )
    }

    override suspend fun getVersionList(
        pageNum: Int,
        pageSize: Int,
    ): List<AppVersionInfo>? {
        return appVersionRemoteApi.getVersionList(pageNum, pageSize, brandType.uppercase())
    }

    override suspend fun getVersionNotes(
        innerAppVersionCode: Int,
    ): String? {
        return appVersionRemoteApi.getVersionNotes(innerAppVersionCode, brandType.uppercase())
    }
}
