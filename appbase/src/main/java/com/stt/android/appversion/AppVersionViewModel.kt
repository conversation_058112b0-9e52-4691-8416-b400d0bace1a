package com.stt.android.appversion

import androidx.lifecycle.LiveData
import androidx.lifecycle.viewModelScope
import com.stt.android.common.coroutines.CoroutineViewModel
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.data.appversion.AppVersionRepository
import com.stt.android.network.interfaces.ANetworkProvider
import com.stt.android.remote.appversion.AppUpgradeInfo
import com.stt.android.ui.utils.SingleLiveEvent
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class AppVersionViewModel @Inject constructor(
    private val appVersionRepository: AppVersionRepository,
    coroutinesDispatchers: CoroutinesDispatchers
) : CoroutineViewModel(coroutinesDispatchers) {

    val newVersionLive: LiveData<AppUpgradeInfo?>
        get() = _newVersionEvent
    private val _newVersionEvent = SingleLiveEvent<AppUpgradeInfo?>()

    private var isAppUpdateCheckSuccess: Boolean = false

    fun checkAppVersion() {
        if (!ANetworkProvider.isOnline()) {
            Timber.d("checkAppVersion network is not online")
            return
        }
        viewModelScope.launch(Dispatchers.IO) {
            runCatching {
                val appUpgradeInfo = appVersionRepository.checkAppVersion()
                isAppUpdateCheckSuccess = true
                _newVersionEvent.postValue(appUpgradeInfo)
            }.onFailure {
                Timber.w(it, "checkAppVersion failure")
                isAppUpdateCheckSuccess = false
                _newVersionEvent.postValue(null)
            }
        }
    }

    fun checkAppUpdateIfNeeded() {
        if (!isAppUpdateCheckSuccess) {
            checkAppVersion()
        }
    }

    fun hasAppUpdateAvailable(): Boolean {
        return _newVersionEvent.value != null
    }

    override fun onCleared() {
        super.onCleared()
        isAppUpdateCheckSuccess = false
        _newVersionEvent.value = null
    }
}
