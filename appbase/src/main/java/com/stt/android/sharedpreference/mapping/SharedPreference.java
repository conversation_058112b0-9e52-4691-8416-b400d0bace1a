
package com.stt.android.sharedpreference.mapping;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.lang.reflect.Type;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface SharedPreference {
    /**
     * The name of the shared preference to store.
     */
    String value();

    /**
     * Whether the shared preference should be stored as {@link String}
     * representation of its value.
     */
    boolean storeAsString() default false;

    String defaultValue();

    /**
     * The type of keys used in a Map field
     */
    Class<?> mapKeyType() default void.class;

    /**
     * The type of values used in a Map field
     */
    Class<?> mapValueType() default void.class;

    /**
     * The type of values used in a Json object field
     */
    Class<?> jsonObjectType() default void.class;
}
