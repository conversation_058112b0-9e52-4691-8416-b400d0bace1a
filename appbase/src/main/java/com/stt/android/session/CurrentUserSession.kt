package com.stt.android.session

import com.stt.android.controllers.CurrentUserController
import com.stt.android.data.session.CurrentUser
import javax.inject.Inject

class CurrentUserSession @Inject constructor(
    private val currentUserController: CurrentUserController,
) : CurrentUser {
    override suspend fun getUsername(): String = currentUserController.username

    override suspend fun isLoggedIn(): Boolean = currentUserController.isLoggedIn

    override suspend fun isFieldTester(): Boolean = currentUserController.isFieldTester
}
