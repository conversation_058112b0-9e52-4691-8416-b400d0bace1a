package com.stt.android.exceptions

import timber.log.Timber
import java.lang.Thread.UncaughtExceptionHandler

/**
 * This is always last resort. We include here only the exceptions that we cannot handle ourselves
 * and would otherwise crash our app. There are usually device/OS- specific bugs or bugs in 3rd
 * party software.
 */
class STTUncaughtExceptionHandler(
    private val defaultUncaughtExceptionHandler: UncaughtExceptionHandler?
) : UncaughtExceptionHandler {

    override fun uncaughtException(t: Thread, e: Throwable) {
        if (shouldAbsorb(e)) {
            return
        } else {
            defaultUncaughtExceptionHandler?.uncaughtException(t, e) ?: throw e
        }
    }

    private fun shouldAbsorb(e: Throwable): <PERSON><PERSON><PERSON> {
        // catching this internal media library error until we migrate to media3
        // https://console.firebase.google.com/u/0/project/suunto-app/crashlytics/app/android:com.stt.android.suunto/issues/b9d2d2decf5da2d964ba38bc9be43585?time=last-seven-days&versions=4.54.2%20(4054002)&types=crash&sessionEventKey=62844E7402A600011694C6F7FA6A5670_1677381493705333381
        if (e::class.simpleName == "SecurityException" && e.stackTrace.any {
                it.className?.contains("IMediaSession") == true &&
                    it.methodName?.contains("unregisterCallbackListener") == true
            }
        ) {
            Timber.w(e, "Absorbing media3 session internal error SecurityException")
            return true
        }

        // absorbing Pixel bug with Android 13 https://issuetracker.google.com/issues/245258072
        if (e::class.simpleName == "CannotDeliverBroadcastException") {
            Timber.w(e, "Absorbing CannotDeliverBroadcastException, Pixel crash with Android 13")
            return true
        }

        return false
    }
}
