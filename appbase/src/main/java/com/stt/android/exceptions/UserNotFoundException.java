
package com.stt.android.exceptions;

/**
 * This class represents when the backend can't found a valid user in his
 * database.
 */
public class UserNotFoundException extends BackendException {
    private static final long serialVersionUID = 1L;

    public UserNotFoundException() {
        super("A valid user could not be found");
    }

    public UserNotFoundException(String message) {
        super(message);
    }

}
