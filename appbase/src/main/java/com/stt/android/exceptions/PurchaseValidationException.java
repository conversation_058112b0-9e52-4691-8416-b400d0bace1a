package com.stt.android.exceptions;

import androidx.annotation.Nullable;
import com.stt.android.domain.STTErrorCodes;

/**
 * Exception created when the backend can't validate the purchase or the purchase is not valid.
 */
public class PurchaseValidationException extends BackendException {
    public static final String MAY_RETRY_FIELD = "mayretry";
    public static final String SUBSCRIBED_USER_EMAIL = "subscribeduseremail";

    private final boolean retry;

    @Nullable
    private final String subscribedUserEmail;

    public PurchaseValidationException(String detailMessage, boolean retry) {
        super(detailMessage);
        this.retry = retry;
        this.subscribedUserEmail = null;
    }

    public PurchaseValidationException(STTErrorCodes error, boolean retry) {
        super(error);
        this.retry = retry;
        this.subscribedUserEmail = null;
    }

    public PurchaseValidationException(STTErrorCodes error, boolean retry, String subscribedUserEmail) {
        super(error);
        this.retry = retry;
        this.subscribedUserEmail = subscribedUserEmail;
    }

    public boolean shouldRetry() {
        return retry;
    }

    @Nullable
    public String getSubscribedUserEmail() {
        return subscribedUserEmail;
    }

    @Override
    public String toString() {
        return super.toString() + ". Should retry: " + retry;
    }
}
