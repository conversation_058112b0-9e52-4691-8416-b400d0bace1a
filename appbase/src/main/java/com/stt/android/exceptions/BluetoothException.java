package com.stt.android.exceptions;

/**
 * Represents an exception while accessing Bluetooth device.
 */
public class BluetoothException extends Exception {
    private static final long serialVersionUID = 1L;

    /**
     * Constructs a new {@link BluetoothException} with its stack trace and
     * detail message filled in.
     * 
     * @param detailMessage the detail message for this exception.
     */
    public BluetoothException(String detailMessage) {
        super(detailMessage);
    }

    /**
     * Constructs a new {@link BluetoothException} with detail message and cause
     * filled in.
     * 
     * @param detailMessage The detail message for the exception.
     * @param cause The detail cause for the exception.
     */
    public BluetoothException(String detailMessage, Throwable cause) {
        super(detailMessage, cause);
    }
}
