package com.stt.android.exceptions;

import android.content.res.Resources;

import com.stt.android.domain.STTErrorCodes;

/**
 * Represents an exception while communicating with the backend or reading data returned.
 */
public class BackendException extends Exception {
    private static final long serialVersionUID = 1L;
    private final STTErrorCodes error;

    /**
     * Constructs a new {@code BackendException} with its stack trace and detail
     * message filled in.
     *
     * @param detailMessage the detail message for this exception.
     */
    public BackendException(String detailMessage) {
        super(detailMessage);
        this.error = STTErrorCodes.UNKNOWN;
    }

    /**
     * Constructs a new instance of this class with detail message and cause
     * filled in.
     *
     * @param detailMessage The detail message for the exception.
     * @param cause         The detail cause for the exception.
     */
    public BackendException(String detailMessage, Throwable cause) {
        super(detailMessage, cause);
        this.error = STTErrorCodes.UNKNOWN;
    }

    public BackendException(STTErrorCodes error) {
        super(error.getDescription());
        this.error = error;
    }

    public BackendException(STTErrorCodes error, Throwable cause) {
        super(error.getDescription(), cause);
        this.error = error;
    }

    public STTErrorCodes getError() {
        return error;
    }

    public String getLocalizedMessage(Resources res, String packageName) {
        return error.getLocalizedMessage(res, packageName);
    }
}
