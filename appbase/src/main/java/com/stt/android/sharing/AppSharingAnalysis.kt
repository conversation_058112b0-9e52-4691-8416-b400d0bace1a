package com.stt.android.sharing

import android.content.SharedPreferences
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.MessageSource
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.locationinfo.FetchLocationInfoUseCase
import com.stt.android.workouts.sharepreview.customshare.ShareTarget
import timber.log.Timber

abstract class AppSharingAnalysis(
    private val fetchLocationInfoUseCase: FetchLocationInfoUseCase,
    private val sharedPreferences: SharedPreferences,
) {
    private val analysisPublicProperties = hashMapOf<String, Any>()

    suspend fun init(source: MessageSource) {
        analysisPublicProperties[AnalyticsEventProperty.MESSAGE_SOURCE] =
            getMessageSourceValue(source)
       // TODO add watch mode name
        analysisPublicProperties[AnalyticsEventProperty.CITY] = runSuspendCatching {
            fetchLocationInfoUseCase.fetchLocationInfo()?.city
        }.onFailure {
            Timber.w(it, "get city failed")
        }.getOrNull().orEmpty()
    }

    abstract fun sendAnalysisForShareMedia(
        shareTarget: ShareTarget,
        shareVideo: Boolean,
        shareSourceName: String,
        imageCount: Int,
        imageIndexes: List<Int>,
    )

    abstract fun senAnalysisForShareLink(
        shareTarget: ShareTarget,
        shareSourceName: String
    )

    abstract fun geSharingMediaAnalysis(
        shareSourceName: String,
        shareVideo: Boolean,
        imageCount: Int,
        imageIndexes: List<Int>
    ): Map<String, Any>

    abstract fun geSharingLinkAnalysis(
        shareSourceName: String
    ): Map<String, Any>

    private fun getMessageSourceValue(messageSource: MessageSource): String {
        return when (messageSource) {
            MessageSource.EMAIL -> AnalyticsPropertyValue.MessageSource.EMAIL
            MessageSource.INBOX -> AnalyticsPropertyValue.MessageSource.INBOX
            MessageSource.PUSH -> AnalyticsPropertyValue.MessageSource.PUSH
            MessageSource.POPUP -> AnalyticsPropertyValue.MessageSource.POPUP
            MessageSource.INLINE -> AnalyticsPropertyValue.MessageSource.INLINE
            MessageSource.BANNER -> AnalyticsPropertyValue.MessageSource.BANNER
        }
    }
}
