/* Copyright (c) 2012 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.stt.android.billing;

import android.text.TextUtils;
import androidx.annotation.NonNull;
import androidx.core.text.TextUtilsCompat;
import java.io.Serializable;
import org.json.JSONException;
import org.json.JSONObject;
import timber.log.Timber;

/**
 * Represents an in-app billing purchase.
 *
 * TODO: remove this class when possible since we're now using Play Billing Library. Currently this
 * class is serialized to the database via [PendingPurchase] class so database migration is needed.
 */
/*
You can create an ACTIVE test purchase for testuser using:
Purchase purchase = new Purchase(InAppBillingHelper.ITEM_TYPE_SUBS, "{\n" +
                        "  \"orderId\": \"someOrderId\",\n" +
                        "  \"packageName\": \"com.stt.android\",\n" +
                        "  \"productId\": \"one_month_subscription_test\",\n" +
                        "  \"purchaseTime\": \"1394808643000\",\n" +
                        "  \"purchaseState\": 0,\n" +
                        "  \"developerPayload\": \"ACTIVE:testuser\",\n" +
                        "  \"purchaseToken\": \"very long token\"\n" +
                        "}", null);
 */
public class Purchase implements Serializable {
    private static final long serialVersionUID = 5646966248336882710L;
    /**
     * See {@link com.stt.android.billing.InAppBillingHelper#ITEM_TYPE_INAPP} and
     * {@link com.stt.android.billing.InAppBillingHelper#ITEM_TYPE_INAPP}
     */
    private final String mItemType;
    /**
     * String containing the signature of the purchase data that was signed with the private key of the developer.
     * The data signature uses the RSASSA-PKCS1-v1_5 scheme.
     */
    private final String mSignature;
    /**
     * A String in JSON format that contains details about the purchase order.
     */
    private final String mOriginalJson;
    /**
     * A unique order identifier for the transaction. This corresponds to the Google Wallet Order ID.
     */
    private final String mOrderId;
    /**
     * The application package from which the purchase originated.
     */
    private final String mPackageName;
    /**
     * The item's product identifier.
     */
    private final String mSku;
    /**
     * The time the product was purchased, in milliseconds since the epoch (Jan 1, 1970).
     */
    private final long mPurchaseTime;
    /**
     * The purchase state of the order. Possible values are 0 (purchased), 1 (canceled), or 2 (refunded).
     */
    private final int mPurchaseState;
    /**
     * A developer-specified string that contains supplemental information about an order. In our case it also
     * contains the subscription type (see
     * {@link InAppBillingHelper#verifyAndGetSubscriptionType(com.stt.android.domain.user.User, Purchase)})
     */
    private final String mDeveloperPayload;
    /**
     * A token that uniquely identifies a purchase for a given item and user pair.
     */
    private final String mToken;

    public Purchase(String itemType, String signature, String orderId, String packageName, String
        sku, long purchaseTime, int purchaseState, String developerPayload, String token, String
        jsonPurchaseInfo) {
        this.mItemType = itemType;
        this.mSignature = signature;
        this.mOrderId = orderId;
        this.mPackageName = packageName;
        this.mSku = sku;
        this.mPurchaseTime = purchaseTime;
        this.mPurchaseState = purchaseState;
        // Seems developer payload can be missing, so fall back to get from the JSON.
        // https://console.firebase.google.com/u/0/project/api-project-1014081224822/crashlytics/app/android:com.stt.android/issues/4887f310b2b4bcb0c31695b370e590f6?time=last-seven-days&types=error&versions=4.106.4%20(4106004)&sessionEventKey=6783B0C303C4000162CB29A0204A1FFF_2037451124447003970
        this.mDeveloperPayload = !TextUtils.isEmpty(developerPayload) ? developerPayload : getDeveloperPayload(jsonPurchaseInfo);
        this.mToken = token;
        this.mOriginalJson = jsonPurchaseInfo;
    }

    public String getItemType() {
        return mItemType;
    }

    public String getOrderId() {
        return mOrderId;
    }

    public String getPackageName() {
        return mPackageName;
    }

    public String getSku() {
        return mSku;
    }

    public long getPurchaseTime() {
        return mPurchaseTime;
    }

    public int getPurchaseState() {
        return mPurchaseState;
    }

    public String getDeveloperPayload() {
        return mDeveloperPayload;
    }

    public String getToken() {
        return mToken;
    }

    public String getOriginalJson() {
        return mOriginalJson;
    }

    public String getSignature() {
        return mSignature;
    }

    @NonNull
    @Override
    public String toString() {
        return "PurchaseInfo(type:" + mItemType + "):" + mOriginalJson;
    }

    private static String getDeveloperPayload(String jsonPurchaseInfo) {
        try {
            JSONObject purchaseInfo = new JSONObject(jsonPurchaseInfo);
            String developerPayload = purchaseInfo.optString("developerPayload");
            if (!TextUtils.isEmpty(developerPayload)) {
                return developerPayload;
            }

            // Starting from Play Billing library v3, we use obfuscatedAccountId, because developer
            // payload is deprecated.
            String obfuscatedAccountId = purchaseInfo.optString("obfuscatedAccountId");
            if (!TextUtils.isEmpty(obfuscatedAccountId)) {
                return obfuscatedAccountId;
            }
        } catch (JSONException e) {
            Timber.w(e, "Failed to parse purchase info JSON: %s", jsonPurchaseInfo);
        }

        return null;
    }
}
