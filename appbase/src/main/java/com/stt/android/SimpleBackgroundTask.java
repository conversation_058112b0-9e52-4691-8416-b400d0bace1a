package com.stt.android;

import android.os.AsyncTask;

import java.util.concurrent.RejectedExecutionException;

import timber.log.Timber;

/**
 * Class that replaces {@link AsyncTask} and provides more friendly callbacks to handle the outcome.
 * Tasks executed using this class will be run in parallel if possible.
 * <p/>
 * You must implement {@link #call()} and probably override {@link #onSuccess(Object)} and
 * {@link #onException(Exception)}
 *
 * @param <Result>
 */
public abstract class SimpleBackgroundTask<Result> extends AsyncTask<Void, Void, Result> {
    private Exception exception;

    /**
     * @deprecated please do your background task in {@link #call()} instead.
     */
    @Override
    @Deprecated
    protected Result doInBackground(Void... params) {
        try {
            return call();
        } catch (Exception e) {
            this.exception = e;
            return null;
        }
    }

    /**
     * @deprecated you must implement {@link #onSuccess(Object)} or {@link #onException(Exception)}
     */
    @Override
    @Deprecated
    protected void onPostExecute(Result result) {
        try {
            if (exception != null) {
                onException(exception);
            } else {
                onSuccess(result);
            }
        } finally {
            onFinally();
        }
    }

    public final void execute() {
        try {
            // Let's try first to run it using a thread pool
            executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR);
            return;
        } catch (RejectedExecutionException e) {
            Timber.e(e, "Rejected to execute task %s", this);
        }
        // We couldn't run the task from the thread pool so let's try directly
        super.execute();
    }

    /**
     * Called once {@link #call()} has been executed successfully (i.e. without exceptions).
     * By default it does nothing.
     *
     * @param result the outcome of {@link #call()}
     */
    protected void onSuccess(Result result) {

    }

    /**
     * Called if {@link #call()} throws an exception.
     * By default it prints the exception using {@link Timber} as an error.
     *
     * @param exception the exception thrown in {@link #call()}
     */
    protected void onException(Exception exception) {
        Timber.e(exception, "Simple Async Task failed");
    }

    protected void onFinally() {

    }

    protected abstract Result call() throws Exception;
}
