
package com.stt.android.bluetooth;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothSocket;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import timber.log.Timber;

public class Hrm2ConnectThread extends ConnectThread {

    public Hrm2ConnectThread(BluetoothAdapter bluetoothAdapter, BluetoothDevice device, ConnectHrListener listener) {
        super(bluetoothAdapter, device, listener);
    }

    /**
     * Uses reflection to create a BT socket and read from it.
     * 
     * @return a socket if successfully opened and read the stream
     */
    @SuppressLint("MissingPermission")
    @Override
    protected BluetoothSocket connectSocketReflection() {
        BluetoothSocket tmpSocket = null;
        InputStream in = null;
        try {
            Timber.d("Third attempt, using reflection to call device.createInsecureRfcommSocket(1)");
            Method m = device.getClass().getMethod("createInsecureRfcommSocket", new Class[] {
                    int.class
            });
            tmpSocket = (BluetoothSocket) m.invoke(device, Integer.valueOf(1));
            tmpSocket.connect();
            in = tmpSocket.getInputStream();
            in.read();
            return tmpSocket;
        } catch (IllegalArgumentException | InvocationTargetException | IllegalAccessException e) {
            Timber.w(e, "Error while using reflection to connect");
        } catch (NoSuchMethodException e) {
            Timber.w(e, "Method 'createInsecureRfcommSocket' not found");
        } catch (IOException e) {
            Timber.w(e, "Error while attempting to connect");
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e2) {
                }
            }
            if (tmpSocket != null) {
                try {
                    Timber.d("Closing temporary Bluetooth socket");
                    tmpSocket.close();
                } catch (IOException e1) {
                }
            }
        } catch (RuntimeException e) {
            Timber.w(e, "Unknown error");
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e2) {
                }
            }
            if (tmpSocket != null) {
                try {
                    Timber.d("Closing temporary Bluetooth socket");
                    tmpSocket.close();
                } catch (IOException e1) {
                }
            }
            throw e;
        }
        return null;
    }

    /**
     * Uses the standard BT API to fetch the remote device, then create a BT
     * socket and read from it.
     * 
     * @return a socket if successfully opened and read the stream
     */
	@SuppressLint("MissingPermission")
    @Override
    protected BluetoothSocket connectSocketRemoteDevice() {
        BluetoothSocket tmpSocket = null;
        InputStream in = null;
        try {
            Timber.d("Second attempt, calling BluetoothAdapter.getRemoteDevice(device.getAddress()).createRfcommSocketToServiceRecord(uuid)");
            tmpSocket = bluetoothAdapter.getRemoteDevice(device.getAddress())
                    .createInsecureRfcommSocketToServiceRecord(uuid);
            tmpSocket.connect();
            in = tmpSocket.getInputStream();
            in.read();
            return tmpSocket;
        } catch (IOException e) {
            Timber.w(e, "Unable to read from BT");
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e2) {
                }
            }
            if (tmpSocket != null) {
                try {
                    Timber.d("Closing temporary Bluetooth socket");
                    tmpSocket.close();
                } catch (IOException e1) {
                }
            }
        } catch (RuntimeException e) {
            Timber.w(e, "Unknown error");
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e2) {
                }
            }
            if (tmpSocket != null) {
                try {
                    Timber.d("Closing temporary Bluetooth socket");
                    tmpSocket.close();
                } catch (IOException e1) {
                }
            }
            throw e;
        }
        return null;
    }

    /**
     * Uses the standard BT API to open a BT socket and read from it.
     * 
     * @return a socket if successfully opened and read the stream
     */
    @SuppressLint("MissingPermission")
    @Override
    protected BluetoothSocket connectSocketDefaultAPI() {
        BluetoothSocket tmpSocket = null;
        InputStream in = null;
        try {
            Timber.d("First attempt, calling device.createRfcommSocketToServiceRecord(uuid)");
            tmpSocket = device.createInsecureRfcommSocketToServiceRecord(uuid);
            tmpSocket.connect();
            in = tmpSocket.getInputStream();
            in.read();
            return tmpSocket;
        } catch (IOException e) {
            Timber.w(e, "Unable to read from BT");
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e2) {
                }
            }
            if (tmpSocket != null) {
                try {
                    Timber.d("Closing temporary Bluetooth socket");
                    tmpSocket.close();
                } catch (IOException e1) {
                }
            }
        } catch (RuntimeException e) {
            Timber.w(e, "Unknown error");
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e2) {
                }
            }
            if (tmpSocket != null) {
                try {
                    Timber.d("Closing temporary Bluetooth socket");
                    tmpSocket.close();
                } catch (IOException e1) {
                }
            }
            throw e;
        }
        return null;
    }
}
