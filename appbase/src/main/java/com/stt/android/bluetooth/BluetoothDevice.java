package com.stt.android.bluetooth;

import java.util.Locale;

/**
 * Basic information for a bluetooth device.
 */
public class BluetoothDevice {
    private final String name;
    private final String address;

    public BluetoothDevice(String name, String address) {
        this.name = name;
        this.address = address;
    }

    /**
     * @return the bluetooth device name
     */
    public String getName() {
        return name;
    }

    /**
     * @return the bluetooth device hardware address
     */
    public String getAddress() {
        return address;
    }

    @Override
    public String toString() {
        return String.format(Locale.US, "[name = %s, address = %s]", name, address);
    }
}
