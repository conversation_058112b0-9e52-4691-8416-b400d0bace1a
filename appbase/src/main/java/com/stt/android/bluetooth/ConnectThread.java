
package com.stt.android.bluetooth;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothSocket;
import com.stt.android.exceptions.BluetoothException;
import java.io.IOException;
import java.util.UUID;
import java.util.concurrent.CancellationException;
import timber.log.Timber;

public abstract class ConnectThread implements Runnable {

    public interface ConnectHrListener {
        /**
         * Called once we have an open socket to ST HRM
         * 
         * @param socket to communicate with the ST HRM
         */
        void onHrConnected(BluetoothSocket socket);

        /**
         * Called if there's some error while trying to connect to the ST HRM
         * 
         * @param e the exception causing the error
         */
        void onHrConnectError(Throwable e);
    }

    /**
     * Common UUID used to communicate with ST HRM
     */
    protected static final UUID uuid = UUID.fromString("00001101-0000-1000-8000-00805f9b34fb");
    protected BluetoothAdapter bluetoothAdapter;
    private final ConnectHrListener listener;
    protected BluetoothDevice device;
    private volatile boolean cancelled = false;
    private volatile boolean connecting = false;

    public ConnectThread(BluetoothAdapter bluetoothAdapter, BluetoothDevice device,
            ConnectHrListener listener) {
        this.bluetoothAdapter = bluetoothAdapter;
        this.listener = listener;
        this.device = device;
    }

    /**
     * Attempts to connect to the given BluetoothDevice. In theory calling
     * {@link BluetoothDevice#createRfcommSocketToServiceRecord(UUID)} should
     * work but in practice we need to play safe and try three different
     * approaches until we succeed to establish a connection with the device.
     */
    @SuppressLint("MissingPermission")
    @Override
    public void run() {
        connecting = true;
        // Cancel discovery because it will slow down the connection
        bluetoothAdapter.cancelDiscovery();
        BluetoothSocket socket = null;
        try {
            if (cancelled) {
                return;
            }

            // First attempt, using standard API
            socket = connectSocketDefaultAPI();

            if (cancelled) {
                throw new CancellationException();
            }

            if (socket == null) {
                // Second attempt, using standard API but first get the device
                socket = connectSocketRemoteDevice();
            }

            if (cancelled) {
                throw new CancellationException();
            }

            if (socket == null) {
                // Third attempt, use reflection
                socket = connectSocketReflection();
            }

            if (cancelled) {
                throw new CancellationException();
            }

            if (socket == null) {
                Timber.e("Unable to get a proper socket connection");
                notifyErrorToListener(new BluetoothException("Unable to create a socket"));
            } else {
                if (listener != null) {
                    listener.onHrConnected(socket);
                }
            }
        } catch (CancellationException e) {
            if (socket != null) {
                Timber.d("Closing Bluetooth socket");
                try {
                    socket.close();
                } catch (IOException ignored) {
                }
            }
        } catch (Exception e) {
            notifyErrorToListener(new BluetoothException("Unexpected exception while connecting to device", e));

            if (socket != null) {
                try {
                    Timber.d("Closing Bluetooth socket");
                    socket.close();
                } catch (IOException ignored) {
                }
            }
        } finally {
            connecting = false;
        }
    }

    /**
     * Uses reflection to create a BT socket and read from it.
     * 
     * @return a socket if successfully opened and read the stream
     */
    protected abstract BluetoothSocket connectSocketReflection();

    /**
     * Uses the standard BT API to fetch the remote device, then create a BT
     * socket and read from it.
     * 
     * @return a socket if successfully opened and read the stream
     */
    protected abstract BluetoothSocket connectSocketRemoteDevice();

    /**
     * Uses the standard BT API to open a BT socket and read from it.
     * 
     * @return a socket if successfully opened and read the stream
     */
    protected abstract BluetoothSocket connectSocketDefaultAPI();

    public void notifyErrorToListener(Throwable error) {
        if (listener != null) {
            listener.onHrConnectError(error);
        }
    }

    public void cancel() {
        cancelled = true;
    }

    public boolean isConnecting() {
        return connecting;
    }
}
