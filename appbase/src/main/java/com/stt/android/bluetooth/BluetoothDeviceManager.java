package com.stt.android.bluetooth;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import androidx.core.content.ContextCompat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import timber.log.Timber;

public abstract class BluetoothDeviceManager {
    public static BluetoothAdapter getBluetoothAdapter(Context context) {
        return ((BluetoothManager) context.getSystemService(Context.BLUETOOTH_SERVICE)).getAdapter();
    }

    private final BroadcastReceiver discoveryReceiver = new BroadcastReceiver() {
        @SuppressLint("MissingPermission")
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (BluetoothDevice.ACTION_FOUND.equals(action)) {
                BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                Timber.d("Bluetooth device found: address=%s, name=%s", device.getAddress(), device.getName());
                if (isSupportedDevice(device)) {
                    hasFoundDevices = true;

                    stopDiscovery();
                    onSupportedDeviceFound(device);
                } else {
                    discoveredDevices.add(device);
                }
            } else if (BluetoothAdapter.ACTION_DISCOVERY_FINISHED.equals(action)) {
                unregisterDiscoveryReceiver();

                if (!hasFoundDevices) {
                    // we still need to check if any of the discovered devices is supported,
                    // because sometimes we can't retrieve the device name when we discover it
                    if (discoveredDevices.size() > 0) {
                        for (BluetoothDevice device : discoveredDevices) {
                            if (isSupportedDevice(device)) {
                                onSupportedDeviceFound(device);
                                return;
                            }
                        }
                    }
                    for (BluetoothDiscoveryListener listener : listeners) {
                        listener.onNoDeviceFound();
                    }
                }
            }
        }

        private void onSupportedDeviceFound(BluetoothDevice device) {
            for (BluetoothDiscoveryListener listener : listeners) {
                listener.onDeviceFound(device);
            }
        }
    };

    private final List<BluetoothDiscoveryListener> listeners = Collections.synchronizedList(new ArrayList<BluetoothDiscoveryListener>());

    protected Context context;
    private BluetoothAdapter bluetoothAdapter;

    private final List<BluetoothDevice> discoveredDevices = new ArrayList<>();
    private boolean hasFoundDevices;

    public void addListener(BluetoothDiscoveryListener listener) {
        if (!listeners.contains(listener)) {
            listeners.add(listener);
        }
    }

    public void removeListener(BluetoothDiscoveryListener listener) {
        listeners.remove(listener);
    }

    @SuppressLint("MissingPermission")
    public void startDiscovery(Context context) {
        if (bluetoothAdapter == null) {
            bluetoothAdapter = getBluetoothAdapter(context);
        }
        if (bluetoothAdapter == null || !bluetoothAdapter.isEnabled()) {
            for (BluetoothDiscoveryListener listener : listeners) {
                listener.onNoConnection();
            }
            return;
        }
        this.context = context.getApplicationContext();

        stopDiscovery();

        discoveredDevices.clear();
        hasFoundDevices = false;

        registerDiscoveryReceiver();
        bluetoothAdapter.startDiscovery();
    }

    private void registerDiscoveryReceiver() {
        ContextCompat.registerReceiver(
            context,
            discoveryReceiver,
            new IntentFilter(BluetoothDevice.ACTION_FOUND),
            ContextCompat.RECEIVER_EXPORTED
        );
        ContextCompat.registerReceiver(
            context,
            discoveryReceiver,
            new IntentFilter(BluetoothAdapter.ACTION_DISCOVERY_FINISHED),
            ContextCompat.RECEIVER_EXPORTED
        );
    }

    @SuppressLint("MissingPermission")
    public void stopDiscovery() {
        if (bluetoothAdapter != null) {
            unregisterDiscoveryReceiver();
            bluetoothAdapter.cancelDiscovery();
        }
    }

    private void unregisterDiscoveryReceiver() {
        try {
            // context might be null, if Bluetooth was not turned on
            if (context != null) {
                context.unregisterReceiver(discoveryReceiver);
            }
        } catch (IllegalArgumentException ignored) {
            // not started, or already unregistered
        }
    }

    protected abstract boolean isSupportedDevice(BluetoothDevice device);
}
