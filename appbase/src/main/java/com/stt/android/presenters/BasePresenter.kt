package com.stt.android.presenters

import androidx.annotation.CallSuper
import com.stt.android.views.MVPView
import io.reactivex.disposables.CompositeDisposable

/**
 * Inspired by https://goo.gl/WEIwVc. Use with RxJava2.
 */
abstract class BasePresenter<V : MVPView> {

    val disposable = CompositeDisposable()

    /**
     * @return the currently attached view or null if none is attached.
     */
    protected var view: V? = null

    /**
     * Called to give this presenter control of a view.
     *
     *
     * It is expected that [.dropView] will be called when the view is no longer active.
     */
    fun takeView(view: V) {
        this.view = view
        onViewTaken()
    }

    /**
     * Called once the view has been attached so it's safe to call methods in the view.
     * Subclasses can override it to perform tasks once the view is available (e.g. restore some
     * state, start som tasks...)
     */
    @CallSuper
    protected open fun onViewTaken() {
    }

    /**
     * Called to surrender control of this view, e.g. when the view is detached.
     */
    fun dropView() {
        this.view = null
        onViewDropped()
    }

    /**
     * Called once the view has been detached so it's not safe to call methods in the view (i.e.
     * the view is not visible any more).
     * Subclasses can override it to perform tasks (e.g. restore save some state, cancel tasks...)
     */
    @CallSuper
    protected open fun onViewDropped() {
        disposable.clear()
    }
}
