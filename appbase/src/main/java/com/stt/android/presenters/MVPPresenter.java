package com.stt.android.presenters;

import androidx.annotation.CallSuper;
import androidx.annotation.Nullable;
import com.stt.android.views.MVPView;
import rx.subscriptions.CompositeSubscription;

/**
 * Inspired by https://goo.gl/WEIwVc
 *
 * @deprecated use {@link BasePresenter} with RxJava2 disposable instead of RxJava1 subscription.
 */
@Deprecated
public abstract class MVPPresenter<V extends MVPView> {

    protected final CompositeSubscription subscription = new CompositeSubscription();
    @Nullable protected V view;

    /**
     * Called to give this presenter control of a view.
     * <p/>
     * It is expected that {@link #dropView()} will be called when the view is no longer active.
     */
    public final void takeView(V view) {
        this.view = view;
        onViewTaken();
    }

    /**
     * Called once the view has been attached so it's safe to call methods in the view.
     * Subclasses can override it to perform tasks once the view is available (e.g. restore some
     * state, start som tasks...)
     */
    @CallSuper
    protected void onViewTaken() {
    }

    /**
     * Called to surrender control of this view, e.g. when the view is detached.
     */
    public final void dropView() {
        this.view = null;
        onViewDropped();
    }

    /**
     * {@link #dropView()} is called only when the view is same.
     * @param view
     */
    public final void dropViewIfSame(V view) {
        if (this.view == view) {
            dropView();
        }
    }

    /**
     * Called once the view has been detached so it's not safe to call methods in the view (i.e.
     * the view is not visible any more).
     * Subclasses can override it to perform tasks (e.g. restore save some state, cancel tasks...)
     */
    @CallSuper
    protected void onViewDropped() {
        subscription.clear();
    }

    /**
     * @return the currently attached view or null if none is attached.
     */
    @Nullable
    protected final V getView() {
        return view;
    }




}
