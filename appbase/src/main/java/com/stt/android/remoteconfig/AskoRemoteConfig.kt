package com.stt.android.remoteconfig

import android.content.SharedPreferences
import android.content.res.Resources
import android.os.Build
import androidx.annotation.VisibleForTesting
import androidx.core.content.edit
import com.stt.android.R
import com.stt.android.TestOpen
import com.stt.android.controllers.UserSettingsController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.di.AskoRemoteConfigPreferences
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.di.VersionCode
import com.stt.android.remote.remoteconfig.ASKO_REMOTE_PRODUCT_SPORTSTRACKER
import com.stt.android.remote.remoteconfig.ASKO_REMOTE_PRODUCT_SUUNTO
import com.stt.android.remote.remoteconfig.AmplitudeEventSampling
import com.stt.android.remote.remoteconfig.AskoRemoteConfigApi
import com.stt.android.remote.remoteconfig.AskoRemoteConfigResponse
import com.stt.android.remote.remoteconfig.AskoRemoteConfigResponse.Companion.KEY_AI_PLANNER_ENABLED
import com.stt.android.remote.remoteconfig.AskoRemoteConfigResponse.Companion.KEY_AMPLITUDE_EVENT_SAMPLING
import com.stt.android.remote.remoteconfig.AskoRemoteConfigResponse.Companion.KEY_ANDROID_COMPANION_LINKING
import com.stt.android.remote.remoteconfig.AskoRemoteConfigResponse.Companion.KEY_ANDROID_COMPANION_LINKING_PARAMETERS
import com.stt.android.remote.remoteconfig.AskoRemoteConfigResponse.Companion.KEY_CUSTOMER_SUPPORT_CHAT_BOT_ENABLED
import com.stt.android.remote.remoteconfig.AskoRemoteConfigResponse.Companion.KEY_DELETE_ACCOUNT
import com.stt.android.remote.remoteconfig.AskoRemoteConfigResponse.Companion.KEY_EMARSYS_ENABLED
import com.stt.android.remote.remoteconfig.AskoRemoteConfigResponse.Companion.KEY_GRAPHHOPPER_URL
import com.stt.android.remote.remoteconfig.AskoRemoteConfigResponse.Companion.KEY_PARTNER_CONNECTIONS_ENABLED
import com.stt.android.remote.remoteconfig.AskoRemoteConfigResponse.Companion.KEY_SML_TO_BACKEND
import com.stt.android.remote.remoteconfig.AskoRemoteConfigResponse.Companion.KEY_ST_FUSED_LOCATION_PARAMETERS
import com.stt.android.remote.remoteconfig.AskoRemoteConfigValuesWithConditions
import com.stt.android.remote.remoteconfig.CompanionLinkingParameters
import com.stt.android.remote.remoteconfig.GraphhopperBaseUrl
import com.stt.android.remote.remoteconfig.STFusedLocationParameters
import com.stt.android.remote.remoteconfig.ValueConditions
import com.stt.android.utils.STTConstants
import io.reactivex.Completable
import kotlinx.coroutines.rx2.rxCompletable
import timber.log.Timber
import java.util.Locale
import java.util.Random
import javax.inject.Inject
import javax.inject.Singleton

/**
 * This class handles fetching remote config values from backend and providing corresponding values
 * for a requested [AskoRemoteConfigType].
 * Subscribe to [fetchConfig] to fetch remote config. It's loaded from cache if valid cached value
 * is found, otherwise it's fetched from backend and cached.
 * Any request from [getValue] will query the in-memory remote config json.
 * When remote config types are queried, the class tries to find the first value matching all the conditions listed.
 * If no matching values are found, or if remote config is missing altogether, [AskoRemoteConfigDefaults] class is
 * expected to provide default values for all supported types.
 * For examples on matching rules, check [AskoRemoteConfigTest] tests.
 */
@Singleton
@TestOpen
class AskoRemoteConfig
@Inject constructor(
    private val resources: Resources,
    @AskoRemoteConfigPreferences private val preferences: SharedPreferences,
    @SuuntoSharedPrefs private val suuntoPreferences: SharedPreferences,
    private val askoRemoteConfigApi: AskoRemoteConfigApi,
    private val askoRemoteConfigDefaults: AskoRemoteConfigDefaults,
    private val userSettingsController: UserSettingsController,
    @get:VisibleForTesting @VersionCode
    internal val currentVersion: Int
) {

    val partnerConnectionsEnabled: Boolean
        get() = requireValue(KEY_PARTNER_CONNECTIONS_ENABLED) {
            partnerConnectionsEnabled
        }

    val smlToBackend: Boolean
        get() = requireValue(KEY_SML_TO_BACKEND) {
            smlToBackend
        }

    val deleteAccount: Boolean
        get() = requireValue(KEY_DELETE_ACCOUNT) {
            deleteAccount
        }

    val stFusedLocationParameters: STFusedLocationParameters
        get() = requireValue(KEY_ST_FUSED_LOCATION_PARAMETERS) {
            stFusedLocationParameters
        }

    val companionLinking: Boolean
        get() = requireValue(KEY_ANDROID_COMPANION_LINKING) {
            companionLinking
        }

    val companionLinkingParameters: CompanionLinkingParameters
        get() = requireValue(KEY_ANDROID_COMPANION_LINKING_PARAMETERS) {
            companionLinkingParemeters
        }

    val emarsysEnabled: Boolean
        get() = requireValue(KEY_EMARSYS_ENABLED) {
            emarsysEnabled
        }

    val amplitudeEventSamplingConfig: List<AmplitudeEventSampling>
        get() = requireValue(KEY_AMPLITUDE_EVENT_SAMPLING) {
            amplitudeEventSamplingConfig
        }

    val graphhopperBaseUrl: GraphhopperBaseUrl
        get() = requireValue(KEY_GRAPHHOPPER_URL) {
            graphhopperBaseUrl
        }

    private val suuntoFlavorSpecific: Boolean by lazy {
        resources.getBoolean(R.bool.suuntoFlavorSpecific)
    }

    private val stFlavorSpecific: Boolean by lazy {
        resources.getBoolean(R.bool.sportsTrackerFlavorSpecific)
    }

    val isCustomerSupportChatBotEnabled: Boolean
        get() = requireValue(KEY_CUSTOMER_SUPPORT_CHAT_BOT_ENABLED) {
            androidCustomerSupportChatBotEnabled
        }

    val isAiPlannerEnabled: Boolean
        get() = requireValue(KEY_AI_PLANNER_ENABLED) {
            aiPlannerEnabled
        }

    // current runtime configuration response
    @Volatile
    @VisibleForTesting
    internal var askoRemoteConfigResponse: AskoRemoteConfigResponse? = null

    fun fetchConfig(): Completable = rxCompletable {
        runSuspendCatching {
            askoRemoteConfigResponse = askoRemoteConfigApi.fetchRemoteConfig()
        }.onFailure { e ->
            Timber.w(e, "Error getting remote config")
        }
    }

    private fun <T : Any> requireValue(
        key: String,
        getConditions: AskoRemoteConfigResponse.() -> AskoRemoteConfigValuesWithConditions<T>?
    ): T {
        return getValue(key, getConditions) ?: askoRemoteConfigDefaults.defaultFor(getConditions)
    }

    private fun <T : Any> getValue(
        key: String,
        getConditions: AskoRemoteConfigResponse.() -> AskoRemoteConfigValuesWithConditions<T>?
    ): T? = askoRemoteConfigResponse?.getConditions()
        ?.let {
            try {
                findMatchingCondition(key, it.values).value
            } catch (e: NoSuchElementException) {
                if (it.values.isNotEmpty()) {
                    Timber.w(e, "No matching condition found, returning last value")
                    it.values.last().value
                } else {
                    Timber.w(e, "No matching condition available, returning null")
                    null
                }
            }
        }

    /**
     * @throws [NoSuchElementException]
     */
    private fun <T> findMatchingCondition(
        key: String,
        valueConditions: List<ValueConditions<T>>
    ): ValueConditions<T> {
        return valueConditions.first {
            matchAppVersion(it.minAppVersion, it.maxAppVersion) &&
                matchOSVersion(Build.VERSION.SDK_INT, it.minOSVersion, it.maxOSVersion) &&
                matchProduct(it.product) &&
                matchPercentile(key, it.percentile) &&
                matchCountry(it.countries) &&
                matchWatchModelVersion(it.watchModels)
        }
    }

    @VisibleForTesting
    internal fun matchWatchModelVersion(watchModels: List<String>?): Boolean {
        if (watchModels == null) {
            return true
        }
        val watchModel = suuntoPreferences.getString(
            STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_MODEL,
            null
        )
        return watchModel != null &&
            watchModels.map { it.lowercase(Locale.US) }
                .contains(watchModel.lowercase(Locale.US))
    }

    @VisibleForTesting
    internal fun matchCountry(countries: List<String>?): Boolean {
        if (countries == null) {
            return true
        }
        val country = userSettingsController.settings.country
        return country.isNotBlank() &&
            countries.map { it.uppercase(Locale.US) }
                .contains(country.uppercase(Locale.US))
    }

    @VisibleForTesting
    internal fun matchPercentile(key: String, percentile: Float?): Boolean {
        // null or invalid percentile is always matching (ignored)
        if (percentile == null || percentile !in 0.0..1.0) {
            return true
        }
        val rollResultKey =
            "${STTConstants.AskoRemoteConfigPreferences.KEY_PERCENTILE_ROLL_RESULT}_$key"
        val rollResult = if (preferences.contains(rollResultKey)) {
            preferences.getFloat(rollResultKey, 1f)
        } else {
            // first time computing percentile, saving it to shared preferences
            rollPercentile().also {
                preferences.edit {
                    putFloat(rollResultKey, it)
                }
            }
        }
        return rollResult <= percentile
    }

    @VisibleForTesting
    internal fun matchOSVersion(
        sdkInt: Int,
        minOSVersion: String?,
        maxOSVersion: String?
    ): Boolean {
        return (minOSVersion == null && maxOSVersion == null) || try {
            (minOSVersion == null || sdkInt >= Integer.valueOf(minOSVersion)) &&
                (maxOSVersion == null || sdkInt <= Integer.valueOf(maxOSVersion))
        } catch (e: Exception) {
            Timber.w(e, "Error parsing os version number")
            false
        }
    }

    @VisibleForTesting
    internal fun matchAppVersion(minAppVersion: String?, maxAppVersion: String?): Boolean {
        return (minAppVersion == null && maxAppVersion == null) || try {
            return (minAppVersion == null || currentVersion >= minAppVersion.toInt()) &&
                (maxAppVersion == null || currentVersion <= maxAppVersion.toInt())
        } catch (e: Exception) {
            Timber.w(e, "Error parsing version numbers")
            false
        }
    }

    @VisibleForTesting
    internal fun rollPercentile(): Float = Random().nextFloat()

    private fun matchProduct(product: String?): Boolean {
        return product == null ||
            (suuntoFlavorSpecific && product == ASKO_REMOTE_PRODUCT_SUUNTO) ||
            (stFlavorSpecific && product == ASKO_REMOTE_PRODUCT_SPORTSTRACKER)
        // todo add atomic support when needed
    }
}
