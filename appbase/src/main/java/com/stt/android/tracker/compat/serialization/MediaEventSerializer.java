
package com.stt.android.tracker.compat.serialization;

import com.stt.android.tracker.event.LegacyMediaEvent;
import com.stt.android.tracker.event.LegacyMediaEvent.MediaType;

import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import java.util.Date;

public class MediaEventSerializer {

    public static LegacyMediaEvent read(DataInputStream stream) throws DeserializationFailedException {
        try {
            stream.readUnsignedByte();
            int id = stream.readInt();
            String key = stream.readUTF();
            int totalTime = stream.readInt();
            MediaType type = LegacyMediaEvent.MediaType.values()[stream.readUnsignedByte()];
            Date timeStamp = new Date((long) stream.readDouble());
            LegacyMediaEvent event = new LegacyMediaEvent(id, key, totalTime, timeStamp, type);

            event.setCoordinate(CoordinatesSerializer.read(stream));

            event.setTitle(stream.readUTF());
            event.setArtist(stream.readUTF());
            event.setAlbum(stream.readUTF());
            event.setDuration(stream.readInt());
            event.setImagePath(stream.readUTF());
            event.setVideoPath(stream.readUTF());
            event.setDescription(stream.readUTF());
            event.setBookmark(stream.readUTF());

            return event;
        } catch (IOException ioe) {
            throw new DeserializationFailedException(ioe);
        }


    }

    public static void write(DataOutputStream stream, LegacyMediaEvent event) throws SerializationFailedException {
        try {
            // header not used, write dummy.
            stream.write(0);
            stream.writeInt(event.getId());
            String key = event.getKey();
            stream.writeUTF(key == null ? "" : key);
            stream.writeInt((int) event.getTotalTime());
            stream.writeByte(event.getType().ordinal());
            stream.writeDouble(event.getTimeStamp().getTime());

            CoordinatesSerializer.write(stream, event.getCoordinate());

            String title = event.getTitle();
            stream.writeUTF(title == null ? "" : title);
            String artist = event.getArtist();
            stream.writeUTF(artist == null ? "" : artist);
            String album = event.getAlbum();
            stream.writeUTF(album == null ? "" : album);
            int duration = (int) event.getDuration();
            stream.writeInt(duration);
            String imagePath = event.getImagePath();
            stream.writeUTF(imagePath == null ? "" : imagePath);
            String videoPath = event.getVideoPath();
            stream.writeUTF(videoPath == null ? "" : videoPath);
            String description = event.getDescription();
            stream.writeUTF(description == null ? "" : description);
            String bookmark = event.getBookmark();
            stream.writeUTF(bookmark == null ? "" : bookmark);

        } catch (IOException ioe) {
            throw new SerializationFailedException(ioe);
        }
    }
}
