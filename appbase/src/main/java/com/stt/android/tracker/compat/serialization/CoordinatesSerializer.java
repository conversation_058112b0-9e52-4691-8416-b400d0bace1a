package com.stt.android.tracker.compat.serialization;

import com.stt.android.domain.workout.WorkoutGeoPoint;
import com.stt.android.utils.CoordinateUtils;

import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;

public class CoordinatesSerializer {

    /**
     * Timestamp in milliseconds for 2000.01.01 at 00:00:00 (UTC)
     */
    private static final long JANUARY_FIRST_2000_MILLISECONDS = 946684800000L;

    public static void write(DataOutputStream stream, WorkoutGeoPoint point) throws SerializationFailedException {
        try {
            // Header not used, write dummy.
            stream.writeByte(0);
            stream.writeUTF(generateLegacyKey(point.getTimestamp()));
            stream.writeInt(getLatitudeAsMinutes(point.getLatitude()));
            stream.writeInt(getLongitudeAsMinutes(point.getLongitude()));
            // internally as meters, in binary in centimeters.
            stream.writeInt((int) (point.getAltitude() * 100));
            // internally meters per second, in binary centimeters per second.
            stream.writeInt((int) (point.getSpeedMetersPerSecond() * 100));
            // internally should be degrees, add two decimal points for binary.
            stream.writeInt((int) (point.getCourse() * 100));
            // Fix quality
            stream.writeByte(0);
        } catch (IOException ioe) {
            throw new SerializationFailedException(ioe);
        }
    }

    private static String generateLegacyKey(long timestamp) {
        return String.valueOf((timestamp - JANUARY_FIRST_2000_MILLISECONDS) / 1000.0D);
    }

    public static WorkoutGeoPoint read(DataInputStream stream) throws DeserializationFailedException {
        try {
            // read header byte and throw it away.
            stream.readByte();
            // Ignore key
            stream.readUTF();
            int latitude = (int) (CoordinateUtils.getPositionAsDecimalDegrees(stream.readInt()) * 1E6);
            int longitude = (int) (CoordinateUtils.getPositionAsDecimalDegrees(stream.readInt()) * 1E6);
            double altitude = stream.readInt() / 100.0;
            float speedMetersPerSecond = stream.readInt() / 100.0f;
            float course = stream.readInt() / 100.0f;
            // Fix quality
            stream.readUnsignedByte();
            return new WorkoutGeoPoint(latitude, longitude, altitude, altitude != 0.0, speedMetersPerSecond, 0, 0, 0, course, 0);
        } catch (IOException ioe) {
            throw new DeserializationFailedException(ioe);
        }
    }

    private static int getLatitudeAsMinutes(double latitude) {
        return (int) (((latitude - (int) latitude) * 0.6 + (int) latitude) * 1000000);
    }

    private static int getLongitudeAsMinutes(double longitude) {
        return (int) (((longitude - (int) longitude) * 0.6 + (int) longitude) * 1000000);
    }
}
