package com.stt.android.tracker.compat.serialization;

import com.stt.android.domain.workout.WorkoutGeoPoint;
import com.stt.android.tracker.event.EventWeather;

import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;

public class EventWeatherSerializer {
    public static EventWeather read(DataInputStream is) throws DeserializationFailedException {
        try {
            // 8 bits of header information. Bits 1..3: Version information, unsigned int range [0...7]
            byte header = is.readByte();
            // Id of the object in the service database.
            String key = is.readUTF();
            // Event timestamp in as real clock time
            long realTime = (long) is.readDouble();
            // Weather station name
            String station = is.readUTF();
            String stationDistance = is.readUTF();
            // Observation time in stations local time
            int timestamp = is.readInt();
            // Temperature in celsius degrees with two decimals
            int temperature = is.readShort();
            // Feel like temperature in celsius degrees with two decimals
            int temperatureFeel = is.readShort();
            String weatherSymbol = is.readUTF();
            // Wind direction in degrees, no decimals
            int windDirection = is.readUnsignedShort();
            int airPressure = is.readInt();
            int relativeHumidity = is.readInt();
            int vendorId = is.readUnsignedByte();
            WorkoutGeoPoint location = CoordinatesSerializer.read(is);
            return new EventWeather(header, key, realTime, station, stationDistance, timestamp, temperature, temperatureFeel,
                    weatherSymbol, windDirection, airPressure, relativeHumidity, vendorId, location);
        } catch (IOException e) {
            throw new DeserializationFailedException(e);
        }
    }

    public static void write(DataOutputStream os, EventWeather event) throws SerializationFailedException {
        try {
            os.writeByte(event.getHeader());
            os.writeUTF(event.getKey());
            os.writeDouble(event.getRealTime());
            os.writeUTF(event.getStation());
            os.writeUTF(event.getStationDistance());
            os.writeInt(event.getTimestamp());
            os.writeShort(event.getTemperature());
            os.writeShort(event.getTemperatureFeel());
            os.writeUTF(event.getWeatherSymbol());
            os.writeShort(event.getWindDirection());
            os.writeInt(event.getAirPressure());
            os.writeInt(event.getRelativeHumidity());
            os.writeByte(event.getVendorId());
            CoordinatesSerializer.write(os, event.getLocation());
        } catch (IOException e) {
            throw new SerializationFailedException(e);
        }
    }
}
