
package com.stt.android.tracker.event;

import com.stt.android.laps.CompleteLap;
import com.stt.android.domain.workout.WorkoutGeoPoint;

public class LegacyLocationEvent {

    /*
    The id field is not used at all.
    Moreover, generateId() is slow (we could improve it by statically calculating the 2000-01-01 timestamp for the
    current timezone). Roughly 30% of the time when reading a workout was spent in generateId()
    Leave this comments in case we need 'id' in the future (hopefully not).
    private int id;
     */
    private String key;
    private double distance;
    private double totalDistance;
    private long realTime;
    private double totalTimeInSeconds; // position in workout data.
    private double altitude;
    private float bearing;
    private float speed;
    private int longitude;
    private int latitude;
    private float accuracy;

    public LegacyLocationEvent(CompleteLap lap) {
        distance = lap.getDistance();
        totalDistance = lap.getWorkoutDistanceOnEnd();
        realTime = lap.getEndTimestamp();
        totalTimeInSeconds = lap.getWorkoutDurationOnEnd() / 1000.0;
        WorkoutGeoPoint endLocation = lap.getEndLocation();
        if (endLocation != null) {
            altitude = endLocation.getAltitude();
            bearing = endLocation.getCourse();
            setLatitude(endLocation.getLatitude());
            setLongitude(endLocation.getLongitude());
        }
        speed = (float) lap.getAverageSpeed();
    }

    public LegacyLocationEvent(WorkoutGeoPoint point) {
        distance = point.getDistance();
        totalDistance = point.getTotalDistance();
        realTime = point.getTimestamp();
        totalTimeInSeconds = point.getMillisecondsInWorkout() / 1000.0;
        altitude = point.getAltitude();
        bearing = point.getCourse();
        setLatitude(point.getLatitude());
        setLongitude(point.getLongitude());
        speed = point.getSpeedMetersPerSecond();
    }

    public LegacyLocationEvent(double totaltimeInSeconds, long realtime, double distance,
            double totaldistance, double altitude, float bearing, float speed, int latitude, int longitude,
            float accuracy) {
        this.altitude = altitude;
        this.bearing = bearing;
        this.latitude = latitude;
        this.longitude = longitude;
        this.speed = speed;
        this.accuracy = accuracy;
        setTotalTimeInSeconds(totaltimeInSeconds);
        setDistance(distance);
        setTotalDistance(totaldistance);
        setRealTime(realtime);
        // Uncomment below line if 'id' field is ever needed.
        // generateId();
    }

    public double getDistance() {
        return distance;
    }

    public void setDistance(double distance) {
        this.distance = distance;
    }

    /**
     * @return the epoch timestamp in milliseconds
     */
    public long getRealTime() {
        return realTime;
    }

    public void setRealTime(long timestamp) {
        this.realTime = timestamp;
    }

    public double getTotalTimeInSeconds() {
        return totalTimeInSeconds;
    }

    /**
     * @param totaltime the totaltime to set
     */
    public void setTotalTimeInSeconds(double totaltime) {
        this.totalTimeInSeconds = totaltime;
    }

    /**
     * @return the totaldistance
     */
    public double getTotalDistance() {
        return totalDistance;
    }

    /**
     * @param totaldistance the totaldistance to set
     */
    public void setTotalDistance(double totaldistance) {
        this.totalDistance = totaldistance;
    }

    /*
    private void generateId() {
        Calendar cal = Calendar.getInstance();
        cal.clear();
        cal.set(2000, Calendar.JANUARY, 1); // 2000-01-01
        id = (int) ((System.currentTimeMillis() - cal.getTimeInMillis()) / 1000.0);
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }
    */

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return "LocationEvent[" +
//                "id=" + id +
                ",key=" + key +
                ",distance=" + distance +
                ",totalDistance=" + totalDistance +
                ",realTime=" + realTime +
                "," + super.toString() + "]";
    }

    public WorkoutGeoPoint getWorkoutGeoPoint() {
        return new WorkoutGeoPoint((int) (getLatitude() * 1E6), (int) (getLongitude() * 1E6),
                getAltitude(), hasAltitude(), getSpeed(), getDistance(), getTotalTimeInSeconds() * 1000,
                getTotalDistance(), getBearing(), getRealTime());
    }

    private float getBearing() {
        return bearing;
    }

    public float getSpeed() {
        return speed;
    }

    private boolean hasAltitude() {
        return true;
    }

    public double getAltitude() {
        return altitude;
    }

    private void setLongitude(double longitude) {
        this.longitude = (int) (((longitude - (int) longitude) * 0.6 + (int) longitude) * 1000000);
    }

    private void setLatitude(double latitude) {
        this.latitude = (int) (((latitude - (int) latitude) * 0.6 + (int) latitude) * 1000000);
    }

    public double getLongitude() {
        int d = longitude / 1000000;
        return ((double) d + ((longitude - d * 1000000.0) / 10000.0) / 60.0);
    }

    public double getLatitude() {
        int d = latitude / 1000000;
        return ((double) d + ((latitude - d * 1000000.0) / 10000.0) / 60.0);
    }

    public float getAccuracy() {
        return accuracy;
    }

    public int getLatitudeInInt() {
        return latitude;
    }

    public int getLongitudeInInt() {
        return longitude;
    }
}
