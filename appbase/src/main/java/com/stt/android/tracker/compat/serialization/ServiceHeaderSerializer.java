package com.stt.android.tracker.compat.serialization;

import com.stt.android.tracker.model.LegacyServiceHeader;

import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;

public class ServiceHeaderSerializer {
    /**
     * Do not use this method to store updated service headers as it will overwrite most of the original data.
     *
     * @param stream
     * @param header
     * @throws SerializationFailedException
     */
    public static void write(DataOutputStream stream, LegacyServiceHeader header) throws SerializationFailedException {
        try {
            // VersionNumber
            stream.writeInt(0);
            // WorkoutId
            stream.writeInt(0);
            stream.writeInt(header.getCommentCount());
            // Comment last update
            stream.writeDouble(0.0);
            stream.writeInt(header.getViewCount());
            // View last update
            stream.writeDouble(0.0);
            // Vote count
            stream.writeInt(0);
            // Vote last update
            stream.writeDouble(0.0);
            // Flag count
            stream.writeInt(0);
            // Flag last update
            stream.writeDouble(0.0);
            // Media image count
            stream.writeInt(0);
            // Media video count
            stream.writeInt(0);
            // Media song count
            stream.writeInt(0);
            // Facebook shared
            stream.writeDouble(0.0);
            // Twitter shared
            stream.writeDouble(0.0);
            // Facebook like count
            stream.writeInt(0);
            // Facebook like last update
            stream.writeDouble(0.0);
            // Twitter like count
            stream.writeInt(0);
            // Twitter like last update
            stream.writeDouble(0.0);
        } catch (IOException ioe) {
            throw new SerializationFailedException(ioe);
        }
    }

    public static LegacyServiceHeader read(DataInputStream stream) throws DeserializationFailedException {
        try {
            // VersionNumber
            stream.readInt();
            // WorkoutId
            stream.readInt();
            int commentCount = stream.readInt();
            // CommentLastUpdate
            stream.readDouble();
            int viewCount = stream.readInt();
            // ViewLastUpdate
            stream.readDouble();
            // VoteCount
            stream.readInt();
            // VoteLastUpdate 
            stream.readDouble();
            // FlagCount
            stream.readInt();
            // FlagLastUpdate 
            stream.readDouble();
            // MediaImageCount
            stream.readInt();
            // MediaVideoCount
            stream.readInt();
            // MediaSongCount
            stream.readInt();
            // FacebookShared 
            stream.readDouble();
            // TwitterShared 
            stream.readDouble();
            // FacebookLikeCount
            stream.readInt();
            // FacebookLikeLastUpdate 
            stream.readDouble();
            // TwitterLikeCount
            stream.readInt();
            // TwitterLikeLastUpdate 
            stream.readDouble();
            return new LegacyServiceHeader(commentCount, viewCount);
        } catch (IOException ioe) {
            throw new DeserializationFailedException(ioe);
        }
    }
}
