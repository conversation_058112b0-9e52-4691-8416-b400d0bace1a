
package com.stt.android.tracker.compat.serialization;

import com.stt.android.tracker.event.LegacyLocationEvent;

import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;

/**
 * Serialization code for location events. Unlike other serializers, correctly
 * handling headers is mandatory, as that dictates using packed or unpacked
 * elements in the binary. Compression of each element is dictated by header
 * bitfield. Structure:
 * <ul>
 * <li>HEADER: 8 bits of header information:
 * <ul>
 * <li>Bits 1..3: Fix quality, unsigned int range [0...7].</li>
 * <li>Bit 4: distance compression, 0 = 2 bytes, 1 = 4 bytes.</li>
 * <li>Bit 5: speed compression, 0 = 1 byte, 1 = 2 bytes.</li>
 * <li>Bit 6: altitude compression, 0 = 2 bytes, 1 = 4 bytes.</li>
 * <li>Bit 7: location compression, 0 = 2 bytes, 1 = 4 bytes.</li>
 * <li>Bit 8: compression information, 0 = not compressed, 1 = compressed
 * format, use bits 4 to 7 to decompress data.</li>
 * </ul>
 * </li>
 * </ul>
 */
public class LocationEventSerializer {
    /**
     * FYI, in legacy it was written as "~(~0 << 3)"
     */
    private static final int VERSION_NUMBER_MASK = 7;

    private static final int VERSION_NUMBER = 1;
    /**
     * Compression bitfield flags.
     */
    private final static int EMaskLocation = 1 << 6;
    private final static int EMaskAltitude = 1 << 5;
    private final static int EMaskSpeed = 1 << 4;
    private final static int EMaskDistance = 1 << 3;
    @SuppressWarnings("unused")
    private final static int EMaskFixQuality = (~0 << 3);
    private final static int EMaskCompressed = 1 << 7;
    private final static int EMaskExtended = 7; // Used to be ~(~0 << 3)

    /**
     * Deserializer returns result from uncompressed event. If event is
     * compressed, something has gone wrong in the top level deserializer.
     * 
     * @param stream
     * @return
     * @throws DeserializationFailedException
     */
    public static LegacyLocationEvent readUncompressed(DataInputStream stream) throws DeserializationFailedException {
        LegacyLocationEvent event = null;

        try {
            // read header, extended header and discard.
            stream.readUnsignedByte();
            stream.readUnsignedByte();

            double totalTime = (double) stream.readInt() / 100.0;
            int _latitude = stream.readInt();
            int _longitude = stream.readInt();
            double altitude = (double) stream.readInt() / 10.0;
            // speed is in cm/s but we want it in m/s
            float speed = (float) stream.readShort() / 100.0F;
            // distance is in cm but we want it in meters
            double distance = (double) stream.readInt() / 100.0;
            short accuracy = stream.readShort();
            long timestamp = (long) stream.readDouble();

            event = new LegacyLocationEvent(totalTime, timestamp, distance, distance, altitude, 0, speed, _latitude,
                    _longitude, accuracy);

        } catch (IOException ioe) {
            throw new DeserializationFailedException(ioe);
        }
        return event;
    }

    /**
     * Deserializer tries to uncompress compressed event. If the event is not
     * compressed, normal code path is chosen.
     * 
     * @param stream
     * @param event
     * @return
     * @throws DeserializationFailedException
     */
    public static LegacyLocationEvent read(DataInputStream stream, LegacyLocationEvent event)
            throws DeserializationFailedException {

        if (event == null) {
            return readUncompressed(stream);
        }

        try {
            byte header = (byte) stream.readUnsignedByte();
            // Read extended header and so stream moves forward
            stream.readByte();

            // Uncompressed format
            if ((header & EMaskCompressed) == 0) {
                // totalTime is in 1/100 of a second but we want it in seconds
                double totalTime = (double) stream.readInt() / 100.0;
                int latitude = stream.readInt();
                int longitude = stream.readInt();
                double altitude = stream.readInt() / 10.0;
                // speed is in cm/s but we want it in m/s
                float speedMetersPerSecond = (float) stream.readShort() / 100.0F;
                // distance is in cm but we want it in meters
                double distanceInMeters = stream.readInt() / 100.0;
                short accuracy = stream.readShort(); // accuracy
                long timestamp = (long) stream.readDouble();

                // NOTE: Distance is relative to the previous distance, not from
                // the start of the workout!!!!
                double totalDistanceInMeters = distanceInMeters + event.getTotalDistance();
                LegacyLocationEvent newEvent = new LegacyLocationEvent(totalTime, timestamp, distanceInMeters,
                        totalDistanceInMeters, altitude, 0, speedMetersPerSecond, latitude, longitude, accuracy);
                return newEvent;
            }

            // binary totalTime is in 1/100 of a second but we want it in
            // seconds
            int dTotalTime = stream.readUnsignedByte();
            double totalTime = (event.getTotalTimeInSeconds() * 100 + dTotalTime) / 100.0;
            int latitude = event.getLatitudeInInt();
            int longitude = event.getLongitudeInInt();
            if ((header & EMaskLocation) == 0) {
                latitude += stream.readShort();
                longitude += stream.readShort();
            } else {
                latitude += stream.readInt();
                longitude += stream.readInt();
            }

            double altitude = event.getAltitude() * 10;
            if ((header & EMaskAltitude) == 0) {
                altitude += stream.readShort();
            } else {
                altitude += stream.readInt();
            }

            float speedCmsPerSecond = event.getSpeed() * 100.0F;
            if ((header & EMaskSpeed) == 0) {
                speedCmsPerSecond += stream.readByte();
            } else {
                speedCmsPerSecond += stream.readShort();
            }

            double distanceInCms;
            if ((header & EMaskDistance) == 0) {
                // reading unsigned short to prevent overflow to negative values
                distanceInCms = stream.readUnsignedShort();
            } else {
                distanceInCms = stream.readInt();
            }

            double totalDistanceInMeters = distanceInCms / 100.0 + event.getTotalDistance();
            int accuracy = stream.readUnsignedShort();
            // reading unsigned short to prevent overflow to negative values
            int realTimeDifference = stream.readUnsignedShort();
            double realtime = event.getRealTime() + realTimeDifference;

            LegacyLocationEvent newEvent = new LegacyLocationEvent(totalTime, (long) realtime, distanceInCms / 100,
                    totalDistanceInMeters, altitude / 10, 0, speedCmsPerSecond / 100, latitude, longitude, accuracy);
            return newEvent;

        } catch (IOException ioe) {
            throw new DeserializationFailedException(ioe);
        }
    }

    @SuppressWarnings("NarrowingCompoundAssignment")
    public static void writeUncompressed(DataOutputStream stream, LegacyLocationEvent event)
            throws SerializationFailedException {
        try {
            byte header = 0;
            byte headerExtended = 0;
            header &= (byte) ~EMaskCompressed;
            header |= (byte) EMaskExtended;
            header |= (byte) (VERSION_NUMBER & VERSION_NUMBER_MASK);
            headerExtended |= 1 << 7;

            double latitude = event.getLatitude();
            double longitude = event.getLongitude();

            stream.writeByte(header);
            stream.writeByte(headerExtended);
            // Apply the right conversions to correct binary here
            int totalTimeInCentiseconds = (int) (event.getTotalTimeInSeconds() * 100);
            stream.writeInt(totalTimeInCentiseconds);
            stream.writeInt((int) (((latitude - (int) latitude) * 0.6 + (int) latitude) * 1000000));
            stream.writeInt((int) (((longitude - (int) longitude) * 0.6 + (int) longitude) * 1000000));
            stream.writeInt((int) (event.getAltitude() * 10.0));
            // LegacyLocationEvent speed is in m/s but we want to store it in
            // cm/s
            stream.writeShort((short) (event.getSpeed() * 100.0F));
            // LegacyLocationEvent distance is in meters but we want to store it
            // in centimeters
            stream.writeInt((int) (event.getDistance() * 100.0F));
            stream.writeShort((short) event.getAccuracy());
            stream.writeDouble((double) event.getRealTime());

        } catch (IOException ioe) {
            throw new SerializationFailedException(ioe);
        }
    }

    /**
     * Serialize as compressed event binary. In order to compress event
     * serialization, we also need previous event. State management is handled
     * in the top level serializer.
     * 
     * @param stream
     * @param curEvent The current location event.
     * @param refEvent Reference event for compression.
     * @throws SerializationFailedException
     */
    public static void write(DataOutputStream stream, LegacyLocationEvent curEvent, LegacyLocationEvent refEvent)
            throws SerializationFailedException {
        // If reference event is not set, write uncompressed.
        if (refEvent == null) {
            writeUncompressed(stream, curEvent);
            return;
        }
        // Decide if we should write uncompressed based on total & real time
        int cTotaltime = (int) (curEvent.getTotalTimeInSeconds() * 100.0);
        int rTotaltime = (int) (refEvent.getTotalTimeInSeconds() * 100.0);
        int dTotaltime = cTotaltime - rTotaltime;

        long cRealtime = curEvent.getRealTime();
        long rRealtime = refEvent.getRealTime();
        int dRealtime = (int) (cRealtime - rRealtime);
        /*
         * TotalTime: Data long range (4 bytes) is [0,2^32] second. Data short
         * range (1 byte) is [0,2.56] second. Relative to previous point.
         */
        if (dTotaltime > 0xff || dRealtime > 0xffff) {
            writeUncompressed(stream, curEvent);
        } else {
            writeCompressing(stream, curEvent, refEvent);
        }
    }

    /**
     * Writes the given element trying to compress each value.
     * <p>
     * <em>Note:total time and real time are assumed to be compressed!</em>
     * </p>
     * 
     * @param stream
     * @param curEvent
     * @param refEvent
     * @throws SerializationFailedException
     */
    @SuppressWarnings("NarrowingCompoundAssignment")
    private static void writeCompressing(DataOutputStream stream, LegacyLocationEvent curEvent,
            LegacyLocationEvent refEvent) throws SerializationFailedException {
        try {
            int cTotaltime = (int) (curEvent.getTotalTimeInSeconds() * 100.0);
            int rTotaltime = (int) (refEvent.getTotalTimeInSeconds() * 100.0);
            int dTotaltime = cTotaltime - rTotaltime;

            long cRealtime = curEvent.getRealTime();
            long rRealtime = refEvent.getRealTime();
            int dRealtime = (int) (cRealtime - rRealtime);
            /*
             * Latitude and longitude are presented as floating point degrees,
             * convert to E6.
             */
            double latitude = curEvent.getLatitude();
            double longitude = curEvent.getLongitude();
            int cLatitude = (int) (((latitude - (int) latitude) * 0.6 + (int) latitude) * 1000000);
            int cLongitude = (int) (((longitude - (int) longitude) * 0.6 + (int) longitude) * 1000000);
            int cAltitude = (int) (curEvent.getAltitude() * 10.0);
            // LegacyLocationEvent speed is in m/s but we want to store it in
            // cm/s
            int cSpeed = (int) (curEvent.getSpeed() * 100F);
            // LegacyLocationEvent distance is in meters but we want to store it
            // in centimeters
            int cDistance = (int) (curEvent.getDistance() * 100F);

            latitude = refEvent.getLatitude();
            longitude = refEvent.getLongitude();
            /*
             * Latitude and longitude are presented as floating point degrees,
             * convert to E6.
             */
            int rLatitude = (int) (((latitude - (int) latitude) * 0.6 + (int) latitude) * 1000000);
            int rLongitude = (int) (((longitude - (int) longitude) * 0.6 + (int) longitude) * 1000000);
            int rAltitude = (int) (refEvent.getAltitude() * 10.0);
            // LegacyLocationEvent speed is in m/s but we want to store it in
            // cm/s
            int rSpeed = (int) (refEvent.getSpeed() * 100.0F);

            int dLatitude = cLatitude - rLatitude;
            int dLongitude = cLongitude - rLongitude;
            int dAltitude = cAltitude - rAltitude;
            int dSpeed = cSpeed - rSpeed;

            byte header = 0;
            byte headerExtended = 0;

            // Can location change be compressed
            if (Math.abs(dLatitude) > (0xffff >> 1) || Math.abs(dLongitude) > (0xffff >> 1)) {
                header |= (byte) EMaskLocation;
            } else {
                header &= (byte) ~EMaskLocation;
            }

            // Can altitude change be compressed
            if (Math.abs(dAltitude) > 0xffff >> 1) {
                header |= (byte) EMaskAltitude;
            } else {
                header &= (byte) ~EMaskAltitude;
            }

            // Can speed change be compressed
            if (Math.abs(dSpeed) > 0xff >> 1) {
                header |= (byte) EMaskSpeed;
            } else {
                header &= (byte) ~EMaskSpeed;
            }

            // Can distance change be compressed
            if (cDistance > 0xffff) {
                header |= (byte) EMaskDistance;
            } else {
                header &= (byte) ~EMaskDistance;
            }

            header |= (byte) EMaskCompressed;
            header |= (byte) EMaskExtended;
            headerExtended |= 1 << 7;

            stream.writeByte(header);
            stream.writeByte(headerExtended);
            stream.writeByte((byte) dTotaltime);

            if ((header & EMaskLocation) == 0) {
                stream.writeShort((short) dLatitude);
                stream.writeShort((short) dLongitude);
            } else {
                stream.writeInt(dLatitude);
                stream.writeInt(dLongitude);
            }

            if ((header & EMaskAltitude) == 0) {
                stream.writeShort((short) dAltitude);
            } else {
                stream.writeInt(dAltitude);
            }

            if ((header & EMaskSpeed) == 0) {
                stream.writeByte((byte) dSpeed);
            } else {
                stream.writeShort(dSpeed);
            }

            if ((header & EMaskDistance) == 0) {
                stream.writeShort((short) (cDistance));
            } else {
                stream.writeInt(cDistance);
            }

            stream.writeShort((short) curEvent.getAccuracy());
            stream.writeShort((short) dRealtime);

        } catch (IOException ioe) {
            throw new SerializationFailedException(ioe);
        }
    }
}
