
package com.stt.android.tracker.event;

import java.util.Date;

public class LegacyHeartRateEvent {

    private int timeoffset; // timestamp relative to the start of the workout.
    private long timestamp; // timestamp as system time.
    private int hr; // heart rate
    private int[] frame; // full binary frame of the heart rate event

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public int getHeartrate() {
        return hr;
    }

    public void setHeartrate(int hr) {
        this.hr = hr;
    }

    public void setFrame(int[] frame) {
        this.frame = frame;
    }

    public int[] getFrame() {
        return this.frame;
    }

    public LegacyHeartRateEvent(long timeoffsetInMilliseconds, long timestamp, int[] frame, int hrValue) {
        this.timeoffset = (int) (timeoffsetInMilliseconds / 10.0f);
        this.timestamp = timestamp;
        this.hr = hrValue;
        this.frame = frame;
    }

    /**
     * @param timeoffset offset in 1/100 of second
     * @param frame
     */
    public LegacyHeartRateEvent(int timeoffset, byte[] frame) {
        this.timeoffset = timeoffset;
        this.timestamp = System.currentTimeMillis();
    }

    public LegacyHeartRateEvent() {

    }

    /**
     * @return the time offset relative to the start of workout in 1/100 of
     *         second
     */
    public int getTimeoffset() {
        return timeoffset;
    }

    /**
     * @param timeoffset the time offset to set, relative to start of workout.
     */
    public void setTimeoffset(int timeoffset) {
        this.timeoffset = timeoffset;
    }

    public Date getTimestampAsDate()
    {
        return new Date(this.timestamp);
    }

    public void setTimestampAsDate(Date timestamp) {
        this.timestamp = timestamp.getTime();
    }

    @Override
    public String toString() {
        return ("timestamp: " + (new Date(timestamp)).toString() + "\n" +
                "offset: " + timeoffset + "\n" +
                "heartrate: " + hr + "\n");
    }

}
