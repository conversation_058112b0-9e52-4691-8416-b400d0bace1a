package com.stt.android.tracker.event;

import com.stt.android.domain.workout.WorkoutGeoPoint;

import java.util.Date;

public class LegacyMediaEvent {

    public enum MediaType {
        NONE(0),
        IMAGE(1),
        VIDEO(2),
        SONG(3),
        DESCRIPTION(4),
        BOOKMARK(5);

        private final int mediaTypeEnum;

        MediaType(int eventType) {
            this.mediaTypeEnum = eventType;
        }

        public int getMediaType() {
            return mediaTypeEnum;
        }
    }

    private int id;
    private String key;

    private long totalTime;
    private Date timeStamp;

    private MediaType type;

    private WorkoutGeoPoint coordinate;
    private String title;
    private String artist;
    private String album;
    private long duration;
    private String imagePath;
    private String videoPath;
    private String description;
    private String bookmark;

    public LegacyMediaEvent(int id, String key, long totalTime, Date timestamp, MediaType type) {
        this.id = id;
        this.key = key;
        this.totalTime = totalTime;
        this.timeStamp = timestamp;
        this.type = type;
    }

    /**
     * @return the id
     */
    public int getId() {
        return id;
    }

    /**
     * @param id the id to set
     */
    public void setId(int id) {
        this.id = id;
    }

    /**
     * @return the key
     */
    public String getKey() {
        return key;
    }

    /**
     * @param key the key to set
     */
    public void setKey(String key) {
        this.key = key;
    }

    /**
     * @return the totalTime
     */
    public long getTotalTime() {
        return totalTime;
    }

    /**
     * @param totalTime the totalTime to set
     */
    public void setTotalTime(long totalTime) {
        this.totalTime = totalTime;
    }

    /**
     * @return the timeStamp
     */
    public Date getTimeStamp() {
        return timeStamp;
    }

    /**
     * @param timeStamp the timeStamp to set
     */
    public void setTimeStamp(Date timeStamp) {
        this.timeStamp = timeStamp;
    }

    /**
     * @return the type
     */
    public MediaType getType() {
        return type;
    }

    /**
     * @param type the type to set
     */
    public void setType(MediaType type) {
        this.type = type;
    }

    /**
     * @return the coordinate
     */
    public WorkoutGeoPoint getCoordinate() {
        return coordinate;
    }

    /**
     * @param coordinate the coordinate to set
     */
    public void setCoordinate(WorkoutGeoPoint coordinate) {
        this.coordinate = coordinate;
    }

    /**
     * @return the title
     */
    public String getTitle() {
        return title;
    }

    /**
     * @param title the title to set
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * @return the artist
     */
    public String getArtist() {
        return artist;
    }

    /**
     * @param artist the artist to set
     */
    public void setArtist(String artist) {
        this.artist = artist;
    }

    /**
     * @return the album
     */
    public String getAlbum() {
        return album;
    }

    /**
     * @param album the album to set
     */
    public void setAlbum(String album) {
        this.album = album;
    }

    /**
     * @return the duration
     */
    public long getDuration() {
        return duration;
    }

    /**
     * @param duration the duration to set
     */
    public void setDuration(long duration) {
        this.duration = duration;
    }

    /**
     * @return the imagePath
     */
    public String getImagePath() {
        return imagePath;
    }

    /**
     * @param imagePath the imagePath to set
     */
    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
    }

    /**
     * @return the videoPath
     */
    public String getVideoPath() {
        return videoPath;
    }

    /**
     * @param videoPath the videoPath to set
     */
    public void setVideoPath(String videoPath) {
        this.videoPath = videoPath;
    }

    /**
     * @return the description
     */
    public String getDescription() {
        return description;
    }

    /**
     * @param description the description to set
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * @return the bookmark
     */
    public String getBookmark() {
        return bookmark;
    }

    /**
     * @param bookmark the bookmark to set
     */
    public void setBookmark(String bookmark) {
        this.bookmark = bookmark;
    }

}
