/**
 * Created		: 16.11.2009
 * Copyright	: Sports Tracking Technologies Oy
 * Author		: <PERSON><PERSON>
 */
package com.stt.android.tracker.event;

import com.stt.android.domain.workout.WorkoutGeoPoint;

public class EventWeather {
    /**
     * 8 bits of header information. Bits 1..3: Version information, unsigned
     * int range [0...7]
     */
    private final byte header;
    /**
     * Id of the object in the service database.
     */
    private final String key;
    /**
     * Event time in milliseconds as real clock time measured from the beginning
     * of the workout.
     */
    private final long realTime;
    private final String station;
    private final String stationDistance;
    /**
     * Observation time in stations local time.
     */
    private final int timestamp;
    /**
     * Temperature in celsius degrees with two decimals.
     */

    private final int temperature;
    /**
     * Feel like temperature in celsius degrees with two decimals.
     */
    private final int temperatureFeel;
    private final String weatherSymbol;
    /**
     * Wind direction in degrees, no decimals.
     */
    private final int windDirection;
    private final int airPressure;
    private final int relativeHumidity;
    private final int vendorId;
    private final WorkoutGeoPoint location;

    public EventWeather(byte header, String key, long realTime, String station, String stationDistance, int timestamp, int temperature,
                        int temperatureFeel, String weatherSymbol, int windDirection, int airPressure, int relativeHumidity,
                        int vendorId, WorkoutGeoPoint location) {
        this.header = header;
        this.key = key;
        this.realTime = realTime;
        this.station = station;
        this.stationDistance = stationDistance;
        this.timestamp = timestamp;
        this.temperature = temperature;
        this.temperatureFeel = temperatureFeel;
        this.weatherSymbol = weatherSymbol;
        this.windDirection = windDirection;
        this.airPressure = airPressure;
        this.relativeHumidity = relativeHumidity;
        this.vendorId = vendorId;
        this.location = location;
    }

    public int getTemperature() {
        return temperature;
    }

    public String getKey() {
        return key;
    }

    public long getRealTime() {
        return realTime;
    }

    public String getStation() {
        return station;
    }

    public String getStationDistance() {
        return stationDistance;
    }

    public int getTimestamp() {
        return timestamp;
    }

    public int getTemperatureFeel() {
        return temperatureFeel;
    }

    public String getWeatherSymbol() {
        return weatherSymbol;
    }

    public int getWindDirection() {
        return windDirection;
    }

    public int getAirPressure() {
        return airPressure;
    }

    public int getRelativeHumidity() {
        return relativeHumidity;
    }

    public int getVendorId() {
        return vendorId;
    }

    public WorkoutGeoPoint getLocation() {
        return location;
    }

    public byte getHeader() {
        return header;
    }
}
