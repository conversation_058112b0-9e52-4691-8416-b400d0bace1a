package com.stt.android.tracker.model;

/**
 * <AUTHOR>
 *
 */
public class LegacyLap {
    private double startTime;
    private double endTime;
    private double lapDuration;
    private double startDistance;
    private double endDistance;
    private double lapDistance;
    private double averageSpeed;
    private double latitude;
    private double longitude;

    public LegacyLap(
            double startTime,
            double endTime,
            double startDistance,
            double endDistance,
            double latitude,
            double longitude
            )
    {
        this.startTime = startTime;
        this.endTime = endTime;
        this.lapDuration = endTime - startTime;
        this.startDistance = startDistance;
        this.endDistance = endDistance;
        this.lapDistance = endDistance - startDistance;
        
        this.averageSpeed = (this.lapDuration > 0 ) ? (this.lapDistance / this.lapDuration) : 0.0F;
        this.latitude = latitude;
        this.longitude = longitude;
    }

    public void update(double endtime, double enddistance, double latitude, double longitude) {
        this.endTime = endtime;
        this.endDistance = enddistance;
        this.latitude = latitude;
        this.longitude = longitude;

        if (lapDuration > 0) {
            this.averageSpeed = lapDistance / lapDuration;
        } else {
            this.averageSpeed = 0;
        }
    }

    public void update(double endtime) {
        this.endTime = endtime;
        this.lapDuration = endtime - startTime;
        this.lapDistance = endDistance - startDistance;

        if (lapDuration > 0) {
            this.averageSpeed = this.lapDistance / this.lapDuration;
        } else {
            this.averageSpeed = 0;
        }
    }

    /**
     * @return the startTime
     */
    public double getStartTime() {
        return startTime;
    }

    /**
     * @param startTime the startTime to set
     */
    public void setStartTime(double startTime) {
        this.startTime = startTime;
    }

    /**
     * @return the endTime
     */
    public double getEndTime() {
        return endTime;
    }

    /**
     * @param endTime the endTime to set
     */
    public void setEndTime(double endTime) {
        this.endTime = endTime;
    }

    /**
     * @return the lapDuration
     */
    public double getLapDuration() {
        return lapDuration;
    }

    /**
     * @param lapDuration the lapDuration to set
     */
    public void setLapDuration(double lapDuration) {
        this.lapDuration = lapDuration;
    }

    /**
     * @return the startDistance
     */
    public double getStartDistance() {
        return startDistance;
    }

    /**
     * @param startDistance the startDistance to set
     */
    public void setStartDistance(double startDistance) {
        this.startDistance = startDistance;
    }

    /**
     * @return the endDistance
     */
    public double getEndDistance() {
        return endDistance;
    }

    /**
     * @param endDistance the endDistance to set
     */
    public void setEndDistance(double endDistance) {
        this.endDistance = endDistance;
    }

    /**
     * @return the averageSpeed
     */
    public double getAverageSpeed() {
        return averageSpeed;
    }

    /**
     * @param averageSpeed the averageSpeed to set
     */
    public void setAverageSpeed(double averageSpeed) {
        this.averageSpeed = averageSpeed;
    }

    /**
     * @return the latitude
     */
    public double getLatitude() {
        return latitude;
    }

    /**
     * @param latitude the latitude to set
     */
    public void setLatitude(double latitude) {
        this.latitude = latitude;
    }

    /**
     * @return the longitude
     */
    public double getLongitude() {
        return longitude;
    }

    /**
     * @param longitude the longitude to set
     */
    public void setLongitude(double longitude) {
        this.longitude = longitude;
    }

    /**
     * @return the lapDistance
     */
    public double getLapDistance() {
        return lapDistance;
    }

    /**
     * @param lapDistance the lapDistance to set
     */
    public void setLapDistance(double lapDistance) {
        this.lapDistance = lapDistance;
    }

    /**
     * @return
     */
    public double getAvgSpeed() {
        return averageSpeed;
    }

}
