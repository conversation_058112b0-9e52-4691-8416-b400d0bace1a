package com.stt.android.tracker.model;

import java.util.Vector;

public class LegacyLaps {
    private Vector<LegacyLap> laps;

    private double lapDistance = 0;
    private double startTime = 0;
    private double startDistance = 0;
    private double lastDistance = 0;
    private double lastTime = 0;

    private int fastestLap = -1;
    private int slowestLap = -1;
    
    public LegacyLaps() {
        laps = new Vector<>();
        laps.add(new LegacyLap(0.0, 0.0, 0.0, 0.0, 0.0, 0.0));
    }

    public LegacyLaps(double lapDistance) {
        this.lapDistance = lapDistance;
        laps = new Vector<>();
        laps.add(new LegacyLap(this.startTime, 0, this.startDistance, 0, 0, 0));
    }

    public Vector<LegacyLap> getLaps() {
        return laps;
    }

    public void updateTime(double time) {
        laps.lastElement().update(time);
        updateIndexes();
    }

    public LegacyLap getCurrentLap() {
        return laps.lastElement();
    }

    public void updateTime(double time, double distance, double latitude, double longitude) {
        laps.lastElement().update(time, distance, latitude, longitude);
        updateIndexes();
    }

    /**
     * Update lap index of slowest and fastest lap. Needs refactoring for
     * performace.
     */
    private void updateIndexes() {
        //FIXME Is this really needed?
    }

    /**
     * Removes latest manually added lap.
     */
    public void removeLatest() {
        if (laps.size() == 0)
            return;

        this.laps.removeElementAt(laps.size() - 1);
    }

    public void addLap(double time, double distance, double latitude, double longitude) {
        updateTime(time, distance, latitude, longitude);
        this.startTime = time;
        this.startDistance = distance;
        this.laps.add(new LegacyLap(this.startTime, time, this.startDistance, distance, latitude, longitude));
    }

    /**
     * @return the lapDistance
     */
    public double getLapDistance() {
        return lapDistance;
    }

    /**
     * @param lapDistance the lapDistance to set
     */
    public void setLapDistance(double lapDistance) {
        this.lapDistance = lapDistance;
    }

    /**
     * @return the startTime
     */
    public double getStartTime() {
        return startTime;
    }

    /**
     * @param startTime the startTime to set
     */
    public void setStartTime(double startTime) {
        this.startTime = startTime;
    }

    /**
     * @return the startDistance
     */
    public double getStartDistance() {
        return startDistance;
    }

    /**
     * @param startDistance the startDistance to set
     */
    public void setStartDistance(double startDistance) {
        this.startDistance = startDistance;
    }

    /**
     * @return the lastDistance
     */
    public double getLastDistance() {
        return lastDistance;
    }

    /**
     * @param lastDistance the lastDistance to set
     */
    public void setLastDistance(double lastDistance) {
        this.lastDistance = lastDistance;
    }

    /**
     * @return the lastTime
     */
    public double getLastTime() {
        return lastTime;
    }

    /**
     * @param lastTime the lastTime to set
     */
    public void setLastTime(double lastTime) {
        this.lastTime = lastTime;
    }

    /**
     * @return the fastestLap
     */
    public int getFastestLap() {
        return fastestLap;
    }

    /**
     * @param fastestLap the fastestLap to set
     */
    public void setFastestLap(int fastestLap) {
        this.fastestLap = fastestLap;
    }

    /**
     * @return the slowestLap
     */
    public int getSlowestLap() {
        return slowestLap;
    }

    /**
     * @param slowestLap the slowestLap to set
     */
    public void setSlowestLap(int slowestLap) {
        this.slowestLap = slowestLap;
    }

    /**
     * @param laps the laps to set
     */
    public void setLaps(Vector<LegacyLap> laps) {
        this.laps = laps;
    }

}
