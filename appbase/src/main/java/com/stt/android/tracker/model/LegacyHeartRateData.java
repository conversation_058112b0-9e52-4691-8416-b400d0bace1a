package com.stt.android.tracker.model;


import com.stt.android.domain.workout.WorkoutHrEvent;
import com.stt.android.utils.STTConstants;

import java.util.List;

import timber.log.Timber;

/**
 * Legacy application compatible heart rate data aggregator. The main purpose of
 * the class is to a) calculate average hr based on distribution b) provide
 * internal structure (bars) for presenting data in the UI. NOTE: This code is
 * probably redundant, but has to be implemented for legacy compatibility for
 * now, see {@link com.stt.android.tracker.compat.serialization.HeartRateDataSerializer} for binary protocol.
 * 
 */
public class LegacyHeartRateData {
    /**
     * Legacy fixed size distribution array.
     */
    public final static int MAX_NUMBER_BARS = 250;

    private int threshold1;
    private int threshold2;
    private int threshold3;
    private int threshold4;
    private int thresholdMax;

    private int heartrateMin;
    private int heartrateMax;
    private int heartrateAvg;

    private int count;
    private int timeInZone0;
    private int timeInZone1;
    private int timeInZone2;
    private int timeInZone3;
    private int timeInZone4;
    private int timeInZone5;

    private int energyConsumption;

    private long[] hrDistribution = new long[MAX_NUMBER_BARS];

    public LegacyHeartRateData() {
    }

    public LegacyHeartRateData(
            int threshold1,
            int threshold2,
            int threshold3,
            int threshold4) {

        this.threshold1 = threshold1;
        this.threshold2 = threshold2;
        this.threshold3 = threshold3;
        this.threshold4 = threshold4;

    }

    public void addHeartrate(int hr, long time) {

        /**
         * Since we're dealing with array aggregating durations to slots, index
         * is hr - 1.
         */
        int hrIdx = hr - 1;

        /**
         * Update distribution.
         */
        if (hrIdx >= 0 && hrIdx < MAX_NUMBER_BARS) {
            hrDistribution[hrIdx] += time;
        }

        /**
         * Update statistics.
         */

        if (count == 0) {
            heartrateMax = heartrateMin = 0;
        }

        if (hr > heartrateMax) {
            heartrateMax = hr;
        }

        if (hr < heartrateMin) {
            heartrateMin = hr;
        }

        count++;

        /**
         * Update average based on distribution. This should be re-factored to
         * use delta aggregation for better performance (less arithmetic per hr
         * sample).
         */
        double sumTime = 0;
        double sumHrTime = 0;

        for (int i = 0; i < MAX_NUMBER_BARS; i++) {
            sumHrTime += (i + 1) * (double) (hrDistribution[i] / 100.0F);
            sumTime += (double) (hrDistribution[i] / 100.0F);
        }

        heartrateAvg = sumTime > 0 ? (int) (sumHrTime / sumTime) : 0;

        updateTimeInZone(hr, time);
    }

    private void updateTimeInZone(int hr, long time) {
        if (hr >= threshold4) {
            timeInZone5 += time;
        } else if (hr >= threshold3) {
            timeInZone4 += time;
        } else if (hr >= threshold2) {
            timeInZone3 += time;
        } else if (hr >= threshold1) {
            timeInZone2 += time;
        } else {
            if (hr >= thresholdMax * 0.2F) {
                timeInZone1 += time;
            } else {
                timeInZone0 += time;
            }
        }
    }

    /**
     * @return the threshold1
     */
    public int getThreshold1() {
        return threshold1;
    }

    /**
     * @param threshold1 the threshold1 to set
     */
    public void setThreshold1(int threshold1) {
        this.threshold1 = threshold1;
    }

    /**
     * @return the threshold2
     */
    public int getThreshold2() {
        return threshold2;
    }

    /**
     * @param threshold2 the threshold2 to set
     */
    public void setThreshold2(int threshold2) {
        this.threshold2 = threshold2;
    }

    /**
     * @return the threshold3
     */
    public int getThreshold3() {
        return threshold3;
    }

    /**
     * @param threshold3 the threshold3 to set
     */
    public void setThreshold3(int threshold3) {
        this.threshold3 = threshold3;
    }

    /**
     * @return the threshold4
     */
    public int getThreshold4() {
        return threshold4;
    }

    /**
     * @param threshold4 the threshold4 to set
     */
    public void setThreshold4(int threshold4) {
        this.threshold4 = threshold4;
    }

    /**
     * @return the thresholdMax
     */
    public int getThresholdMax() {
        return thresholdMax;
    }

    /**
     * @param thresholdMax the thresholdMax to set
     */
    public void setThresholdMax(int thresholdMax) {
        this.thresholdMax = thresholdMax;
    }

    /**
     * @return the heartrateMin
     */
    public int getHeartrateMin() {
        return heartrateMin;
    }

    /**
     * @return the heartrateMax
     */
    public int getHeartrateMax() {
        return heartrateMax;
    }

    /**
     * @return the heartrateAvg
     */
    public int getHeartrateAvg() {
        return heartrateAvg;
    }

    /**
     * @return the timeInZone0
     */
    public int getTimeInZone0() {
        return timeInZone0;
    }

    /**
     * @return the timeInZone1
     */
    public int getTimeInZone1() {
        return timeInZone1;
    }

    /**
     * @return the timeInZone2
     */
    public int getTimeInZone2() {
        return timeInZone2;
    }

    /**
     * @return the timeInZone3
     */
    public int getTimeInZone3() {
        return timeInZone3;
    }

    /**
     * @return the timeInZone4
     */
    public int getTimeInZone4() {
        return timeInZone4;
    }

    /**
     * @return the timeInZone5
     */
    public int getTimeInZone5() {
        return timeInZone5;
    }

    /**
     * @return the energyConsumption
     */
    public int getEnergyConsumption() {
        return energyConsumption;
    }

    /**
     * @return the hrDistribution
     */
    public long[] getHrDistribution() {
        return hrDistribution;
    }

    /**
     * @return the count
     */
    public int getCount() {
        return count;
    }

    /**
     * @param count the count to set
     */
    public void setCount(int count) {
        this.count = count;
    }

    /**
     * @param heartrateMin the heartrateMin to set
     */
    public void setHeartrateMin(int heartrateMin) {
        this.heartrateMin = heartrateMin;
    }

    /**
     * @param heartrateMax the heartrateMax to set
     */
    public void setHeartrateMax(int heartrateMax) {
        this.heartrateMax = heartrateMax;
    }

    /**
     * @param heartrateAvg the heartrateAvg to set
     */
    public void setHeartrateAvg(int heartrateAvg) {
        this.heartrateAvg = heartrateAvg;
    }

    /**
     * @param timeInZone0 the timeInZone0 to set
     */
    public void setTimeInZone0(int timeInZone0) {
        this.timeInZone0 = timeInZone0;
    }

    /**
     * @param timeInZone1 the timeInZone1 to set
     */
    public void setTimeInZone1(int timeInZone1) {
        this.timeInZone1 = timeInZone1;
    }

    /**
     * @param timeInZone2 the timeInZone2 to set
     */
    public void setTimeInZone2(int timeInZone2) {
        this.timeInZone2 = timeInZone2;
    }

    /**
     * @param timeInZone3 the timeInZone3 to set
     */
    public void setTimeInZone3(int timeInZone3) {
        this.timeInZone3 = timeInZone3;
    }

    /**
     * @param timeInZone4 the timeInZone4 to set
     */
    public void setTimeInZone4(int timeInZone4) {
        this.timeInZone4 = timeInZone4;
    }

    /**
     * @param timeInZone5 the timeInZone5 to set
     */
    public void setTimeInZone5(int timeInZone5) {
        this.timeInZone5 = timeInZone5;
    }

    /**
     * @param energyConsumption the energyConsumption to set
     */
    public void setEnergyConsumption(int energyConsumption) {
        this.energyConsumption = energyConsumption;
    }

    public void createHrDistributionAndTimeZones(List<WorkoutHrEvent> heartRateEvents, long totalWorkoutTimeInSeconds) {
        if (heartRateEvents == null || heartRateEvents.isEmpty()) {
            return;
        }
        // set initial values
        int previousHrValue = heartRateEvents.get(0).getHeartRate();
        long previousMsInWorkout = 0;
        for (WorkoutHrEvent heartRateEvent : heartRateEvents) {
            long currentMsInWorkout = heartRateEvent.getMillisecondsInWorkout();
            int currentHrValue = heartRateEvent.getHeartRate();
            // Use the avg between to points to decide where to store the value. Value in hrDistribution[0] is for bpm 1,
            // hrDistribution[1] for bpm 2...
            int avgHeartRate = (int) ((currentHrValue + previousHrValue) / 2.0);
            int barIdx = avgHeartRate - 1;
            if (barIdx >= 0 && barIdx < MAX_NUMBER_BARS) {
                long durationMs = currentMsInWorkout - previousMsInWorkout;
                hrDistribution[barIdx] += durationMs;
                updateTimeInZone(avgHeartRate, durationMs);
            }
            previousHrValue = currentHrValue;
            previousMsInWorkout = currentMsInWorkout;
        }
        // Add the last HR value to the distribution with time up to the end of the workout
        long lastValueDuration = totalWorkoutTimeInSeconds * 1000 - previousMsInWorkout;
        int barIdx = previousHrValue - 1;
        if (barIdx >= 0 && barIdx < MAX_NUMBER_BARS) {
            hrDistribution[barIdx] += lastValueDuration;
            updateTimeInZone(previousHrValue, lastValueDuration);
        }
        if (STTConstants.DEBUG) {
            for (int i = 0; i < hrDistribution.length; i++) {
                long time = hrDistribution[i];
                Timber.d("HR[%d] = %d", i, time);
            }
        }
    }

    public void setHrDistribution(long[] hrDistribution) {
        this.hrDistribution = hrDistribution;
    }
}
