
package com.stt.android.tracker.compat.serialization;

import com.stt.android.tracker.event.LegacyHeartRateEvent;

import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;

public class HeartrateEventSerializer {

    // Needs to be public so that the non-legacy heart rate providers can write the correct
    // amount of data to the bogus frame
    public final static int K_FRAME_LENGTH = 16;
    private final static int versionNumber = 0;
    private final static int EMaskVersionNumber = ~0 << 3;

    public static void write(DataOutputStream stream, LegacyHeartRateEvent event) throws SerializationFailedException {
        try {
            // write dummy header.
            stream.writeByte(0);
            stream.writeInt(event.getTimeoffset());
            stream.writeDouble(event.getTimestamp());
            stream.writeByte(event.getHeartrate());

            int[] frame = event.getFrame();
            for (int i = 0; i < K_FRAME_LENGTH; i++) {
                stream.writeByte(frame[i]);
            }
        } catch (IOException ioe) {
            throw new SerializationFailedException(ioe);
        }
    }

    public static LegacyHeartRateEvent read(DataInputStream stream) throws DeserializationFailedException {

        try {
            LegacyHeartRateEvent event = new LegacyHeartRateEvent();
            // read header and discard.
            stream.readUnsignedByte();
            event.setTimeoffset(stream.readInt());
            event.setTimestamp((long) stream.readDouble());
            event.setHeartrate(stream.readUnsignedByte());

            int[] frame = new int[K_FRAME_LENGTH];
            for (int i = 0; i < K_FRAME_LENGTH; i++) {
                frame[i] = stream.readUnsignedByte();
            }
            event.setFrame(frame);
            return event;
        } catch (IOException ioe) {
            throw new DeserializationFailedException(ioe);
        }

    }
}
