
package com.stt.android.tracker.compat.serialization;

import com.stt.android.tracker.event.Event;

import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;

public class EventSerializer {

    public static void write(DataOutputStream stream, Event event) throws SerializationFailedException {
        try {
            // header is not used, write dummy data.
            stream.writeByte(0);
            // binary is stored as centiseconds, convert from milliseconds.
            stream.writeInt((int) event.getTimeWhenEventHappened() / 10);
            stream.writeByte((byte) event.getType().getEventType());
            stream.writeDouble((double) event.getRealTime());
        } catch (IOException ioe) {
            throw new SerializationFailedException(ioe);
        }
    }

    public static Event read(DataInputStream stream) throws DeserializationFailedException {
        try {
            // read header data and discard.
            stream.readUnsignedByte();

            // binary is in centiseconds, convert to milliseconds.
            double whenChanged = stream.readInt() * 10;
            int eventTypeId = stream.readUnsignedByte();
            Event.EventType type = Event.EventType.valueOf(eventTypeId);
            long realTime = (long) (stream.readDouble());
            return new Event(type, realTime, whenChanged);
        } catch (IOException ioe) {
            throw new DeserializationFailedException(ioe);
        }

    }
}
