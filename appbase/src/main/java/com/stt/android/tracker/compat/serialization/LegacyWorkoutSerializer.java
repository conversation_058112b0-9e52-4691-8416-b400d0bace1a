
package com.stt.android.tracker.compat.serialization;

import com.stt.android.tracker.event.Event;
import com.stt.android.tracker.event.LegacyHeartRateEvent;
import com.stt.android.tracker.event.LegacyLocationEvent;
import com.stt.android.tracker.event.LegacyMediaEvent;
import com.stt.android.tracker.model.LegacyWorkout;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import timber.log.Timber;

public class LegacyWorkoutSerializer {
    public static LegacyWorkout read(DataInputStream br) throws DeserializationFailedException {
        try {
            int eventCount = br.readInt();
            List<Event> eventGeneral = new ArrayList<>(eventCount);
            for (int i = 0; i < eventCount; ++i) {
                // We don't really store the general events so skip them
                eventGeneral.add(EventSerializer.read(br));
            }

            eventCount = br.readInt();
            Timber.d("LegacyWorkoutSerializer.read() reading %d location events", eventCount);
            List<LegacyLocationEvent> eventLocations = new ArrayList<>(eventCount);
            LegacyLocationEvent previousLocation = null;

            for (int i = 0; i < eventCount; ++i) {
                LegacyLocationEvent currentLocation = LocationEventSerializer.read(br, previousLocation);
                eventLocations.add(currentLocation);
                previousLocation = currentLocation;
            }

            // air pressure events
            br.readInt();

            // temperature events
            br.readInt();

            eventCount = br.readInt();
            Timber.d("LegacyWorkoutSerializer.read() reading %d heart rate events", eventCount);
            List<LegacyHeartRateEvent> eventHeartRates = new ArrayList<>(eventCount);
            for (int i = 0; i < eventCount; ++i) {
                eventHeartRates.add(HeartrateEventSerializer.read(br));
            }
            // Heart rate calculation for automatic laps requires that the hr events are listed in
            // order. For some reason there's at least one workout where this is not the case:
            // https://app.suunto.com/move/suzzlo/5be6a59ce883bf11c0a9da5b
            eventHeartRates.sort(Comparator.comparingLong(LegacyHeartRateEvent::getTimestamp));

            eventCount = br.readInt();
            Timber.d("LegacyWorkoutSerializer.read() reading %d location events for laps", eventCount);
            List<LegacyLocationEvent> eventLaps = new ArrayList<>(eventCount);
            previousLocation = null;
            for (int i = 0; i < eventCount; ++i) {
                LegacyLocationEvent lap = LocationEventSerializer.read(br, previousLocation);
                eventLaps.add(lap);
                previousLocation = lap;
            }

            eventCount = br.readInt();
            List<LegacyMediaEvent> eventMedias = new ArrayList<>(eventCount);
            for (int i = 0; i < eventCount; ++i) {
                LegacyMediaEvent event = MediaEventSerializer.read(br);
                eventMedias.add(event);
            }

            eventCount = br.readInt();
            for (int i = 0; i < eventCount; ++i) {
                EventWeatherSerializer.read(br);
            }
            return new LegacyWorkout(eventHeartRates, eventLocations,eventMedias, eventLaps, eventGeneral);
        } catch (IOException e) {
            throw new DeserializationFailedException(e);
        }
    }

    public static void write(DataOutputStream bw, LegacyWorkout wo) throws SerializationFailedException {
        try {
            // general events
            bw.writeInt(wo.getEvents().size());
            for (Event generalEvent : wo.getEvents()) {
                EventSerializer.write(bw, generalEvent);
            }

            // location events
            bw.writeInt(wo.getEventLocations().size());
            LegacyLocationEvent refLocation = null;

            for (LegacyLocationEvent curLocation : wo.getEventLocations()) {
                LocationEventSerializer.write(bw, curLocation, refLocation);
                refLocation = curLocation;
            }

            // air pressure events
            bw.writeInt(0);

            // temperature events
            bw.writeInt(0);

            // heart rate events
            bw.writeInt(wo.getEventHeartRates().size());
            for (LegacyHeartRateEvent hrEvent : wo.getEventHeartRates()) {
                HeartrateEventSerializer.write(bw, hrEvent);
            }

            // lap events
            bw.writeInt(wo.getEventLaps().size());
            for (LegacyLocationEvent event : wo.getEventLaps()) {
                LocationEventSerializer.writeUncompressed(bw, event);
            }

            // media events
            bw.writeInt(wo.getEventMedias().size());
            for (LegacyMediaEvent eventMedia : wo.getEventMedias()) {
                MediaEventSerializer.write(bw, eventMedia);
            }

            // weather events
            bw.writeInt(0);
        } catch (IOException e) {
            throw new SerializationFailedException(e);
        }
    }
}
