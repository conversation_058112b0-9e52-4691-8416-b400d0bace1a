package com.stt.android.tracker.model;

import com.stt.android.tracker.event.Event;
import com.stt.android.tracker.event.LegacyHeartRateEvent;
import com.stt.android.tracker.event.LegacyLocationEvent;
import com.stt.android.tracker.event.LegacyMediaEvent;
import java.util.Collections;
import java.util.List;

public class LegacyWorkout {
    private final List<Event> events;
    private final List<LegacyHeartRateEvent> eventHeartRates;
    private final List<LegacyLocationEvent> eventLocations;
    private final List<LegacyLocationEvent> eventLaps;
    private final List<LegacyMediaEvent> eventMedias;

    public LegacyWorkout(List<LegacyHeartRateEvent> heartRateEvents,
        List<LegacyLocationEvent> eventLocations, List<LegacyMediaEvent> eventMedias,
        List<LegacyLocationEvent> eventLaps, List<Event> generalEvents) {
        this.events = generalEvents;
        this.eventHeartRates = heartRateEvents;
        this.eventLaps = eventLaps;
        this.eventLocations = eventLocations;
        this.eventMedias = eventMedias;
    }

    public static LegacyWorkout createDummy() {
        List<LegacyHeartRateEvent> hrEvents = Collections.emptyList();
        List<LegacyLocationEvent> eventLocations = Collections.emptyList();
        List<LegacyMediaEvent> eventMedias = Collections.emptyList();
        List<LegacyLocationEvent> eventLaps = Collections.emptyList();
        List<Event> generalEvents = Collections.emptyList();
        return new LegacyWorkout(hrEvents, eventLocations, eventMedias, eventLaps, generalEvents);
    }

    public List<LegacyLocationEvent> getEventLocations() {
        return eventLocations;
    }

    public List<LegacyHeartRateEvent> getEventHeartRates() {
        return eventHeartRates;
    }

    /**
     * @return manual laps triggered by the user
     */
    public List<LegacyLocationEvent> getEventLaps() {
        return eventLaps;
    }

    public List<Event> getEvents() {
        return events;
    }

    public List<LegacyMediaEvent> getEventMedias() {
        return eventMedias;
    }
}
