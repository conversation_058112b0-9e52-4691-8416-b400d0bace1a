package com.stt.android.tracker.compat.serialization;

import com.stt.android.domain.workout.WorkoutGeoPoint;
import com.stt.android.tracker.model.LegacyHeader;
import com.stt.android.tracker.model.LegacyHeartRateData;
import com.stt.android.domain.advancedlaps.Statistics;

import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import java.util.TimeZone;

public class HeaderSerializer {

    private static final int GENERAL_EVENTS_FEATURE_FLAG = 0x2;
    private static final int EVENT_LOCATIONS_FEATURE_FLAG = 0x4;
    private static final int EVENT_LAPS_FEATURE_FLAG = 0x8;
    private static final int HR_FEATURE_FLAG = 0x10;
    private static final int EVENT_MEDIA_IMAGES_FEATURE_FLAG = 0x20;
    private static final int EVENT_MEDIA_VIDEOS_FEATURE_FLAG = 0x40;
    private static final int EVENT_MEDIA_SONGS_FEATURE_FLAG = 0x80;
    private static final int EVENT_WEATHER_FEATURE_FLAG = 0x100;
    private static final int STEP_COUNT_FEATURE_FLAG = 0x200;
    private static final int MANUALLY_ADDED_FEATURE_FLAG = 0x800;
    private static final int NST_IMPORTED_FEATURE_FLAG = 0x1000;
    private static final int MAX_DESCRIPTION_LENGTH = 256;
    private static final int MAX_USERNAME_LENGTH = 32;
    private static final int MAX_WORKOUT_NAME_LENGTH = 32;
    private static final int SYMBIAN_APP_ID = 239678952;

    /**
     * Do not use this method when storing updated workouts as some values will be overwritten and not from the original workout (e.g.
     * version number, application ID...)
     *
     * @param stream
     * @param header
     * @param userWeight
     * @throws SerializationFailedException
     */
    public static void write(DataOutputStream stream, LegacyHeader header, int userWeight) throws SerializationFailedException {
        try {
            // Version number
            stream.writeInt(LegacyHeader.getVersionNumber());
            // header size
            stream.writeInt(LegacyHeader.getHeaderSize());
            stream.writeInt(header.getApplicationId());
            stream.writeInt(header.getMachineId());

            int featureFlags = 0;

            if (header.getEventGeneralsCount() > 0) {
                featureFlags |= GENERAL_EVENTS_FEATURE_FLAG;
            }
            if (header.getEventLocationsCount() > 0) {
                featureFlags |= EVENT_LOCATIONS_FEATURE_FLAG;
            }
            if (header.getEventLapsCount() > 0) {
                featureFlags |= EVENT_LAPS_FEATURE_FLAG;
            }
            // FIXME: There's no reliable way to detect if there's HR data or not. For example, a manually created workout might not have
            //  HR events at all but user has set average/max/energy...
            if (header.getHrData() != null) {
                featureFlags |= HR_FEATURE_FLAG;
            }
            if (header.getEventMediaImages() > 0) {
                featureFlags |= EVENT_MEDIA_IMAGES_FEATURE_FLAG;
            }
            // Ignore event media videos
            // Ignore event media songs
            // Ignore event weather
            // Ignore step count
            if (header.getStepCount() > 0) {
                featureFlags |= STEP_COUNT_FEATURE_FLAG;
            }

            if (header.isManuallyAdded()) {
                featureFlags |= MANUALLY_ADDED_FEATURE_FLAG;
            }
            // Ignore nst imported

            // features
            stream.writeInt(0);
            stream.writeShort(featureFlags);
            stream.writeShort(header.getSharingFlags());

            stream.writeInt(header.getWorkoutId());
            String description = header.getDescription();
            if (description == null) {
                description = "";
            } else if (description.length() > MAX_DESCRIPTION_LENGTH) {
                description = description.substring(0, MAX_DESCRIPTION_LENGTH);
            }
            stream.writeUTF(description);
            // key
            stream.writeUTF("");
            // user ID
            stream.writeInt(0);

            // this should be route ID, but used for average and max cadence to keep compatibility with iOS
            stream.writeInt((header.getAverageCadence() << 16) | header.getMaxCadence());

            stream.writeInt(header.getActivityId());
            String userName = header.getUserName();
            if (userName.length() > MAX_USERNAME_LENGTH) {
                userName = userName.substring(0, MAX_USERNAME_LENGTH);
            }
            stream.writeUTF(userName);
            String workoutName = header.getWorkoutName();
            if (workoutName.length() > MAX_WORKOUT_NAME_LENGTH) {
                workoutName = workoutName.substring(0, MAX_WORKOUT_NAME_LENGTH);
            }
            stream.writeUTF(workoutName);
            long startTime = header.getStartTime();
            stream.writeInt(getCurrentTimeOffset(startTime));

            stream.writeDouble(startTime);
            stream.writeDouble(header.getStopTime());
            // Binary format requires centiseconds
            stream.writeInt((int) (header.getTotalTime() * 100.0));
            // Binary format requires centimeters
            stream.writeInt((int) (header.getTotalDistance() * 100.0));
            // weight in kg
            stream.writeInt(userWeight);
            stream.writeInt(header.getEnergy());

            stream.writeByte(header.getMeasurementUnit());
            stream.writeUTF(header.getMcc());
            stream.writeUTF(header.getMnc());

            WorkoutGeoPoint startPosition = header.getStartPosition();
            if (startPosition == null) {
                // we need at least a dummy position
                startPosition = new WorkoutGeoPoint(0, 0, 0, false, 0.0f, 0.0, 0.0, 0.0, 0.0f, 0);
            }
            CoordinatesSerializer.write(stream, startPosition);
            WorkoutGeoPoint stopPosition = header.getStopPosition();
            if (stopPosition == null) {
                // we need at least a dummy position
                stopPosition = new WorkoutGeoPoint(0, 0, 0, false, 0.0f, 0.0, 0.0, 0.0, 0.0f, 0);
            }
            CoordinatesSerializer.write(stream, stopPosition);
            StatisticsSerializer.write(stream, header.getStatLatitude());
            StatisticsSerializer.write(stream, header.getStatLongitude());
            StatisticsSerializer.write(stream, header.getStatAltitude());
            StatisticsSerializer.write(stream, header.getStatSpeed());
            StatisticsSerializer.write(stream, header.getStatHeartRate());

            // Binary only supports integer for altitude offset
            stream.writeInt((int) header.getAltitudeOffset());
            // modification time
            stream.writeDouble(0.0);
            // sync
            stream.writeShort(0);
            // game points related to some never implemented gamification feature
            stream.writeInt(0);
            if ((featureFlags & GENERAL_EVENTS_FEATURE_FLAG) != 0) {
                stream.writeInt(header.getEventGeneralsCount());
            }
            if ((featureFlags & EVENT_LOCATIONS_FEATURE_FLAG) != 0) {
                stream.writeInt(header.getEventLocationsCount());
            }
            if ((featureFlags & EVENT_LAPS_FEATURE_FLAG) != 0) {
                stream.writeInt(header.getEventLapsCount());
            }
            if ((featureFlags & HR_FEATURE_FLAG) != 0) {
                stream.writeInt(header.getEventHeartratesCount());
                HeartRateDataSerializer.write(stream, header.getHrData());
            }
            if ((featureFlags & EVENT_MEDIA_IMAGES_FEATURE_FLAG) != 0) {
                stream.writeInt(header.getEventMediaImages());
            }
            if ((featureFlags & EVENT_MEDIA_VIDEOS_FEATURE_FLAG) != 0) {
                stream.writeInt(0);
            }
            if ((featureFlags & EVENT_MEDIA_SONGS_FEATURE_FLAG) != 0) {
                stream.writeInt(0);
            }
            if ((featureFlags & EVENT_WEATHER_FEATURE_FLAG) != 0) {
                stream.writeInt(0);
            }
            if ((featureFlags & STEP_COUNT_FEATURE_FLAG) != 0) {
                stream.writeInt(header.getStepCount());
            }

            CoordinatesSerializer.write(stream, header.getCenterPosition());
        } catch (IOException ioe) {
            throw new SerializationFailedException(ioe);
        }
    }

    /**
     * @param time
     * @return the offset in milliseconds from UTC. The offset includes daylight savings time if the given date is
     * within the daylight savings time period.
     */
    private static int getCurrentTimeOffset(long time) {
        // Get the current user time zone
        TimeZone tz = TimeZone.getDefault();
        // Use given time to return an offset which takes into account the daylight savings
        return tz.getOffset(time);
    }

    public static LegacyHeader read(DataInputStream stream) throws DeserializationFailedException {
        try {
            //versionNumber
            stream.readInt();
            // headerSize
            stream.readInt();
            int applicationId = stream.readInt();
            // machineId
            int machineId = stream.readInt();

            // features
            stream.readInt();
            int featureFlags = stream.readUnsignedShort();
            int sharingFlags = stream.readUnsignedShort();

            // Read and discard the workoutId
            int workoutId = stream.readInt();

            String description = stream.readUTF();
            // key
            stream.readUTF();

            // userId
            stream.readInt();

            // this should be route ID, but used for average and max cadence to keep compatibility with iOS
            int cadenceValue = stream.readInt();
            int averageCadence = cadenceValue >> 16;
            int maxCadence = cadenceValue & 0xFFFF;

            int activityId = stream.readInt();
            String userName = stream.readUTF();
            String workoutName = stream.readUTF();

            int timeOffset = stream.readInt();

            long startTime = (long) stream.readDouble();

            long startTimeLocal;
            if (applicationId != 0 && applicationId != SYMBIAN_APP_ID) {
                startTimeLocal = startTime + timeOffset;
            } else {
                startTimeLocal = startTime - timeOffset;
            }

            long stopTime = (long) stream.readDouble();

            long stopTimeLocal;
            if (applicationId != 0 && applicationId != SYMBIAN_APP_ID) {
                stopTimeLocal = stopTime + timeOffset;
            } else {
                stopTimeLocal = stopTime - timeOffset;
            }
            // Binary format provides centiseconds
            double totalTime = stream.readInt() / 100.0;
            // Binary format provides centimeters
            double totalDistance = stream.readInt() / 100.0;
            // weight
            stream.readInt();
            int energy = stream.readInt();

            int measurementUnit = stream.readUnsignedByte();

            String mcc = stream.readUTF();
            String mnc = stream.readUTF();

            WorkoutGeoPoint startPosition = CoordinatesSerializer.read(stream);
            WorkoutGeoPoint stopPosition = CoordinatesSerializer.read(stream);
            Statistics statLatitude = StatisticsSerializer.read(stream);
            Statistics statLongitude = StatisticsSerializer.read(stream);
            Statistics statAltitude = StatisticsSerializer.read(stream);
            Statistics statSpeed = StatisticsSerializer.read(stream);
            Statistics statHeartRate = StatisticsSerializer.read(stream);

            int altitudeOffset = stream.readInt();
            // modificationTime
            stream.readDouble();
            // sync
            stream.readUnsignedShort();
            // points
            stream.readInt();

            int eventGeneralsCount = 0;
            if ((featureFlags & GENERAL_EVENTS_FEATURE_FLAG) != 0) {
                eventGeneralsCount = stream.readInt();
            }
            int eventLocationsCount = 0;
            if ((featureFlags & EVENT_LOCATIONS_FEATURE_FLAG) != 0) {
                eventLocationsCount = stream.readInt();
            }
            int eventLapsCount = 0;
            if ((featureFlags & EVENT_LAPS_FEATURE_FLAG) != 0) {
                eventLapsCount = stream.readInt();
            }
            LegacyHeartRateData hrData = null;
            int eventHeartRatesCount = 0;
            if ((featureFlags & HR_FEATURE_FLAG) != 0) {
                eventHeartRatesCount = stream.readInt();
                hrData = HeartRateDataSerializer.read(stream);
            }
            int eventMediaImages = 0;
            if ((featureFlags & EVENT_MEDIA_IMAGES_FEATURE_FLAG) != 0) {
                eventMediaImages = stream.readInt();
            }
            if ((featureFlags & EVENT_MEDIA_VIDEOS_FEATURE_FLAG) != 0) {
                // eventMediaVideos
                stream.readInt();
            }
            if ((featureFlags & EVENT_MEDIA_SONGS_FEATURE_FLAG) != 0) {
                // eventMediaSongs
                stream.readInt();
            }
            if ((featureFlags & EVENT_WEATHER_FEATURE_FLAG) != 0) {
                // eventWeathersCount
                stream.readInt();
            }
            int stepCount = 0;
            if ((featureFlags & STEP_COUNT_FEATURE_FLAG) != 0) {
                // stepCount
                stepCount = stream.readInt();
            }
            boolean manuallyAdded = (featureFlags & MANUALLY_ADDED_FEATURE_FLAG) != 0;

            WorkoutGeoPoint centerPosition = CoordinatesSerializer.read(stream);
            return new LegacyHeader(sharingFlags, workoutId, activityId, userName, workoutName, description, startTime, stopTime,
                startTimeLocal, stopTimeLocal, totalTime, totalDistance, startPosition, stopPosition, centerPosition, altitudeOffset,
                hrData, eventGeneralsCount, eventLocationsCount, eventLapsCount, eventMediaImages, eventHeartRatesCount,
                averageCadence, maxCadence, measurementUnit, stepCount, manuallyAdded, energy, mcc, mnc, statLatitude, statLongitude,
                statAltitude, statSpeed, statHeartRate, machineId, applicationId);
        } catch (IOException ioe) {
            throw new DeserializationFailedException(ioe);
        }

    }
}
