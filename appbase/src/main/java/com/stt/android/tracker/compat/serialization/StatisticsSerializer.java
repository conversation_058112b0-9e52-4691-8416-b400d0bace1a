
package com.stt.android.tracker.compat.serialization;

import com.stt.android.domain.advancedlaps.Statistics;

import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;

public class StatisticsSerializer {

    public static void write(DataOutputStream stream, Statistics stats) throws SerializationFailedException {
        try {
            stream.writeFloat((float) stats.getLastValue());
            stream.writeInt(stats.getCount());
            stream.writeFloat((float) stats.getSum());
            stream.writeFloat((float) stats.getMin());
            stream.writeFloat((float) stats.getMax());
            stream.writeInt(stats.getIndexMin());
            stream.writeInt(stats.getIndexMax());
            stream.writeInt(stats.getCountZero());
            stream.writeInt(stats.getCountPositive());
            stream.writeInt(stats.getCountNegative());
            stream.writeFloat((float) stats.getDeltaUp());
            stream.writeFloat((float) stats.getDeltaDown());
        } catch (IOException ioe) {
            throw new SerializationFailedException(ioe);
        }
    }

    public static Statistics read(DataInputStream stream) throws DeserializationFailedException {
        try {
            Statistics stats = new Statistics();
            stats.setLastValue(stream.readFloat());
            stats.setCount(stream.readInt());
            stats.setSum(stream.readFloat());
            stats.setMin(stream.readFloat());
            stats.setMax(stream.readFloat());
            stats.setIndexMin(stream.readInt());
            stats.setIndexMax(stream.readInt());
            stats.setCountZero(stream.readInt());
            stats.setCountPositive(stream.readInt());
            stats.setCountNegative(stream.readInt());
            stats.setDeltaUp(stream.readFloat());
            stats.setDeltaDown(stream.readFloat());
            return stats;
        } catch (IOException ioe) {
            throw new DeserializationFailedException(ioe);
        }
    }
}
