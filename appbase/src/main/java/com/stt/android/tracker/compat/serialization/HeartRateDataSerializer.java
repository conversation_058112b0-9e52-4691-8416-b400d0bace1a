package com.stt.android.tracker.compat.serialization;

import com.stt.android.tracker.model.LegacyHeartRateData;

import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;

public class HeartRateDataSerializer {
    private final static int VERSION_NUMBER = 1;
    /**
     * FYI, in legacy it was written as "~(~0 << 3)"
     */
    private static final int VERSION_NUMBER_MASK = 7;

    public static LegacyHeartRateData read(DataInputStream br) throws DeserializationFailedException {
        try {
            LegacyHeartRateData data = new LegacyHeartRateData();

            // read header and discard it.
            br.readByte();

            // Read hr distribution data.
            int barcount = br.readUnsignedByte();
            long[] hrBarData = new long[LegacyHeartRateData.MAX_NUMBER_BARS];
            for (int i = 0; i < barcount; i++) {
                int idx = br.readUnsignedByte();
                hrBarData[idx] = br.readInt() * 10;
            }
            data.setHrDistribution(hrBarData);

            // settings
            data.setThreshold1(br.readUnsignedByte());
            data.setThreshold2(br.readUnsignedByte());
            data.setThreshold3(br.readUnsignedByte());
            data.setThreshold4(br.readUnsignedByte());
            data.setThresholdMax(br.readUnsignedByte());

            // values
            data.setCount(br.readInt());
            data.setHeartrateMin(br.readUnsignedByte());
            data.setHeartrateMax(br.readUnsignedByte());
            data.setHeartrateAvg(br.readUnsignedByte());
            data.setEnergyConsumption(br.readInt());

            // zone aggregates
            data.setTimeInZone0(br.readInt() * 10); // 1/100 secs
            data.setTimeInZone1(br.readInt() * 10); // 1/100 secs
            data.setTimeInZone2(br.readInt() * 10); // 1/100 secs
            data.setTimeInZone3(br.readInt() * 10); // 1/100 secs
            data.setTimeInZone4(br.readInt() * 10); // 1/100 secs
            data.setTimeInZone5(br.readInt() * 10); // 1/100 secs

            return data;
        } catch (IOException ioe) {
            throw new DeserializationFailedException(ioe);
        }
    }

    public static void write(DataOutputStream bw, LegacyHeartRateData data) throws SerializationFailedException {
        try {
            // Store the version number filtering out the three less significant bits
            bw.writeByte((byte) (VERSION_NUMBER & VERSION_NUMBER_MASK));

            long[] hrData = data.getHrDistribution();

            int barcount = 0;
            for (int i = 0; i < LegacyHeartRateData.MAX_NUMBER_BARS; i++) {
                if (hrData[i] > 0) barcount++;
            }
            bw.write((byte) barcount);

            for (int i = 0; i < LegacyHeartRateData.MAX_NUMBER_BARS; i++) {
                // We only write useful values
                if (hrData[i] > 0) {
                    // Write the index
                    bw.writeByte((byte) i);
                    // Then write the actual value in centiseconds
                    bw.writeInt((int) (hrData[i] / 10));
                }
            }

            // settings
            bw.writeByte((byte) data.getThreshold1());
            bw.writeByte((byte) data.getThreshold2());
            bw.writeByte((byte) data.getThreshold3());
            bw.writeByte((byte) data.getThreshold4());
            bw.writeByte((byte) data.getThresholdMax());

            // values
            bw.writeInt(data.getCount());
            bw.writeByte((byte) data.getHeartrateMin());
            bw.writeByte((byte) data.getHeartrateMax());
            bw.writeByte((byte) data.getHeartrateAvg());
            bw.writeInt(data.getEnergyConsumption());

            // internal representation is 1/1000 secs, convert to 1/100 secs
            bw.writeInt(data.getTimeInZone0() / 10);
            bw.writeInt(data.getTimeInZone1() / 10);
            bw.writeInt(data.getTimeInZone2() / 10);
            bw.writeInt(data.getTimeInZone3() / 10);
            bw.writeInt(data.getTimeInZone4() / 10);
            bw.writeInt(data.getTimeInZone5() / 10);
        } catch (IOException ioe) {
            throw new SerializationFailedException(ioe);
        }
    }
}
