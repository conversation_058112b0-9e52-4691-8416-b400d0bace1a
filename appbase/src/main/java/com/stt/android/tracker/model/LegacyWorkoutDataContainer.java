package com.stt.android.tracker.model;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.stt.android.domain.Point;
import com.stt.android.domain.advancedlaps.Statistics;
import com.stt.android.domain.user.ImageInformation;
import com.stt.android.domain.user.MeasurementUnit;
import com.stt.android.domain.user.MeasurementUnitKt;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.domain.workout.Workout;
import com.stt.android.domain.workout.WorkoutData;
import com.stt.android.domain.workout.WorkoutGeoPoint;
import com.stt.android.domain.workout.WorkoutHrEvent;
import com.stt.android.laps.CompleteLap;
import com.stt.android.laps.CompleteLapFactory;
import com.stt.android.laps.Laps;
import com.stt.android.tracker.event.Event;
import com.stt.android.tracker.event.LegacyHeartRateEvent;
import com.stt.android.tracker.event.LegacyLocationEvent;
import com.stt.android.tracker.event.LegacyMediaEvent;
import com.stt.android.tracker.event.LegacyMediaEvent.MediaType;
import com.stt.android.workouts.AltitudeChangeCalculator;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class LegacyWorkoutDataContainer {
    private final LegacyHeader legacyHeader;
    private final LegacyServiceHeader legacyServiceHeader;
    private final LegacyWorkout legacyWorkout;
    private final AltitudeChangeCalculator altitudeChangeCalculator = new AltitudeChangeCalculator();

    public LegacyWorkoutDataContainer(Workout workout, int machineId, int applicationId) {
        this(workout.getHeader(), workout.getData(), workout.getPictures(), machineId,
            applicationId);
    }

    private LegacyWorkoutDataContainer(WorkoutHeader header, WorkoutData data,
        List<ImageInformation> pictures, int machineId, int applicationId) {
        this(header, data.getRoutePoints(), data.getHeartRateEvents(), data.getManualLaps(),
            data.getEvents(), data.getAltitudeStatistics(), data.getHeartRateStatistics(),
            data.getLatitudeStatistics(), data.getLongitudeStatistics(), data.getSpeedStatistics(),
            data.getMeasurementUnit(), data.getAltitudeOffset(),
            new WorkoutGeoPoint((int) data.getLatitudeStatistics().getAvg(),
                (int) data.getLongitudeStatistics().getAvg(), 0.0, false, 0.0f, 0.0, 0.0, 0.0, 0,
                0), data.getHeartRateMaximum(), pictures, data.getMobileNetworkCode(),
            data.getMobileCountryCode(), machineId, applicationId);
    }

    private LegacyWorkoutDataContainer(WorkoutHeader header, List<WorkoutGeoPoint> routePoints,
        List<WorkoutHrEvent> heartRateEvents, List<CompleteLap> laps, List<Event> events,
        Statistics statsAltitude, Statistics statsHeartRate, Statistics statsLatitude,
        Statistics statsLongitude, Statistics statsSpeed, MeasurementUnit measurementUnit,
        float altitudeOffset, WorkoutGeoPoint centerPosition, int hrMax,
        List<ImageInformation> pictures, String mobileNetworkCode, String mobileCountryCode,
        int machineId, int applicationId) {
        List<LegacyLocationEvent> locationEvents = new ArrayList<>(routePoints.size());
        for (WorkoutGeoPoint p : routePoints) {
            LegacyLocationEvent event = new LegacyLocationEvent(p);
            locationEvents.add(event);
            if (p.hasAltitude()) {
                altitudeChangeCalculator.addAltitude(p.getAltitude());
            }
        }

        List<LegacyHeartRateEvent> legacyHeartRateEvents = new ArrayList<>(heartRateEvents.size());
        for (WorkoutHrEvent e : heartRateEvents) {
            LegacyHeartRateEvent legacyHrEvent =
                new LegacyHeartRateEvent(e.getMillisecondsInWorkout(), e.getTimestamp(),
                    e.getRawData(), e.getHeartRate());
            legacyHeartRateEvents.add(legacyHrEvent);
        }

        List<LegacyLocationEvent> eventLaps = new ArrayList<>(laps.size());
        for (CompleteLap lap : laps) {
            eventLaps.add(new LegacyLocationEvent(lap));
        }

        List<LegacyMediaEvent> eventMedias = new ArrayList<>(pictures.size());
        for (ImageInformation picture : pictures) {
            Date timestamp = new Date(picture.getTimestamp());
            LegacyMediaEvent mediaEvent = new LegacyMediaEvent(picture.getId(), picture.getKey(),
                (long) picture.getTotalTime(), timestamp, MediaType.IMAGE);
            Point location = picture.getLocation();
            WorkoutGeoPoint coordinate;
            if (location == null) {
                // Use dummy coordinate with lat/long 0,0
                coordinate = new WorkoutGeoPoint(0, 0, 0, false, 0, 0, picture.getTotalTime(), 0, 0,
                    picture.getTimestamp());
            } else {
                coordinate =
                    new WorkoutGeoPoint(location.getLatitudeE6(), location.getLongitudeE6(), 0,
                        false, 0, 0, picture.getTotalTime(), 0, 0, picture.getTimestamp());
            }
            mediaEvent.setCoordinate(coordinate);
            eventMedias.add(mediaEvent);
        }

        legacyHeader =
            new LegacyHeader(header, measurementUnit, events.size(), eventLaps.size(), routePoints,
                statsAltitude, statsHeartRate, statsLatitude, statsLongitude, statsSpeed,
                altitudeOffset, centerPosition, hrMax, mobileNetworkCode, mobileCountryCode,
                machineId, applicationId, heartRateEvents);
        legacyWorkout =
            new LegacyWorkout(legacyHeartRateEvents, locationEvents, eventMedias, eventLaps,
                events);
        legacyServiceHeader =
            new LegacyServiceHeader(header.getCommentCount(), header.getViewCount());
    }

    public LegacyWorkoutDataContainer(LegacyHeader legacyHeader, LegacyWorkout legacyWorkout,
        LegacyServiceHeader legacyServiceHeader) {
        this.legacyHeader = legacyHeader;
        this.legacyWorkout = legacyWorkout;
        this.legacyServiceHeader = legacyServiceHeader;
    }

    public LegacyHeader getLegacyHeader() {
        return legacyHeader;
    }

    public LegacyServiceHeader getLegacyServiceHeader() {
        return legacyServiceHeader;
    }

    public LegacyWorkout getLegacyWorkout() {
        return legacyWorkout;
    }

    public WorkoutData getWorkoutData() {
        // Complete laps depend on other events like altitude and HR
        List<ManualLapBuilder> manualLapBuilders =
            getManualLapBuilders(legacyWorkout.getEventLaps());

        ManualLapBuilder currentManualLapBuilder = null;
        Iterator<ManualLapBuilder> unprocessedManualLapBuilders = manualLapBuilders.iterator();
        if (unprocessedManualLapBuilders.hasNext()) {
            currentManualLapBuilder = unprocessedManualLapBuilders.next();
        }

        List<LegacyLocationEvent> locationEvents = legacyWorkout.getEventLocations();
        List<WorkoutGeoPoint> routePoints = new ArrayList<>(locationEvents.size());
        AltitudeChangeCalculator altitudeChangeCalculator = new AltitudeChangeCalculator();
        // Iterate through all the location events and create own WorkoutGeoPoints at the same
        // time that we figure out the right ascend/descend for the manual laps
        LegacyLocationEvent previousLocation = null;
        for (LegacyLocationEvent location : locationEvents) {
            WorkoutGeoPoint workoutGeoPoint = location.getWorkoutGeoPoint();

            // Some workouts have duplicates of earlier, real location events at the end of the list or near pauses.
            // Skip duplicated events in the list to prevent the duplicated locations leaking into the app logic.
            // See TP#130683 and TP#133666
            if (shouldSkipEvent(routePoints, location, previousLocation)) {
                previousLocation = location;
                continue;
            }

            routePoints.add(workoutGeoPoint);
            if (currentManualLapBuilder != null
                && workoutGeoPoint.getMillisecondsInWorkout()
                <= currentManualLapBuilder.workoutDurationOnEnd) {
                altitudeChangeCalculator.addAltitude(workoutGeoPoint.getAltitude());
                currentManualLapBuilder.totalAscent(altitudeChangeCalculator.getTotalAscent());
                currentManualLapBuilder.totalDescent(altitudeChangeCalculator.getTotalDescent());
                currentManualLapBuilder.minAltitude(altitudeChangeCalculator.getMinAltitude());
                currentManualLapBuilder.maxAltitude(altitudeChangeCalculator.getMaxAltitude());
            } else if (unprocessedManualLapBuilders.hasNext()) {
                altitudeChangeCalculator = new AltitudeChangeCalculator();
                currentManualLapBuilder = unprocessedManualLapBuilders.next();
            } else {
                currentManualLapBuilder = null;
            }
            previousLocation = location;
        }

        currentManualLapBuilder = null;
        unprocessedManualLapBuilders = manualLapBuilders.iterator();
        if (unprocessedManualLapBuilders.hasNext()) {
            currentManualLapBuilder = unprocessedManualLapBuilders.next();
        }
        List<LegacyHeartRateEvent> legacyEventHeartRates = legacyWorkout.getEventHeartRates();
        List<WorkoutHrEvent> heartRateEvents = new ArrayList<>(legacyEventHeartRates.size());
        Statistics lapWorkoutHrStatistics = new Statistics();
        for (LegacyHeartRateEvent legacyHeartRateEvent : legacyEventHeartRates) {
            long timestamp = legacyHeartRateEvent.getTimestamp();
            int heartRateInBpm = legacyHeartRateEvent.getHeartrate();
            int[] binaryData = legacyHeartRateEvent.getFrame();
            // We need to transform from 1/100th of a second to milliseconds
            long millisecondsInWorkout = legacyHeartRateEvent.getTimeoffset() * 10;
            WorkoutHrEvent heartRateEvent =
                new WorkoutHrEvent(timestamp, heartRateInBpm, binaryData, millisecondsInWorkout);
            heartRateEvents.add(heartRateEvent);

            if (currentManualLapBuilder != null
                && heartRateEvent.getMillisecondsInWorkout()
                <= currentManualLapBuilder.workoutDurationOnEnd) {
                lapWorkoutHrStatistics.addSample(heartRateEvent.getHeartRate());
                currentManualLapBuilder.avgHeartRate(
                    (int) Math.round(lapWorkoutHrStatistics.getAvg()));
            } else if (unprocessedManualLapBuilders.hasNext()) {
                lapWorkoutHrStatistics = new Statistics();
                currentManualLapBuilder = unprocessedManualLapBuilders.next();
            } else {
                currentManualLapBuilder = null;
            }
        }
        List<CompleteLap> manualLaps = new ArrayList<>(manualLapBuilders.size());
        for (ManualLapBuilder manualLapBuilder : manualLapBuilders) {
            manualLaps.add(manualLapBuilder.build());
        }

        List<Event> events = legacyWorkout.getEvents();
        Statistics longitudeStatistics = legacyHeader.getStatLongitude();
        Statistics latitudeStatistics = legacyHeader.getStatLatitude();
        Statistics altitudeStatistics = legacyHeader.getStatAltitude();
        Statistics centimeterSpeedStatistics = legacyHeader.getStatSpeed();
        Statistics speedStatistics = new Statistics();
        speedStatistics.setAvg(centimeterSpeedStatistics.getAvg() / 100);
        speedStatistics.setDeltaDown(centimeterSpeedStatistics.getDeltaDown() / 100);
        speedStatistics.setDeltaUp(centimeterSpeedStatistics.getDeltaUp() / 100);
        speedStatistics.setLastValue(centimeterSpeedStatistics.getLastValue() / 100);
        speedStatistics.setMax(centimeterSpeedStatistics.getMax() / 100);
        speedStatistics.setMin(centimeterSpeedStatistics.getMin() / 100);
        speedStatistics.setSum(centimeterSpeedStatistics.getSum() / 100);

        Statistics heartRateStatistics = legacyHeader.getStatHeartRate();

        Statistics cadenceStatistics = new Statistics();
        cadenceStatistics.setAvg(legacyHeader.getAverageCadence());
        cadenceStatistics.setMax(legacyHeader.getMaxCadence());

        float altitudeOffset = legacyHeader.getAltitudeOffset();
        LegacyHeartRateData hrData = legacyHeader.getHrData();
        int heartRateMaximum = 0;
        if (hrData != null) {
            heartRateMaximum = hrData.getThresholdMax();
        }

        String mcc = legacyHeader.getMcc();
        String mnc = legacyHeader.getMnc();

        MeasurementUnit measurementUnit =
            MeasurementUnitKt.valueOf(legacyHeader.getMeasurementUnit());
        return new WorkoutData(routePoints, heartRateEvents, manualLaps, measurementUnit, events,
            longitudeStatistics, latitudeStatistics, altitudeStatistics, speedStatistics,
            heartRateStatistics, cadenceStatistics, altitudeOffset, heartRateMaximum, mnc, mcc);
    }

    private boolean shouldSkipEvent(List<WorkoutGeoPoint> routePoints,
        LegacyLocationEvent current,
        @Nullable LegacyLocationEvent previous) {
        long currentEventTimestamp = current.getWorkoutGeoPoint().getTimestamp();

        if (!routePoints.isEmpty()
            && routePoints.get(routePoints.size() - 1).getTimestamp() > currentEventTimestamp) {
            // skip event if it's older than latest item in routepoints list
            return true;
        } else if (!routePoints.isEmpty()
            && routePoints.get(routePoints.size() - 1).getTimestamp() == currentEventTimestamp
            && (previous != null
            && previous.getWorkoutGeoPoint().getTimestamp() != currentEventTimestamp)) {
            // skip event with duplicate timestamp if it's not previous in original event list
            return true;
        } else {
            return false;
        }
    }

    @NonNull
    private List<ManualLapBuilder> getManualLapBuilders(
        List<LegacyLocationEvent> legacyLapLocationEvents) {
        List<ManualLapBuilder> manualLapBuilders = new ArrayList<>(legacyLapLocationEvents.size());
        int previousDuration = 0;
        double previousDistance = 0;
        for (LegacyLocationEvent legacyLocationEvent : legacyLapLocationEvents) {
            manualLapBuilders.add(new ManualLapBuilder().locationEvent(legacyLocationEvent)
                .previousDistance(previousDistance)
                .previousDuration(previousDuration));
            previousDuration = (int) (legacyLocationEvent.getTotalTimeInSeconds() * 1000);
            previousDistance = legacyLocationEvent.getTotalDistance();
        }
        return manualLapBuilders;
    }

    private static class ManualLapBuilder {
        private int avgHeartRate;
        private double totalAscent;
        private double totalDescent;
        private double minAltitude;
        private double maxAltitude;
        private int previousDuration;
        private double previousDistance;
        private int workoutDurationOnEnd;
        private double currentDistance;
        private WorkoutGeoPoint endLocation;
        private long endTimestamp;

        public ManualLapBuilder locationEvent(LegacyLocationEvent locationEvent) {
            this.workoutDurationOnEnd = (int) (locationEvent.getTotalTimeInSeconds() * 1000);
            this.currentDistance = locationEvent.getTotalDistance();
            this.endLocation = locationEvent.getWorkoutGeoPoint();
            this.endTimestamp = locationEvent.getRealTime();
            return this;
        }

        public ManualLapBuilder totalAscent(double totalAscent) {
            this.totalAscent = totalAscent;
            return this;
        }

        public ManualLapBuilder totalDescent(double totalDescent) {
            this.totalDescent = totalDescent;
            return this;
        }

        public ManualLapBuilder minAltitude(double minAltitude) {
            this.minAltitude = minAltitude;
            return this;
        }

        public ManualLapBuilder maxAltitude(double maxAltitude) {
            this.maxAltitude = maxAltitude;
            return this;
        }

        public ManualLapBuilder avgHeartRate(int avgHeartRate) {
            this.avgHeartRate = avgHeartRate;
            return this;
        }

        public ManualLapBuilder previousDuration(int previousDuration) {
            this.previousDuration = previousDuration;
            return this;
        }

        public ManualLapBuilder previousDistance(double previousDistance) {
            this.previousDistance = previousDistance;
            return this;
        }

        public CompleteLap build() {
            return CompleteLapFactory.create(previousDuration, workoutDurationOnEnd,
                previousDistance, currentDistance, endLocation, endTimestamp, Laps.Type.MANUAL,
                null, totalAscent, totalDescent, minAltitude, maxAltitude, avgHeartRate);
        }
    }
}
