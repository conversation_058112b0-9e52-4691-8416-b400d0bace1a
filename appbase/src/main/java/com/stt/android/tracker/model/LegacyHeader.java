package com.stt.android.tracker.model;

import com.stt.android.domain.advancedlaps.Statistics;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.domain.workout.WorkoutGeoPoint;
import com.stt.android.domain.workout.WorkoutHrEvent;
import com.stt.android.tracker.compat.serialization.HeaderSerializer;
import com.stt.android.domain.user.MeasurementUnit;

import com.stt.android.hr.HeartRateZone;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;

/**
 * Holds the required data representing a workout header for legacy systems.
 * Basically used in
 * {@link HeaderSerializer#write(java.io.DataOutputStream, LegacyHeader, int)} and
 * {@link HeaderSerializer#read(java.io.DataInputStream)}
 */
public class LegacyHeader {
    private static final int VERSION_NUMBER = 20000;
    private static final short HEADER_SIZE = 2048;
    /*
     * This is bad. We should not be using the default locale since each client
     * will use its own date format. But this is how the legacy android client
     * did it.
     */
    private static SimpleDateFormat df = new SimpleDateFormat();
    private final int sharingFlags;
    private final int workoutId;
    private final int activityId;
    private final String userName;
    private final String workoutName;
    private final String description;
    private final long startTime;
    private final long stopTime;
    private final long startTimeLocal;
    private final long stopTimeLocal;
    private final double totalTime;
    private final double totalDistance;
    private final WorkoutGeoPoint startPosition;
    private final WorkoutGeoPoint stopPosition;
    private final WorkoutGeoPoint centerPosition;
    private final float altitudeOffset;
    private final LegacyHeartRateData hrData;
    private final int eventGeneralsCount;
    private final int eventLocationsCount;
    private final int eventLapsCount;
    private final int eventMediaImages;
    private final int eventHeartratesCount;
    private final int averageCadence;
    private final int maxCadence;
    private final int measurementUnit;
    private final int stepCount;
    private final boolean isManuallyAdded;
    private final int energy;
    private final String mcc;
    private final String mnc;
    private final Statistics statLatitude;
    private final Statistics statLongitude;
    private final Statistics statAltitude;
    /**
     * Speed statistics in cm/s
     */
    private final Statistics statSpeed;
    private final Statistics statHeartRate;
    private final int machineId;
    private final int applicationId;

    public LegacyHeader(int sharingFlags, int workoutId, int activityId, String userName, String workoutName, String description,
        long startTime, long stopTime, long startTimeLocal, long stopTimeLocal, double totalTime, double totalDistance,
        WorkoutGeoPoint startPosition, WorkoutGeoPoint stopPosition, WorkoutGeoPoint centerPosition,
        float altitudeOffset, LegacyHeartRateData hrData, int eventGeneralsCount, int eventLocationsCount,
        int eventLapsCount, int eventMediaImages, int eventHeartratesCount, int averageCadence, int maxCadence,
        int measurementUnit, int stepCount, boolean isManuallyAdded, int energy, String mcc, String mnc, Statistics statLatitude,
        Statistics statLongitude, Statistics statAltitude, Statistics statSpeed, Statistics statHeartRate, int machineId,
        int applicationId) {
        this.sharingFlags = sharingFlags;
        this.workoutId = workoutId;
        this.activityId = activityId;
        this.userName = userName;
        this.workoutName = workoutName;
        this.description = description;
        this.startTime = startTime;
        this.stopTime = stopTime;
        this.startTimeLocal = startTimeLocal;
        this.stopTimeLocal = stopTimeLocal;
        this.totalTime = totalTime;
        this.totalDistance = totalDistance;
        this.startPosition = startPosition;
        this.stopPosition = stopPosition;
        this.centerPosition = centerPosition;
        this.altitudeOffset = altitudeOffset;
        this.hrData = hrData;
        this.eventGeneralsCount = eventGeneralsCount;
        this.eventLocationsCount = eventLocationsCount;
        this.eventLapsCount = eventLapsCount;
        this.eventMediaImages = eventMediaImages;
        this.eventHeartratesCount = eventHeartratesCount;
        this.averageCadence = averageCadence;
        this.maxCadence = maxCadence;
        this.measurementUnit = measurementUnit;
        this.stepCount = stepCount;
        this.isManuallyAdded = isManuallyAdded;
        this.energy = energy;
        this.mcc = mcc;
        this.mnc = mnc;
        this.statLatitude = statLatitude;
        this.statLongitude = statLongitude;
        this.statAltitude = statAltitude;
        this.statSpeed = statSpeed;
        this.statHeartRate = statHeartRate;
        this.machineId = machineId;
        this.applicationId = applicationId;
    }

    public LegacyHeader(WorkoutHeader workoutHeader, MeasurementUnit measurementUnit, int eventAmount,
        int lapEventAmount, List<WorkoutGeoPoint> routePoints, Statistics statsAltitude, Statistics statsHeartRate,
        Statistics statsLatitude, Statistics statsLongitude, Statistics statsSpeed, float altitudeOffset,
        WorkoutGeoPoint centerPosition, int hrMax, String mobileNetworkCode,
        String mobileCountryCode, int machineId, int applicationId, List<WorkoutHrEvent> heartRateEvents) {
        this.activityId = workoutHeader.getActivityType().getId();
        this.averageCadence = workoutHeader.getAverageCadence();
        this.maxCadence = workoutHeader.getMaxCadence();
        this.altitudeOffset = altitudeOffset;
        this.centerPosition = centerPosition;
        this.description = workoutHeader.getDescription();
        this.userName = workoutHeader.getUsername();
        this.energy = (int) workoutHeader.getEnergyConsumption();
        this.eventGeneralsCount = eventAmount;
        this.eventHeartratesCount = heartRateEvents.size();
        this.eventLapsCount = lapEventAmount;
        int amountRoutePoints = routePoints.size();
        this.eventLocationsCount = amountRoutePoints;
        this.eventMediaImages = workoutHeader.getPictureCount();

        int hrThreshold1 = HeartRateZone.WARMUP.getHighBpm(hrMax);
        int hrThreshold2 = HeartRateZone.ENDURANCE.getHighBpm(hrMax);
        int hrThreshold3 = HeartRateZone.AEROBIC.getHighBpm(hrMax);
        int hrThreshold4 = HeartRateZone.ANAEROBIC.getHighBpm(hrMax);
        LegacyHeartRateData hrdata = new LegacyHeartRateData(hrThreshold1, hrThreshold2, hrThreshold3, hrThreshold4);
        hrdata.setThresholdMax(hrMax);
        hrdata.setHeartrateAvg((int) workoutHeader.getHeartRateAverage());
        hrdata.setHeartrateMax((int) workoutHeader.getHeartRateMax());
        hrdata.setEnergyConsumption((int) workoutHeader.getEnergyConsumption());
        hrdata.setCount(eventHeartratesCount);
        hrdata.createHrDistributionAndTimeZones(heartRateEvents, (long) workoutHeader.getTotalTime());
        this.hrData = hrdata;

        this.startTime = workoutHeader.getStartTime();
        this.workoutId = generateWorkoutId(startTime);
        this.workoutName = generateWorkoutName(startTime);
        this.mnc = mobileNetworkCode;
        this.mcc = mobileCountryCode;

        this.stepCount = workoutHeader.getStepCount();
        // Only true if entered via the web service.
        this.isManuallyAdded = workoutHeader.getManuallyAdded();
        this.measurementUnit = measurementUnit.getKey();
        this.sharingFlags = workoutHeader.getSharingFlags();

        WorkoutGeoPoint startPosition = null;
        if (amountRoutePoints > 0) {
            startPosition = routePoints.get(0);
        }

        WorkoutGeoPoint stopPosition = null;
        if (amountRoutePoints > 0) {
            stopPosition = routePoints.get(amountRoutePoints - 1);
        }

        this.startPosition = startPosition;
        this.startTimeLocal = startTime;

        this.stopPosition = stopPosition;
        this.stopTime = workoutHeader.getStopTime();
        this.stopTimeLocal = stopTime;

        this.statAltitude = statsAltitude;
        this.statHeartRate = statsHeartRate;
        this.statLatitude = statsLatitude;
        this.statLongitude = statsLongitude;
        Statistics statsSpeedCmPerS = new Statistics();
        statsSpeedCmPerS.setAvg(statsSpeed.getAvg() * 100);
        statsSpeedCmPerS.setDeltaDown(statsSpeed.getDeltaDown() * 100);
        statsSpeedCmPerS.setDeltaUp(statsSpeed.getDeltaUp() * 100);
        statsSpeedCmPerS.setLastValue(statsSpeed.getLastValue() * 100);
        statsSpeedCmPerS.setMax(statsSpeed.getMax() * 100);
        statsSpeedCmPerS.setMin(statsSpeed.getMin() * 100);
        statsSpeedCmPerS.setSum(statsSpeed.getSum() * 100);
        this.statSpeed = statsSpeedCmPerS;

        this.totalTime = workoutHeader.getTotalTime();
        this.totalDistance = workoutHeader.getTotalDistance();
        this.machineId = machineId;
        this.applicationId = applicationId;
    }

    /**
     * @param startTime
     * @return a string representation of the start time that can be used as
     * workout name.
     */
    private static String generateWorkoutName(long startTime) {
        return df.format(startTime);
    }

    /**
     * @param referenceTimestamp
     * @return an ID that is used by legacy clients to identify the workout.
     */
    private static int generateWorkoutId(long referenceTimestamp) {
        Calendar cal = Calendar.getInstance();
        cal.clear();
        cal.set(2000, Calendar.JANUARY, 1); // 2000-01-01
        return (int) ((referenceTimestamp - cal.getTimeInMillis()) / 1000.0);
    }

    public static int getVersionNumber() {
        return VERSION_NUMBER;
    }

    public static short getHeaderSize() {
        return HEADER_SIZE;
    }

    public int getMeasurementUnit() {
        return measurementUnit;
    }

    public Statistics getStatLongitude() {
        return statLongitude;
    }

    public Statistics getStatLatitude() {
        return statLatitude;
    }

    public Statistics getStatAltitude() {
        return statAltitude;
    }

    public Statistics getStatSpeed() {
        return statSpeed;
    }

    public Statistics getStatHeartRate() {
        return statHeartRate;
    }

    public float getAltitudeOffset() {
        return altitudeOffset;
    }

    public LegacyHeartRateData getHrData() {
        return hrData;
    }

    public String getMcc() {
        return mcc;
    }

    public String getMnc() {
        return mnc;
    }

    public int getApplicationId() {
        return applicationId;
    }

    public int getMachineId() {
        return machineId;
    }

    public int getSharingFlags() {
        return sharingFlags;
    }

    public int getWorkoutId() {
        return workoutId;
    }

    public int getActivityId() {
        return activityId;
    }

    public String getUserName() {
        return userName;
    }

    public String getWorkoutName() {
        return workoutName;
    }

    public String getDescription() {
        return description;
    }

    public long getStartTime() {
        return startTime;
    }

    public long getStopTime() {
        return stopTime;
    }

    public long getStartTimeLocal() {
        return startTimeLocal;
    }

    public long getStopTimeLocal() {
        return stopTimeLocal;
    }

    public double getTotalTime() {
        return totalTime;
    }

    public double getTotalDistance() {
        return totalDistance;
    }

    public WorkoutGeoPoint getStartPosition() {
        return startPosition;
    }

    public WorkoutGeoPoint getStopPosition() {
        return stopPosition;
    }

    public WorkoutGeoPoint getCenterPosition() {
        return centerPosition;
    }

    public int getEventGeneralsCount() {
        return eventGeneralsCount;
    }

    public int getEventLocationsCount() {
        return eventLocationsCount;
    }

    public int getEventLapsCount() {
        return eventLapsCount;
    }

    public int getEventMediaImages() {
        return eventMediaImages;
    }

    public int getEventHeartratesCount() {
        return eventHeartratesCount;
    }

    public int getStepCount() {
        return stepCount;
    }

    public boolean isManuallyAdded() {
        return isManuallyAdded;
    }

    public int getEnergy() {
        return energy;
    }

    public int getAverageCadence() {
        return averageCadence;
    }

    public int getMaxCadence() {
        return maxCadence;
    }
}
