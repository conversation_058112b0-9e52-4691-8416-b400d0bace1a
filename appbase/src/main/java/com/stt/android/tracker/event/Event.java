package com.stt.android.tracker.event;

import com.google.gson.annotations.SerializedName;

public class Event {
    /**
     * The time when the event happened relative to the workout duration
     */
    @SerializedName("timeWhenEventHappened")
    private final double timeWhenEventHappened;
    /**
     * The epoch timestamp in milliseconds
     */
    @SerializedName("realTime")
    private final long realTime;

    @SerializedName("type")
    private final EventType type;

    public Event(EventType eventType, long currentTimestamp, double currentTime) {
        this.type = eventType;
        this.realTime = currentTimestamp;
        this.timeWhenEventHappened = currentTime;
    }

    /**
     * @deprecated Unreliable, some workouts recorded with iOS app have these calculated wrong during recording.
     *   The issues can be CONTINUE events jumping to the future or next (AUTO)PAUSE having
     *   same time as the previous CONTINUE event.
     *   Realtime has correct values, so these values can be recalculated when needed.
     *   See TP#130559
     */
    @Deprecated
    public double getTimeWhenEventHappened() {
        return timeWhenEventHappened;
    }

    public long getRealTime() {
        return realTime;
    }

    public EventType getType() {
        return type;
    }

    public enum EventType {
        NONE(0),
        START(1),
        /*
         * TODO: What's the difference between STOP and PAUSE/AUTOPAUSE. Should
         * STOP be END?
         */
        STOP(2),
        PAUSE(3),
        AUTOPAUSE(4),
        CONTINUE(5),
        LAP(6),
        INTERVAL(7),
        FIXLOST(8),
        FIXBACK(9),
        LOCATION(10),
        AIRPRESSURE(11),
        TEMPERATURE(12),
        HEARTRATE(13),
        UNKNOWN(-1);

        private final int eventTypeEnum;

        private EventType(int eventType) {
            this.eventTypeEnum = eventType;
        }

        public int getEventType() {
            return eventTypeEnum;
        }

        public static EventType valueOf(int eventTypeId) {
            for (EventType eventType : values()) {
                if (eventType.getEventType() == eventTypeId) {
                    return eventType;
                }
            }
            return UNKNOWN;
        }
    }
}
