package com.stt.android.domain.database;

import android.database.sqlite.SQLiteDatabase;
import com.j256.ormlite.support.ConnectionSource;
import com.j256.ormlite.table.TableUtils;
import com.stt.android.workoutdetail.comments.WorkoutComment;
import java.sql.SQLException;

public class DatabaseUpgrade10To12<PERSON>elper extends DatabaseUpgradeHelper {
    public DatabaseUpgrade10To12Helper(SQLiteDatabase db, ConnectionSource connectionSource, DatabaseHelper databaseHelper) {
        super(db, connectionSource, databaseHelper);
    }

    @Override
    public void upgrade() throws SQLException {
        TableUtils.createTableIfNotExists(connectionSource, WorkoutComment.class);

        // a horrible mistake was introduced when I added the table, so need to clear the whole table
        TableUtils.clearTable(connectionSource, WorkoutComment.class);
    }
}
