package com.stt.android.domain.database;

import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.text.TextUtils;
import androidx.annotation.NonNull;
import com.j256.ormlite.dao.Dao;
import com.j256.ormlite.support.ConnectionSource;
import com.j256.ormlite.table.TableUtils;
import com.stt.android.domain.database.deprecated.OldRouteTable;
import com.stt.android.domain.user.ImageInformation;
import com.stt.android.domain.user.LegacyUser;
import com.stt.android.domain.user.LegacyWorkoutHeader;
import com.stt.android.utils.STTConstants;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class DatabaseUpgrade12To13Helper extends DatabaseUpgradeHelper {
    public DatabaseUpgrade12To13Helper(SQLiteDatabase db, ConnectionSource connectionSource,
        DatabaseHelper databaseHelper) {
        super(db, connectionSource, databaseHelper);
    }

    @Override
    public void upgrade() throws SQLException {
        //noinspection deprecation
        TableUtils.createTableIfNotExists(connectionSource, OldRouteTable.class);

        db.execSQL("ALTER TABLE "
            + LegacyWorkoutHeader.TABLE_NAME
            + " ADD COLUMN "
            + LegacyWorkoutHeader.DbFields.STEP_COUNT
            + ";");

        // adds the "user name" column for "image info" table
        db.execSQL("ALTER TABLE "
            + ImageInformation.TABLE_NAME
            + " ADD COLUMN "
            + ImageInformation.DbFields.USER_NAME
            + ";");

        Dao<ImageInformation, Integer> imageDao = databaseHelper.getDao(ImageInformation.class);

        List<ImageInformation> allImages = imageDao.queryForAll();
        int count = allImages.size();
        Set<String> workoutKeys = new HashSet<>();
        for (int i = 0; i < count; ++i) {
            String workoutKey = allImages.get(i).getWorkoutKey();
            if (!TextUtils.isEmpty(workoutKey)) {
                workoutKeys.add(workoutKey);
            }
        }

        if (workoutKeys.size() > 0) {
            StringBuilder in = new StringBuilder();
            for (String workoutKey : workoutKeys) {
                in.append('\'').append(workoutKey).append("',");
            }
            in.setLength(in.length() - 1); // remove the trailing ","
        }

        String currentUserName = getCurrentUserName();
        List<ImageInformation> updatedImages = new ArrayList<>(count);
        for (int i = 0; i < count; ++i) {
            ImageInformation original = allImages.get(i);
            String workoutKey = original.getWorkoutKey();
            if (TextUtils.isEmpty(workoutKey)) {
                // no workout key, meaning not synced yet, so the image must belong to current user
                updatedImages.add(original.updateUserName(currentUserName));
            }
        }
        count = updatedImages.size();
        for (int i = 0; i < count; ++i) {
            imageDao.createOrUpdate(updatedImages.get(i));
        }
    }

    @SuppressWarnings("deprecation")
    @NonNull
    private String getCurrentUserName() {
        // new columns added to users table later, so have to do the query manually here
        try (Cursor cursor = db.query(LegacyUser.TABLE_NAME,
            new String[] { LegacyUser.DbFields.USERNAME },
            LegacyUser.DbFields.ID + " = ?",
            new String[] { Integer.toString(STTConstants.CURRENT_USER_DEFAULT_ID) }, null, null,
            null)) {
            if (cursor.moveToFirst()) {
                return cursor.getString(0);
            }
        }

        return LegacyUser.ANONYMOUS_USERNAME;
    }
}
