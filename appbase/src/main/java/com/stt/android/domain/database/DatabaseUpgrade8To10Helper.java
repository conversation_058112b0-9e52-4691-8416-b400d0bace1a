package com.stt.android.domain.database;

import android.database.sqlite.SQLiteDatabase;
import com.j256.ormlite.support.ConnectionSource;
import com.j256.ormlite.table.TableUtils;
import com.stt.android.domain.user.SubscriptionInfo;
import java.sql.SQLException;
import timber.log.Timber;

public class DatabaseUpgrade8To10<PERSON>elper extends DatabaseUpgradeHelper {
    public DatabaseUpgrade8To10Helper(SQLiteDatabase db, ConnectionSource connectionSource,
                                      DatabaseHelper databaseHelper) {
        super(db, connectionSource, databaseHelper);
    }

    @Override
    public void upgrade() throws SQLException {
        Timber.d("DatabaseUpgrade8To10Helper.upgrade");
        TableUtils.dropTable(connectionSource, SubscriptionInfo.class, true);
        TableUtils.createTable(connectionSource, SubscriptionInfo.class);
    }
}
