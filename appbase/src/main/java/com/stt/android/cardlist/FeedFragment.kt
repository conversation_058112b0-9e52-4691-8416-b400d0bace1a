package com.stt.android.cardlist

import android.os.Bundle
import android.view.View
import androidx.annotation.CallSuper
import androidx.core.view.doOnNextLayout
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.stt.android.FeatureFlags
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.home.explore.routes.FeedCardHolderFactory
import com.stt.android.home.explore.routes.addtowatch.AddRouteToWatchViewModel
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.maps.MapSnapshotter
import com.stt.android.ui.extensions.requireTheme
import com.stt.android.ui.utils.WideScreenPaddingDecoration
import io.reactivex.disposables.CompositeDisposable
import javax.inject.Inject

/**
 * Provides basic functionality to show a list with cards in it. Supported cards so far:
 *
 *  * [FeedCard] through [FeedAdapter].
 *
 * Note: derived classes should use @AndroidEntryPoint annotation
 */
abstract class FeedFragment : Fragment() {
    @Inject
    lateinit var feedCardHolderFactoryMap: Map<Int, @JvmSuppressWildcards FeedCardHolderFactory>

    @Inject
    lateinit var featureFlags: FeatureFlags

    @Inject
    lateinit var infoModelFormatter: InfoModelFormatter

    @Inject
    lateinit var currentUserController: CurrentUserController

    @Inject
    lateinit var mapSnapshotter: MapSnapshotter

    @Inject
    lateinit var userSettingsController: UserSettingsController

    protected var isFeedReady = false

    private var feedAdapter: FeedAdapter<FeedCard>? = null

    protected val compositeDisposable = CompositeDisposable()

    private val addRouteToWatchViewModel: AddRouteToWatchViewModel by viewModels()

    /**
     * This listener will be used for to notify when the list is idle.
     */
    private val feedScrollListener = object : RecyclerView.OnScrollListener() {
        private var scrollState = RecyclerView.SCROLL_STATE_IDLE

        override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
            scrollState = newState
            if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                forVisibleViewHolders { it.onRecyclerViewIdle() }
            } else if (newState == RecyclerView.SCROLL_STATE_DRAGGING) {
                forVisibleViewHolders { it.onRecyclerViewBusy() }
            }
        }
    }

    protected abstract fun getRecyclerView(): RecyclerView

    protected abstract fun mapSnapshotterSizeProvider(): View?

    protected open fun createRecyclerViewLayoutManager(): RecyclerView.LayoutManager {
        return LinearLayoutManager(context, RecyclerView.VERTICAL, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initializeRecyclerView()
        initializeMapSnapshotter()
    }

    private fun initializeRecyclerView() {
        val recyclerView = getRecyclerView()
        recyclerView.addOnScrollListener(feedScrollListener)
        recyclerView.layoutManager = createRecyclerViewLayoutManager()

        // TODO This disables all feed animations to test if we get rid of RecyclerView crashing
        recyclerView.itemAnimator = null

        recyclerView.addItemDecoration(WideScreenPaddingDecoration(resources, requireTheme()))

        activity?.let {
            feedAdapter = FeedAdapter<FeedCard>(
                it,
                infoModelFormatter,
                feedCardHolderFactoryMap,
                userSettingsController
            ) { _, route, isChecked ->
                addRouteToWatchViewModel.onAddToWatchToggled(
                    isChecked,
                    route,
                    AnalyticsPropertyValue.SuuntoSyncRoutesContext.EDIT_ROUTE
                )
            }.apply {
                recyclerView.adapter = this
            }
        }
    }

    private fun initializeMapSnapshotter() {
        mapSnapshotterSizeProvider()?.doOnNextLayout {
            feedAdapter?.setMapCacheSize(
                it.width,
                it.height
            )
            onFeedReady()
        }

        viewLifecycleOwner.lifecycleScope.launchWhenCreated {
            mapSnapshotter.runSnapshotterEngine(requireContext().applicationContext)
        }
    }

    override fun onStart() {
        super.onStart()

        forAllViewHolders { it.onStart() }
    }

    override fun onStop() {
        forAllViewHolders { it.onStop() }

        super.onStop()
    }

    override fun onDestroyView() {
        getRecyclerView().removeOnScrollListener(feedScrollListener)
        super.onDestroyView()
    }

    override fun onDestroy() {
        compositeDisposable.dispose()
        super.onDestroy()
    }

    /**
     * Called once the feed is ready to be populated with data.
     * Classes overriding this method must call its super implementation
     */
    @CallSuper
    protected open fun onFeedReady() {
        isFeedReady = true
    }

    fun setContents(newFeedCards: List<FeedCard>) {
        if (context == null) return
        feedAdapter?.setItems(newFeedCards)
    }

    protected fun getItemCount() = feedAdapter?.itemCount ?: 0

    private fun getVisibleItemRange(): IntRange {
        val layoutManager = getRecyclerView().layoutManager as LinearLayoutManager
        val firstVisible = layoutManager.findFirstVisibleItemPosition()
        val lastVisible = layoutManager.findLastVisibleItemPosition()
        return if (firstVisible != RecyclerView.NO_POSITION &&
            lastVisible != RecyclerView.NO_POSITION
        ) {
            IntRange(firstVisible, lastVisible)
        } else {
            IntRange.EMPTY
        }
    }

    private fun forVisibleViewHolders(operation: (FeedViewHolder<*>) -> Unit) {
        val recyclerView = getRecyclerView()
        getVisibleItemRange()
            .map {
                recyclerView.findViewHolderForAdapterPosition(it) as? FeedViewHolder<*>
            }
            .forEach { it?.apply(operation) }
    }

    private fun forAllViewHolders(operation: (FeedViewHolder<*>) -> Unit) {
        val itemCount = feedAdapter?.itemCount ?: return
        val recyclerView = getRecyclerView()
        (0 until itemCount)
            .map { recyclerView.findViewHolderForAdapterPosition(it) as? FeedViewHolder<*> }
            .forEach { it?.apply(operation) }
    }
}
