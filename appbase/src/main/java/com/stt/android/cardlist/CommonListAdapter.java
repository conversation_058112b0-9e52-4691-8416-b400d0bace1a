package com.stt.android.cardlist;

import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

abstract public class CommonListAdapter<VH extends RecyclerView.ViewHolder, C extends FeedCard>
    extends RecyclerView.Adapter<VH> {
    private final List<C> items = new ArrayList<>();

    public void setItems(List<C> items) {
        animateTo(items);
    }

    private C removeItem(int position) {
        C item = items.remove(position);
        notifyItemRemoved(position);
        return item;
    }

    private void addItem(int position, C item) {
        items.add(position, item);
        notifyItemInserted(position);
    }

    private void moveItem(int fromPosition, int toPosition) {
        Collections.swap(items, fromPosition, toPosition);
        notifyItemMoved(fromPosition, toPosition);
    }

    private void updateItem(Integer pos, C item) {
        C oldItem = items.get(pos);
        List<Object> payloads = item.calculateDifferences(oldItem);
        // When updating an item in the list we must maintain the ID otherwise it will trigger a
        // full rebind
        item.setId(oldItem.getId());
        items.set(pos, item);

        // If there're too many changes, let's simply update them at once.
        int payloadCount = payloads == null ? 0 : payloads.size();
        if (payloadCount != 1) {
            notifyItemChanged(pos);
        } else {
            notifyItemChanged(pos, payloads.get(0));
        }
    }

    private void animateTo(List<C> newItems) {
        List<Integer> removals = findRemovals(newItems);
        // Create a temporary list with the removed items so we can figure out the right position
        // where the new ones would be added
        ArrayList<C> currentMinusRemoved = new ArrayList<>(this.items);
        if (!removals.isEmpty()) {
            for (Integer position : removals) {
                currentMinusRemoved.remove((int) position);
            }
        }
        List<Integer> additions = findAdditions(currentMinusRemoved, newItems);

        Set<Integer> updates = new HashSet<>();
        if (!additions.isEmpty() && !removals.isEmpty()) {
            for (Iterator<Integer> iterator = additions.iterator(); iterator.hasNext(); ) {
                Integer additionPos = iterator.next();
                // If the additionPos is also in removals then it becomes an update
                if (/* This remove is by object not position*/ removals.remove(additionPos)) {
                    updates.add(additionPos);
                    iterator.remove();
                }
            }
        }

        for (Integer pos : updates) {
            C item = newItems.get(pos);
            updateItem(pos, item);
        }

        for (Integer pos : removals) {
            removeItem(pos);
        }

        for (Integer pos : additions) {
            addItem(pos, newItems.get(pos));
        }

        applyAndAnimateMovedItems(newItems);
    }

    /**
     * Iterates through the internal List of the Adapter backwards and checks if each item is
     * contained in the new List. If not, then it removes it.
     *
     * @return a list of positions that would be removed from the current list {@link #items}
     */
    private List<Integer> findRemovals(List<C> newItems) {
        List<Integer> result = new ArrayList<>();
        for (int i = items.size() - 1; i >= 0; i--) {
            final C model = items.get(i);
            if (!newItems.contains(model)) {
                result.add(i);
            }
        }
        return result;
    }

    /**
     * Iterates through the filtered List and checks if the item exists in the internal List. If
     * not, then it's added.
     *
     * @return list of positions to be added to {@code currentItems} from {@code newItems}
     */
    private List<Integer> findAdditions(List<C> currentItems, List<C> newItems) {
        List<Integer> result = new ArrayList<>();
        for (int i = 0, count = newItems.size(); i < count; i++) {
            final C model = newItems.get(i);
            if (!currentItems.contains(model)) {
                result.add(i);
            }
        }
        return result;
    }

    /**
     * Iterates through the new List backwards and looks up the index of each item in the
     * internal List. If it detects a difference in the index it calls
     * {@link #moveItem(int, int)} to bring the internal List of the Adapter in line with the
     * filtered List.
     */
    private void applyAndAnimateMovedItems(List<C> newItems) {
        for (int toPosition = newItems.size() - 1; toPosition >= 0; toPosition--) {
            final C model = newItems.get(toPosition);
            final int fromPosition = items.indexOf(model);
            if (fromPosition >= 0 && fromPosition != toPosition) {
                moveItem(fromPosition, toPosition);
            }
        }
    }

    public C getItem(int position) {
        return items.get(position);
    }

    @Override
    public int getItemCount() {
        return items.size();
    }
}
