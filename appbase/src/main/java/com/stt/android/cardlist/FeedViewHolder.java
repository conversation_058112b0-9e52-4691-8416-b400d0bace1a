package com.stt.android.cardlist;

import android.view.View;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import java.util.List;

abstract public class FeedViewHolder<C extends FeedCard> extends RecyclerView.ViewHolder {
    public FeedViewHolder(View itemView) {
        super(itemView);
    }

    public abstract void bind(C feedCard, int mapCacheWidth, int mapCacheHeight);

    public void bind(C feedCard, int mapCacheWidth, int mapCacheHeight, @NonNull List payloads) {
        bind(feedCard, mapCacheWidth, mapCacheHeight);
    }

    public void unbind() {
    }

    public void onRecyclerViewIdle() {
    }

    public void onRecyclerViewBusy() {
    }

    public void onStart() {
    }

    public void onStop() {
    }
}
