package com.stt.android.cardlist

import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.stt.android.domain.sml.TraverseEvent

interface MapCard {
    val polylineHashCode: Int
    val bounds: LatLngBounds?

    // TODO replace route with activityRoutes and nonActivityRoutes
    val route: List<LatLng>?
    val activityRoutes: List<List<LatLng>>?
    val nonActivityRoutes: List<List<LatLng>>?

    interface Factory {
        suspend fun create(
            polyline: String,
            activityRoutes: List<List<LatLng>> = emptyList(),
            nonActivityRoutes: List<List<LatLng>> = emptyList(),
        ): MapCard
        suspend fun create(position: LatLng): MapCard
    }
}

// For WorkoutCoverMap only, not supported by MapSnapshotter
interface MultisportMapCard : MapCard {
    val routes: List<List<LatLng>>
    val indexOfHighlightedRoute: Int

    interface Factory {
        suspend fun create(
            polyline: String,
            routes: List<List<LatLng>>,
            indexOfHighlightedRoute: Int,
            activityRoutes: List<List<LatLng>>,
            nonActivityRoutes: List<List<LatLng>>,
        ): MapCard
    }
}

// For WorkoutCoverMap only, not supported by MapSnapshotter
interface TraverseMapCard : MapCard {
    val traverseEvents: List<TraverseEvent>

    interface Factory {
        suspend fun create(
            polyline: String,
            traverseEvents: List<TraverseEvent>,
        ): MapCard
    }
}
