package com.stt.android.cardlist;

import android.app.Activity;
import android.content.res.Resources;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.stt.android.controllers.UserSettingsController;
import com.stt.android.home.explore.routes.FeedCardHolderFactory;
import com.stt.android.mapping.InfoModelFormatter;
import com.stt.android.workoutsettings.WorkoutSettingsActivity;
import com.stt.android.workoutsettings.follow.NoWorkoutsCardHolder;
import com.stt.android.workoutsettings.follow.OnAddToWatchToggledListener;
import com.stt.android.workoutsettings.follow.RouteCardHolder;
import com.stt.android.workoutsettings.follow.SelectedFollowCardHolder;
import com.stt.android.workoutsettings.follow.TargetWorkoutCardHolder;
import java.util.List;
import java.util.Map;

public class FeedAdapter<C extends FeedCard> extends CommonListAdapter<FeedViewHolder, C> {
    private final Activity activity;
    private final Resources resources;
    protected final LayoutInflater inflater;
    private int mapCacheWidth = -1;
    private int mapCacheHeight = -1;
    private final UserSettingsController userSettingsController;
    private final InfoModelFormatter infoModelFormatter;
    private final Map<Integer, FeedCardHolderFactory> feedCardHolderFactoryMap;

    @Nullable
    private final OnAddToWatchToggledListener onAddToWatchToggledListener;

    public FeedAdapter(
        Activity activity,
        InfoModelFormatter infoModelFormatter,
        Map<Integer, FeedCardHolderFactory> feedCardHolderFactoryMap,
        UserSettingsController userSettingsController,
        @Nullable OnAddToWatchToggledListener onAddToWatchToggledListener
    ) {
        this.activity = activity;
        this.resources = activity.getResources();
        this.inflater = LayoutInflater.from(activity);
        this.infoModelFormatter = infoModelFormatter;
        this.feedCardHolderFactoryMap = feedCardHolderFactoryMap;
        setHasStableIds(true);
        this.userSettingsController = userSettingsController;
        this.onAddToWatchToggledListener = onAddToWatchToggledListener;
    }

    // protected crashes with R8 + AGP4
    public void setMapCacheSize(int width, int height) {
        mapCacheWidth = width;
        mapCacheHeight = height;
    }

    @NonNull
    @Override
    public FeedViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        switch (viewType) {
            case FeedCard.FOLLOW_WORKOUT_CARD:
                if (!(activity instanceof WorkoutSettingsActivity)) {
                    throw new IllegalArgumentException("FOLLOW_WORKOUT_CARD usable only in WorkoutSettingsActivity");
                }
                return new TargetWorkoutCardHolder((WorkoutSettingsActivity) activity, inflater, parent);
            case FeedCard.ROUTE_CARD:
                if (!(activity instanceof WorkoutSettingsActivity)) {
                    throw new IllegalArgumentException("ROUTE_CARD usable only in WorkoutSettingsActivity");
                }
                return new RouteCardHolder(
                    inflater,
                    parent,
                    (WorkoutSettingsActivity) activity,
                    resources,
                    userSettingsController,
                    infoModelFormatter,
                    onAddToWatchToggledListener
                );
            case FeedCard.NO_WORKOUTS_CARD:
                return new NoWorkoutsCardHolder(inflater, parent);
            case FeedCard.SELECTED_FOLLOW_CARD:
                if (!(activity instanceof WorkoutSettingsActivity)) {
                    throw new IllegalArgumentException("SELECTED_FOLLOW_CARD usable only in WorkoutSettingsActivity");
                }
                return new SelectedFollowCardHolder(inflater, parent, (WorkoutSettingsActivity) activity);
            default:
                final FeedCardHolderFactory card = feedCardHolderFactoryMap.get(viewType);
                if (card != null) {
                    return card.create(inflater, parent, activity);
                }
                throw new IllegalArgumentException("Invalid viewType " + viewType);
        }
    }

    @Override
    public void onBindViewHolder(
        @NonNull FeedViewHolder holder, int position, @NonNull List payloads) {
        if (payloads.size() == 0) {
            onBindViewHolder(holder, position);
        } else {
            holder.bind(getItem(position), mapCacheWidth, mapCacheHeight, payloads);
        }
    }

    @Override
    public void onBindViewHolder(@NonNull FeedViewHolder holder, int position) {
        holder.bind(getItem(position), mapCacheWidth, mapCacheHeight);
        holder.onStart();
    }

    @Override
    public void onViewRecycled(@NonNull FeedViewHolder holder) {
        holder.onStop();
        holder.unbind();
    }

    @FeedCard.FeedViewType
    @Override
    public int getItemViewType(int position) {
        return getItem(position).getViewType();
    }

    @Override
    public long getItemId(int position) {
        return getItem(position).getId();
    }

    // protected crashes with R8 + AGP4
    public FeedCard getCard(int position) {
        return getItem(position);
    }
}
