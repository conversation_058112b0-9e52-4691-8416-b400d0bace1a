package com.stt.android.cardlist

import androidx.annotation.IntDef

/**
 * Marker interface for different cards in [FeedAdapter]
 */
interface FeedCard {
    @get:FeedViewType
    val viewType: Int
    var id: Long

    /**
     * Creates a list of payloads describing the differences between this card and `other`
     * card that will be send to [RecyclerView.Adapter.notifyItemChanged]
     *
     * @return list of differences (use null to identify a "full" update)
     */
    fun calculateDifferences(other: FeedCard?): List<Any>?

    @IntDef(
        ROUTE_CARD,
        NO_ROUTES_CARD,
        NO_WORKOUTS_CARD,
        FOLLOW_WORKOUT_CARD,
        SELECTED_FOLLOW_CARD
    )
    @Retention(AnnotationRetention.SOURCE)
    annotation class FeedViewType

    companion object {
        const val ROUTE_CARD = 0 // Items for WorkoutSettings/Ghost workout selection
        const val NO_ROUTES_CARD = 1 // Empty state for WorkoutSettings/Follow route selection
        const val NO_WORKOUTS_CARD = 2 // Empty state for WorkoutSettings/Ghost workout selection
        const val FOLLOW_WORKOUT_CARD = 3 // Items for WorkoutSettings/Ghost workout selection
        const val SELECTED_FOLLOW_CARD = 4 // Selected item in WorkoutSettings/Follow route
    }
}
