package com.stt.android

import android.content.Context
import android.content.res.Configuration
import android.graphics.drawable.Drawable
import android.util.TypedValue
import androidx.annotation.AttrRes
import androidx.annotation.ColorInt
import androidx.core.content.ContextCompat
import timber.log.Timber

object ThemeColors {
    /**
     * Return the primary text color RGB value for current theme
     */
    @JvmStatic
    @ColorInt
    fun primaryTextColor(context: Context): Int = resolveColor(context, android.R.attr.textColorPrimary)

    /**
     * Return the secondary text color RGB value for current theme
     */
    @JvmStatic
    @ColorInt
    fun secondaryTextColor(context: Context): Int = resolveColor(context, android.R.attr.textColorSecondary)

    /**
     * Look up and return the given R.attr.xxx drawable from the given context theme.
     */
    @JvmStatic
    fun attrDrawable(context: Context, @AttrRes attrId: Int): Drawable? {
        val attrs = context.theme.obtainStyledAttributes(intArrayOf(attrId))
        val id = attrs.getResourceId(0, 0)
        attrs.recycle()
        return context.getDrawable(id)
    }

    /**
     * Returns true if the context configuration has night mode enabled.
     */
    @JvmStatic
    fun isNightMode(context: Context): Boolean {
        val nightModeFlags = context.resources.configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK
        return nightModeFlags == Configuration.UI_MODE_NIGHT_YES
    }

    /**
     * Return an RGB color value for color attribute [attrId] from theme. Please make sure the used theme actually
     * defines a color value for the attribute, otherwise this method will log a warning and return black.
     */
    @JvmStatic
    @ColorInt
    fun resolveColor(context: Context, @AttrRes attrId: Int): Int {
        if (context is STTApplication) {
            Timber.w("Colors cannot be resolved using the Application context. Use Activity/Fragment context instead.")
            if (BuildConfig.DEBUG) {
                throw UnsupportedOperationException("Colors cannot be resolved using the Application context. Use Activity/Fragment context instead.")
            }
        }
        val typedValue = TypedValue()
        val resolved = context.theme.resolveAttribute(attrId, typedValue, true)

        return when {
            resolved && typedValue.type == TypedValue.TYPE_REFERENCE && typedValue.resourceId != 0 -> {
                ContextCompat.getColor(context, typedValue.resourceId)
            }

            resolved && typedValue.type in TypedValue.TYPE_FIRST_COLOR_INT..TypedValue.TYPE_LAST_COLOR_INT -> {
                typedValue.data
            }

            else -> {
                Timber.w("Unable to resolve color: attrId=$attrId typedValue=$typedValue")
                ContextCompat.getColor(context, com.stt.android.core.R.color.black)
            }
        }
    }
}
