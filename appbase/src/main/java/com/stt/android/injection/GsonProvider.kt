package com.stt.android.injection

import android.location.Location
import com.google.android.gms.maps.model.LatLng
import com.google.gson.ExclusionStrategy
import com.google.gson.FieldAttributes
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.annotations.Expose
import com.stt.android.domain.Point
import com.stt.android.domain.user.ImageInformation
import com.stt.android.domain.user.SubscriptionInfo
import com.stt.android.domain.user.UserSubscription
import com.stt.android.domain.user.workoutextension.BackendWorkoutExtension
import com.stt.android.home.explore.routes.RouteSegment
import com.stt.android.utils.LatLngDeserializer
import com.stt.android.utils.LatLngSerializer
import com.stt.android.utils.LocationDeserializer
import com.stt.android.utils.LocationSerializer
import javax.inject.Provider

object GsonProvider : Provider<Gson> {

    override fun get(): Gson = instance

    /**
     * By default [com.google.gson.annotations.Expose] will only work if
     * [com.google.gson.GsonBuilder.excludeFieldsWithoutExposeAnnotation] is set.
     * Unfortunately that means that fields without Expose annotation will be ignored.
     * This custom strategy will allow fields without Expose to be serialized / deserialized
     */
    private class NonExclusiveExposeExclusionStrategy(private val serialization: Boolean) : ExclusionStrategy {

        override fun shouldSkipField(f: FieldAttributes): Boolean {
            val expose = f.getAnnotation(Expose::class.java)
            return if (expose != null) {
                if (serialization) !expose.serialize else !expose.deserialize
            } else {
                false
            }
        }

        override fun shouldSkipClass(clazz: Class<*>): Boolean {
            return false
        }
    }

    private const val JSON_VERSION = 1.0

    @JvmStatic
    val instance: Gson by lazy {
        commonGsonBuilder.create()
    }

    private val commonGsonBuilder: GsonBuilder
        get() {
            val gsonBuilder = GsonBuilder()
            gsonBuilder.registerTypeAdapter(
                BackendWorkoutExtension::class.java,
                BackendWorkoutExtension.Deserializer()
            )
            gsonBuilder.registerTypeAdapter(
                ImageInformation::class.java,
                ImageInformation.Deserializer()
            )
            gsonBuilder.registerTypeAdapter(
                UserSubscription::class.java,
                UserSubscription.Deserializer()
            )
            gsonBuilder.registerTypeAdapter(
                SubscriptionInfo::class.java,
                SubscriptionInfo.Deserializer()
            )
            gsonBuilder.registerTypeAdapter(Location::class.java, LocationDeserializer())
            gsonBuilder.registerTypeAdapter(Location::class.java, LocationSerializer())
            gsonBuilder.registerTypeAdapter(LatLng::class.java, LatLngDeserializer())
            gsonBuilder.registerTypeAdapter(LatLng::class.java, LatLngSerializer())
            gsonBuilder.registerTypeAdapter(
                RouteSegment::class.java,
                RouteSegment.RouteSegmentSerializer()
            )
            gsonBuilder.registerTypeAdapter(Point::class.java, Point.JsonSerializer())
            gsonBuilder.registerTypeAdapterFactory(CompleteLapAdapterFactory())
            gsonBuilder.setVersion(JSON_VERSION)
            gsonBuilder.addDeserializationExclusionStrategy(
                NonExclusiveExposeExclusionStrategy(false)
            )
            gsonBuilder.addSerializationExclusionStrategy(
                NonExclusiveExposeExclusionStrategy(true)
            )
            return gsonBuilder
        }

    fun createGsonWithSpecialFloatingPointValues(): Gson {
        val gsonBuilder = commonGsonBuilder
        gsonBuilder.serializeSpecialFloatingPointValues()
        return gsonBuilder.create()
    }
}
