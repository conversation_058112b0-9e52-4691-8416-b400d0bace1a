package com.stt.android.injection;

import com.google.gson.Gson;
import com.google.gson.TypeAdapter;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.stt.android.laps.CompleteLap;
import com.stt.android.laps.CompleteLapFactory;
import com.stt.android.laps.ParcelableCompleteLap;

/**
 * {@link TypeAdapterFactory} that takes care of serializing/deserializing
 * {@link CompleteLapFactory}, {@link CompleteLap} and {@link ParcelableCompleteLap}.
 * This is needed because we serialize {@link CompleteLap}s and the actual {@link
 * CompleteLapFactory}
 */
class CompleteLapAdapterFactory implements TypeAdapterFactory {
    @Override
    @SuppressWarnings("unchecked")
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
        Class<T> rawType = (Class<T>) type.getRawType();
        if (CompleteLapFactory.class.isAssignableFrom(rawType)
            || CompleteLap.class.isAssignableFrom(rawType)
            || ParcelableCompleteLap.class.isAssignableFrom(rawType)) {
            return (TypeAdapter<T>) gson.getAdapter(CompleteLapFactory.class);
        } else {
            return null;
        }
    }
}
