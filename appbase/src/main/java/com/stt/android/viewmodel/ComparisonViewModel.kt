package com.stt.android.viewmodel

import androidx.lifecycle.ViewModel
import com.stt.android.domain.workout.WorkoutGeoPoint
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class ComparisonViewModel : ViewModel() {
    private val _currentAndTargetWorkout =
        MutableStateFlow<Pair<WorkoutGeoPoint?, WorkoutGeoPoint?>?>(null)
    val currentAndTargetWorkout: StateFlow<Pair<WorkoutGeoPoint?, WorkoutGeoPoint?>?> =
        _currentAndTargetWorkout

    fun setCurrentAndTargetWorkout(current: WorkoutGeoPoint, target: WorkoutGeoPoint) {
        _currentAndTargetWorkout.value = Pair(current, target)
    }
}
