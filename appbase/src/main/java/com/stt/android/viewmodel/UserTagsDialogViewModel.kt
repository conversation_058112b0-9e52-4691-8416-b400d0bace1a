package com.stt.android.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.domain.tags.UserTag
import com.stt.android.domain.tags.UserTagsRepository
import com.stt.android.domain.user.subscription.IsSubscribedToPremiumUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.tags.CheckAndGetSuuntoTagFromCustomTagNameUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class UserTagsDialogViewModel @Inject constructor(
    private val userTagsRepository: UserTagsRepository,
    private val checkAndGetSuuntoTagFromCustomTagNameUseCase: CheckAndGetSuuntoTagFromCustomTagNameUseCase,
    private val isSubscribedToPremiumUseCase: IsSubscribedToPremiumUseCase,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : ViewModel() {
    private val _viewState: MutableStateFlow<UserTagsDialogViewState> =
        MutableStateFlow(UserTagsDialogViewState())
    val viewState: StateFlow<UserTagsDialogViewState> = _viewState

    fun init(workoutHeader: WorkoutHeader) {
        viewModelScope.launch {
            val currentAllUserTags = _viewState.value.allUserTags
            val allUserTags = async(coroutinesDispatchers.io) {
                if (currentAllUserTags != null) {
                    currentAllUserTags
                } else {
                    _viewState.update {
                        it.copy(
                            isLoading = true
                        )
                    }
                    userTagsRepository.getAllUserTags()
                }
            }

            val isSubscribedToPremium = async(coroutinesDispatchers.io) {
                isSubscribedToPremiumUseCase.invoke().first()
            }

            _viewState.update {
                it.copy(
                    isLoading = false,
                    workoutHeader = it.workoutHeader ?: workoutHeader,
                    allUserTags = allUserTags.await(),
                    isSubscribedToPremium = isSubscribedToPremium.await()
                )
            }
        }
    }

    fun onSuuntoTagChecked(suuntoTag: SuuntoTag, isChecked: Boolean) {
        _viewState.update {
            it.copy(
                workoutHeader = it.workoutHeader?.copy(
                    suuntoTags =
                    if (isChecked) {
                        it.workoutHeader.suuntoTags.plus(suuntoTag)
                    } else {
                        it.workoutHeader.suuntoTags.minus(suuntoTag)
                    }
                )
            )
        }
    }

    fun onUserTagChecked(userTag: UserTag, isChecked: Boolean) {
        _viewState.update {
            it.copy(
                workoutHeader = it.workoutHeader?.copy(
                    userTags =
                    if (isChecked) {
                        it.workoutHeader.userTags.plus(userTag)
                    } else {
                        it.workoutHeader.userTags.minus(userTag)
                    }
                )
            )
        }
    }

    fun onFilterChanged(value: String) {
        _viewState.update {
            it.copy(
                filterValue = value.trimStart().take(MAX_FILTER_LENGTH)
            )
        }
    }

    fun insertNewUserTag(name: String) {
        if (name.isBlank()) {
            return
        }

        val trimmedName = name.trim()

        val suuntoTagWithTheSameName = checkAndGetSuuntoTagFromCustomTagNameUseCase(trimmedName)

        if (suuntoTagWithTheSameName != null) {
            val isSuuntoTagAlreadyAddedToWorkoutHeader =
                _viewState.value.workoutHeader?.suuntoTags?.any { suuntoTagWithTheSameName == it }
                    ?: false

            if (!isSuuntoTagAlreadyAddedToWorkoutHeader) {
                onSuuntoTagChecked(suuntoTagWithTheSameName, true)
            }
            // if a tag exists in a workout, just reset the filter, just to give the impression that the tag has been added
            _viewState.update {
                it.copy(
                    filterValue = ""
                )
            }
            return
        }

        val isUserTagAlreadyAddedToWorkoutHeader = _viewState.value.workoutHeader?.userTags?.any {
            it.name.equals(
                trimmedName,
                ignoreCase = true
            )
        } ?: false

        if (!isUserTagAlreadyAddedToWorkoutHeader) {
            val userTag =
                _viewState.value.allUserTags?.find { it.name.equals(trimmedName, ignoreCase = true) }
                    ?: UserTag.empty(name = trimmedName)
            _viewState.update {
                it.copy(
                    workoutHeader = it.workoutHeader?.copy(
                        userTags = it.workoutHeader.userTags.plus(userTag)
                    ),
                    filterValue = ""
                )
            }
        } else {
            // if a tag exists in a workout, just reset the filter, just to give the impression that the tag has been added
            _viewState.update {
                it.copy(
                    filterValue = ""
                )
            }
        }
    }

    companion object {
        const val MAX_FILTER_LENGTH = 25
    }
}

data class UserTagsDialogViewState(
    val isLoading: Boolean = false,
    val filterValue: String = "",
    internal val workoutHeader: WorkoutHeader? = null,
    internal val allUserTags: List<UserTag>? = null,
    private val isSubscribedToPremium: Boolean = true,
) {
    val canAddUserTags: Boolean = isSubscribedToPremium

    fun selectedSuuntoTags(): ImmutableList<SuuntoTag> =
        workoutHeader?.suuntoTags
            ?.filter { suuntoTag ->
                isSubscribedToPremium || suuntoTag.editable
            }
            .sort()
            .toImmutableList()

    val selectedUserTags: ImmutableList<UserTag> = workoutHeader?.userTags
        .sort()
        .toImmutableList()

    // Suunto tags requires translation to be filtered
    fun suggestedSuuntoTags(getString: (Int) -> String, ): ImmutableList<SuuntoTag> =
        SuuntoTag.entries
            .filter { suuntoTag ->
                if (!suuntoTag.editable) {
                    return@filter false
                }
                if (selectedSuuntoTags().contains(suuntoTag)) {
                    return@filter false
                }

                getString(suuntoTag.nameRes).contains(filterValue.trim(), ignoreCase = true)
            }
            .toImmutableList()

    val suggestedUserTags: ImmutableList<UserTag> = allUserTags
        .takeIf { canAddUserTags }
        .orEmpty()
        .minus(selectedUserTags.toSet())
        .filter { it.name.contains(filterValue.trim(), ignoreCase = true) }
        .sort()
        .toImmutableList()

    fun isAtLeastOnePredefinedTagIsShown(getString: (Int) -> String): Boolean =
        selectedSuuntoTags().any { it in SuuntoTag.entries } ||
            suggestedSuuntoTags(getString).any { it in SuuntoTag.entries }
}

@JvmName("sortSuuntoTag")
fun List<SuuntoTag>?.sort(): List<SuuntoTag> = orEmpty().sortedBy { it.name }

@JvmName("sortUserTag")
fun List<UserTag>?.sort(): List<UserTag> = orEmpty().sortedWith(compareBy(String.CASE_INSENSITIVE_ORDER) { it.name })
