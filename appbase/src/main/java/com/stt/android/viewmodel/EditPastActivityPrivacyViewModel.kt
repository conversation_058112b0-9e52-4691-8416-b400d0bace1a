package com.stt.android.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.controllers.CurrentUserController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.workouts.update.WorkoutUpdateRepository
import com.stt.android.domain.workouts.WorkoutDataSource
import com.stt.android.remote.otp.GenerateOTPUseCase
import com.stt.android.remote.workouts.update.SharingFlags
import com.stt.android.ui.fragments.settings.PrivacyUpdateHelper
import com.stt.android.ui.utils.SingleLiveEvent
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import kotlin.math.min

@HiltViewModel
class EditPastActivityPrivacyViewModel @Inject constructor(
    private val workoutUpdateRepository: WorkoutUpdateRepository,
    private val workoutDataSource: WorkoutDataSource,
    private val currentUserController: CurrentUserController,
    private val generateOTPUseCase: GenerateOTPUseCase
) : ViewModel() {
    private val _privacyProgressLiveData = SingleLiveEvent<Int>()
    private var _hasSubmitSharingFlag: Boolean = false
    val privacyProgressLiveData: LiveData<Int>
        get() = _privacyProgressLiveData
    val sharingFlags = PrivacyUpdateHelper.sharingFlags
    var settingSharingFlags: SharingFlags = SharingFlags.UNKNOWN
    val hasSubmitSharingFlags: Boolean get() = _hasSubmitSharingFlag

    fun fetchWorkoutSharingFlagsUpdateCount() {
        viewModelScope.launch(Dispatchers.IO) {
            runSuspendCatching {
                val totp = generateOTPUseCase.generateTOTP()
                val result =
                    workoutUpdateRepository.fetchWorkoutSharingFlagsUpdateCount(
                        totp,
                        sharingFlags.value.flag,
                        PrivacyUpdateHelper.until
                    )
                Timber.i("fetchWorkoutSharingFlagsUpdateCount(${sharingFlags.value.flag},${PrivacyUpdateHelper.until}) result $result")
                if (result != null) {
                    if (result.totalCount == 0) { // No exercise data
                        _privacyProgressLiveData.postValue(100)
                        PrivacyUpdateHelper.updateSharingFlags(SharingFlags.UNKNOWN)
                    } else if (result.count >= result.totalCount) {
                        _privacyProgressLiveData.postValue(100)
                        PrivacyUpdateHelper.updateSharingFlags(SharingFlags.UNKNOWN)
                    } else {
                        _privacyProgressLiveData.postValue(min(1,(result.count * 100f / result.totalCount).toInt()))
                    }
                }
            }.onFailure {
                Timber.w(it, "fetchWorkoutSharingFlagsUpdateCount failure $sharingFlags")
            }
        }
    }

    fun resetEditPastPrivacyStatus() {
        _privacyProgressLiveData.value = -1
        PrivacyUpdateHelper.until = 0
        PrivacyUpdateHelper.updateSharingFlags(SharingFlags.UNKNOWN)
    }

    fun submitSharingFlags(sharingFlags: SharingFlags, callback: (Boolean) -> Unit) {
        _hasSubmitSharingFlag = true
        viewModelScope.launch(Dispatchers.IO) {
            runSuspendCatching {
                val totp = generateOTPUseCase.generateTOTP()
                val result = workoutUpdateRepository.batchUpdateSharingFlags(totp,sharingFlags.flag)
                Timber.i("batchUpdateSharingFlags(${sharingFlags.flag}) result $result")
                if (result != null) {
                    PrivacyUpdateHelper.updateSharingFlags(sharingFlags)
                    PrivacyUpdateHelper.until = result
                    _privacyProgressLiveData.postValue(1) // init progress
                    workoutDataSource.updateSharingFlagsByUsername(
                        currentUserController.currentUser.username,
                        sharingFlags.flag
                    )
                }
                withContext(Dispatchers.Main) {
                    callback.invoke(result != null)
                }
            }.onFailure {
                Timber.w(it, "batchUpdateSharingFlags failure $sharingFlags")
                withContext(Dispatchers.Main) {
                    _hasSubmitSharingFlag = false
                    callback.invoke(false)
                }
            }
        }
    }

}
