package com.stt.android.colorfultrack

import com.soy.algorithms.intensity.IntensityZonesData
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.IntensityExtensionDataModel
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.domain.workout.WorkoutHrEvent
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.extensions.loadExtension
import com.stt.android.intensityzone.ZonesLimitsUtils
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.withContext
import javax.inject.Inject

@ActivityRetainedScoped
class PaceWorkoutColorfulTrackLoader @Inject constructor(
    private val intensityExtensionDataModel: IntensityExtensionDataModel,
    private val measurementUnit: MeasurementUnit,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : WorkoutColorfulTrackLoader {
    override suspend fun loadColorfulTrack(
        geoPoints: List<WorkoutGeoPoint>,
        header: WorkoutHeader,
        sml: Sml?,
        heartRateEvents: List<WorkoutHrEvent>?,
        noDashLines: Boolean,
    ): WorkoutColorfulTrackMapData = withContext(coroutinesDispatchers.io) {
        if (sml == null || sml.streamData.speed.isEmpty()) {
            return@withContext defaultWorkoutColorfulTrackMapData(geoPoints)
        }

        val speedZones = intensityExtensionDataModel.loadExtension(header)
            ?.intensityZones
            ?.speed
            ?: return@withContext defaultWorkoutColorfulTrackMapData(geoPoints)

        // When recorded with watch, the lowest value for pace is 40min/km i.e. speed is
        // at min ~0.42 m/s in SML.
        val paces = sml.streamData
            .speed
            .mapNotNull { point ->
                if (point.value > 0.1F) {
                    point.timestamp to measurementUnit.toPaceUnit(point.value.toDouble()).toFloat()
                } else {
                    null
                }
            }
        val paceZoneLimits =
            ZonesLimitsUtils.getPaceZoneLimits(
                paces,
                speedToPaceUnit(speedZones, measurementUnit)
            )
        val workoutPropertyValues = paces.map { pace ->
            WorkoutPropertyValue(pace.first, pace.second)
        }

        generateColorfulTrackWorkoutMapData(
            geoPoints,
            sml.streamData.takeUnless { noDashLines },
            getGeoPointsSplitPositions(workoutPropertyValues, paceZoneLimits),
        )
    }

    private fun speedToPaceUnit(
        zones: IntensityZonesData,
        measurementUnit: MeasurementUnit
    ): IntensityZonesData {
        fun Float.toPaceUnit() = measurementUnit.toPaceUnit(this.toDouble()).toFloat()

        return zones.copy(
            zone2LowerLimit = zones.zone2LowerLimit.toPaceUnit(),
            zone3LowerLimit = zones.zone3LowerLimit.toPaceUnit(),
            zone4LowerLimit = zones.zone4LowerLimit.toPaceUnit(),
            zone5LowerLimit = zones.zone5LowerLimit.toPaceUnit()
        )
    }
}
