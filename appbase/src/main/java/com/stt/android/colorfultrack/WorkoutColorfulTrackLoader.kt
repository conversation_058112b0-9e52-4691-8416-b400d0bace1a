package com.stt.android.colorfultrack

import androidx.annotation.ColorRes
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.sml.SmlStreamData
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.domain.workout.WorkoutHrEvent
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.intensityzone.IntensityZoneLimits
import com.stt.android.ui.map.MapHelper

data class WorkoutPropertyValue(val timestamp: Long, val value: Float)
data class SplitPointValue(val timestamp: Long, @ColorRes val color: Int, val altitude: Int = 0)

interface WorkoutColorfulTrackLoader {
    suspend fun loadColorfulTrack(
        geoPoints: List<WorkoutGeoPoint>,
        header: WorkoutHeader,
        sml: Sml? = null,
        heartRateEvents: List<WorkoutHrEvent>? = null,
        noDashLines: Boolean = false,
    ): WorkoutColorfulTrackMapData

    fun defaultWorkoutColorfulTrackMapData(
        geoPoints: List<WorkoutGeoPoint>
    ): WorkoutColorfulTrackMapData {
        return WorkoutColorfulTrackMapData(
            activityRoutesWithColor = emptyList(),
            nonActivityRoutesWithColor = emptyList(),
            bounds = getLatLngBounds(geoPoints.map { it.latLng }),
            activityRoutes = listOf(geoPoints.mapNotNull { it.latLng }),
            nonActivityRoutes = emptyList(),
        )
    }

    fun generateColorfulTrackWorkoutMapData(
        geoPoints: List<WorkoutGeoPoint>,
        streamData: SmlStreamData?,
        splitPositions: List<SplitPointValue>
    ): WorkoutColorfulTrackMapData {
        val activityGeoPointsWithColors = mutableListOf<WorkoutGeoPointsWithColor>()
        val nonActivityGeoPointsWithColors = mutableListOf<WorkoutGeoPointsWithColor>()
        val activityRoutes = mutableListOf<List<LatLng>>()
        val nonActivityRoutes = mutableListOf<List<LatLng>>()

        val (inActivityGeoPoints, notInActivityGeoPoints) = streamData?.let {
            MapHelper.filterGeoPointsActivityRoutes(geoPoints, it)
        } ?: Pair(emptyList(), emptyList())

        val geoPointsWithColor = getSplitWorkoutGeoPointsWithColorId(
            geoPoints,
            splitPositions,
        )

        inActivityGeoPoints.map { route -> route.map { it.latLng } }.let(activityRoutes::addAll)
        if (inActivityGeoPoints.isNotEmpty()) {
            copyColorsFromOriginalGeoPointsWithColor(
                inActivityGeoPoints.flatMap { getSplitWorkoutGeoPointsWithColorId(it, splitPositions) },
                geoPointsWithColor
            ).let(activityGeoPointsWithColors::addAll)
        } else {
            activityGeoPointsWithColors.addAll(geoPointsWithColor)
        }

        notInActivityGeoPoints.map { route -> route.map { it.latLng } }.let(nonActivityRoutes::addAll)
        copyColorsFromOriginalGeoPointsWithColor(
            notInActivityGeoPoints.flatMap { getSplitWorkoutGeoPointsWithColorId(it, splitPositions) },
            geoPointsWithColor
        ).let(nonActivityGeoPointsWithColors::addAll)

        if (activityRoutes.isEmpty()) {
            activityRoutes.add(geoPoints.mapNotNull { it.latLng })
        }

        return WorkoutColorfulTrackMapData(
            activityRoutesWithColor = activityGeoPointsWithColors,
            nonActivityRoutesWithColor = nonActivityGeoPointsWithColors,
            bounds = getLatLngBounds(geoPoints.map { it.latLng }),
            activityRoutes = activityRoutes,
            nonActivityRoutes = nonActivityRoutes,
        )
    }

    fun copyColorsFromOriginalGeoPointsWithColor(
        geoPointsWithColor: List<WorkoutGeoPointsWithColor>,
        originalGeoPointsWithColor: List<WorkoutGeoPointsWithColor>
    ): List<WorkoutGeoPointsWithColor> {
        // Create a map from latLng to color for quick lookup
        val latLngToColorMap = originalGeoPointsWithColor
            .flatMap { it.points.map { point -> point.latLng to it.color } }
            .toMap()

        return geoPointsWithColor.map { workoutGeoPointsWithColor ->
            val colors = workoutGeoPointsWithColor.points.map { point ->
                latLngToColorMap[point.latLng] ?: workoutGeoPointsWithColor.color
            }
            workoutGeoPointsWithColor.copy(
                color = colors.firstOrNull() ?: workoutGeoPointsWithColor.color
            )
        }
    }

    fun getGeoPointsSplitPositions(
        workoutPropertyValues: List<WorkoutPropertyValue>,
        intensityZoneLimits: IntensityZoneLimits
    ): List<SplitPointValue> {
        val firstValue = workoutPropertyValues.first().value
        var currentZone = intensityZoneLimits.inWhichZone(firstValue)
        return mutableListOf<SplitPointValue>().apply {
            workoutPropertyValues.forEachIndexed { index, workoutPropertyValue ->
                val value = workoutPropertyValue.value
                if (!currentZone.inZone(value)) {
                    add(
                        SplitPointValue(
                            workoutPropertyValue.timestamp,
                            currentZone.intensityZone.color
                        )
                    )
                    currentZone = intensityZoneLimits.inWhichZone(value)
                }
                if (index == workoutPropertyValues.indices.last) {
                    // the last segment
                    add(
                        SplitPointValue(
                            workoutPropertyValue.timestamp,
                            currentZone.intensityZone.color
                        )
                    )
                }
            }
        }
    }

    fun getSplitWorkoutGeoPointsWithColorId(
        geoPoints: List<WorkoutGeoPoint>,
        splitPositions: List<SplitPointValue>,
    ): List<WorkoutGeoPointsWithColor> {
        if (splitPositions.isEmpty() || geoPoints.isEmpty()) {
            return emptyList()
        }

        return buildList {
            if (splitPositions.size == 1) {
                // only one zone
                add(geoPoints.toWorkoutGeoPointsWithColor(splitPositions.first().color))
                return@buildList
            }

            var remainingGeoPoints = geoPoints
            splitPositions.forEach { splitPoint ->
                val index = remainingGeoPoints
                    .indexOfFirst { it.timestamp >= splitPoint.timestamp }
                    .takeIf { it > 0 }
                    ?: return@forEach
                val splitData = remainingGeoPoints.subList(0, index + 1)
                add(splitData.toWorkoutGeoPointsWithColor(splitPoint.color))

                // split the remaining geopoints
                remainingGeoPoints = remainingGeoPoints.subList(index, remainingGeoPoints.size)
            }

            // add the last segment geopoints
            if (remainingGeoPoints.isNotEmpty()) {
                add(remainingGeoPoints.toWorkoutGeoPointsWithColor(splitPositions.last().color))
            }
        }
    }

    private fun getLatLngBounds(latLngs: List<LatLng>): LatLngBounds {
        val builder = LatLngBounds.builder()
        if (latLngs.isNotEmpty()) {
            latLngs.forEach(builder::include)
        } else {
            builder.include(LatLng(0.0, 0.0))
        }
        return builder.build()
    }

    private companion object {
        fun List<WorkoutGeoPoint>.toWorkoutGeoPointsWithColor(@ColorRes color: Int): WorkoutGeoPointsWithColor =
            WorkoutGeoPointsWithColor(
                points = map { workoutGeoPoint ->
                    PointWithTimestamp(workoutGeoPoint.millisecondsInWorkout.toLong(), workoutGeoPoint.latLng)
                },
                color = color,
            )
    }
}
