package com.stt.android.colorfultrack

import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.IntensityExtensionDataModel
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.domain.workout.WorkoutHrEvent
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.extensions.loadExtension
import com.stt.android.intensityzone.ZonesLimitsUtils
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.withContext
import javax.inject.Inject

@ActivityRetainedScoped
class HeartRateWorkoutColorfulTrackLoader @Inject constructor(
    private val intensityExtensionDataModel: IntensityExtensionDataModel,
    private val userSettingsController: UserSettingsController,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : WorkoutColorfulTrackLoader {
    /**
     * split workout tracks according to heart rate zone
     * first: Find the time point(millisecondsInWorkout) at which the heart rate zone changes
     * second: Split the workout track according to this time point
     */
    override suspend fun loadColorfulTrack(
        geoPoints: List<WorkoutGeoPoint>,
        header: WorkoutHeader,
        sml: Sml?,
        heartRateEvents: List<WorkoutHrEvent>?,
        noDashLines: Boolean,
    ): WorkoutColorfulTrackMapData = withContext(coroutinesDispatchers.io) {
        if (heartRateEvents.isNullOrEmpty() || heartRateEvents.size == 1) {
            return@withContext defaultWorkoutColorfulTrackMapData(geoPoints)
        }

        val intensityExtension = intensityExtensionDataModel.loadExtension(header)
        var maxHeartRate = heartRateEvents.first().heartRate.toFloat()
        val workoutPropertyValues = heartRateEvents.map {
            maxHeartRate = it.heartRate.toFloat().coerceAtLeast(maxHeartRate)
            WorkoutPropertyValue(it.timestamp, it.heartRate.toFloat())
        }
        val hrLimits = ZonesLimitsUtils.getHrZoneLimits(
            header,
            intensityExtension,
            userSettingsController,
            maxHeartRate,
        )

        generateColorfulTrackWorkoutMapData(
            geoPoints,
            sml?.streamData.takeUnless { noDashLines },
            getGeoPointsSplitPositions(workoutPropertyValues, hrLimits),
        )
    }
}
