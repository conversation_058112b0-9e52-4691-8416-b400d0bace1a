package com.stt.android.colorfultrack

import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.IntensityExtensionDataModel
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.domain.workout.WorkoutHrEvent
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.extensions.loadExtension
import com.stt.android.intensityzone.ZonesLimitsUtils
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.withContext
import javax.inject.Inject

@ActivityRetainedScoped
class PowerWorkoutColorfulTrackLoader @Inject constructor(
    private val intensityExtensionDataModel: IntensityExtensionDataModel,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : WorkoutColorfulTrackLoader {
    override suspend fun loadColorfulTrack(
        geoPoints: List<WorkoutGeoPoint>,
        header: WorkoutHeader,
        sml: Sml?,
        heartRateEvents: List<WorkoutHrEvent>?,
        noDashLines: Boolean,
    ): WorkoutColorfulTrackMapData = withContext(coroutinesDispatchers.io) {
        if (sml == null || sml.streamData.power.isEmpty()) {
            return@withContext defaultWorkoutColorfulTrackMapData(geoPoints)
        }

        val powerZones = intensityExtensionDataModel.loadExtension(header)
            ?.intensityZones
            ?.power
            ?: return@withContext defaultWorkoutColorfulTrackMapData(geoPoints)

        val powers = sml.streamData.power
        val powerZoneLimits = ZonesLimitsUtils.getPowerZoneLimits(powers, powerZones)
        val workoutPropertyValues = powers.map { power ->
            WorkoutPropertyValue(power.timestamp, power.value)
        }

        generateColorfulTrackWorkoutMapData(
            geoPoints,
            sml.streamData.takeUnless { noDashLines },
            getGeoPointsSplitPositions(workoutPropertyValues, powerZoneLimits)
        )
    }
}
