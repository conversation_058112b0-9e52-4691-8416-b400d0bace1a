package com.stt.android.appupdates.ui.navigation

internal object AppUpdatesRoute {
    const val APP_UPDATES = "app_updates"
    const val OLD_VERSIONS = "app_versions"
    const val VERSION_NOTE_PREFIX = "app_version"
    const val VERSION_NOTE_ARG_VERSION_NAME = "version_name"
    const val VERSION_NOTE_ARG_VERSION_CODE = "version_code"
    const val VERSION_NOTE =
        "$VERSION_NOTE_PREFIX/{$VERSION_NOTE_ARG_VERSION_NAME}/{$VERSION_NOTE_ARG_VERSION_CODE}"

    fun buildVersionNoteRoute(versionName: String, versionCode: Int): String {
        return "$VERSION_NOTE_PREFIX/$versionName/$versionCode"
    }
}
