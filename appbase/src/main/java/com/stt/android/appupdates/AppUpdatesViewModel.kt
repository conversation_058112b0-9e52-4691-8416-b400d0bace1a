package com.stt.android.appupdates

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.appupdates.model.AppReleaseNoteUiState
import com.stt.android.appupdates.model.AppUpgradeUiState
import com.stt.android.appupdates.usecase.AppReleaseNoteUseCase
import com.stt.android.appupdates.usecase.AppUpgradeUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class AppUpdatesViewModel @Inject constructor(
    private val appUpgradeUseCase: AppUpgradeUseCase,
    private val appReleaseNoteUseCase: AppReleaseNoteUseCase,
) : ViewModel() {

    private val _appUpgradeUiState = MutableStateFlow<AppUpgradeUiState>(AppUpgradeUiState.Loading)
    val appUpgradeUiState = _appUpgradeUiState.asStateFlow()

    private val _appReleaseNoteUiState = MutableStateFlow<AppReleaseNoteUiState?>(null)
    val appReleaseNoteUiState = _appReleaseNoteUiState.asStateFlow()

    init {
        viewModelScope.launch {
            checkForUpdates()
        }
        _appUpgradeUiState.filterIsInstance<AppUpgradeUiState.Success>()
            .onEach {
                fetchReleaseNote(it.versionName, it.versionCode)
            }
            .launchIn(viewModelScope)
    }

    suspend fun checkForUpdates() {
        _appUpgradeUiState.value = AppUpgradeUiState.Loading
        _appUpgradeUiState.value = appUpgradeUseCase()
    }

    suspend fun fetchReleaseNote(versionName: String, versionCode: Int) {
        _appReleaseNoteUiState.value = AppReleaseNoteUiState.Loading(versionName, versionCode)
        _appReleaseNoteUiState.value = appReleaseNoteUseCase(versionName, versionCode)
    }
}
