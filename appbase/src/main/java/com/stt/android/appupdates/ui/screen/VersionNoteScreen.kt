package com.stt.android.appupdates.ui.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.appupdates.VersionNoteViewModel
import com.stt.android.appupdates.model.AppReleaseNoteUiState
import com.stt.android.appupdates.ui.widget.AppUpdatesTopBar
import com.stt.android.appupdates.ui.widget.AppVersionView
import com.stt.android.appupdates.ui.widget.ReleaseNoteError
import com.stt.android.appupdates.ui.widget.ReleaseNoteLoading
import com.stt.android.appupdates.ui.widget.TemplateWebView
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.theme.nearWhite
import kotlinx.coroutines.launch

@Composable
internal fun VersionNoteScreen(
    viewModel: VersionNoteViewModel,
    onNavigateUp: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val coroutineScope = rememberCoroutineScope()

    Column(
        modifier = modifier
            .background(MaterialTheme.colorScheme.nearWhite)
            .fillMaxSize(),
    ) {
        AppUpdatesTopBar(
            titleText = stringResource(R.string.app_updates_version_note_title),
            onBackPressed = onNavigateUp,
        )
        ContentCenteringColumn(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f),
        ) {
            val uiState = viewModel.appReleaseNoteUiState.collectAsState().value
            LazyColumn(modifier = Modifier.fillMaxSize()) {
                item(key = "app_version") {
                    AppVersionView(version = uiState.versionName)
                }
                item(key = "app_version_divider") {
                    Spacer(modifier = Modifier.height(10.dp))
                }
                item(key = "release_note") {
                    when (uiState) {
                        is AppReleaseNoteUiState.Success ->
                            TemplateWebView(template = uiState.releaseNote)

                        is AppReleaseNoteUiState.Loading -> ReleaseNoteLoading()
                        is AppReleaseNoteUiState.Error -> ReleaseNoteError(onTryAgainClicked = {
                            coroutineScope.launch {
                                viewModel.fetchReleaseNote()
                            }
                        })
                    }
                }
            }
        }
    }
}
