package com.stt.android.appupdates.model

sealed class AppReleaseNoteUiState {
    abstract val versionName: String
    abstract val versionCode: Int

    data class Loading(
        override val versionName: String,
        override val versionCode: Int,
    ) : AppReleaseNoteUiState()

    data class Success(
        override val versionName: String,
        override val versionCode: Int,
        val releaseNote: String,
    ) : AppReleaseNoteUiState()

    data class Error(
        override val versionName: String,
        override val versionCode: Int,
        val throwable: Throwable,
    ) : AppReleaseNoteUiState()
}
