package com.stt.android.appupdates.usecase

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.appversion.AppVersionRepository
import com.stt.android.remote.appversion.AppVersionInfo
import javax.inject.Inject

class VersionListPagingSource @Inject constructor(
    private val appVersionRepository: AppVersionRepository,
) : PagingSource<Int, AppVersionInfo>() {
    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, AppVersionInfo> {
        return runSuspendCatching {
            val nextPageNum = params.key ?: 1
            val list = appVersionRepository.getVersionList(nextPageNum, params.loadSize)
            if (list == null) throw Exception("Failed to load data")
            val hasMore = list.size == params.loadSize
            LoadResult.Page(
                data = list,
                prevKey = null,
                nextKey = if (hasMore) nextPageNum + 1 else null,
            )
        }.fold(
            onSuccess = { it },
            onFailure = { LoadResult.Error(it) },
        )
    }

    override fun getRefreshKey(state: PagingState<Int, AppVersionInfo>): Int? {
        return state.anchorPosition?.let { anchorPosition ->
            val anchorPage = state.closestPageToPosition(anchorPosition)
            anchorPage?.prevKey?.plus(1) ?: anchorPage?.nextKey?.minus(1)
        }
    }
}
