package com.stt.android.appupdates.ui.navigation

import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import com.stt.android.appupdates.ui.screen.OldVersionsScreen

internal fun NavGraphBuilder.oldVersionsDestination(
    onNavigateUp: () -> Unit,
    onVersionSelected: (String, Int) -> Unit,
) {
    composable(AppUpdatesRoute.OLD_VERSIONS) {
        OldVersionsScreen(
            viewModel = hiltViewModel(),
            onNavigateUp = onNavigateUp,
            onVersionSelected = onVersionSelected,
        )
    }
}

internal fun NavController.navigateToOldVersions() = navigate(AppUpdatesRoute.OLD_VERSIONS) {
    launchSingleTop = true
}

internal fun NavController.popUpToOldVersions() = popBackStack(
    AppUpdatesRoute.OLD_VERSIONS,
    inclusive = false,
)
