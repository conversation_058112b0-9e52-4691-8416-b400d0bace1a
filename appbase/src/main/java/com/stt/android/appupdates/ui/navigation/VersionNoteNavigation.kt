package com.stt.android.appupdates.ui.navigation

import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import com.stt.android.appupdates.ui.screen.VersionNoteScreen

internal fun NavGraphBuilder.versionNoteDestination(
    onNavigateUp: () -> Unit,
) {
    composable(
        AppUpdatesRoute.VERSION_NOTE,
        arguments = listOf(
            navArgument(AppUpdatesRoute.VERSION_NOTE_ARG_VERSION_NAME) {
                type = NavType.StringType
            },
            navArgument(AppUpdatesRoute.VERSION_NOTE_ARG_VERSION_CODE) {
                type = NavType.IntType
            }
        )
    ) {
        VersionNoteScreen(
            viewModel = hiltViewModel(),
            onNavigateUp = onNavigateUp,
        )
    }
}

internal fun NavController.navigateToVersionNote(
    versionName: String,
    versionCode: Int,
) = navigate(AppUpdatesRoute.buildVersionNoteRoute(versionName, versionCode)) {
    launchSingleTop = true
}
