package com.stt.android.appupdates.usecase

import com.stt.android.appupdates.model.AppUpgradeUiState
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.appversion.AppVersionRepository
import com.stt.android.di.VersionCode
import com.stt.android.di.VersionName
import kotlinx.coroutines.withContext
import javax.inject.Inject

class AppUpgradeUseCase @Inject constructor(
    private val appVersionRepository: AppVersionRepository,
    @VersionCode private val currentVersion: Int,
    @VersionName private val appVersionName: String,
    private val dispatchers: CoroutinesDispatchers,
) {
    suspend operator fun invoke() = withContext(dispatchers.io) {
        runSuspendCatching {
            appVersionRepository.checkAppVersion()
        }.fold(
            onSuccess = { info ->
                info?.let {
                    AppUpgradeUiState.Success(
                        versionName = it.appVersionCode,
                        versionCode = it.innerAppVersionCode,
                        needUpdate = true,
                        appStoreLink = it.appStoreLink,
                    )
                } ?: AppUpgradeUiState.Success(
                    versionName = appVersionName,
                    versionCode = currentVersion,
                    needUpdate = false,
                    appStoreLink = "",
                )
            },
            onFailure = { error ->
                AppUpgradeUiState.Error(error)
            },
        )
    }
}
