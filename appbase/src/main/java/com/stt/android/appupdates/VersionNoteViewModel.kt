package com.stt.android.appupdates

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.appupdates.model.AppReleaseNoteUiState
import com.stt.android.appupdates.ui.navigation.AppUpdatesRoute.VERSION_NOTE_ARG_VERSION_CODE
import com.stt.android.appupdates.ui.navigation.AppUpdatesRoute.VERSION_NOTE_ARG_VERSION_NAME
import com.stt.android.appupdates.usecase.AppReleaseNoteUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class VersionNoteViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    private val appReleaseNoteUseCase: AppReleaseNoteUseCase,
) : ViewModel() {

    private val versionName: String = savedStateHandle[VERSION_NOTE_ARG_VERSION_NAME]!!
    private val versionCode: Int = savedStateHandle[VERSION_NOTE_ARG_VERSION_CODE]!!

    private val _appReleaseNoteUiState = MutableStateFlow<AppReleaseNoteUiState>(
        AppReleaseNoteUiState.Loading(versionName, versionCode)
    )
    val appReleaseNoteUiState = _appReleaseNoteUiState.asStateFlow()

    init {
        viewModelScope.launch {
            fetchReleaseNote()
        }
    }

    suspend fun fetchReleaseNote() {
        _appReleaseNoteUiState.value = AppReleaseNoteUiState.Loading(versionName, versionCode)
        _appReleaseNoteUiState.value = appReleaseNoteUseCase(versionName, versionCode)
    }
}
