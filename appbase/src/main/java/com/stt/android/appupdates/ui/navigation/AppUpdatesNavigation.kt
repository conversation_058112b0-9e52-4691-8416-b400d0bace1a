package com.stt.android.appupdates.ui.navigation

import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import com.stt.android.appupdates.ui.screen.AppUpdatesScreen

internal fun NavGraphBuilder.appUpdatesDestination(
    onNavigateUp: () -> Unit,
    onAppVersionClicked: () -> Unit,
    onUpdateClicked: (String) -> Unit,
    onOldVersionsClicked: () -> Unit,
) {
    composable(AppUpdatesRoute.APP_UPDATES) {
        AppUpdatesScreen(
            viewModel = hiltViewModel(),
            onNavigateUp = onNavigateUp,
            onAppVersionClicked = onAppVersionClicked,
            onUpdateClicked = onUpdateClicked,
            onOldVersionsClicked = onOldVersionsClicked,
        )
    }
}

internal fun NavController.popUpToAppUpdates() = popBackStack(
    AppUpdatesRoute.APP_UPDATES,
    inclusive = false,
)
