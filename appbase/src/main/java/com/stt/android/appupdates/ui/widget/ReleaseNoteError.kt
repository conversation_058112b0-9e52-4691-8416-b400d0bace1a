package com.stt.android.appupdates.ui.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.material3.bodyBold
import com.stt.android.compose.theme.spacing

@Composable
internal fun ReleaseNoteError(
    onTryAgainClicked: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .background(MaterialTheme.colorScheme.surface)
            .fillMaxWidth()
            .aspectRatio(2f),
        contentAlignment = Alignment.Center,
    ) {
        Button(
            modifier = Modifier
                .padding(all = MaterialTheme.spacing.medium)
                .fillMaxWidth()
                .defaultMinSize(minHeight = 48.dp),
            onClick = onTryAgainClicked,
            colors = ButtonDefaults.buttonColors(
                containerColor = Color.Transparent,
                contentColor = MaterialTheme.colorScheme.primary,
            ),
            shape = RoundedCornerShape(8.dp),
        ) {
            Text(
                text = stringResource(R.string.try_again).uppercase(),
                style = MaterialTheme.typography.bodyBold,
            )
        }
    }
}
