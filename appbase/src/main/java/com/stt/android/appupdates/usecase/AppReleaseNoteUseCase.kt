package com.stt.android.appupdates.usecase

import com.stt.android.appupdates.model.AppReleaseNoteUiState
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.appversion.AppVersionRepository
import kotlinx.coroutines.withContext
import javax.inject.Inject

class AppReleaseNoteUseCase @Inject constructor(
    private val appVersionRepository: AppVersionRepository,
    private val dispatchers: CoroutinesDispatchers,
) {
    suspend operator fun invoke(
        versionName: String,
        versionCode: Int,
    ) = withContext(dispatchers.io) {
        runSuspendCatching {
            appVersionRepository.getVersionNotes(versionCode)
        }.fold(
            onSuccess = { releaseNote ->
                releaseNote?.let {
                    AppReleaseNoteUiState.Success(versionName, versionCode, releaseNote)
                } ?: AppReleaseNoteUiState.Error(
                    versionName,
                    versionCode,
                    Exception("Release note is null"),
                )
            },
            onFailure = {
                AppReleaseNoteUiState.Error(versionName, versionCode, it)
            },
        )
    }
}
