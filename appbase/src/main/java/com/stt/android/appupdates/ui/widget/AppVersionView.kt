package com.stt.android.appupdates.ui.widget

import androidx.compose.foundation.Indication
import androidx.compose.foundation.LocalIndication
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.spacing

@Composable
internal fun AppVersionView(
    version: String,
    modifier: Modifier = Modifier,
    summary: String? = null,
    showIcon: Boolean = true,
    showExtra: Boolean = false,
    indication: Indication? = LocalIndication.current,
    onClick: (() -> Unit)? = null,
) {
    Row(
        modifier = modifier
            .then(onClick?.let {
                Modifier.clickable(
                    onClick = it,
                    indication = indication,
                    interactionSource = null,
                )
            } ?: Modifier)
            .background(MaterialTheme.colorScheme.surface)
            .fillMaxWidth()
            .defaultMinSize(minHeight = 70.dp)
            .padding(MaterialTheme.spacing.medium),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        if (showIcon) {
            Icon(
                modifier = Modifier.size(MaterialTheme.iconSizes.medium),
                painter = painterResource(R.drawable.phone_outline),
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurface,
            )
        }
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
        ) {
            Text(
                text = stringResource(R.string.app_updates_version, version),
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
            )
            summary?.let {
                Text(
                    text = it,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.secondary,
                )
            }
        }
        if (showExtra) {
            Icon(
                painter = painterResource(R.drawable.chevron_right),
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurface,
            )
        }
    }
}

@Preview
@Composable
private fun AppVersionViewPreview() {
    M3AppTheme {
        Column(verticalArrangement = Arrangement.spacedBy(1.dp)) {
            AppVersionView(
                version = "1.2.3",
                summary = "4.08M",
                showIcon = true,
                showExtra = false,
            )
            AppVersionView(
                version = "1.2.3",
                summary = "2025/3/3",
                showIcon = false,
                showExtra = true,
            )
        }
    }
}
