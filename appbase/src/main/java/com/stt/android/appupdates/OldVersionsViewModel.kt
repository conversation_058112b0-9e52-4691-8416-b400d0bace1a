package com.stt.android.appupdates

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.cachedIn
import androidx.paging.map
import com.stt.android.appupdates.model.AppVersionInfoModel
import com.stt.android.appupdates.usecase.VersionListPagingSource
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.map
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.format.FormatStyle
import javax.inject.Inject

@HiltViewModel
class OldVersionsViewModel @Inject constructor(
    private val pagingSource: VersionListPagingSource,
) : ViewModel() {

    val versionList = Pager(PagingConfig(pageSize = PAGE_SIZE)) {
        pagingSource
    }.flow.map {
        val formatter = DateTimeFormatter.ofPattern(DATETIME_PATTERN)
        it.map {
            AppVersionInfoModel(
                id = it.id,
                versionName = it.appVersionCode,
                versionCode = it.innerAppVersionCode,
                createdDate = runCatching {
                    it.createdTime.parseToDateTime(formatter).toDateFormat()
                }.getOrNull() ?: it.createdTime,
            )
        }
    }.cachedIn(viewModelScope)

    private fun String.parseToDateTime(formatter: DateTimeFormatter): LocalDateTime {
        return LocalDateTime.parse(this, formatter)
    }

    private fun LocalDateTime.toDateFormat(): String {
        return DateTimeFormatter.ofLocalizedDate(FormatStyle.SHORT).format(this)
    }

    companion object {
        private const val PAGE_SIZE = 10
        private const val DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss"
    }
}
