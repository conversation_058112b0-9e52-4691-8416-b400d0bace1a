package com.stt.android.appupdates.ui.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.paging.compose.collectAsLazyPagingItems
import androidx.paging.compose.itemKey
import com.stt.android.R
import com.stt.android.appupdates.OldVersionsViewModel
import com.stt.android.appupdates.ui.widget.AppUpdatesTopBar
import com.stt.android.appupdates.ui.widget.AppVersionView
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.theme.nearWhite

@Composable
internal fun OldVersionsScreen(
    viewModel: OldVersionsViewModel,
    onNavigateUp: () -> Unit,
    onVersionSelected: (String, Int) -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .background(MaterialTheme.colorScheme.nearWhite)
            .fillMaxSize(),
    ) {
        AppUpdatesTopBar(
            titleText = stringResource(R.string.app_updates_old_versions_title),
            onBackPressed = onNavigateUp,
        )
        ContentCenteringColumn(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f),
        ) {
            val list = viewModel.versionList.collectAsLazyPagingItems()
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(vertical = 10.dp),
                verticalArrangement = Arrangement.spacedBy(1.dp),
            ) {
                items(
                    list.itemCount,
                    key = list.itemKey { it.id },
                ) { index ->
                    list[index]?.let { info ->
                        AppVersionView(
                            version = info.versionName,
                            summary = info.createdDate,
                            showIcon = false,
                            showExtra = true,
                            onClick = {
                                onVersionSelected(info.versionName, info.versionCode)
                            },
                        )
                    }
                }
            }
        }
    }
}
