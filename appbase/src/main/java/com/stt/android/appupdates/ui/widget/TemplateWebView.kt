package com.stt.android.appupdates.ui.widget

import android.annotation.SuppressLint
import android.content.Intent
import android.view.View
import android.view.ViewGroup
import android.webkit.WebChromeClient
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.FrameLayout
import android.widget.ProgressBar
import androidx.activity.compose.LocalActivity
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.view.isVisible

private const val TAG_TEMPLATE_WEB_VIEW = "template_web_view"

@Stable
internal class TemplateWebViewState {
    var webViewContainer by mutableStateOf<ViewGroup?>(null)

    var minHeight by mutableIntStateOf(0)
}

@Composable
internal fun rememberTemplateWebViewState() = remember { TemplateWebViewState() }

@SuppressLint("SetJavaScriptEnabled")
@Composable
internal fun TemplateWebView(
    template: String,
    modifier: Modifier = Modifier,
    webViewState: TemplateWebViewState = rememberTemplateWebViewState(),
) {
    val activity = LocalActivity.current ?: return

    AndroidView(
        modifier = modifier
            .background(MaterialTheme.colorScheme.surface)
            .fillMaxWidth()
            .defaultMinSize(minHeight = with(LocalDensity.current) { webViewState.minHeight.toDp() }),
        factory = { context ->
            webViewState.webViewContainer ?: WebView(context).apply {
                tag = TAG_TEMPLATE_WEB_VIEW

                // https://github.com/google/accompanist/issues/1442
                clipToOutline = true

                settings.javaScriptEnabled = true
                settings.domStorageEnabled = true
                settings.mediaPlaybackRequiresUserGesture = false

                webChromeClient = object : WebChromeClient() {
                    private var customView: View? = null
                    private var customViewCallback: CustomViewCallback? = null
                    private var originalOrientation: Int = 0
                    private var fullscreenContainer: FrameLayout? = null

                    override fun onShowCustomView(view: View?, callback: CustomViewCallback?) {
                        if (customView != null) {
                            onHideCustomView()
                        }

                        customView = view
                        customViewCallback = callback
                        originalOrientation = activity.requestedOrientation

                        activity.window.decorView.systemUiVisibility = (
                            View.SYSTEM_UI_FLAG_FULLSCREEN
                                or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                                or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                            )

                        fullscreenContainer = FrameLayout(activity).apply {
                            layoutParams = FrameLayout.LayoutParams(
                                ViewGroup.LayoutParams.MATCH_PARENT,
                                ViewGroup.LayoutParams.MATCH_PARENT
                            )
                            addView(view)
                        }

                        (activity.window.decorView as FrameLayout).addView(fullscreenContainer)
                        <EMAIL> = false
                    }

                    override fun onHideCustomView() {
                        customView?.let {
                            (activity.window.decorView as FrameLayout).removeView(
                                fullscreenContainer
                            )
                            fullscreenContainer = null
                            customView = null
                            <EMAIL> = true
                            customViewCallback?.onCustomViewHidden()
                            activity.requestedOrientation = originalOrientation
                            activity.window.decorView.systemUiVisibility =
                                View.SYSTEM_UI_FLAG_VISIBLE
                        }
                    }

                    override fun getVideoLoadingProgressView(): View {
                        return ProgressBar(activity)
                    }
                }

                webViewClient = object : WebViewClient() {
                    override fun shouldOverrideUrlLoading(
                        view: WebView?,
                        request: WebResourceRequest?
                    ): Boolean {
                        val uri = request?.url
                        val url = uri?.toString()
                        if (url?.startsWith("http") == true || url?.startsWith("https") == true) {
                            runCatching {
                                @Suppress("UnsafeImplicitIntentLaunch")
                                activity.startActivity(Intent(Intent.ACTION_VIEW).setData(uri))
                            }
                            return true
                        }

                        return false
                    }

                    override fun onPageFinished(view: WebView?, url: String?) {
                        view?.post {
                            view.evaluateJavascript(
                                "(function() { return document.body.scrollHeight; })();"
                            ) { heightStr ->
                                heightStr?.toIntOrNull()?.let { height ->
                                    webViewState.minHeight = height
                                }
                            }
                        }
                    }
                }
            }.let {
                // https://wbrawner.com/2024/08/28/android-webview-crash-in-jetpack-compose/
                FrameLayout(context).apply { addView(it) }
            }.also {
                webViewState.webViewContainer = it
            }
        },
        update = { webViewContainer ->
            webViewContainer.findViewWithTag<WebView>(TAG_TEMPLATE_WEB_VIEW)
                .loadDataWithBaseURL(null, template, "text/html", "UTF-8", null)
        },
    )
}
