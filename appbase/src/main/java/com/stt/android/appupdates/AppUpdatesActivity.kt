package com.stt.android.appupdates

import android.annotation.SuppressLint
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.net.toUri
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.rememberNavController
import com.stt.android.R
import com.stt.android.STTApplication
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.appupdates.ui.navigation.AppUpdatesRoute
import com.stt.android.appupdates.ui.navigation.appUpdatesDestination
import com.stt.android.appupdates.ui.navigation.navigateToOldVersions
import com.stt.android.appupdates.ui.navigation.navigateToVersionNote
import com.stt.android.appupdates.ui.navigation.oldVersionsDestination
import com.stt.android.appupdates.ui.navigation.popUpToAppUpdates
import com.stt.android.appupdates.ui.navigation.popUpToOldVersions
import com.stt.android.appupdates.ui.navigation.versionNoteDestination
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.eventtracking.EventTracker
import com.stt.android.ui.extensions.shortToast
import com.stt.android.utils.FlavorUtils
import dagger.hilt.android.AndroidEntryPoint
import java.util.concurrent.atomic.AtomicInteger
import javax.inject.Inject

/**
 * It has an "easter egg" that can be
 * activated by the user by clicking {@link #CLICKS_TO_ACTIVATE_EASTER_EGG}
 * times the preference. The easter eggs performs the following actions:
 * <p/>
 * <pre>
 * - Copy the main app database to the sdcard
 * - Copy the shared preferences to the sdcard
 * - Copy the not-sync workouts to the sdcard
 * - Trigger an error report silently with a custom exception
 * - Activate writing logs to the sdcard
 * </pre>
 * <p/>
 * Writing logs to the sdcard can slow down the app so the user can disable it
 * by clicking again {@link #CLICKS_TO_ACTIVATE_EASTER_EGG} times the
 * preference.
 */
@AndroidEntryPoint
class AppUpdatesActivity : AppCompatActivity() {

    private val mainHandler = Handler(Looper.getMainLooper())
    private val resetCounter = Runnable { clickCounter.set(0) }
    private val clickCounter = AtomicInteger(0)

    @Inject
    lateinit var eventTracker: EventTracker

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentWithM3Theme {
            val navController = rememberNavController()
            NavHost(
                navController = navController,
                startDestination = AppUpdatesRoute.APP_UPDATES,
            ) {
                appUpdatesDestination(
                    onNavigateUp = { onBackPressedDispatcher.onBackPressed() },
                    onAppVersionClicked = ::onAppVersionClicked,
                    onUpdateClicked = ::showAppUpdateConfirmDialog,
                    onOldVersionsClicked = { navController.navigateToOldVersions() },
                )
                oldVersionsDestination(
                    onNavigateUp = { navController.popUpToAppUpdates() },
                    onVersionSelected = { versionName, versionCode ->
                        navController.navigateToVersionNote(versionName, versionCode)
                    },
                )
                versionNoteDestination(
                    onNavigateUp = { navController.popUpToOldVersions() },
                )
            }
        }
    }

    private fun onAppVersionClicked() {
        if (clickCounter.incrementAndGet() >= CLICKS_TO_ACTIVATE_EASTER_EGG) {
            mainHandler.removeCallbacks(resetCounter)
            clickCounter.set(0)

            // EasterEgg activated.
            val application = applicationContext as STTApplication
            application.toggleEasterEgg()
        } else {
            mainHandler.removeCallbacks(resetCounter)
            mainHandler.postDelayed(resetCounter, RESET_COUNTER_DELAY)
        }
    }

    @SuppressLint("UnsafeImplicitIntentLaunch")
    private fun showAppUpdateConfirmDialog(url: String) {
        val storeName =
            getString(if (FlavorUtils.isSuuntoAppChina) R.string.app_updates_app_store else R.string.app_updates_google_play_store)
        AlertDialog.Builder(this)
            .setTitle(getString(R.string.app_updates_confirm_dialog_title, storeName))
            .setPositiveButton(R.string.app_updates_open) { _, _ ->
                try {
                    val intent = Intent(Intent.ACTION_VIEW, url.toUri())
                    startActivity(intent)

                    trackAppUpdateEvent()
                } catch (_: ActivityNotFoundException) {
                    shortToast(R.string.app_updates_store_not_found)
                }
            }
            .setNegativeButton(android.R.string.cancel, null)
            .show()
    }

    private fun trackAppUpdateEvent() {
        intent.getStringExtra(EXTRA_SOURCE)?.let { source ->
            eventTracker.trackEvent(
                AnalyticsEvent.APP_UPDATE_NOW_BUTTON_CLICK,
                mapOf(AnalyticsEventProperty.APP_UPDATE_NOW_SOURCE to source),
            )
        }
    }

    companion object {
        private const val RESET_COUNTER_DELAY = 5000L
        private const val CLICKS_TO_ACTIVATE_EASTER_EGG = 7

        private const val EXTRA_SOURCE = "com.stt.android.appupdates.EXTRA_SOURCE"

        const val SOURCE_MANDATORY_UPDATE_POPUP = "MandatoryUpdatePopup"
        const val SOURCE_RECOMMENDED_UPDATE_POPUP = "RecommendedUpdatePopup"
        const val SOURCE_SETTINGS = "Settings"

        @JvmStatic
        fun newStartIntent(context: Context, source: String): Intent {
            return Intent(context, AppUpdatesActivity::class.java).putExtra(EXTRA_SOURCE, source)
        }
    }
}
