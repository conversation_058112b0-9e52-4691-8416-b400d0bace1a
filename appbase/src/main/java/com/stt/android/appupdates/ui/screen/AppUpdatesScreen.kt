package com.stt.android.appupdates.ui.screen

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.appupdates.AppUpdatesViewModel
import com.stt.android.appupdates.model.AppReleaseNoteUiState
import com.stt.android.appupdates.model.AppUpgradeUiState
import com.stt.android.appupdates.ui.widget.AppUpdatesTopBar
import com.stt.android.appupdates.ui.widget.AppVersionView
import com.stt.android.appupdates.ui.widget.ReleaseNoteError
import com.stt.android.appupdates.ui.widget.ReleaseNoteLoading
import com.stt.android.appupdates.ui.widget.TemplateWebView
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.modifiers.clickable
import com.stt.android.compose.theme.material3.bodyBold
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.nearWhite
import com.stt.android.compose.theme.spacing
import kotlinx.coroutines.launch

@Composable
internal fun AppUpdatesScreen(
    viewModel: AppUpdatesViewModel,
    onNavigateUp: () -> Unit,
    onAppVersionClicked: () -> Unit,
    onUpdateClicked: (String) -> Unit,
    onOldVersionsClicked: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val coroutineScope = rememberCoroutineScope()

    Column(
        modifier = modifier
            .background(MaterialTheme.colorScheme.nearWhite)
            .fillMaxSize(),
    ) {
        AppUpdatesTopBar(
            titleText = stringResource(R.string.app_updates_title),
            onBackPressed = onNavigateUp,
        )
        ContentCenteringColumn(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f),
        ) {
            val upgradeUiState by viewModel.appUpgradeUiState.collectAsState()
            when (val uiState = upgradeUiState) {
                is AppUpgradeUiState.Success -> {
                    val releaseNoteUiState by viewModel.appReleaseNoteUiState.collectAsState()
                    AppUpgradeSuccess(
                        uiState = uiState,
                        releaseNoteUiState = releaseNoteUiState,
                        onAppVersionClicked = onAppVersionClicked,
                        onUpdateClicked = onUpdateClicked,
                        onOldVersionsClicked = onOldVersionsClicked,
                        onReleaseNoteTryAgainClicked = {
                            coroutineScope.launch {
                                viewModel.fetchReleaseNote(uiState.versionName, uiState.versionCode)
                            }
                        },
                    )
                }

                AppUpgradeUiState.Loading -> AppUpgradeLoading()
                is AppUpgradeUiState.Error -> AppUpgradeError(
                    onLaterClicked = onNavigateUp,
                    onTryAgainClicked = {
                        coroutineScope.launch {
                            viewModel.checkForUpdates()
                        }
                    },
                )
            }
        }
    }
}

@Composable
private fun AppUpgradeSuccess(
    uiState: AppUpgradeUiState.Success,
    releaseNoteUiState: AppReleaseNoteUiState?,
    onAppVersionClicked: () -> Unit,
    onUpdateClicked: (String) -> Unit,
    onOldVersionsClicked: () -> Unit,
    onReleaseNoteTryAgainClicked: () -> Unit,
    modifier: Modifier = Modifier,
) {
    LazyColumn(modifier = modifier.fillMaxSize()) {
        item(key = "app_version_header") {
            Text(
                modifier = Modifier.padding(MaterialTheme.spacing.medium),
                text = if (uiState.needUpdate)
                    stringResource(R.string.app_updates_update_available)
                else stringResource(R.string.app_updates_latest_version),
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
            )
        }
        item(key = "app_version") {
            AppVersionView(
                version = uiState.versionName,
                onClick = onAppVersionClicked,
                indication = null,
            )
        }
        if (uiState.needUpdate) {
            item(key = "app_version_update") {
                AppVersionUpdateView(onClick = { onUpdateClicked(uiState.appStoreLink) })
            }
        }
        item(key = "app_version_divider") {
            Spacer(modifier = Modifier.height(10.dp))
        }
        if (releaseNoteUiState != null) {
            item(key = "release_note") {
                when (releaseNoteUiState) {
                    is AppReleaseNoteUiState.Success ->
                        TemplateWebView(template = releaseNoteUiState.releaseNote)

                    is AppReleaseNoteUiState.Loading -> ReleaseNoteLoading()
                    is AppReleaseNoteUiState.Error -> ReleaseNoteError(onTryAgainClicked = onReleaseNoteTryAgainClicked)
                }
            }
            item(key = "release_note_divider") {
                Spacer(modifier = Modifier.height(10.dp))
            }
        }
        item(key = "old_versions") {
            OldVersionsView(onClick = onOldVersionsClicked)
        }
    }
}

@Composable
private fun OldVersionsView(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .clickable(onClick = onClick)
            .background(MaterialTheme.colorScheme.surface)
            .fillMaxWidth()
            .defaultMinSize(minHeight = 64.dp)
            .padding(MaterialTheme.spacing.medium),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
            modifier = Modifier.weight(1f),
            text = stringResource(R.string.app_updates_old_versions_title),
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurface,
        )
        Icon(
            painter = painterResource(R.drawable.ic_trailing),
            contentDescription = null,
            tint = MaterialTheme.colorScheme.onSurface,
        )
    }
}

@Composable
private fun AppUpgradeLoading(modifier: Modifier = Modifier) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center,
    ) {
        CircularProgressIndicator()
    }
}

@Composable
private fun AppUpgradeError(
    onLaterClicked: () -> Unit,
    onTryAgainClicked: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .padding(top = 10.dp)
            .background(MaterialTheme.colorScheme.surface)
            .fillMaxSize(),
    ) {
        Column(
            modifier = Modifier
                .verticalScroll(rememberScrollState())
                .fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(1f),
                painter = painterResource(R.drawable.ic_generic_failed),
                contentDescription = null,
                contentScale = ContentScale.Inside,
            )
            Text(
                modifier = Modifier.padding(
                    start = MaterialTheme.spacing.medium,
                    end = MaterialTheme.spacing.medium,
                    top = MaterialTheme.spacing.small,
                ),
                text = stringResource(R.string.app_updates_error),
                style = MaterialTheme.typography.bodyLargeBold,
                color = MaterialTheme.colorScheme.onSurface,
                textAlign = TextAlign.Center,
            )
            Text(
                modifier = Modifier.padding(
                    start = MaterialTheme.spacing.medium,
                    end = MaterialTheme.spacing.medium,
                    top = 6.dp,
                    bottom = MaterialTheme.spacing.small,
                ),
                text = stringResource(R.string.app_updates_error_tips),
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.secondary,
                textAlign = TextAlign.Center,
            )
            Spacer(modifier = Modifier.height(70.dp))
            Button(
                modifier = Modifier
                    .padding(all = MaterialTheme.spacing.medium)
                    .fillMaxWidth()
                    .defaultMinSize(minHeight = 48.dp),
                onClick = onLaterClicked,
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color.Transparent,
                    contentColor = MaterialTheme.colorScheme.primary,
                ),
                shape = RoundedCornerShape(8.dp),
            ) {
                Text(
                    text = stringResource(R.string.later).uppercase(),
                    style = MaterialTheme.typography.bodyBold,
                )
            }
            Button(
                modifier = Modifier
                    .padding(all = MaterialTheme.spacing.medium)
                    .fillMaxWidth()
                    .defaultMinSize(minHeight = 48.dp),
                onClick = onTryAgainClicked,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    contentColor = MaterialTheme.colorScheme.onPrimary,
                ),
                shape = RoundedCornerShape(8.dp),
            ) {
                Text(
                    text = stringResource(R.string.try_again).uppercase(),
                    style = MaterialTheme.typography.bodyBold,
                )
            }
        }
    }
}

@Composable
private fun AppVersionUpdateView(onClick: () -> Unit, modifier: Modifier = Modifier) {
    Box(
        modifier = modifier
            .background(MaterialTheme.colorScheme.surface)
            .fillMaxWidth()
            .padding(MaterialTheme.spacing.medium),
    ) {
        Button(
            modifier = Modifier
                .fillMaxWidth()
                .defaultMinSize(minHeight = 48.dp),
            onClick = onClick,
            colors = ButtonDefaults.buttonColors(
                containerColor = MaterialTheme.colorScheme.primary,
                contentColor = MaterialTheme.colorScheme.onPrimary,
            ),
            shape = RoundedCornerShape(8.dp),
        ) {
            Text(
                text = stringResource(R.string.app_updates_update_now).uppercase(),
                style = MaterialTheme.typography.bodyBold,
            )
        }
    }
}
