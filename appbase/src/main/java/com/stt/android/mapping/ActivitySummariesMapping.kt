package com.stt.android.mapping

import com.stt.android.domain.workout.ActivityType
import com.stt.android.infomodel.SummaryGraph

object ActivitySummariesMapping {
    /**
     * This method removes the graphs that are read from <PERSON><PERSON> file but not implemented yet
     *
     * @param activityType  type of the activity
     * @param graphNameList original list to be filtered
     * @return list that contains same items as graphNameList minus not implemented graphs
     */
    fun filterUnimplementedGraphs(
        activityType: ActivityType,
        graphNameList: List<SummaryGraph>
    ): List<SummaryGraph> {
        return graphNameList.filter {
            when (it) {
                SummaryGraph.PACE,
                SummaryGraph.SPEED,
                SummaryGraph.ALTITUDE,
                SummaryGraph.POWER,
                SummaryGraph.VERTICALSPEED,
                SummaryGraph.TEMPERATURE,
                SummaryGraph.CADENCE,
                SummaryGraph.SPEEDKNOTS,
                SummaryGraph.SWOLF,
                SummaryGraph.SWIMSTROKERATE,
                SummaryGraph.VERTICALOSCILLATION,
                SummaryGraph.BREATHINGRATE,
                SummaryGraph.AVGBREASTSTROKEBREATHANGLE,
                SummaryGraph.AVGFREESTYLEBREATHANGLE,
                SummaryGraph.DURATION,
                SummaryGraph.BREASTSTROKEHEADANGLE,
                SummaryGraph.FREESTYLEPITCHANGLE,
                SummaryGraph.BREASTSTROKEGLIDETIME,
                SummaryGraph.GROUNDCONTACTTIME,
                SummaryGraph.AVGSKIPSRATE,
                SummaryGraph.AVGSKIPSPERROUND -> true
                SummaryGraph.DEPTH -> activityType.supportsDiveProfile

                SummaryGraph.GASCONSUMPTION,
                SummaryGraph.TANKPRESSURE -> activityType.isDiving
                else -> false
            }
        }
    }
}
