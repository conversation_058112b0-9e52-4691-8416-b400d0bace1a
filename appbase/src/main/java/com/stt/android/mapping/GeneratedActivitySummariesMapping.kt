package com.stt.android.mapping

import com.stt.android.domain.advancedlaps.LapsTableType
import com.stt.android.infomodel.ActivityMapping
import com.stt.android.infomodel.ActivitySummary
import com.stt.android.infomodel.SummaryCategory
import com.stt.android.infomodel.SummaryCategoryMap
import com.stt.android.infomodel.SummaryGraph
import com.stt.android.infomodel.SummaryItem
import com.stt.android.infomodel.getActivitySummaryForActivity

fun getActivitySummaryForActivityId(stId: Int): ActivitySummary {
    val activityMapping: ActivityMapping? = ActivityMapping.entries
        .firstOrNull { it.stId == stId }
    return getActivitySummaryForActivity(activityMapping)
}

fun getSummaryItemListByStId(stId: Int): List<SummaryItem> {
    return getActivitySummaryForActivityId(stId).items
}

fun getSummaryGraphListByStId(stId: Int): List<SummaryGraph> {
    return getActivitySummaryForActivityId(stId).graphs
}

/**
 * Returns list of [SummaryItem]s for that specific activity id, mapped by [SummaryCategory].
 */
fun getSummaryCategoriesByStId(stId: Int, lapsTableType: LapsTableType): Map<SummaryCategory, List<SummaryItem>> {
    val activitySummary = getActivitySummaryForActivityId(stId)
    val items = when (lapsTableType) {
        LapsTableType.MANUAL -> activitySummary.manualLaps
        LapsTableType.INTERVAL -> activitySummary.intervalLaps
        LapsTableType.DISTANCE_AUTO_LAP -> activitySummary.distanceAutoLaps
        LapsTableType.DURATION_AUTO_LAP -> activitySummary.durationAutoLaps
        LapsTableType.ONE_KM_AUTO_LAP -> activitySummary.distanceAutoLaps
        LapsTableType.FIVE_KM_AUTO_LAP -> activitySummary.distanceAutoLaps
        LapsTableType.TEN_KM_AUTO_LAP -> activitySummary.distanceAutoLaps
        LapsTableType.ONE_MILE_AUTO_LAP -> activitySummary.distanceAutoLaps
        LapsTableType.FIVE_MILE_AUTO_LAP -> activitySummary.distanceAutoLaps
        LapsTableType.TEN_MILE_AUTO_LAP -> activitySummary.distanceAutoLaps
        LapsTableType.DOWNHILL -> activitySummary.downhillLaps
        LapsTableType.DIVE -> activitySummary.diveAutoLaps
    }
    return SummaryCategoryMap.mapValues { (_, list) ->
        list.intersect(items).toList().sorted()
    }.filter { it.value.isNotEmpty() }
}
