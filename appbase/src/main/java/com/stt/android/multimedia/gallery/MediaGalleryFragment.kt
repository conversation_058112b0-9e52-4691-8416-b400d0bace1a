package com.stt.android.multimedia.gallery

import android.os.Bundle
import android.view.View
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.GridLayoutManager
import com.google.android.material.snackbar.Snackbar
import com.stt.android.R
import com.stt.android.common.viewstate.ViewStateListFragment2
import com.stt.android.databinding.FragmentMediaGalleryBinding
import com.stt.android.multimedia.picker.MediaPickerHelper2
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

@AndroidEntryPoint
class MediaGalleryFragment : ViewStateListFragment2<MediaGalleryContainer, MediaGalleryViewModel>(
    padWideItems = false
) {

    override val viewModel: MediaGalleryViewModel by activityViewModels()

    override val layoutId = R.layout.fragment_media_gallery

    private val binding: FragmentMediaGalleryBinding get() = requireBinding()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupGrid()
        refreshMedia()
    }

    private fun setupGrid() {
        binding.list.layoutManager = GridLayoutManager(requireContext(), GRID_SPAN)
        (binding.list.itemAnimator as? DefaultItemAnimator)?.supportsChangeAnimations = false
        controller.spanCount = GRID_SPAN
    }

    private fun refreshMedia() {
        val context = context ?: return

        // Load media in fragment code because it depends on Context
        lifecycleScope.launch {
            try {
                val media = withContext(IO) {
                    kotlin.runCatching {
                        MediaPickerHelper2.loadImages(context) +
                            MediaPickerHelper2.loadVideos(context)
                    }
                }

                viewModel.setMedia(media.getOrThrow())
            } catch (e: Exception) {
                if (isBindingAvailable) {
                    Snackbar.make(binding.root, R.string.error_generic, Snackbar.LENGTH_LONG).show()
                }
            }
        }
    }

    companion object {
        private const val GRID_SPAN = 3
    }
}
