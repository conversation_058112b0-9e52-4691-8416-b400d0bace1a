package com.stt.android.multimedia.gallery

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import coil3.load
import com.airbnb.epoxy.AfterPropsSet
import com.airbnb.epoxy.CallbackProp
import com.airbnb.epoxy.ModelProp
import com.airbnb.epoxy.ModelView
import com.stt.android.R
import com.stt.android.coil.placeholderWithFallback
import com.stt.android.databinding.ItemMediaGalleryPickerBinding
import com.stt.android.multimedia.picker.MediaInfoForPicker
import com.stt.android.multimedia.picker.getThumbnailUri

@ModelView(autoLayout = ModelView.Size.MATCH_WIDTH_WRAP_HEIGHT)
class MediaGalleryGridItem
@JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    @set:[ModelProp]
    var mediaInfoForPicker: MediaInfoForPicker? = null

    @set:[ModelProp]
    var itemSelected = false

    @set:[CallbackProp]
    var onClicked: OnClickListener? = null

    val binding = ItemMediaGalleryPickerBinding.inflate(LayoutInflater.from(context), this)

    @AfterPropsSet
    fun setup() {
        val isVideo = mediaInfoForPicker?.isVideo ?: false
        val thumbnailUri = mediaInfoForPicker?.getThumbnailUri(context)
        binding.thumbnail.scaleType = ImageView.ScaleType.CENTER_CROP
        binding.thumbnail.load(thumbnailUri) {
            placeholderWithFallback(binding.thumbnail.context, R.drawable.media_grid_cell_placeholder)
        }

        binding.play.visibility = if (isVideo) VISIBLE else GONE
        binding.selectionIndication.setImageResource(
            if (itemSelected) {
                R.drawable.ic_selected_green
            } else {
                R.drawable.ic_circle_outline
            }
        )

        binding.touchArea.setOnClickListener(onClicked)
    }
}
