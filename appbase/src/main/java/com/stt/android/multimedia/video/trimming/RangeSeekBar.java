package com.stt.android.multimedia.video.trimming;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

public class RangeSeekBar extends View {
    interface Listener {
        void onRangeUpdated(int lowerRangeInPixel, int higherRangeInPixel);
    }

    /**
     * If the horizontal distance between touch point and high light is equal or less than this
     * (in DP), the user can start dragging the seek bar.
     */
    private static final int ENABLE_DRAG_THRESHOLD_IN_DP = 12;
    private static final int SEEK_BAR_WIDTH_IN_DP = 8;
    private static final int SHADOW_COLOR = 0x68FFFFFF;

    private final Paint borderPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private final Paint shadowPaint = new Paint(Paint.ANTI_ALIAS_FLAG);

    private int enableDragThresholdInPixel;
    private int minSeekBarGapInPixel;
    private int maxSeekBarGapInPixel;
    private int maxXInPixel;

    private final Paint seekBarPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private final Paint seekBarHandlePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private float seekBarWidthInPixel;
    private int leftSeekBarX;
    private int rightSeekBarX;
    private boolean leftSeekBarFocused = false;
    private boolean rightSeekBarFocused = false;
    private int minSeekBarX;

    @Nullable
    Listener listener;

    public RangeSeekBar(Context context) {
        super(context);
        init(context);
    }

    public RangeSeekBar(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public RangeSeekBar(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    public RangeSeekBar(Context context, @Nullable AttributeSet attrs, int defStyleAttr,
        int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init(context);
    }

    private void init(Context context) {
        borderPaint.setColor(Color.WHITE);
        shadowPaint.setColor(SHADOW_COLOR);
        seekBarPaint.setColor(Color.BLACK);
        seekBarHandlePaint.setColor(Color.WHITE);

        float density = context.getResources().getDisplayMetrics().density;
        enableDragThresholdInPixel = Math.round(ENABLE_DRAG_THRESHOLD_IN_DP * density);
        seekBarWidthInPixel = SEEK_BAR_WIDTH_IN_DP * density;
        seekBarPaint.setStrokeWidth(seekBarWidthInPixel);
        seekBarHandlePaint.setStrokeWidth(seekBarWidthInPixel / 4.0F);
        leftSeekBarX = minSeekBarX = Math.round(VideoTimelineView.THUMBNAIL_WIDTH_IN_DP * density);
    }

    @Override
    public boolean onTouchEvent(@NonNull MotionEvent event) {
        int action = event.getAction();
        if (action == MotionEvent.ACTION_DOWN) {
            int x = Math.round(event.getX());
            int diffWithLeft = Math.abs(leftSeekBarX - x);
            int diffWithRight = Math.abs(rightSeekBarX - x);
            if (enableDragThresholdInPixel < Math.min(diffWithLeft, diffWithRight)) {
                // The touch point is too far away from the seek bar, do nothing.
                return super.onTouchEvent(event);
            }
            if (diffWithLeft <= diffWithRight) {
                leftSeekBarFocused = true;
                rightSeekBarFocused = false;
                leftSeekBarX = Math.max(minSeekBarX, calculateXForLeftSeekBar(x));
            } else {
                leftSeekBarFocused = false;
                rightSeekBarFocused = true;
                rightSeekBarX = Math.min(maxXInPixel, calculateXForRightSeekBar(x));
            }
            if (listener != null) {
                listener.onRangeUpdated(leftSeekBarX, rightSeekBarX);
            }
            invalidate();
            return true;
        } else if (action == MotionEvent.ACTION_MOVE) {
            if (leftSeekBarFocused) {
                leftSeekBarX =
                    Math.max(minSeekBarX, calculateXForLeftSeekBar(Math.round(event.getX())));
                invalidate();
            } else if (rightSeekBarFocused) {
                rightSeekBarX =
                    Math.min(maxXInPixel, calculateXForRightSeekBar(Math.round(event.getX())));
                invalidate();
            }
            if ((leftSeekBarFocused || rightSeekBarFocused) && listener != null) {
                listener.onRangeUpdated(leftSeekBarX, rightSeekBarX);
            }
            return true;
        } else if (action == MotionEvent.ACTION_CANCEL || action == MotionEvent.ACTION_UP) {
            leftSeekBarFocused = false;
            rightSeekBarFocused = false;
            return true;
        }
        return super.onTouchEvent(event);
    }

    private int calculateXForLeftSeekBar(int x) {
        int calculated;
        int diffWithOther = rightSeekBarX - x;
        if (diffWithOther > maxSeekBarGapInPixel) {
            calculated = rightSeekBarX - maxSeekBarGapInPixel;
        } else if (diffWithOther < minSeekBarGapInPixel) {
            calculated = rightSeekBarX - minSeekBarGapInPixel;
        } else {
            calculated = x;
        }
        return calculated;
    }

    private int calculateXForRightSeekBar(int x) {
        int calculated;
        int diffWithOther = x - leftSeekBarX;
        if (diffWithOther > maxSeekBarGapInPixel) {
            calculated = leftSeekBarX + maxSeekBarGapInPixel;
        } else if (diffWithOther < minSeekBarGapInPixel) {
            calculated = leftSeekBarX + minSeekBarGapInPixel;
        } else {
            calculated = x;
        }
        return calculated;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        int width = getWidth();
        int height = getHeight();

        canvas.drawRect(0.0F, 0.0F, leftSeekBarX, height, shadowPaint);
        canvas.drawRect(rightSeekBarX, 0.0F, width, height, shadowPaint);

        float left = leftSeekBarX + seekBarWidthInPixel / 2.0F;
        canvas.drawLine(left, 0.0F, left, height, seekBarPaint);
        float right = rightSeekBarX + seekBarWidthInPixel / 2.0F;
        canvas.drawLine(right, 0.0F, right, height, seekBarPaint);

        float top = height * 0.25F;
        float bottom = height * 0.75F;
        canvas.drawLine(left, top, left, bottom, seekBarHandlePaint);
        canvas.drawLine(right, top, right, bottom, seekBarHandlePaint);

        canvas.drawLine(0.0F, 0.0F, width, 0.0F, borderPaint);
        canvas.drawLine(width, 0.0F, width, height, borderPaint);
        canvas.drawLine(width, height, 0.0F, height, borderPaint);
        canvas.drawLine(0.0F, height, 0.0F, 0.0F, borderPaint);
    }

    void setListener(@Nullable Listener listener) {
        this.listener = listener;
    }

    void setGap(int minGapInPixel, int maxGapInPixel) {
        minSeekBarGapInPixel = minGapInPixel;
        maxSeekBarGapInPixel = maxGapInPixel;
        rightSeekBarX = minSeekBarX + maxGapInPixel;

        invalidate();
    }

    void setMaxX(int maxXInPixel) {
        this.maxXInPixel = minSeekBarX + maxXInPixel;
        invalidate();
    }

    int getLowerRangeInPixel() {
        return leftSeekBarX;
    }

    int getHigherRangeInPixel() {
        return rightSeekBarX;
    }
}
