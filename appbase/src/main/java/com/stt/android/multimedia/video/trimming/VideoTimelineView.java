package com.stt.android.multimedia.video.trimming;

import android.content.Context;
import android.media.MediaMetadataRetriever;
import android.net.Uri;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.stt.android.databinding.VideoTimelineViewInnerBinding;
import timber.log.Timber;

public class VideoTimelineView extends FrameLayout implements RangeSeekBar.Listener {
    public interface Listener {
        void onSelectedTimeChanged(long startTimeInMills, long endTimeInMills);
    }

    static final int THUMBNAIL_WIDTH_IN_DP = 48;
    static final int THUMBNAIL_GAP_IN_MILLIS = 3000;
    private static final int MIN_VIDEO_SELECTION_TIME_IN_MILLIS = 1000;
    private static final int MAX_VIDEO_SELECTION_TIME_IN_MILLIS = 15000;

    VideoTimelineViewInnerBinding binding;

    private final RecyclerView.OnScrollListener onScrollListener =
        new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                long startTimeInMillis = getSelectedStartTimeInMills();
                long endTimeInMills = getSelectedEndTimeInMills();
                if (listener != null) {
                    listener.onSelectedTimeChanged(startTimeInMillis, endTimeInMills);
                }
            }
        };

    private TimelineAdapter adapter;
    private LinearLayoutManager layoutManager;
    int thumbnailWidthInPixel;

    @Nullable
    Listener listener;

    public VideoTimelineView(@NonNull Context context) {
        super(context);
        init(context);
    }

    public VideoTimelineView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public VideoTimelineView(@NonNull Context context, @Nullable AttributeSet attrs,
        int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    public VideoTimelineView(@NonNull Context context, @Nullable AttributeSet attrs,
        int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init(context);
    }

    private void init(Context context) {
        binding = VideoTimelineViewInnerBinding.inflate(LayoutInflater.from(getContext()), this);

        layoutManager = new LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false);
        binding.thumbnails.setLayoutManager(layoutManager);

        adapter = new TimelineAdapter(context);
        binding.thumbnails.setAdapter(adapter);

        binding.thumbnails.addOnScrollListener(onScrollListener);
        binding.rangeSeekBar.setListener(this);

        thumbnailWidthInPixel =
            Math.round(THUMBNAIL_WIDTH_IN_DP * getResources().getDisplayMetrics().density);
    }

    @Override
    public void onRangeUpdated(int lowerRangeInPixel, int higherRangeInPixel) {
        long startTimeInMillis = calculateTimeInMillis(lowerRangeInPixel);
        long endTimeInMills = calculateTimeInMillis(higherRangeInPixel);
        if (listener != null) {
            listener.onSelectedTimeChanged(startTimeInMillis, endTimeInMills);
        }
    }

    public void setListener(@Nullable Listener listener) {
        this.listener = listener;
    }

    public void setVideo(Uri video) {
        long mediaDurationInMills;
        try {
            MediaMetadataRetriever metadataRetriever = new MediaMetadataRetriever();
            metadataRetriever.setDataSource(getContext(), video);
            mediaDurationInMills = Long.parseLong(
                metadataRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION));
            metadataRetriever.release();
        } catch (Exception e) {
            Timber.e(e, "Failed to read duration.");
            mediaDurationInMills = 0L;
        }
        adapter.setVideo(video, mediaDurationInMills);

        int minGapInPixel =
            thumbnailWidthInPixel * MIN_VIDEO_SELECTION_TIME_IN_MILLIS / THUMBNAIL_GAP_IN_MILLIS;
        int maxGapInPixel = (int) (thumbnailWidthInPixel * Math.min(mediaDurationInMills,
            MAX_VIDEO_SELECTION_TIME_IN_MILLIS) / THUMBNAIL_GAP_IN_MILLIS);
        binding.rangeSeekBar.setGap(minGapInPixel, maxGapInPixel);

        ViewGroup.LayoutParams layoutParams = binding.rangeSeekBar.getLayoutParams();
        layoutParams.width = adapter.getItemCount() * thumbnailWidthInPixel;
        binding.rangeSeekBar.setLayoutParams(layoutParams);

        final long duration = mediaDurationInMills;
        getRootView().getViewTreeObserver()
            .addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                @Override
                public void onGlobalLayout() {
                    getRootView().getViewTreeObserver().removeOnGlobalLayoutListener(this);

                    int maxXInPixel = (int) Math.min(binding.rangeSeekBar.getWidth(),
                        thumbnailWidthInPixel * duration / THUMBNAIL_GAP_IN_MILLIS);
                    binding.rangeSeekBar.setMaxX(maxXInPixel);
                }
            });
    }

    public long getSelectedStartTimeInMills() {
        return calculateTimeInMillis(binding.rangeSeekBar.getLowerRangeInPixel());
    }

    private long calculateTimeInMillis(int x) {
        int firstVisibleItem = layoutManager.findFirstVisibleItemPosition();
        RecyclerView.ViewHolder viewHolder =
            binding.thumbnails.findViewHolderForAdapterPosition(firstVisibleItem);
        if (viewHolder != null) {
            int offset = viewHolder.itemView.getLeft();
            return Math.round(
                (firstVisibleItem - 1 + ((double) (x - offset)) / thumbnailWidthInPixel)
                    * THUMBNAIL_GAP_IN_MILLIS);
        } else {
            return 0L;
        }
    }

    public long getSelectedEndTimeInMills() {
        return calculateTimeInMillis(binding.rangeSeekBar.getHigherRangeInPixel());
    }
}
