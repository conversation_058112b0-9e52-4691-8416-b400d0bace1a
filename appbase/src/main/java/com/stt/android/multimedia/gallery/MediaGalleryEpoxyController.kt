package com.stt.android.multimedia.gallery

import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.ViewStateEpoxyController
import com.stt.android.multimedia.picker.calculateId
import javax.inject.Inject

class MediaGalleryEpoxyController
@Inject constructor() : ViewStateEpoxyController<MediaGalleryContainer>() {

    override fun buildModels(viewState: ViewState<MediaGalleryContainer?>) {
        viewState.data?.media?.forEach { (item, isSelected) ->
            mediaGalleryGridItem {
                id(item.calculateId())
                itemSelected(isSelected)
                mediaInfoForPicker(item)
                onClicked { _, _, _, index ->
                    <EMAIL>?.data?.let { data ->
                        data.onClick(data.media[index].media)
                    }
                }
            }
        }
    }
}
