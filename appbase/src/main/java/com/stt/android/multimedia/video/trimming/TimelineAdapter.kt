package com.stt.android.multimedia.video.trimming

import android.annotation.SuppressLint
import android.content.Context
import android.net.Uri
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import coil3.dispose
import coil3.load
import coil3.video.videoFrameMillis
import com.stt.android.R
import kotlin.math.ceil

internal class TimelineAdapter(
    context: Context
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private val inflater: LayoutInflater = LayoutInflater.from(context)
    private var video: Uri? = null
    private var itemCount = 0

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return object : RecyclerView.ViewHolder(
            inflater.inflate(R.layout.video_timeline_view_thumbnail, parent, false)
        ) {}
    }

    override fun onBindViewHolder(viewHolder: RecyclerView.ViewHolder, position: Int) {
        if (video == null) {
            return
        }
        val imageView = viewHolder.itemView as ImageView
        imageView.dispose()

        // first is place holder
        if (position == 0) {
            return
        }
        val frameMillis = position.toLong() * VideoTimelineView.THUMBNAIL_GAP_IN_MILLIS
        imageView.load(video) {
            videoFrameMillis(frameMillis)
        }
    }

    override fun getItemCount(): Int {
        return if (video != null) itemCount else 0
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setVideo(video: Uri?, mediaDurationInMills: Long) {
        this.video = video
        itemCount = ceil(
            mediaDurationInMills / VideoTimelineView.THUMBNAIL_GAP_IN_MILLIS.toDouble()
        ).toInt() + 1
        notifyDataSetChanged()
    }
}
