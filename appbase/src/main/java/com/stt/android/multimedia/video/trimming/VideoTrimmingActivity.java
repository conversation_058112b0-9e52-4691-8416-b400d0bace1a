package com.stt.android.multimedia.video.trimming;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import com.google.android.exoplayer2.ExoPlayer;
import com.google.android.exoplayer2.Player;
import com.stt.android.R;
import com.stt.android.databinding.VideoTrimmingActivityBinding;
import com.stt.android.multimedia.video.ExoPlayerHelper;
import com.stt.android.remote.UserAgent;
import dagger.hilt.android.AndroidEntryPoint;
import javax.inject.Inject;
import timber.log.Timber;

@AndroidEntryPoint
public class VideoTrimmingActivity extends AppCompatActivity implements VideoTimelineView.Listener {
    private static final String KEY_VIDEO_URI = "com.stt.android.KEY_VIDEO";
    public static final String KEY_SELECTED_START_TIME = "com.stt.android.KEY_SELECTED_START_TIME";
    public static final String KEY_SELECTED_END_TIME = "com.stt.android.KEY_SELECTED_END_TIME";
    public static final String KEY_AUDIO_INCLUDED = "com.stt.android.KEY_AUDIO_INCLUDED";

    public static Intent newStartIntent(Context context, Uri videoUri) {
        return new Intent(context, VideoTrimmingActivity.class).putExtra(KEY_VIDEO_URI, videoUri);
    }

    VideoTrimmingActivityBinding binding;

    @Inject
    @UserAgent
    String userAgent;

    ExoPlayer player;
    final Handler handler = new Handler(Looper.getMainLooper());
    final Runnable seekToStartIfNeeded = new Runnable() {
        @Override
        public void run() {
            if (player.getCurrentPosition() > endTimeInMills) {
                player.seekTo(startTimeInMills);
            }

            handler.postDelayed(this, 100L);
        }
    };
    private final Runnable loadMedia = new Runnable() {
        @Override
        public void run() {
            Uri uri = getUri();
            if (uri != null) {
                ExoPlayerHelper.setMedia(player, uri, false);

                if (player != null) {
                    player.prepare();
                    player.setPlayWhenReady(true);

                    startTimeInMills = binding.timeline.getSelectedStartTimeInMills();
                    endTimeInMills = binding.timeline.getSelectedEndTimeInMills();

                    handler.postDelayed(seekToStartIfNeeded, 100L);
                } else {
                    Timber.w("Unable to open video source, URI=%s", uri);
                    setResult(RESULT_CANCELED);
                    finish();
                }
            } else {
                Timber.w("Missing URI from intent");
                setResult(RESULT_CANCELED);
                finish();
            }
        }
    };

    long startTimeInMills;
    long endTimeInMills;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        binding = VideoTrimmingActivityBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        binding.toolbar.setNavigationIcon(R.drawable.ic_cancel_cross);
        binding.toolbar.setNavigationOnClickListener(view -> {
            setResult(RESULT_CANCELED);
            finish();
        });

        binding.toolbar.inflateMenu(R.menu.menu_trim_video);
        binding.toolbar.setOnMenuItemClickListener(item -> {
            if (item.getItemId() == R.id.save) {
                Intent data = new Intent()
                    .setData(getUri())
                    .putExtra(KEY_SELECTED_START_TIME, startTimeInMills)
                    .putExtra(KEY_SELECTED_END_TIME, endTimeInMills)
                    .putExtra(KEY_AUDIO_INCLUDED, binding.audio.isChecked());
                setResult(RESULT_OK, data);
                finish();
                return true;
            }
            return false;
        });

        player = ExoPlayerHelper.createPlayer(this, userAgent);
        binding.exoPlayerView.setPlayer(player);

        binding.timeline.setListener(this);
        binding.timeline.setVideo(getUri());

        binding.audio.setOnCheckedChangeListener((compoundButton, checked) -> {
            binding.audioDescription.setText(
                checked ? R.string.audio_included : R.string.audio_excluded);
            player.setVolume(checked ? 1.0F : 0.0F);
        });
        binding.audio.setChecked(true);
    }

    @Override
    protected void onStart() {
        super.onStart();

        if (Player.STATE_IDLE == player.getPlaybackState()) {
            // It's very slow to initialize the player, so delay until the UI is ready.
            handler.postDelayed(loadMedia, 500L);
        }
    }

    @Override
    protected void onStop() {
        player.setPlayWhenReady(false);
        handler.removeCallbacks(loadMedia);
        handler.removeCallbacks(seekToStartIfNeeded);
        super.onStop();
    }

    @Override
    protected void onDestroy() {
        player.stop();
        player.release();
        binding.exoPlayerView.setPlayer(null);

        super.onDestroy();
    }

    @Override
    public void onSelectedTimeChanged(long startTimeInMills, long endTimeInMills) {
        if (this.startTimeInMills != startTimeInMills) {
            player.seekTo(startTimeInMills);
        } else if (player.getCurrentPosition() > endTimeInMills) {
            player.seekTo(startTimeInMills);
        }

        this.startTimeInMills = startTimeInMills;
        this.endTimeInMills = endTimeInMills;
    }

    @Nullable
    private Uri getUri() {
        Intent intent = getIntent();
        return intent != null ? intent.getParcelableExtra(KEY_VIDEO_URI) : null;
    }
}
