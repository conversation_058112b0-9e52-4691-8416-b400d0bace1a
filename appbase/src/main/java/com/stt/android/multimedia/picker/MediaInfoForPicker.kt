package com.stt.android.multimedia.picker

import android.content.Context
import android.net.Uri
import android.os.Parcelable
import androidx.annotation.IntDef
import com.stt.android.domain.user.ImageInformation
import com.stt.android.domain.user.VideoInformation
import kotlinx.parcelize.Parcelize

/**
 * A data class representing an image or video when dealing with adding new media to workouts or
 * managing media already attached to a workout.
 *
 * This can represent an image read from the database via an ImageInformation object, a video
 * read from the database via a VideoInformation object, or media fetched from MediaStore or via
 * a pick intent.
 */
sealed class MediaInfoForPicker(
    @MediaType open val mediaType: Int,
    @JvmField val lastModified: Long,
    open val width: Int,
    open val height: Int,
) : Parcelable {

    @IntDef(MEDIA_TYPE_IMAGE, MEDIA_TYPE_VIDEO)
    @Retention(AnnotationRetention.SOURCE)
    annotation class MediaType

    val isVideo: Boolean
        get() = mediaType == MEDIA_TYPE_VIDEO

    companion object {
        const val MEDIA_TYPE_IMAGE = 0
        const val MEDIA_TYPE_VIDEO = 1
    }

    // All the concrete child classes are data classes and have proper hashCode and equals.
    // However, Epoxy's HashCodeValidator does not understand this, so we need some non-default
    // implementation here.
    override fun hashCode(): Int =
        throw NotImplementedError("MediaInfoForPicker: hashCode should be implemented by concrete classes")

    override fun equals(other: Any?): Boolean =
        throw NotImplementedError("MediaInfoForPicker: equals should be implemented by concrete classes")
}

@Parcelize
data class PictureInfoForPicker(
    val image: ImageInformation
) : MediaInfoForPicker(MEDIA_TYPE_IMAGE, image.timestamp, image.width, image.height), Parcelable

@Parcelize
data class VideoInfoForPicker(
    val video: VideoInformation
) : MediaInfoForPicker(MEDIA_TYPE_VIDEO, video.timestamp, video.width, video.height), Parcelable

@Parcelize
data class PickedMediaInfoForPicker(
    @MediaType override val mediaType: Int,
    val contentUri: Uri,
    val timestamp: Long,
    override val width: Int,
    override val height: Int,
) : MediaInfoForPicker(mediaType, timestamp, width, height)

fun MediaInfoForPicker.calculateId(): String = when (this) {
    is PictureInfoForPicker -> image.key + image.id.toString()
    is VideoInfoForPicker -> video.key + video.id.toString()
    is PickedMediaInfoForPicker -> contentUri.toString()
}

fun MediaInfoForPicker.getThumbnailUri(context: Context): Uri? = when (this) {
    is PictureInfoForPicker ->
        image.getThumbnailUri(context)
    is VideoInfoForPicker ->
        video.getThumbnailUri(context)
    is PickedMediaInfoForPicker -> contentUri
}
