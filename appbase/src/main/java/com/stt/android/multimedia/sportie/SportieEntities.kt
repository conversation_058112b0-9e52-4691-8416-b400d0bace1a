package com.stt.android.multimedia.sportie

import android.graphics.Bitmap
import android.net.Uri
import android.os.Parcelable
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.core.domain.GraphType
import com.stt.android.divetrack.DiveTrack
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.user.ImageInformation
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.domain.workout.WorkoutHrEvent
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.maps.MapSnapshotSpec
import com.stt.android.workouts.details.values.WorkoutValue
import kotlinx.parcelize.Parcelize

sealed class SportieItem(
    open val workoutHeader: WorkoutHeader,
    open val workoutExtensions: List<WorkoutExtension>,
    open val sml: Sml,
    open val infoModelFormatter: InfoModelFormatter,
    open val geoPoints: List<WorkoutGeoPoint>,
    open val hrEvents: List<WorkoutHrEvent>,
    open val shareType: SportieShareType,
)

data class SportieMap(
    val mapSnapshotSpec: MapSnapshotSpec,
    val originalHrEvents: List<WorkoutHrEvent>,
    override val workoutHeader: WorkoutHeader,
    override val workoutExtensions: List<WorkoutExtension>,
    override val sml: Sml,
    override val infoModelFormatter: InfoModelFormatter,
    override val geoPoints: List<WorkoutGeoPoint>,
    override val hrEvents: List<WorkoutHrEvent>,
) : SportieItem(
    workoutHeader,
    workoutExtensions,
    sml,
    infoModelFormatter,
    geoPoints,
    hrEvents,
    SportieShareType.MAP,
)

data class SportieDiveTrack(
    val diveTrack: DiveTrack,
    // Unfortunately, if we add dive track view to an "invisible" view, we can not get the snapshot,
    // so have to fetch it from preview.
    // TODO Figure out a way to take the snapshot from within SportieHelper
    val snapshot: Bitmap?,
    override val workoutHeader: WorkoutHeader,
    override val workoutExtensions: List<WorkoutExtension>,
    override val sml: Sml,
    override val infoModelFormatter: InfoModelFormatter,
) : SportieItem(
    workoutHeader,
    workoutExtensions,
    sml,
    infoModelFormatter,
    emptyList(),
    emptyList(),
    SportieShareType.DIVE_TRACK,
)

data class SportieImage(
    val imageInformation: ImageInformation,
    override val workoutHeader: WorkoutHeader,
    override val workoutExtensions: List<WorkoutExtension>,
    override val sml: Sml,
    override val infoModelFormatter: InfoModelFormatter,
    override val geoPoints: List<WorkoutGeoPoint> = emptyList(),
    override val hrEvents: List<WorkoutHrEvent> = emptyList()
) : SportieItem(
    workoutHeader,
    workoutExtensions,
    sml,
    infoModelFormatter,
    geoPoints,
    hrEvents,
    SportieShareType.PHOTO,
)

data class SportieAddPhoto(
    override val workoutHeader: WorkoutHeader,
    override val workoutExtensions: List<WorkoutExtension>,
    override val sml: Sml,
    override val infoModelFormatter: InfoModelFormatter,
) : SportieItem(
    workoutHeader,
    workoutExtensions,
    sml,
    infoModelFormatter,
    emptyList(),
    emptyList(),
    SportieShareType.ADD_PHOTO,
)

@Parcelize
data class SportieSelection(
    val firstWorkoutValue: WorkoutValue? = null,
    val secondWorkoutValue: WorkoutValue? = null,
    val thirdWorkoutValue: WorkoutValue? = null,
    val sportieShareType: SportieShareType = SportieShareType.UNKNOWN,
    val isShareCustomised: Boolean = false,
    val sportieGraphType: GraphType? = null,
    val sportieShareSource: SportieShareSource = SportieShareSource.UNKNOWN,
    val sportiePhotoSize: SportieAspectRatio = SportieAspectRatio.UNKNOWN
) : Parcelable {

    fun toAnalyticsProperties(): AnalyticsProperties {
        return AnalyticsProperties().apply {
            put(AnalyticsEventProperty.STAT1_TYPE, firstWorkoutValue?.item?.key ?: "")
            put(AnalyticsEventProperty.STAT2_TYPE, secondWorkoutValue?.item?.key ?: "")
            put(AnalyticsEventProperty.STAT3_TYPE, thirdWorkoutValue?.item?.key ?: "")
            put(AnalyticsEventProperty.STAT4_TYPE, "") // todo value not supported for now
            put(AnalyticsEventProperty.TYPE, sportieShareType.type)
            putYesNo(AnalyticsEventProperty.SHARE_CUSTOMIZED, isShareCustomised)
            put(AnalyticsEventProperty.GRAPH_TYPE, sportieGraphType?.key)
            put(AnalyticsEventProperty.SOURCE, sportieShareSource.type)
            put(AnalyticsEventProperty.PHOTO_SIZE, sportiePhotoSize.type)
        }
    }
}

@Parcelize
data class SportieInfo(
    val firstDataIndex: Int,
    val secondDataIndex: Int,
    val thirdDataIndex: Int,
    val descriptionText: String = "",
    val graphIndex: Int,
    val showActivityType: Boolean = true,
    val showStartTime: Boolean = true
) : Parcelable

enum class SportieShareType(val type: String) {
    PHOTO("Photo"),
    LINK("Link"),
    VIDEO("Video"),
    MAP("Map"),
    DIVE_TRACK("DiveTrack"),
    ADD_PHOTO("AddPhoto"),
    LINK_3D("3DLink"),
    VIDEO_3D("3DVideo"),
    LONG_SCREENSHOT("Detail"),
    BRIEF("Brief"),
    UNKNOWN(""),
}

enum class SportieAspectRatio(val type: String) {
    ORIGINAL("Original"),
    ONE_TO_ONE("Cropped"),
    UNKNOWN("");

    fun validValues() = entries.filter { it != UNKNOWN }

    fun nextValid() = validValues().let {
        val i = it.indexOf(this)
        if (i < 0) UNKNOWN else it[(i + 1) % it.size]
    }
}

/**
 * Currently Android supports sharing only from WorkoutDetails
 */
@Parcelize
enum class SportieShareSource(val type: String) : Parcelable {
    FEED("Feed"),
    DIARY("Diary"),
    PHOTOS("Photos"),
    WORKOUT_DETAILS("WorkoutDetails"),
    FEED_SHARE_CAROUSEL("FeedShareCarousel"),
    WORKOUT_SUMMARY("WorkoutSummary"),
    WORKOUT_DETAILS_SCREENSHOT("WorkoutDetailsScreenShot"),
    UNKNOWN(""),
}

data class SportieShareInfo(
    val uri: Uri,
    val sportieSelection: SportieSelection
)
