package com.stt.android.multimedia.sportie

import com.stt.android.domain.sml.Sml
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.toDomainWindow
import com.stt.android.infomodel.SummaryItem
import com.stt.android.logbook.SuuntoLogbookWindow
import com.stt.android.mapping.getActivitySummaryForActivityId
import com.stt.android.workouts.details.values.WorkoutValueFactory
import com.stt.android.workouts.details.values.applyScubaDiveFilteringRules

internal object SportieUtils {
    fun extractWorkoutValuesForSportie(
        sportieItem: SportieItem,
        sml: Sml,
        activityWindow: SuuntoLogbookWindow?,
    ): Pair<WorkoutValueFactory, List<SummaryItem>> {
        val workoutValueFactory = WorkoutValueFactory(
            workoutExtensions = sportieItem.workoutExtensions,
            workoutHeader = sportieItem.workoutHeader,
            infoModelFormatter = sportieItem.infoModelFormatter,
            activityWindow = activityWindow.toDomainWindow(),
            sml = sml,
        )

        val activityType = sportieItem.workoutHeader.activityType
        // first get activity specific workout values in order
        val summaryItems = getActivitySummaryForActivityId(activityType.id).items.toMutableList()
            .let { items ->
                if (workoutValueFactory.diveExtension != null && activityType === ActivityType.SCUBADIVING) {
                    applyScubaDiveFilteringRules(workoutValueFactory.diveExtension.algorithm, items)
                        .filterNot { it == SummaryItem.ALGORITHM }
                } else if (activityType == ActivityType.SAILING) {
                    items.filterNot {
                        it == SummaryItem.AVGSPEED ||
                            it == SummaryItem.MAXSPEED ||
                            it == SummaryItem.AVGPACE ||
                            it == SummaryItem.MAXPACE
                    }
                } else {
                    items
                }
            }

        return Pair(workoutValueFactory, summaryItems)
    }
}
