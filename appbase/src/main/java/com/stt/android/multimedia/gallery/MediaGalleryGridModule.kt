package com.stt.android.multimedia.gallery

import com.stt.android.common.viewstate.ViewStateEpoxyController
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class MediaGalleryGridModule {

    @Binds
    abstract fun bindController(controller: MediaGalleryEpoxyController): ViewStateEpoxyController<MediaGalleryContainer>
}
