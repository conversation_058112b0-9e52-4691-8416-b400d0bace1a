package com.stt.android.multimedia.video

import android.annotation.SuppressLint
import android.content.Context
import android.net.Uri
import com.google.android.exoplayer2.DefaultRenderersFactory
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.Player.REPEAT_MODE_ALL
import com.google.android.exoplayer2.Player.REPEAT_MODE_OFF
import com.google.android.exoplayer2.database.StandaloneDatabaseProvider
import com.google.android.exoplayer2.source.DefaultMediaSourceFactory
import com.google.android.exoplayer2.upstream.DataSource
import com.google.android.exoplayer2.upstream.DefaultDataSource
import com.google.android.exoplayer2.upstream.DefaultHttpDataSource
import com.google.android.exoplayer2.upstream.HttpDataSource
import com.google.android.exoplayer2.upstream.cache.Cache
import com.google.android.exoplayer2.upstream.cache.CacheDataSource
import com.google.android.exoplayer2.upstream.cache.LeastRecentlyUsedCacheEvictor
import com.google.android.exoplayer2.upstream.cache.SimpleCache
import com.stt.android.utils.FileUtils
import com.stt.android.utils.STTConstants

object ExoPlayerHelper {
    private const val CACHE_SIZE = 200L * 1024L * 1024L // 200 MB

    @SuppressLint("StaticFieldLeak")
    @Volatile
    private lateinit var mediaSourceFactory: DefaultDataSource.Factory

    @JvmStatic
    fun createPlayer(
        context: Context,
        userAgent: String,
    ): ExoPlayer = ExoPlayer.Builder(context)
        .setMediaSourceFactory(
            DefaultMediaSourceFactory(createMediaSourceFactory(context, userAgent))
        )
        .build()

    @JvmStatic
    fun setMedia(player: Player?, uri: Uri, repeatIndefinitely: Boolean) {
        if (player == null) return
        player.clearMediaItems()
        player.addMediaItem(MediaItem.fromUri(uri))
        player.repeatMode = if (repeatIndefinitely) REPEAT_MODE_ALL else REPEAT_MODE_OFF
    }

    private fun createMediaSourceFactory(
        context: Context,
        userAgent: String
    ): DefaultDataSource.Factory {
        synchronized(ExoPlayerHelper::class.java) {
            return if (::mediaSourceFactory.isInitialized) {
                mediaSourceFactory
            } else {
                val httpDataSourceFactory: HttpDataSource.Factory =
                    DefaultHttpDataSource.Factory()
                        .setUserAgent(userAgent)
                        .setAllowCrossProtocolRedirects(true)

                val cache: Cache = SimpleCache(
                    FileUtils.getCacheDirectory(context, STTConstants.DIRECTORY_VIDEOS),
                    LeastRecentlyUsedCacheEvictor(CACHE_SIZE),
                    StandaloneDatabaseProvider(context)
                )

                val cacheDataSourceFactory: DataSource.Factory = CacheDataSource.Factory()
                    .setCache(cache)
                    .setUpstreamDataSourceFactory(httpDataSourceFactory)

                // Wrap the HttpDataSource.Factory in a DefaultDataSource.Factory, which adds in
                // support for requesting data from other sources (e.g., files, resources,
                // content schemes uris etc).
                DefaultDataSource.Factory(context, cacheDataSourceFactory).also {
                    mediaSourceFactory = it
                }
            }
        }
    }
}
