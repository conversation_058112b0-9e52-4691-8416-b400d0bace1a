package com.stt.android.multimedia.gallery

import android.content.Intent
import android.net.Uri
import androidx.annotation.WorkerThread
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.work.WorkManager
import com.stt.android.common.viewstate.LoadingStateViewModel
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.PicturesController
import com.stt.android.controllers.VideoModel
import com.stt.android.domain.Point
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.review.ReviewState
import com.stt.android.domain.sync.SyncRequest
import com.stt.android.domain.sync.SyncRequestHandlerWorker
import com.stt.android.domain.user.ImageInformation
import com.stt.android.domain.user.VideoInformation
import com.stt.android.domain.workouts.GetWorkoutHeaderByIdUseCase
import com.stt.android.domain.workouts.SaveWorkoutHeaderUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.pictures.Picture
import com.stt.android.domain.workouts.videos.Video
import com.stt.android.extensions.bindToWorkout
import com.stt.android.multimedia.picker.MediaInfoForPicker
import com.stt.android.multimedia.picker.PickedMediaInfoForPicker
import com.stt.android.multimedia.picker.PictureInfoForPicker
import com.stt.android.multimedia.picker.VideoInfoForPicker
import com.stt.android.ui.tasks.BitmapLoadAndResizer
import com.stt.android.ui.tasks.LoadAndResizeResult
import com.stt.android.ui.tasks.WorkoutImageViewModel
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.utils.STTConstants
import com.stt.android.utils.WorkoutImageFilesHelper
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import javax.inject.Inject
import kotlin.random.Random

@HiltViewModel
class MediaGalleryViewModel
@Inject constructor(
    private val handle: SavedStateHandle,
    private val workoutImageFilesHelper: WorkoutImageFilesHelper,
    private val bitmapLoadAndResizer: BitmapLoadAndResizer,
    private val picturesController: PicturesController,
    private val currentUserController: CurrentUserController,
    private val getWorkoutHeaderByIdUseCase: GetWorkoutHeaderByIdUseCase,
    private val saveWorkoutHeaderUseCase: SaveWorkoutHeaderUseCase,
    private val videoModel: VideoModel,
    private val workManager: WorkManager,
    private val localBroadcastManager: LocalBroadcastManager,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
) : LoadingStateViewModel<MediaGalleryContainer>(ioThread, mainThread) {

    override fun retryLoading() = Unit

    private val selectedMedia: MutableSet<MediaInfoForPicker> = mutableSetOf()
    private val currentMedia: List<MediaInfoForPicker>
        get() = viewState.value?.data?.media?.map { it.media } ?: emptyList()

    val currentSelection: Set<MediaInfoForPicker>
        get() = selectedMedia

    val addingToWorkoutFinished: LiveData<Boolean> // true=some changes were saved successfully
        get() = _addingToWorkoutFinished
    private val _addingToWorkoutFinished = SingleLiveEvent<Boolean>()

    val setResultAndFinish: LiveData<ArrayList<MediaInfoForPicker>>
        get() = _setResultAndFinish
    private val _setResultAndFinish = SingleLiveEvent<ArrayList<MediaInfoForPicker>>()

    val startTrimmingVideo: LiveData<Uri?>
        get() = _startTrimmingVideo
    private val _startTrimmingVideo = SingleLiveEvent<Uri?>()

    val showError: LiveData<Unit>
        get() = _showError
    private val _showError = SingleLiveEvent<Unit>()

    private val videoTrimQueue = mutableListOf<Uri>()
    private val processedMedia = mutableListOf<MediaInfoForPicker>()

    val processingOngoing: LiveData<Boolean>
        get() = _processingOngoing
    private val _processingOngoing = MutableLiveData(false)

    // If we have a workout header, save and attach media immediately. Otherwise return selected
    // media via setResultAndFinish.
    private var workoutHeader: WorkoutHeader? = null
    private val addToWorkoutDirectly: Boolean
        get() = workoutHeader != null

    // In case user used camera or picker instead of selecting images from grid
    private var mediaFromPicker: PickedMediaInfoForPicker? = null

    // Loading media occurs in fragment implementation because it depends on Context // TODO: wrap in a helper class
    fun setMedia(media: List<MediaInfoForPicker>, sort: Boolean = true) {
        val sortedMedia = if (sort) {
            val prioritizedTimePeriodStart =
                handle.get<Long?>(PRIORITIZED_TIME_PERIOD_START_MILLIS_EXTRA)
            val prioritizedTimePeriodEnd =
                handle.get<Long?>(PRIORITIZED_TIME_PERIOD_END_MILLIS_EXTRA)

            if (prioritizedTimePeriodStart != null && prioritizedTimePeriodEnd != null) {
                val prioritizedTimeRange = prioritizedTimePeriodStart..prioritizedTimePeriodEnd
                val (prioritized, other) = media.partition {
                    val lastModifiedMillis = it.lastModified * 1000
                    lastModifiedMillis in prioritizedTimeRange
                }
                prioritized.sortedByDescending { it.lastModified } + other.sortedByDescending { it.lastModified }
            } else {
                media.sortedByDescending { it.lastModified }
            }
        } else {
            media
        }

        val mediaInfoWithSelection = sortedMedia.map {
            MediaInfoWithSelection(media = it, selected = selectedMedia.contains(it))
        }

        notifyDataLoaded(
            MediaGalleryContainer(
                media = mediaInfoWithSelection,
                onClick = ::toggleSelection
            )
        )
    }

    fun setMediaFromPicker(uri: Uri, mimeType: String) {
        Timber.d("setMediaFromPicker: got URI from camera or document picker: $uri")

        val type = if (mimeType.startsWith("video")) {
            MediaInfoForPicker.MEDIA_TYPE_VIDEO
        } else {
            MediaInfoForPicker.MEDIA_TYPE_IMAGE
        }

        mediaFromPicker = PickedMediaInfoForPicker(
            mediaType = type,
            contentUri = uri,
            timestamp = System.currentTimeMillis(),
            width = 0, // Size is not used, the image/video is resized anyway
            height = 0
        )
    }

    private fun toggleSelection(item: MediaInfoForPicker) {
        if (selectedMedia.contains(item)) {
            selectedMedia.remove(item)
        } else {
            selectedMedia.add(item)
        }
        setMedia(currentMedia, sort = false)
    }

    fun startProcessing(workoutHeader: WorkoutHeader?) {
        this.workoutHeader = workoutHeader
        launch {
            _processingOngoing.postValue(true)
            try {
                resizeImagesAndTrimVideo()
            } catch (e: Exception) {
                Timber.d(e, "Failed to process media")
                _processingOngoing.postValue(false)
                _showError.call()
            }
        }
    }

    @WorkerThread
    private fun saveToWorkout() = launch {
        var changeCounter = 0
        workoutHeader?.let { workout ->
            var index = workout.pictureCount
            for (item in processedMedia) {
                try {
                    if (item is PictureInfoForPicker) {
                        val imageInformation = ImageInformation.fromPicture(
                            item.image.toPicture().bindToWorkout(workout, index++)
                        )
                        picturesController.store(imageInformation)
                        changeCounter++
                    } else if (item is VideoInfoForPicker) {
                        val videoInformation = VideoInformation.fromVideo(
                            item.video.toVideo().bindToWorkout(workout)
                        )
                        videoModel.storeMetaData(videoInformation)
                        changeCounter++
                    }
                } catch (e: Exception) {
                    Timber.w(e, "Failed to add media to workout (media=$item)")
                }
            }
        }

        workoutHeader?.id?.let { id ->
            withContext(io) {
                try {
                    // Fetch workout header from database in case there has been updates since
                    // starting media editing
                    getWorkoutHeaderByIdUseCase(id)?.let { header ->
                        val newCount = picturesController.findByWorkoutId(id).size
                        if (header.pictureCount != newCount) {
                            saveWorkoutHeaderUseCase(header.copy(pictureCount = newCount))
                        }
                    }
                } catch (e: Exception) {
                    Timber.w(e, "Failed to update picture count")
                }

                sendBroadcast(id)
            }
        }

        scheduleWorkoutSync()

        // True value indicates something was successfully changed
        _addingToWorkoutFinished.postValue(changeCounter > 0)
    }

    private suspend fun resizeImagesAndTrimVideo() {
        processedMedia.clear()
        videoTrimQueue.clear()

        val mediaToBeProcessed =
            if (mediaFromPicker != null) listOf(mediaFromPicker) else selectedMedia

        // Reset media from camera/picker immediately in case processing fails
        mediaFromPicker = null

        // Resize all picked images
        mediaToBeProcessed
            .filterIsInstance<PickedMediaInfoForPicker>()
            .filter { !it.isVideo }
            .forEach {
                try {
                    Timber.d("Processing image $it")
                    processedMedia.add(processImage(it))
                } catch (e: Exception) {
                    Timber.w(e, "Failed to process image $it")
                }
            }

        // Launch video trimming UI for all picked videos via LiveData event
        mediaToBeProcessed
            .filterIsInstance<PickedMediaInfoForPicker>()
            .filter { it.isVideo }
            .map { it.contentUri }
            .toCollection(videoTrimQueue)

        triggerVideoTranscodeOrFinish(popFirst = false)
    }

    private suspend fun processImage(image: PickedMediaInfoForPicker): PictureInfoForPicker {
        val res = withContext(IO) {
            runCatching {
                getLoadAndResizeResult(
                    uri = image.contentUri,
                    timestamp = image.timestamp,
                    position = null
                )
            }
        }
            .getOrThrow()

        // Workout specific fields will be overridden later, before saving to db
        return PictureInfoForPicker(
            ImageInformation.fromPicture(
                Picture(
                    Random.nextInt(),
                    key = null,
                    location = res.positionFromImage,
                    timestamp = image.timestamp,
                    totalTime = 0.0,
                    fileName = res.name,
                    workoutId = null,
                    workoutKey = null,
                    md5Hash = res.destFileMD5,
                    locallyChanged = true,
                    description = null,
                    username = currentUserController.username,
                    width = res.width,
                    height = res.height,
                    indexInWorkoutHeader = 0,
                )
            )
        )
    }

    @Suppress("BlockingMethodInNonBlockingContext")
    private suspend fun getLoadAndResizeResult(
        uri: Uri,
        timestamp: Long,
        position: Point? = null
    ): LoadAndResizeResult {
        val lastSegment = uri.lastPathSegment
            ?: throw IllegalArgumentException("Invalid URI for getLoadAndResizeResult: $uri")
        val filename = "${lastSegment}_$timestamp"

        val positionFromImage = position ?: bitmapLoadAndResizer.positionFromImage(uri)
        val dest = workoutImageFilesHelper.getDestinationPathForResize(filename)
        Timber.d("Resizing picture: original=$uri, destination=${dest.absolutePath}")
        val resizedBitmap = bitmapLoadAndResizer.loadBitmapAtTargetResolution(
            uri,
            WorkoutImageViewModel.TARGET_1080P_WIDTH,
            WorkoutImageViewModel.TARGET_1080P_HEIGHT
        )
        Timber.d("Resized picture at resolution ${resizedBitmap.width}x${resizedBitmap.height}")
        val destFileMD5 = workoutImageFilesHelper.saveBitmapToJpegFile(resizedBitmap, dest)

        return LoadAndResizeResult(
            filename,
            destFileMD5,
            positionFromImage,
            resizedBitmap.width,
            resizedBitmap.height
        )
    }

    private fun scheduleWorkoutSync() {
        Timber.d("Enqueuing workout sync to update media")
        SyncRequestHandlerWorker.enqueue(workManager, SyncRequest.push())
    }

    fun handleVideoTranscodeCompleted(
        transcoded: File,
        thumbnail: File,
        transcodedVideoWidth: Int,
        transcodedVideoHeight: Int
    ) {
        // Workout specific fields will be overridden later, before saving to db
        processedMedia.add(
            VideoInfoForPicker(
                VideoInformation.fromVideo(
                    Video(
                        id = Random.nextInt(),
                        key = null,
                        workoutId = null,
                        workoutKey = null,
                        username = currentUserController.username,
                        totalTime = 0L,
                        timestamp = System.currentTimeMillis(),
                        description = null,
                        location = null,
                        url = null,
                        thumbnailUrl = null,
                        width = transcodedVideoWidth,
                        height = transcodedVideoHeight,
                        filename = transcoded.name,
                        thumbnailFilename = thumbnail.name,
                        locallyChanged = true,
                        reviewState = ReviewState.PASS
                    )
                )
            )
        )

        triggerVideoTranscodeOrFinish(popFirst = true)
    }

    fun handleVideoTranscodeFailed() {
        _showError.call()
        triggerVideoTranscodeOrFinish(popFirst = true)
    }

    fun handleVideoTranscodeCanceled() {
        triggerVideoTranscodeOrFinish(popFirst = true)
    }

    private fun sendBroadcast(workoutId: Int) {
        Intent(STTConstants.BroadcastActions.PICTURE_OR_VIDEO_STORED).apply {
            putExtra(STTConstants.ExtraKeys.WORKOUT_ID, workoutId)
            localBroadcastManager.sendBroadcast(this)
        }
    }

    private fun triggerVideoTranscodeOrFinish(popFirst: Boolean) {
        if (popFirst && videoTrimQueue.isNotEmpty()) {
            videoTrimQueue.removeAt(0)
        }

        if (videoTrimQueue.isEmpty()) {
            _processingOngoing.postValue(false)

            if (addToWorkoutDirectly) {
                Timber.d("Finished processing - saving changes to workout directly")
                saveToWorkout()
            } else {
                Timber.d("Finished processing - returning media as activity result")
                _setResultAndFinish.value = ArrayList(processedMedia)
            }
            _startTrimmingVideo.value = null
        } else {
            _startTrimmingVideo.value = videoTrimQueue.firstOrNull()
        }
    }

    companion object {
        const val PRIORITIZED_TIME_PERIOD_START_MILLIS_EXTRA =
            "com.stt.android.multimedia.gallery.PRIORITIZED_TIME_PERIOD_START"
        const val PRIORITIZED_TIME_PERIOD_END_MILLIS_EXTRA =
            "com.stt.android.multimedia.gallery.PRIORITIZED_TIME_PERIOD_END"
    }
}
