package com.stt.android.multimedia.sportie

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.IntentSender
import android.content.res.Resources
import android.graphics.Bitmap
import android.graphics.Canvas
import android.net.Uri
import android.view.View.MeasureSpec
import androidx.core.app.ShareCompat
import coil3.imageLoader
import coil3.request.ImageRequest
import coil3.request.transformations
import coil3.toBitmap
import com.stt.android.R
import com.stt.android.coil.CropTransformation
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.user.ImageInformation
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.maps.MapSnapshotSpec
import com.stt.android.maps.MapSnapshotter
import com.stt.android.multimedia.MediaStoreUtils
import com.stt.android.network.interfaces.ANetworkProvider
import com.stt.android.utils.FileUtils
import com.stt.android.workouts.sharepreview.DefaultImageResInformation
import com.stt.android.workouts.sharepreview.customshare.WorkoutShareHelper
import kotlinx.coroutines.withContext
import java.io.FileOutputStream
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.floor
import kotlin.math.max
import kotlin.math.min

@Singleton
class SportieHelper @Inject constructor(
    private val userSettingsController: UserSettingsController,
    private val mapSnapshotter: MapSnapshotter,
    private val workoutShareHelper: WorkoutShareHelper,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) {
    suspend fun prepareSportie(
        context: Context,
        sportieItem: SportieItem,
        currentSportieInfo: SportieInfo,
        aspectRatio: SportieAspectRatio,
    ): SportieShareInfo {
        val bitmap = loadImageOrMap(context, sportieItem, aspectRatio)
        val sportieSelection = addSportieOverlay(
            context,
            bitmap,
            sportieItem,
            userSettingsController.settings.measurementUnit,
            currentSportieInfo,
            aspectRatio,
        )
        val uri = storeSportie(context, bitmap, sportieItem, workoutShareHelper.hasCustomIntentHandling())
        return SportieShareInfo(uri, sportieSelection)
    }

    private suspend fun loadImageOrMap(
        context: Context,
        sportieItem: SportieItem,
        aspectRatio: SportieAspectRatio
    ): Bitmap = when (sportieItem) {
        is SportieImage -> loadImageBitmap(context, sportieItem.imageInformation, aspectRatio)

        is SportieMap -> loadMap(sportieItem.mapSnapshotSpec)

        is SportieDiveTrack -> sportieItem.snapshot
            ?.copy(Bitmap.Config.ARGB_8888, true)
            ?: throw IllegalArgumentException("Null dive track")

        is SportieAddPhoto -> throw RuntimeException("Unknown sportie item")
    }

    private fun scaleTargetDimensions(original: Pair<Int, Int>): Pair<Int, Int> {
        val width = original.first
        val height = original.second
        return if (width > height) {
            val scaledTargetWidth = SCALED_TARGET_SIZE
            val scaleRatio = scaledTargetWidth / width.toFloat()
            val scaledTargetHeight = (scaleRatio * height).toInt()
            Pair(scaledTargetWidth, scaledTargetHeight)
        } else {
            val scaledTargetHeight = SCALED_TARGET_SIZE
            val scaleRatio = scaledTargetHeight / height.toFloat()
            val scaledTargetWidth = (scaleRatio * width).toInt()
            Pair(scaledTargetWidth, scaledTargetHeight)
        }
    }

    private suspend fun loadImageBitmap(
        context: Context,
        imageInfo: ImageInformation,
        aspectRatio: SportieAspectRatio,
    ): Bitmap = withContext(coroutinesDispatchers.io) {
        val targetDimensions = if (aspectRatio == SportieAspectRatio.ORIGINAL) {
            getTargetDimensions(imageInfo)
        } else {
            val minDimension = min(imageInfo.width, imageInfo.height)
            Pair(minDimension, minDimension)
        }
        val scaledDimensions = if (max(targetDimensions.first, targetDimensions.second) < SCALED_TARGET_SIZE) {
            // Scale the resolution to a fixed size, so avoid the image is too small and the text is not clear。
            scaleTargetDimensions(targetDimensions)
        } else {
            targetDimensions
        }

        val request = ImageRequest.Builder(context)
            .data(
                if (imageInfo is DefaultImageResInformation) {
                    imageInfo.defaultImageResId
                } else {
                    imageInfo.getHighResUri(context)
                }
            )
            .transformations(CropTransformation(CropTransformation.CropType.CENTER))
            .size(scaledDimensions.first, scaledDimensions.second)
            .build()
        context.imageLoader
            .execute(request)
            .image
            ?.toBitmap()
            // Make a copy of the loaded bitmap for editing. Otherwise we end up
            // editing a bitmap that is in the memory cache of Coil.
            ?.copy(Bitmap.Config.ARGB_8888, true)
            ?: throw RuntimeException("Null drawable")
    }

    private suspend fun loadMap(
        spec: MapSnapshotSpec,
    ): Bitmap = withContext(coroutinesDispatchers.io) {
        val bitmap = mapSnapshotter.getBitmapFromCache(spec)
            ?: mapSnapshotter.requestSnapshotBitmap(spec)

        if (bitmap.isMutable) {
            bitmap
        } else {
            // Take a mutable copy of the bitmap
            bitmap.copy(Bitmap.Config.ARGB_8888, true)
                ?: throw NullPointerException("Bitmap copy() returned null")
        }
    }

    private suspend fun addSportieOverlay(
        context: Context,
        bitmap: Bitmap,
        sportieItem: SportieItem,
        measurementUnit: MeasurementUnit,
        currentSportieInfo: SportieInfo,
        aspectRatio: SportieAspectRatio
    ): SportieSelection = withContext(coroutinesDispatchers.main) {
        val sportieOverlay = SportieOverlayView(context)
        sportieOverlay.setWorkout(
            sportieItem,
            measurementUnit,
            currentSportieInfo,
        )
        sportieOverlay.measure(
            MeasureSpec.makeMeasureSpec(bitmap.width, MeasureSpec.EXACTLY),
            MeasureSpec.makeMeasureSpec(bitmap.height, MeasureSpec.EXACTLY)
        )
        val sportieSelection = sportieOverlay.getSportieSelection().copy(
            sportieShareType = sportieItem.shareType,
            sportiePhotoSize = aspectRatio
        )
        val canvas = Canvas(bitmap)
        sportieOverlay.layout(0, 0, canvas.width, canvas.height)
        sportieOverlay.draw(canvas)

        sportieSelection
    }

    private suspend fun storeSportie(
        context: Context,
        bitmap: Bitmap,
        sportieItem: SportieItem,
        hasCustomIntent: Boolean,
    ): Uri = withContext(coroutinesDispatchers.io) {
        val imageInfo = (sportieItem as? SportieImage)?.imageInformation
        var fileName = imageInfo?.fileName

        if (fileName.isNullOrEmpty()) {
            fileName = imageInfo?.key?.let {
                FileUtils.generateLocalPath(it)
            }
        }
        if (fileName.isNullOrEmpty()) {
            fileName = UUID.randomUUID().toString().take(8)
        }

        if (!fileName.endsWith(SPORTIE_FILE_EXTENSION, true)) {
            fileName += SPORTIE_FILE_EXTENSION
        }

        if (hasCustomIntent) {
            MediaStoreUtils.saveImageToExternalFilesDir(context, fileName) {
                FileOutputStream(it).use { outputStream ->
                    bitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)
                }
            }
        } else {
            MediaStoreUtils.saveMediaToMediaStore(context.contentResolver, fileName) { pfd ->
                FileOutputStream(pfd.fileDescriptor).use { outputStream ->
                    bitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)
                }
            }
        }
    }

    companion object {
        private const val MIN_ASPECT_RATIO = 9f / 16f
        private const val MAX_ASPECT_RATIO = 16f / 9f
        private const val SPORTIE_FILE_EXTENSION = ".jpg"
        const val MAX_HR_VALUES_SHARE: Int = 500
        private const val SCALED_TARGET_SIZE = 1080

        fun getTargetDimensions(imageInfo: ImageInformation): Pair<Int, Int> {
            var width = imageInfo.width
            var height = imageInfo.height

            // Adjust image dimensions to fit aspect ratio limits.
            val aspectRatio = width.toFloat() / height
            if (aspectRatio < MIN_ASPECT_RATIO) {
                height = floor((width / MIN_ASPECT_RATIO).toDouble()).toInt()
            } else if (aspectRatio > MAX_ASPECT_RATIO) {
                width = floor((height * MAX_ASPECT_RATIO).toDouble()).toInt()
            }

            return Pair(width, height)
        }

        fun buildShareIntent(
            activity: Activity,
            resources: Resources,
            workoutHeader: WorkoutHeader,
            sportieUri: Uri
        ): Intent {
            val text = StringBuilder(100).append(resources.getString(R.string.share_hashtag))
                .append(
                    String.format(
                        " #%s",
                        workoutHeader.activityType.getLocalizedName(resources)
                    )
                )
            if (workoutHeader.isShared) {
                text.append(" ")
                    .append(
                        ANetworkProvider.buildSecureWebWorkoutUrl(
                            workoutHeader.username,
                            workoutHeader.key,
                        )
                    )
            } else {
                text.append(
                    String.format(
                        " https://%s",
                        resources.getString(R.string.share_app_url_st_homepage)
                    )
                )
            }

            val shareIntent = ShareCompat.IntentBuilder(activity)
                .setType("image/jpeg")
                .setStream(sportieUri)
                .intent
            shareIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            shareIntent.putExtra(Intent.EXTRA_TEXT, text.toString())
            return shareIntent
        }

        fun buildChooserIntent(
            resources: Resources,
            shareIntent: Intent,
            intentSender: IntentSender? = null
        ): Intent = Intent.createChooser(
            shareIntent,
            resources.getString(R.string.dialog_title_select),
            intentSender
        )
    }
}
