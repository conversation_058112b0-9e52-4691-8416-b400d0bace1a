@file:Suppress("DEPRECATION")

package com.stt.android.multimedia.gallery

import android.Manifest
import android.app.ProgressDialog
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.net.toUri
import com.stt.android.R
import com.stt.android.common.ui.observeK
import com.stt.android.common.ui.observeNotNull
import com.stt.android.common.ui.observeWhenNotNull
import com.stt.android.databinding.ActivityMediaGalleryBinding
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.extensions.openAppSettings
import com.stt.android.multimedia.gallery.MediaGalleryViewModel.Companion.PRIORITIZED_TIME_PERIOD_END_MILLIS_EXTRA
import com.stt.android.multimedia.gallery.MediaGalleryViewModel.Companion.PRIORITIZED_TIME_PERIOD_START_MILLIS_EXTRA
import com.stt.android.multimedia.picker.VideoPicker
import com.stt.android.utils.HarmonyUtils
import com.stt.android.utils.ImagePicker
import com.stt.android.utils.STTConstants
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import java.io.File

@AndroidEntryPoint
class MediaGalleryActivity :
    AppCompatActivity(),
    ImagePicker.Listener,
    VideoPicker.VideoTranscodingListener {

    private lateinit var binding: ActivityMediaGalleryBinding

    private val viewModel: MediaGalleryViewModel by viewModels()

    private var mediaTaskDialog: ProgressDialog? = null

    private val requestPermissionLauncher =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted: Boolean ->
            if (isGranted) {
                ImagePicker.captureImage(this)
            } else {
                if (!shouldShowRequestPermissionRationale(Manifest.permission.CAMERA)) {
                    openAppSettings()
                }
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityMediaGalleryBinding.inflate(layoutInflater)

        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        binding.toolbar.setNavigationOnClickListener {
            setResult(RESULT_CANCELED)
            finish()
        }

        viewModel.setResultAndFinish.observeNotNull(this) { arrayList ->
            if (arrayList.isEmpty()) {
                setResult(RESULT_CANCELED)
            } else {
                setResult(
                    RESULT_OK,
                    Intent().apply {
                        putParcelableArrayListExtra(GALLERY_RESULT_KEY, arrayList)
                    }
                )
            }
            finish()
        }

        viewModel.addingToWorkoutFinished.observeK(this) {
            if (it == true) {
                Toast.makeText(
                    applicationContext,
                    R.string.workout_media_updated,
                    Toast.LENGTH_LONG
                ).show()
                setResult(STTConstants.RequestResult.MEDIA_EDITED)
            }
            finish()
        }

        viewModel.startTrimmingVideo.observeWhenNotNull(this) {
            trimVideo(it)
        }

        viewModel.processingOngoing.observeNotNull(this) {
            updateProcessingDialogVisibility(it)
        }

        setContentView(binding.root)
    }

    private fun updateProcessingDialogVisibility(visible: Boolean) {
        if (visible && mediaTaskDialog == null) {
            // used on purpose
            @Suppress("DEPRECATION")
            mediaTaskDialog = ProgressDialog(this).apply {
                setCancelable(false)
                setTitle(R.string.please_wait)
                show()
            }
        } else {
            mediaTaskDialog?.dismiss()
            mediaTaskDialog = null
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_media_gallery, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            R.id.save -> handleSave()
            R.id.launchCamera -> launchCamera()
            R.id.launchPicker -> launchPicker()
            else -> return super.onOptionsItemSelected(item)
        }

        return true
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == STTConstants.RequestCodes.PICK_IMAGE_OR_VIDEO && resultCode == RESULT_OK && data != null) {
            handlePickImageOrVideoResult(data)
        } else if (ImagePicker.onActivityResult(this, requestCode, resultCode, data, this)) {
            // Result was handled by ImagePicker and/or forwarded to listener
        } else if (VideoPicker.onActivityResult(this, requestCode, resultCode, data, this)) {
            // Result was handled by VideoPicker and/or forwarded to listener
        } else {
            super.onActivityResult(requestCode, resultCode, data)
        }
    }

    override fun onVideoTranscodeCompleted(
        transcoded: File?,
        thumbnail: File?,
        transcodedVideoWidth: Int,
        transcodedVideoHeight: Int
    ) {
        Timber.w("onVideoTranscodeCompleted transcoded=$transcoded thumbnail=$thumbnail")
        if (transcoded != null && thumbnail != null) {
            viewModel.handleVideoTranscodeCompleted(
                transcoded,
                thumbnail,
                transcodedVideoWidth,
                transcodedVideoHeight
            )
        } else {
            onVideoTranscodeFailed()
        }
    }

    override fun onVideoTranscodeFailed() = viewModel.handleVideoTranscodeFailed()
    override fun onVideoTranscodeCanceled() = viewModel.handleVideoTranscodeCanceled()

    private fun handleSave() {
        val workoutHeader = intent.getParcelableExtra<WorkoutHeader>(WORKOUT_HEADER_EXTRA)

        // If workoutHeader is null, result is returned as intent result.
        // If workoutHeader was given, changes are persisted here immediately.
        viewModel.startProcessing(workoutHeader)
    }

    private fun handlePickImageOrVideoResult(data: Intent) {
        val uri = data.data ?: return
        val type = data.type ?: contentResolver.getType(uri) ?: return

        viewModel.setMediaFromPicker(uri, type)
        handleSave()
    }

    private fun trimVideo(uri: Uri) {
        try {
            VideoPicker.trimVideo(this, uri)
        } catch (e: Exception) {
            Timber.w("Failed to trim video from URI $uri")
            viewModel.handleVideoTranscodeFailed()
        }
    }

    private fun launchCamera() = requestPermissionLauncher.launch(Manifest.permission.CAMERA)

    private fun launchPicker() {
        // gallery intent
        val intent = Intent(Intent.ACTION_GET_CONTENT).apply {
            type = "*/*"
            putExtra(Intent.CATEGORY_OPENABLE, true)
            putExtra(Intent.EXTRA_LOCAL_ONLY, true)
            putExtra(Intent.EXTRA_MIME_TYPES, arrayOf("image/*", "video/*"))
        }

        startActivityForResult(intent, STTConstants.RequestCodes.PICK_IMAGE_OR_VIDEO)
    }

    override fun onImagePicked(uri: Uri) {
        try {
            viewModel.setMediaFromPicker(uri, "image/jpeg")
            handleSave()
        } catch (e: Exception) {
            Timber.w(e, "Failed to process picked image URI $uri")
        }
    }

    override fun onImagePicked(file: File) {
        onImagePicked(file.toUri())
    }

    companion object {
        const val GALLERY_RESULT_KEY = "com.stt.android.GALLERY_RESULT"

        private const val WORKOUT_HEADER_EXTRA = "com.stt.android.multimedia.gallery.WORKOUT_HEADER"

        /**
         * Pick images/videos from Gallery and return as intent result
         */
        fun newIntentForPicking(context: Context) =
            Intent(context, MediaGalleryActivity::class.java)

        /**
         * Pick images/videos from Gallery and return as intent result
         */
        fun newIntentForPickingWithPrioritizedTimePeriod(
            context: Context,
            prioritizedTimePeriodStart: Long,
            prioritizedTimePeriodEnd: Long
        ) =
            Intent(context, MediaGalleryActivity::class.java).apply {
                putExtra(
                    PRIORITIZED_TIME_PERIOD_START_MILLIS_EXTRA,
                    prioritizedTimePeriodStart
                )

                putExtra(
                    PRIORITIZED_TIME_PERIOD_END_MILLIS_EXTRA,
                    prioritizedTimePeriodEnd
                )
            }

        /**
         * Pick images/videos from Gallery and directly add to an existing workout
         */
        fun newIntentForDirectAddToWorkout(
            context: Context,
            workoutHeader: WorkoutHeader
        ) = Intent(context, MediaGalleryActivity::class.java).apply {
            putExtra(WORKOUT_HEADER_EXTRA, workoutHeader)

            putExtra(
                PRIORITIZED_TIME_PERIOD_START_MILLIS_EXTRA,
                workoutHeader.startTime
            )

            putExtra(
                PRIORITIZED_TIME_PERIOD_END_MILLIS_EXTRA,
                workoutHeader.stopTime
            )
        }
    }
}
