package com.stt.android.multimedia.picker;

import android.app.Activity;
import android.content.ActivityNotFoundException;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.media.MediaMetadataRetriever;
import android.net.Uri;
import android.os.Parcelable;
import android.provider.MediaStore;
import android.widget.Toast;
import androidx.annotation.NonNull;
import com.otaliastudios.transcoder.Transcoder;
import com.otaliastudios.transcoder.TranscoderListener;
import com.otaliastudios.transcoder.TranscoderOptions;
import com.otaliastudios.transcoder.common.TrackType;
import com.otaliastudios.transcoder.source.BlankAudioDataSource;
import com.otaliastudios.transcoder.source.ClipDataSource;
import com.otaliastudios.transcoder.source.DataSource;
import com.otaliastudios.transcoder.source.UriDataSource;
import com.otaliastudios.transcoder.strategy.DefaultAudioStrategy;
import com.otaliastudios.transcoder.strategy.DefaultVideoStrategies;
import com.stt.android.R;
import com.stt.android.multimedia.video.trimming.VideoTrimmingActivity;
import com.stt.android.utils.FileUtils;
import com.stt.android.utils.STTConstants;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import timber.log.Timber;

public abstract class VideoPicker {
    private static final int AUDIO_BIT_RATE = 128 * 1024; // 128 Kbps
    private static final int AUDIO_CHANNELS = 1;

    public static boolean onActivityResult(Activity activity, int requestCode, int resultCode,
        Intent data, VideoTranscodingListener listener) {
        if (requestCode == STTConstants.RequestCodes.PICK_WORKOUT_VIDEO) {
            Uri uri = data != null ? data.getData() : null;
            if (uri != null) {
                trimVideo(activity, uri);
            }
            return true;
        } else if (requestCode == STTConstants.RequestCodes.TRIM_WORKOUT_VIDEO) {
            if (resultCode == Activity.RESULT_OK) {
                Uri uri = data != null ? data.getData() : null;
                if (uri != null) {
                    long startTime =
                        data.getLongExtra(VideoTrimmingActivity.KEY_SELECTED_START_TIME, 0L);
                    long endTime =
                        data.getLongExtra(VideoTrimmingActivity.KEY_SELECTED_END_TIME, 0L);
                    boolean audioIncluded =
                        data.getBooleanExtra(VideoTrimmingActivity.KEY_AUDIO_INCLUDED, true);
                    transcodeVideo(activity, uri, startTime, endTime, audioIncluded,
                        listener);
                } else {
                    listener.onVideoTranscodeCanceled();
                }
            } else if (resultCode == Activity.RESULT_CANCELED) {
                listener.onVideoTranscodeCanceled();
            }
            return true;
        }

        return false;
    }

    private static String sanitizedFilename(String input) {
        return input.replaceAll("[\\\\/:*?\"<>|]", "_");
    }

    private static void transcodeVideo(Context context, Uri source, long startTimeMilliseconds,
        long stopTimeMilliseconds, boolean audioIncluded, final VideoTranscodingListener listener) {
        try {
            String name = source.getLastPathSegment();
            if (name == null) {
                throw new IllegalArgumentException("Invalid source URI for transcodeVideo: " + source);
            }

            name = sanitizedFilename(name);

            final File output = FileUtils.getInternalFilePath(
                context,
                STTConstants.DIRECTORY_VIDEOS,
                name + ".mp4");

            final File thumbnail = FileUtils.getInternalFilePath(
                context,
                STTConstants.DIRECTORY_VIDEOS,
                name + ".jpg");

            // API requires microseconds
            long startInUs = startTimeMilliseconds * 1000;
            long stopInUs = stopTimeMilliseconds * 1000;
            DataSource videoSource = new UriDataSource(context, source);
            TranscoderOptions.Builder builder = Transcoder.into(output.getPath())
                .setVideoRotation(0)
                .setVideoTrackStrategy(DefaultVideoStrategies.for720x1280())
                .setAudioTrackStrategy(
                    DefaultAudioStrategy.builder()
                        .channels(AUDIO_CHANNELS)
                        .bitRate(AUDIO_BIT_RATE)
                        .build());
            if (audioIncluded) {
                builder.addDataSource(
                    new ClipDataSource(
                        videoSource,
                        startInUs,
                        stopInUs));
            } else {
                builder.addDataSource(
                        TrackType.VIDEO,
                        new ClipDataSource(
                            videoSource,
                            startInUs,
                            stopInUs))
                    .addDataSource(
                        TrackType.AUDIO,
                        new BlankAudioDataSource(
                            stopInUs) // api will clip the audio to correct length automatically.
                    );
            }
            builder.setListener(
                    new TranscoderListener() {
                        public void onTranscodeProgress(double progress) {
                            Timber.d("Transcode progress: %d", (int) (progress * 100));
                        }

                        public void onTranscodeCompleted(int successCode) {
                            MediaMetadataRetriever mediaMetadataRetriever = null;
                            FileOutputStream os = null;
                            int width = 1280;
                            int height = 720;
                            try {
                                // Extract thumbnail
                                mediaMetadataRetriever = new MediaMetadataRetriever();
                                mediaMetadataRetriever.setDataSource(output.getPath());
                                Bitmap thumbnailBitmap = mediaMetadataRetriever.getFrameAtTime(0L);
                                // Get actual resolution
                                width = thumbnailBitmap.getWidth();
                                height = thumbnailBitmap.getHeight();
                                os = new FileOutputStream(thumbnail);
                                // Write thumbnail as JPEG
                                thumbnailBitmap.compress(Bitmap.CompressFormat.JPEG, 90, os);
                            } catch (Throwable ignored) {
                            } finally {
                                if (mediaMetadataRetriever != null) {
                                    try {
                                        mediaMetadataRetriever.release();
                                    } catch (IOException e) {
                                        Timber.w(e, "Failed to releaseMediaMetadataRetriever");
                                    }
                                }
                                if (os != null) {
                                    try {
                                        os.close();
                                    } catch (Throwable ignored) {
                                    }
                                }

                                listener.onVideoTranscodeCompleted(
                                    output,
                                    thumbnail,
                                    width,
                                    height);
                            }
                        }

                        public void onTranscodeCanceled() {
                            Timber.d("Transcode canceled.");
                        }

                        public void onTranscodeFailed(@NonNull Throwable exception) {
                            Timber.e(exception, "Failed to transcode video.");
                            listener.onVideoTranscodeFailed();
                        }
                    }).transcode();
        } catch (Throwable e) {
            Timber.e(e, "Failed to transcode video.");
            listener.onVideoTranscodeFailed();
        }
    }

    public static void captureVideo(Activity activity) {
        try {
            activity.startActivityForResult(new Intent(MediaStore.ACTION_VIDEO_CAPTURE),
                STTConstants.RequestCodes.PICK_WORKOUT_VIDEO);
        } catch (ActivityNotFoundException e) {
            Toast.makeText(activity.getApplicationContext(), R.string.camera_activity_not_found, Toast.LENGTH_LONG).show();
        } catch (IllegalStateException e) {
            Toast.makeText(activity.getApplicationContext(), R.string.error_0, Toast.LENGTH_LONG).show();
        }
    }

    public static void pickVideo(Activity activity) {
        try {
            // camera intent
            Intent cameraIntent = new Intent(MediaStore.ACTION_VIDEO_CAPTURE);

            // gallery intent
            Intent galleryIntent = new Intent(Intent.ACTION_PICK);
            galleryIntent.setType("video/*");

            // create the choose
            final Intent chooserIntent = Intent.createChooser(galleryIntent,
                activity.getString(R.string.dialog_title_select));
            chooserIntent.putExtra(Intent.EXTRA_INITIAL_INTENTS, new Parcelable[] { cameraIntent });
            activity.startActivityForResult(chooserIntent,
                STTConstants.RequestCodes.PICK_WORKOUT_VIDEO);
        } catch (ActivityNotFoundException e) {
            Toast.makeText(activity.getApplicationContext(), R.string.camera_activity_not_found, Toast.LENGTH_LONG).show();
        } catch (IllegalStateException e) {
            Toast.makeText(activity.getApplicationContext(), R.string.error_0, Toast.LENGTH_LONG).show();
        }
    }

    public interface VideoTranscodingListener {
        void onVideoTranscodeCompleted(File transcoded, File thumbnail, int transcodedVideoWidth,
            int transcodedVideoHeight);

        void onVideoTranscodeFailed();

        void onVideoTranscodeCanceled();
    }

    public static void trimVideo(Activity activity, Uri uri) {
        activity.startActivityForResult(VideoTrimmingActivity.newStartIntent(activity, uri),
            STTConstants.RequestCodes.TRIM_WORKOUT_VIDEO);
    }
}
