package com.stt.android.multimedia.picker

import android.content.ContentUris
import android.content.Context
import android.os.Build
import android.provider.MediaStore
import androidx.annotation.WorkerThread
import com.stt.android.controllers.PicturesController
import com.stt.android.controllers.VideoModel
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.workouts.WorkoutHeader
import kotlinx.coroutines.yield
import timber.log.Timber
import java.time.Duration
import java.time.temporal.ChronoUnit

object MediaPickerHelper2 {
    // TODO: make this a use case
    suspend fun loadWorkoutMedia(
        picturesController: PicturesController,
        videoModel: VideoModel,
        workoutHeader: WorkoutHeader,
    ): List<MediaInfoForPicker> {
        val images = loadWorkoutImages(
            picturesController,
            workoutHeader,
        )

        val videos = loadWorkoutVideos(videoModel, workoutHeader)

        return (images + videos).sortedByDescending { it.lastModified }
    }

    private suspend fun loadWorkoutImages(
        picturesController: PicturesController,
        workoutHeader: WorkoutHeader,
    ): List<MediaInfoForPicker> = runSuspendCatching {
        picturesController.findByWorkoutId(workoutHeader.id)
            .map {
                yield()
                PictureInfoForPicker(it)
            }
    }.getOrElse { e ->
        Timber.w(e, "loadWorkoutImages failed")
        emptyList()
    }

    private suspend fun loadWorkoutVideos(
        videoModel: VideoModel,
        workoutHeader: WorkoutHeader,
    ): List<MediaInfoForPicker> = runSuspendCatching {
        videoModel.findByWorkoutId(workoutHeader.id)
            .map {
                yield()
                VideoInfoForPicker(it)
            }
    }.getOrElse { e ->
        Timber.w(e, "loadWorkoutVideos failed")
        emptyList()
    }

    @WorkerThread
    suspend fun loadImages(context: Context): List<MediaInfoForPicker> {
        val images = mutableListOf<MediaInfoForPicker>()

        val collection =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                MediaStore.Images.Media.getContentUri(
                    MediaStore.VOLUME_EXTERNAL
                )
            } else {
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI
            }

        val columns = arrayOf(
            MediaStore.Images.ImageColumns._ID,
            MediaStore.Images.ImageColumns.WIDTH,
            MediaStore.Images.ImageColumns.HEIGHT,
            MediaStore.Images.ImageColumns.DATE_MODIFIED
        )

        runSuspendCatching {
            context.applicationContext.contentResolver.query(
                collection,
                columns,
                null,
                null,
                null
            ).use { cursor ->
                if (cursor == null) return emptyList()

                yield()
                val idIndex = cursor.getColumnIndex(MediaStore.Images.ImageColumns._ID)
                val widthIndex = cursor.getColumnIndex(MediaStore.Images.ImageColumns.WIDTH)
                val heightIndex = cursor.getColumnIndex(MediaStore.Images.ImageColumns.HEIGHT)
                val lastModifiedIndex =
                    cursor.getColumnIndex(MediaStore.Images.ImageColumns.DATE_MODIFIED)

                while (cursor.moveToNext()) {
                    val id = cursor.getLong(idIndex)
                    val timestamp =
                        Duration.of(cursor.getLong(lastModifiedIndex), ChronoUnit.SECONDS).toMillis()
                    var width = 0
                    if (widthIndex >= 0) {
                        width = cursor.getInt(widthIndex)
                    }

                    var height = 0
                    if (heightIndex >= 0) {
                        height = cursor.getInt(heightIndex)
                    }

                    val contentUri = ContentUris.withAppendedId(
                        MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                        id
                    )

                    images.add(
                        PickedMediaInfoForPicker(
                            mediaType = MediaInfoForPicker.MEDIA_TYPE_IMAGE,
                            contentUri = contentUri,
                            timestamp = timestamp,
                            width = width,
                            height = height
                        )
                    )
                }
            }
        }.onFailure { e ->
            Timber.w(e, "Failed to get images")
            throw e
        }

        return images
    }

    @WorkerThread
    suspend fun loadVideos(context: Context): List<MediaInfoForPicker> {
        val videos = mutableListOf<MediaInfoForPicker>()

        val collection =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                MediaStore.Video.Media.getContentUri(
                    MediaStore.VOLUME_EXTERNAL
                )
            } else {
                MediaStore.Video.Media.EXTERNAL_CONTENT_URI
            }

        val columns = arrayOf(
            MediaStore.Video.VideoColumns._ID,
            MediaStore.Video.VideoColumns.WIDTH,
            MediaStore.Video.VideoColumns.HEIGHT,
            MediaStore.Video.VideoColumns.DATE_MODIFIED
        )

        runSuspendCatching {
            context.applicationContext.contentResolver.query(
                collection,
                columns,
                null,
                null,
                null
            ).use { cursor ->
                if (cursor == null) return emptyList()

                yield()
                val idIndex = cursor.getColumnIndex(MediaStore.Video.VideoColumns._ID)
                val widthIndex = cursor.getColumnIndex(MediaStore.Video.VideoColumns.WIDTH)
                val heightIndex = cursor.getColumnIndex(MediaStore.Video.VideoColumns.HEIGHT)
                val lastModifiedIndex =
                    cursor.getColumnIndex(MediaStore.Video.VideoColumns.DATE_MODIFIED)

                while (cursor.moveToNext()) {
                    val id = cursor.getLong(idIndex)
                    val timestamp =
                        Duration.of(cursor.getLong(lastModifiedIndex), ChronoUnit.SECONDS).toMillis()
                    var width = 0
                    if (widthIndex >= 0) {
                        width = cursor.getInt(widthIndex)
                    }

                    var height = 0
                    if (heightIndex >= 0) {
                        height = cursor.getInt(heightIndex)
                    }

                    val contentUri = ContentUris.withAppendedId(
                        MediaStore.Video.Media.EXTERNAL_CONTENT_URI,
                        id
                    )

                    videos.add(
                        PickedMediaInfoForPicker(
                            mediaType = MediaInfoForPicker.MEDIA_TYPE_VIDEO,
                            contentUri = contentUri,
                            timestamp = timestamp,
                            width = width,
                            height = height
                        )
                    )
                }
            }
        }.onFailure { e ->
            Timber.w(e, "Failed to get videos")
            throw e
        }

        return videos
    }
}
