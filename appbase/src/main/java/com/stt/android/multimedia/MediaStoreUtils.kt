package com.stt.android.multimedia

import android.content.ContentResolver
import android.content.ContentValues
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.os.ParcelFileDescriptor
import android.provider.MediaStore
import android.util.Base64
import android.webkit.MimeTypeMap
import androidx.annotation.ChecksSdkIntAtLeast
import androidx.annotation.WorkerThread
import androidx.core.content.FileProvider
import com.stt.android.BuildConfig
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.util.UUID

enum class MediaType(
    val uri: Uri,
    val displayNameKey: String,
    val pendingKey: String,
    val relativePath: String
) {
    VIDEO(
        uri = if (isAfterP()) MediaStore.Video.Media.getContentUri(MediaStore.VOLUME_EXTERNAL_PRIMARY) else MediaStore.Video.Media.EXTERNAL_CONTENT_URI,
        displayNameKey = MediaStore.Video.Media.DISPLAY_NAME,
        pendingKey = MediaStore.Video.Media.IS_PENDING,
        relativePath = if (isAfterP()) {
            "${Environment.DIRECTORY_MOVIES}/${BuildConfig.DIRECTORY_APPLICATION}"
        } else ""
    ),
    IMAGE(
        uri = if (isAfterP()) MediaStore.Images.Media.getContentUri(MediaStore.VOLUME_EXTERNAL_PRIMARY) else MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
        displayNameKey = MediaStore.Images.Media.DISPLAY_NAME,
        pendingKey = MediaStore.Images.Media.IS_PENDING,
        relativePath = if (isAfterP()) {
            "${Environment.DIRECTORY_PICTURES}/${BuildConfig.DIRECTORY_APPLICATION}"
        } else ""
    )
}

@ChecksSdkIntAtLeast(api = Build.VERSION_CODES.Q)
private fun isAfterP() = Build.VERSION.SDK_INT > Build.VERSION_CODES.P

object MediaStoreUtils {
    val SUPPORTED_IMAGE_MIME_TYPES = listOfNotNull(
        MimeTypeMap.getSingleton().getMimeTypeFromExtension("jpg"),
        MimeTypeMap.getSingleton().getMimeTypeFromExtension("png")
    ).toTypedArray()

    suspend fun isSupportedImageMimeType(context: Context, uri: Uri): Boolean = withContext(Dispatchers.IO) {
        val contentMimeType = context.contentResolver.getType(uri)
        return@withContext if (contentMimeType.isNullOrBlank()) {
            true
        } else {
            contentMimeType in SUPPORTED_IMAGE_MIME_TYPES
        }
    }

    @WorkerThread
    inline fun saveMediaToMediaStore(
        resolver: ContentResolver,
        displayName: String,
        mediaType: MediaType = MediaType.IMAGE,
        writeOperation: (ParcelFileDescriptor) -> Unit
    ): Uri {
        // Insert entry as IS_PENDING
        val mediaDetails = ContentValues().apply {
            put(mediaType.displayNameKey, displayName)
            if (Build.VERSION.SDK_INT >= 29) {
                put(mediaType.pendingKey, 1)
                put(
                    MediaStore.Video.Media.RELATIVE_PATH,
                    mediaType.relativePath
                )
            }
        }

        // Write data using file descriptor
        val uri = resolver.insert(mediaType.uri, mediaDetails)
            ?: throw FileNotFoundException("Unable to insert $displayName to MediaStore")
        resolver.openFileDescriptor(uri, "w", null).use { pfd ->
            if (pfd == null) {
                throw FileNotFoundException("Unable to open $displayName for writing")
            } else {
                writeOperation(pfd)
            }
        }

        // Clear IS_PENDING flag
        if (Build.VERSION.SDK_INT >= 29) {
            mediaDetails.clear()
            mediaDetails.put(mediaType.pendingKey, 0)
            resolver.update(uri, mediaDetails, null, null)
        }

        return uri
    }

    /**
     * save a temp image for share
     */
    @WorkerThread
    inline fun saveImageToExternalFilesDir(
        applicationContext: Context,
        displayName: String,
        writeOperation: (File) -> Unit
    ): Uri {
        // weibo sdk only can get the file that is from external file root dir path
        val externalFilesDir = applicationContext.getExternalFilesDir(null)
        val imageFile = File(externalFilesDir, displayName)
        writeOperation(imageFile)
        return FileProvider.getUriForFile(
            applicationContext,
            "${applicationContext.packageName}.FileProvider",
            imageFile
        )
    }

    /**
     * save multiple images to the external directory
     * @param images image base64
     * since weibo share need a image uri in externalFileDir root dir
     */
    @WorkerThread
    fun saveImagesToExternalFileDir(
        externalFilesDir: File,
        images: List<String>,
    ): List<File> {
        val imageFiles = arrayListOf<File>()
        images.forEach { base64 ->
            val imageFile = File(externalFilesDir,  UUID.randomUUID().toString() + ".jpg")
            saveBase64ImageToFile(imageFile, base64)
            imageFiles.add(imageFile)
        }
        return imageFiles
    }

    @WorkerThread
    fun saveBase64ImageToFile(imageFile: File, base64Data: String) {
        FileOutputStream(imageFile).use {
            val decode = Base64.decode(base64Data, Base64.DEFAULT)
            BitmapFactory.decodeByteArray(decode, 0, decode.size)
                .compress(Bitmap.CompressFormat.JPEG, 90, it)
        }
    }
}
