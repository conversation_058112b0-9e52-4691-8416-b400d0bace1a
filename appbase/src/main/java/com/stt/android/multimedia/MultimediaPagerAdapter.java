package com.stt.android.multimedia;

import android.content.Context;
import android.net.Uri;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewpager.widget.PagerAdapter;
import coil3.SingletonImageLoader;
import coil3.request.ImageRequest;
import coil3.request.ImageRequestsKt;
import coil3.request.ImageRequests_androidKt;
import com.github.chrisbanes.photoview.OnOutsidePhotoTapListener;
import com.github.chrisbanes.photoview.OnPhotoTapListener;
import com.github.chrisbanes.photoview.OnScaleChangedListener;
import com.github.chrisbanes.photoview.PhotoView;
import com.google.android.exoplayer2.ExoPlayer;
import com.google.android.exoplayer2.PlaybackException;
import com.google.android.exoplayer2.Player;
import com.stt.android.R;
import com.stt.android.analytics.AnalyticsEvent;
import com.stt.android.databinding.VideoPageBinding;
import com.stt.android.domain.user.ImageInformation;
import com.stt.android.domain.user.VideoInformation;
import com.stt.android.multimedia.video.ExoPlayerHelper;
import com.stt.android.ui.utils.AnimationHelper;
import java.lang.ref.WeakReference;
import java.util.List;
import timber.log.Timber;

public class MultimediaPagerAdapter extends PagerAdapter
    implements OnPhotoTapListener, OnOutsidePhotoTapListener, View.OnClickListener,
    OnScaleChangedListener {
    public interface Listener {
        void onMediaClicked();

        void onZoom();

        void sendAnalyticsEvent(
            @AnalyticsEvent.EventName String event,
            String propertyName,
            String propertyValue);
    }

    private final LayoutInflater inflater;
    private final Listener listener;

    private final List<VideoInformation> videos;
    private final List<ImageInformation> images;

    private final WeakReference<VideoViewHolder>[] videoViewHolders;
    private int currentPosition = -1;
    private final String userAgent;

    public MultimediaPagerAdapter(Context context, Listener listener, List<VideoInformation> videos,
        List<ImageInformation> images, String userAgent) {
        this.inflater = LayoutInflater.from(context);
        this.listener = listener;
        this.videos = videos;
        this.images = images;
        videoViewHolders = new WeakReference[videos.size()];
        this.userAgent = userAgent;
    }

    @Override
    public int getCount() {
        return videos.size() + images.size();
    }

    @NonNull
    @Override
    public Object instantiateItem(@NonNull ViewGroup container, int position) {
        int videoCount = videos.size();
        if (position < videoCount) {
            // It's a video.
            View root = inflater.inflate(R.layout.video_page, container, false);

            VideoViewHolder videoViewHolder = new VideoViewHolder(root, listener, userAgent);
            videoViewHolder.bind(videos.get(position));
            if (position == currentPosition) {
                videoViewHolder.startPlaying();
            }
            root.setTag(videoViewHolder);

            videoViewHolders[position] = new WeakReference<>(videoViewHolder);
            container.addView(root);
            return root;
        } else {
            // It's an image.
            position -= videoCount;

            PhotoView workoutImage =
                (PhotoView) inflater.inflate(R.layout.workout_image_pager_item,
                    container, false);
            workoutImage.setOnScaleChangeListener(this);
            workoutImage.setOnPhotoTapListener(this);
            workoutImage.setOnOutsidePhotoTapListener(this);

            container.addView(workoutImage);

            Context ctx = workoutImage.getContext();
            workoutImage.setScaleType(ImageView.ScaleType.FIT_CENTER);
            ImageRequest.Builder builder = new ImageRequest.Builder(ctx)
                .data(images.get(position).getHighResUri(ctx));
            ImageRequestsKt.crossfade(builder, false);
            ImageRequests_androidKt.target(builder, workoutImage);
            SingletonImageLoader.get(ctx).enqueue(builder.build());
            return workoutImage;
        }
    }

    @Override
    public void destroyItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
        if (position < videos.size()) {
            // It's a video.
            VideoViewHolder videoViewHolder =
                videoViewHolders[position] != null ? videoViewHolders[position].get() : null;
            if (videoViewHolder != null) {
                videoViewHolder.unbind();
            }
            videoViewHolders[position] = null;

            container.removeView((View) object);
        } else {
            // It's an image.
            View view = (View) object;
            container.removeView(view);
        }
    }

    @Override
    public boolean isViewFromObject(@NonNull View view, @NonNull Object object) {
        return view == object;
    }

    @Override
    public void onClick(View v) {
        listener.onMediaClicked();
    }

    @Override
    public void onScaleChange(float scaleFactor, float focusX, float focusY) {
        listener.onZoom();
    }

    @NonNull
    public List<ImageInformation> getImages() {
        return images;
    }

    @NonNull
    public List<VideoInformation> getVideos() {
        return videos;
    }

    @Override
    public void onPhotoTap(ImageView imageView, float v, float v1) {
        listener.onMediaClicked();
    }

    @Override
    public void onOutsidePhotoTap(ImageView imageView) {
        listener.onMediaClicked();
    }

    @Nullable
    public ImageInformation getImageInformation(int adapterPosition) {
        adapterPosition -= videos.size();
        return adapterPosition < 0 ? null : images.get(adapterPosition);
    }

    @Nullable
    public VideoInformation getVideoInformation(int adapterPosition) {
        return adapterPosition < videos.size() ? videos.get(adapterPosition) : null;
    }

    public void setCurrentPosition(int position) {
        currentPosition = position;
        for (int i = videoViewHolders.length - 1; i >= 0; --i) {
            VideoViewHolder videoViewHolder =
                videoViewHolders[i] != null ? videoViewHolders[i].get() : null;
            if (videoViewHolder != null) {
                if (i == position) {
                    videoViewHolder.startPlaying();
                } else {
                    videoViewHolder.stopPlaying();
                }
            }
        }
    }

    public void onStop() {
        for (WeakReference<VideoViewHolder> ref : videoViewHolders) {
            VideoViewHolder videoViewHolder = ref != null ? ref.get() : null;
            if (videoViewHolder != null) {
                videoViewHolder.stopPlaying();
            }
        }
    }

    public void onDestroy() {
        for (int i = videoViewHolders.length - 1; i >= 0; --i) {
            VideoViewHolder videoViewHolder =
                videoViewHolders[i] != null ? videoViewHolders[i].get() : null;
            if (videoViewHolder != null) {
                videoViewHolder.unbind();
            }
            videoViewHolders[i] = null;
        }
    }

    public void showControls() {
        for (WeakReference<VideoViewHolder> ref : videoViewHolders) {
            VideoViewHolder videoViewHolder = ref != null ? ref.get() : null;
            if (videoViewHolder != null) {
                videoViewHolder.showControls();
            }
        }
    }

    public void hideControls() {
        for (WeakReference<VideoViewHolder> ref : videoViewHolders) {
            VideoViewHolder videoViewHolder = ref != null ? ref.get() : null;
            if (videoViewHolder != null) {
                videoViewHolder.hideControls();
            }
        }
    }

    static class VideoViewHolder implements View.OnClickListener, Player.Listener {
        VideoPageBinding binding;

        private final Listener listener;

        private boolean isMute = false;

        VideoViewHolder(View root, Listener listener, String userAgent) {
            this.listener = listener;
            binding = VideoPageBinding.bind(root);
            root.setOnClickListener(this);
            binding.muteVideo.setOnClickListener(this);

            ExoPlayer player = ExoPlayerHelper.createPlayer(root.getContext(), userAgent);
            binding.exoPlayerView.setPlayer(player);
            player.addListener(this);
        }

        void bind(VideoInformation videoInformation) {
            Uri uri = videoInformation.getUri(binding.exoPlayerView.getContext());
            if (uri == null) {
                return;
            }

            Player player = binding.exoPlayerView.getPlayer();
            ExoPlayerHelper.setMedia(player, uri, true);
            if (player != null) {
                player.prepare();
                player.setPlayWhenReady(false);
            }

            isMute = false;
            updateMuteState();
        }

        private void updateMuteState() {
            binding.muteVideo.setImageResource(isMute ? com.stt.android.R.drawable.ic_speaker_off
                : com.stt.android.R.drawable.ic_speaker_full);
            Player player = binding.exoPlayerView.getPlayer();
            if (player != null) {
                player.setVolume(isMute ? 0.0F : 1.0F);
            }
        }

        void unbind() {
            Player player = binding.exoPlayerView.getPlayer();
            if (player != null) {
                player.stop();
                player.release();
                binding.exoPlayerView.setPlayer(null);
            }
        }

        void startPlaying() {
            Player player = binding.exoPlayerView.getPlayer();
            if (player != null) {
                player.setPlayWhenReady(true);
                listener.sendAnalyticsEvent(AnalyticsEvent.LOADING_VIDEO, "Location", "FullscreenView");
            }
        }

        void stopPlaying() {
            Player player = binding.exoPlayerView.getPlayer();
            if (player != null) {
                player.setPlayWhenReady(false);
            }
        }

        void showControls() {
            AnimationHelper.fadeIn(binding.muteVideo);
        }

        void hideControls() {
            AnimationHelper.fadeOut(binding.muteVideo);
        }

        @Override
        public void onClick(View v) {
            if (binding.muteVideo == v) {
                isMute = !isMute;
                updateMuteState();
            } else {
                listener.onMediaClicked();
            }
        }

        @Override
        public void onPlayWhenReadyChanged(boolean playWhenReady, int reason) {
            if (playWhenReady) {
                listener.sendAnalyticsEvent(AnalyticsEvent.PLAY_VIDEO, "Location",
                    "FullscreenView");
            }
        }

        @Override
        public void onPlayerError(PlaybackException e) {
            Timber.e(e, "Error occured while playing video.");
        }
    }
}
