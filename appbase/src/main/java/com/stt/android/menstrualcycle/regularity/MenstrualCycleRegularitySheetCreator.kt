package com.stt.android.menstrualcycle.regularity

import androidx.fragment.app.DialogFragment
import com.stt.android.domain.user.MenstrualCycleRegularity
import javax.inject.Inject

class MenstrualCycleRegularitySheetCreator @Inject constructor() {

    fun create(regularity: MenstrualCycleRegularity): DialogFragment? = when (regularity) {
        MenstrualCycleRegularity.REGULAR -> RegularSheetFragment()
        MenstrualCycleRegularity.IRREGULAR -> IrregularSheetFragment()
        else -> null
    }
}
