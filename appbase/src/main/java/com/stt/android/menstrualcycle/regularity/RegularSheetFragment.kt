package com.stt.android.menstrualcycle.regularity

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import com.stt.android.compose.util.setContentWithTheme
import com.stt.android.ui.utils.SmartBottomSheetDialogFragment

class RegularSheetFragment : SmartBottomSheetDialogFragment() {

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ) = ComposeView(requireContext()).apply {
        setViewCompositionStrategy(
            ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
        )

        setContentWithTheme {
            RegularSheetContent(
                onDismissClick = { dismiss() },
            )
        }
    }
}
