package com.stt.android.menstrualcycle

import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty.MENSTRUAL_CYCLE_PERIOD_LOGGED_ACTION
import com.stt.android.analytics.AnalyticsEventProperty.MENSTRUAL_CYCLE_PERIOD_LOGGED_CYCLE_LENGTH
import com.stt.android.analytics.AnalyticsEventProperty.MENSTRUAL_CYCLE_PERIOD_LOGGED_CYCLE_REGULARITY
import com.stt.android.analytics.AnalyticsEventProperty.MENSTRUAL_CYCLE_PERIOD_LOGGED_END_DAY
import com.stt.android.analytics.AnalyticsEventProperty.MENSTRUAL_CYCLE_PERIOD_LOGGED_FROM
import com.stt.android.analytics.AnalyticsEventProperty.MENSTRUAL_CYCLE_PERIOD_LOGGED_LOG_ONGOING_OR_BACKLOG
import com.stt.android.analytics.AnalyticsEventProperty.MENSTRUAL_CYCLE_PERIOD_LOGGED_START_DAY
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.AnalyticsUserProperty
import com.stt.android.domain.user.MenstrualCycleRegularity
import com.stt.android.menstrualcycle.domain.MenstrualCycle
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import javax.inject.Inject

enum class LoggedFrom(val analyticsName: String) {
    HOME("HomeScreenPlusMenu"),
    CALENDAR("CalendarScreen"),
    DAY_VIEW("DayDetailsScreen"),
}

enum class OngoingOrBacklog(val analyticsName: String) {
    ONGOING("Ongoing"),
    BACKLOG("Backlog"),
}

enum class CycleAction(val analyticsName: String) {
    NEW_CYCLE("NewCycle"),
    EDIT_CYCLE("EditCycle"),
    DELETE_DAY("DeleteDay"),
    DELETE_CYCLE("DeleteCycle"),
}

class MenstrualCycleAnalyticsUtils @Inject constructor(
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
) {

    fun trackLogged(
        from: LoggedFrom,
        ongoingOrBacklog: OngoingOrBacklog,
        action: CycleAction,
        previousMenstrualCycle: MenstrualCycle?,
        menstrualCycle: MenstrualCycle,
        regularity: MenstrualCycleRegularity
    ) {
        AnalyticsProperties().apply {
            put(MENSTRUAL_CYCLE_PERIOD_LOGGED_FROM, from.analyticsName)
            put(
                MENSTRUAL_CYCLE_PERIOD_LOGGED_LOG_ONGOING_OR_BACKLOG,
                ongoingOrBacklog.analyticsName
            )
            put(MENSTRUAL_CYCLE_PERIOD_LOGGED_ACTION, action.analyticsName)
            if (menstrualCycle.includedDates.isNotEmpty()) {
                previousMenstrualCycle?.let {
                    val cycleLength =
                        ChronoUnit.DAYS.between(it.startDate, menstrualCycle.startDate).toInt()
                    put(MENSTRUAL_CYCLE_PERIOD_LOGGED_CYCLE_LENGTH, cycleLength)
                }
                put(
                    MENSTRUAL_CYCLE_PERIOD_LOGGED_START_DAY,
                    menstrualCycle.startDate.format(DateTimeFormatter.ISO_LOCAL_DATE)
                )
                put(
                    MENSTRUAL_CYCLE_PERIOD_LOGGED_END_DAY,
                    menstrualCycle.endDate.format(DateTimeFormatter.ISO_LOCAL_DATE)
                )
            }
            put(MENSTRUAL_CYCLE_PERIOD_LOGGED_CYCLE_REGULARITY, regularity.analyticsName)
        }.also {
            amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.MENSTRUAL_CYCLE_PERIOD_LOGGED, it)
        }
    }

    fun trackDeleted(from: LoggedFrom, action: CycleAction, regularity: MenstrualCycleRegularity) {
        AnalyticsProperties().apply {
            put(MENSTRUAL_CYCLE_PERIOD_LOGGED_FROM, from.analyticsName)
            put(MENSTRUAL_CYCLE_PERIOD_LOGGED_ACTION, action.analyticsName)
            put(MENSTRUAL_CYCLE_PERIOD_LOGGED_CYCLE_REGULARITY, regularity.analyticsName)
        }.also {
            amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.MENSTRUAL_CYCLE_PERIOD_LOGGED, it)
        }
    }

    fun trackIsUsingFeature(isUsing: Boolean) {
        amplitudeAnalyticsTracker.trackUserProperty(
            AnalyticsUserProperty.IS_USING_MENSTRUAL_CYCLE_FEATURE,
            if (isUsing) AnalyticsPropertyValue.YES else AnalyticsPropertyValue.NO
        )
    }
}
