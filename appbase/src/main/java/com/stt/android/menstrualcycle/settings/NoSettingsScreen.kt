package com.stt.android.menstrualcycle.settings

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.R
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.spacing
import java.util.Locale

@Composable
fun NoSettings(
    onSetUpClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .verticalScroll(rememberScrollState())
            .fillMaxHeight()
            .background(MaterialTheme.colors.surface),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = stringResource(id = R.string.settings_menstrual_cycle_tracking_stopped_description),
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.padding(MaterialTheme.spacing.medium)
        )

        TextButton(onClick = { onSetUpClick() }) {
            Text(
                text = stringResource(id = R.string.set_up).uppercase(Locale.getDefault()),
                style = MaterialTheme.typography.bodyBold,
                color = MaterialTheme.colors.primary
            )
        }

        Spacer(
            modifier = Modifier
                .weight(1F)
                .background(MaterialTheme.colors.background)
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun NoSettingsPreview() {
    AppTheme {
        NoSettings(
            onSetUpClick = {}
        )
    }
}
