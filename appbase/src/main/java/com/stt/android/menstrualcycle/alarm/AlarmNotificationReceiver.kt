package com.stt.android.menstrualcycle.alarm

import android.app.Notification
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import androidx.core.app.NotificationCompat
import androidx.core.app.TaskStackBuilder
import androidx.core.net.toUri
import com.stt.android.R
import com.stt.android.analytics.AnalyticsPropertyValue.AppOpenSourceType.PUSH_MESSAGE
import com.stt.android.analytics.AnalyticsPropertyValue.AppOpenSourceTypeDetail.LOG_PERIOD
import com.stt.android.analytics.AnalyticsPropertyValue.AppOpenSourceTypeDetail.NEXT_CYCLE
import com.stt.android.analytics.AppOpenAnalyticsActivity
import com.stt.android.launcher.BaseProxyActivity
import com.stt.android.menstrualcycle.MenstrualCycleDeepLinks
import com.stt.android.notifications.CHANNEL_ID_LOG_PERIOD_REMINDER
import com.stt.android.notifications.CHANNEL_ID_UPCOMING_PERIOD
import com.stt.android.notifications.NotificationGroup
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_MENSTRUAL_CYCLE_NOTIFY_UPCOMING_PERIOD
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_MENSTRUAL_CYCLE_NOTIFY_UPCOMING_PERIOD_DEFAULT
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_MENSTRUAL_CYCLE_REMIND_LOG_PERIOD
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_MENSTRUAL_CYCLE_REMIND_LOG_PERIOD_DEFAULT
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS_DEFAULT
import dagger.hilt.android.AndroidEntryPoint
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import javax.inject.Inject

@AndroidEntryPoint
class AlarmNotificationReceiver : BroadcastReceiver() {

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    override fun onReceive(context: Context, intent: Intent?) {
        val notificationManager =
            context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        if (intent?.action == ALARM_ACTION_REMINDER_LATER) {
            notificationManager.cancel(ALARM_NOTIFICATION_ID)
            return
        }

        // If disabled show predictions, no reminder notifications are shown.
        if (!readShowPredictions()) {
            return
        }

        when {
            // Check the "Notify me of my upcoming period".
            intent?.action == ALARM_ACTION_PREDICT && readNotifyUpcomingPeriod() ->
                createPredictAlarmNotification(context, intent)

            // Check the "Remind me to log period".
            intent?.action == ALARM_ACTION_LOG && readRemindLogPeriod() ->
                createLogAlarmNotification(context)

            else -> null
        }?.let {
            // Don't appear at the same time, so use the same id.
            notificationManager.notify(ALARM_NOTIFICATION_ID, it)
        }
    }

    private fun buildMonthCalendarPendingIntent(context: Context): PendingIntent? {
        val stackBuilder = TaskStackBuilder.create(context)
        stackBuilder.addNextIntent(BaseProxyActivity.newStartIntentClearStack(context).apply {
            data = MenstrualCycleDeepLinks.MONTH_CALENDAR.toUri()
        })
        stackBuilder.addNextIntent(AppOpenAnalyticsActivity.newStartIntent(context, PUSH_MESSAGE, NEXT_CYCLE))
        return stackBuilder.getPendingIntent(0, PendingIntent.FLAG_IMMUTABLE)
    }

    private fun buildLogMenstrualCyclePendingIntent(context: Context): PendingIntent? {
        val stackBuilder = TaskStackBuilder.create(context)
        stackBuilder.addNextIntent(BaseProxyActivity.newStartIntentClearStack(context).apply {
            data = MenstrualCycleDeepLinks.LOG_MENSTRUAL_CYCLE.toUri()
        })
        stackBuilder.addNextIntent(AppOpenAnalyticsActivity.newStartIntent(context, PUSH_MESSAGE, LOG_PERIOD))
        return stackBuilder.getPendingIntent(0, PendingIntent.FLAG_IMMUTABLE)
    }

    private fun buildReminderLaterAction(context: Context) = NotificationCompat.Action.Builder(
        null,
        context.getString(R.string.alarm_menstrual_cycle_log_action_remind_later),
        PendingIntent.getBroadcast(
            context,
            0,
            Intent(context, AlarmNotificationReceiver::class.java).apply {
                action = ALARM_ACTION_REMINDER_LATER
            },
            PendingIntent.FLAG_IMMUTABLE
        )
    ).build()

    private fun createPredictAlarmNotification(context: Context, intent: Intent): Notification? {
        val menstrualCycleStartDate =
            intent.getStringExtra(EXTRA_MENSTRUAL_CYCLE_START_DATE)?.let { LocalDate.parse(it) }
        val today = LocalDate.now()
        if (menstrualCycleStartDate == null || !menstrualCycleStartDate.isAfter(today)) return null

        return alarmNotificationBuilder(
            context,
            CHANNEL_ID_UPCOMING_PERIOD,
            context.getString(R.string.alarm_menstrual_cycle_predict_title),
            context.getString(
                R.string.alarm_menstrual_cycle_predict_content,
                ChronoUnit.DAYS.between(today, menstrualCycleStartDate)
            )
        )
            .setContentIntent(buildMonthCalendarPendingIntent(context))
            .setGroup(NotificationGroup.GROUP_ID_UPCOMING_PERIOD.groupName)
            .build()
            .apply { flags = flags or Notification.FLAG_LOCAL_ONLY }
    }

    private fun createLogAlarmNotification(context: Context) = alarmNotificationBuilder(
        context,
        CHANNEL_ID_LOG_PERIOD_REMINDER,
        context.getString(R.string.alarm_menstrual_cycle_log_title),
        context.getString(R.string.alarm_menstrual_cycle_log_content)
    )
        .setContentIntent(buildLogMenstrualCyclePendingIntent(context))
        .addAction(buildReminderLaterAction(context))
        .setGroup(NotificationGroup.GROUP_ID_LOG_PERIOD_REMINDER.groupName)
        .build()
        .apply { flags = flags or Notification.FLAG_LOCAL_ONLY }

    private fun alarmNotificationBuilder(context: Context, channel: String, title: String, content: String) =
        NotificationCompat.Builder(context, channel)
            .setSmallIcon(R.drawable.icon_notification)
            .setWhen(System.currentTimeMillis())
            .setContentTitle(title)
            .setContentText(content)
            .setPriority(NotificationCompat.PRIORITY_MAX)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .setAutoCancel(true)

    private fun readShowPredictions() = sharedPreferences.getBoolean(
        KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS,
        KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS_DEFAULT
    )

    private fun readNotifyUpcomingPeriod() = sharedPreferences.getBoolean(
        KEY_MENSTRUAL_CYCLE_NOTIFY_UPCOMING_PERIOD,
        KEY_MENSTRUAL_CYCLE_NOTIFY_UPCOMING_PERIOD_DEFAULT
    )

    private fun readRemindLogPeriod() = sharedPreferences.getBoolean(
        KEY_MENSTRUAL_CYCLE_REMIND_LOG_PERIOD,
        KEY_MENSTRUAL_CYCLE_REMIND_LOG_PERIOD_DEFAULT
    )

    companion object {
        private val ALARM_NOTIFICATION_ID = R.id.menstrual_cycle_alarm_notification

        const val ALARM_ACTION_PREDICT = "com.stt.android.ALARM_ACTION_PREDICT"
        const val ALARM_ACTION_LOG = "com.stt.android.ALARM_ACTION_LOG"
        private const val ALARM_ACTION_REMINDER_LATER =
            "com.stt.android.ALARM_ACTION_REMINDER_LATER"

        const val EXTRA_MENSTRUAL_CYCLE_START_DATE =
            "com.stt.android.EXTRA_MENSTRUAL_CYCLE_START_DATE"
    }
}
