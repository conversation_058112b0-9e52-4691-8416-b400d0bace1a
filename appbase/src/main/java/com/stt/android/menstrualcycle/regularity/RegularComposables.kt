package com.stt.android.menstrualcycle.regularity

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.R
import com.stt.android.compose.markdown.MarkdownDocument
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bottomSheetShape
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.rememberViewInteropNestedScrollConnection
import com.stt.android.compose.widgets.DraggableBottomSheetHandle
import com.stt.android.compose.widgets.PrimaryButton
import org.commonmark.parser.Parser

@Composable
fun RegularSheetContent(
    onDismissClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current
    val content = context.getString(R.string.menstrual_cycle_become_regular_document)
    val markdownDocument = remember(content) {
        Parser.Builder().build().parse(content)
    }

    Column(
        modifier = modifier
            .nestedScroll(rememberViewInteropNestedScrollConnection())
            .fillMaxSize()
            .clip(MaterialTheme.shapes.bottomSheetShape)
            .background(MaterialTheme.colors.surface)
    ) {
        DraggableBottomSheetHandle()

        Column(
            modifier = Modifier
                .verticalScroll(rememberScrollState())
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.spacing.smaller)
        ) {
            MarkdownDocument(
                rootNode = markdownDocument
            )

            PrimaryButton(
                text = stringResource(R.string.ok),
                onClick = onDismissClick,
                modifier = Modifier
                    .align(Alignment.CenterHorizontally)
                    .padding(vertical = MaterialTheme.spacing.xlarge)
            )
        }
    }
}

@Preview
@Composable
private fun RegularSheetContentPreview() {
    AppTheme {
        RegularSheetContent(
            onDismissClick = {}
        )
    }
}
