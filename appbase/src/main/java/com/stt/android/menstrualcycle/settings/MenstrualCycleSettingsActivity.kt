package com.stt.android.menstrualcycle.settings

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.stt.android.R
import com.stt.android.compose.component.SuuntoTopBar
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.util.setContentWithTheme
import com.stt.android.menstrualcycle.MenstrualCycleOnboardingNavigator
import com.stt.android.menstrualcycle.OnboardingDoneReason
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class MenstrualCycleSettingsActivity : AppCompatActivity() {

    private val viewModel: MenstrualCycleSettingsViewModel by viewModels()

    @Inject
    lateinit var onboardingNavigator: MenstrualCycleOnboardingNavigator

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContentWithTheme {
            Scaffold(
                topBar = {
                    MenstrualCycleSettingsTopAppBar(
                        onBackClick = ::finish
                    )
                }
            ) { paddingValues ->
                viewModel.menstrualCycleSettings?.let { menstrualCycleSettings ->
                    SettingsDetailsWithSheet(
                        menstrualCycleSettings = menstrualCycleSettings,
                        onCycleLengthChanged = { viewModel.onCycleLengthChanged(it) },
                        onPeriodDurationChanged = { viewModel.onPeriodDurationChanged(it) },
                        onTrackingStopped = { viewModel.onTrackingStopped() },
                        showPredictions = viewModel.showPredictions,
                        onShowPredictionsChanged = { viewModel.onShowPredictionsChanged(it) },
                        notifyUpcomingPeriod = viewModel.notifyUpcomingPeriod,
                        onNotifyUpcomingPeriodChanged = { viewModel.onNotifyUpcomingPeriodChanged(it) },
                        remindLogPeriod = viewModel.remindLogPeriod,
                        onRemindLogPeriodChanged = { viewModel.onRemindLogPeriodChanged(it) },
                        averageCycleLength = viewModel.averageCycleLength.takeIf { it > 0 },
                        averagePeriodLength = viewModel.averagePeriodLength.takeIf { it > 0 },
                        modifier = Modifier
                            .padding(paddingValues)
                    )
                } ?: run {
                    NoSettings(
                        onSetUpClick = {
                            startActivity(
                                onboardingNavigator.newOnboardingActivityIntent(
                                    this,
                                    OnboardingDoneReason.SETTINGS
                                )
                            )
                        },
                        modifier = Modifier
                            .narrowContent()
                            .padding(paddingValues)
                    )
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        viewModel.fetchRealCycleLengthAndPeriodLength()
    }

    companion object {

        fun newStartIntent(context: Context) =
            Intent(context, MenstrualCycleSettingsActivity::class.java)
    }
}

@Composable
private fun MenstrualCycleSettingsTopAppBar(
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    SuuntoTopBar(
        title = stringResource(id = R.string.settings_menstrual_cycle),
        onNavigationClick = onBackClick,
        modifier = modifier,
    )
}
