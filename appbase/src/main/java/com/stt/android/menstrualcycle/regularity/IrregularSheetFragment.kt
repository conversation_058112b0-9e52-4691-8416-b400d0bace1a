package com.stt.android.menstrualcycle.regularity

import android.content.SharedPreferences
import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.core.content.edit
import com.stt.android.compose.util.setContentWithTheme
import com.stt.android.menstrualcycle.settings.MenstrualCycleSettingsActivity
import com.stt.android.ui.utils.SmartBottomSheetDialogFragment
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS_DEFAULT
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class IrregularSheetFragment : SmartBottomSheetDialogFragment() {

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ) = ComposeView(requireContext()).apply {
        setViewCompositionStrategy(
            ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
        )

        setContentWithTheme {
            IrregularSheetContent(
                onSettingsClick = {
                    // Since the activity cannot be opened through action on the HarmonyOS 4, start it directly through startActivity.
                    requireContext().startActivity(
                        MenstrualCycleSettingsActivity.newStartIntent(requireContext())
                    )
                },
                onKeepPredictionsComingClick = { onShowPredictionsChanged(true) },
                onTurnPredictionsOffClick = { onShowPredictionsChanged(false) },
            )
        }
    }

    private fun onShowPredictionsChanged(showPredictions: Boolean) {
        val oldValue = sharedPreferences.getBoolean(
            KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS,
            KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS_DEFAULT
        )
        if (oldValue != showPredictions) {
            sharedPreferences.edit {
                putBoolean(KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS, showPredictions)
            }
        }
        dismiss()
    }
}
