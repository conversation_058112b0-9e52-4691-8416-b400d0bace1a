package com.stt.android.menstrualcycle.settings

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Divider
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.MaterialTheme
import androidx.compose.material.ModalBottomSheetValue
import androidx.compose.material.Switch
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.material.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.pluralStringResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.layout.CenteringModalBottomSheetLayout
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.theme.switchColors
import com.stt.android.compose.widgets.BottomSheetHandle
import com.stt.android.data.usersettings.MenstrualCycleSettings
import com.stt.android.domain.user.MenstrualCycleRegularity
import kotlinx.coroutines.launch
import java.util.Locale
import com.stt.android.core.R as CR

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun SettingsDetailsWithSheet(
    menstrualCycleSettings: MenstrualCycleSettings,
    onCycleLengthChanged: (Int) -> Unit,
    onPeriodDurationChanged: (Int) -> Unit,
    onTrackingStopped: () -> Unit,
    showPredictions: Boolean,
    onShowPredictionsChanged: (Boolean) -> Unit,
    notifyUpcomingPeriod: Boolean,
    onNotifyUpcomingPeriodChanged: (Boolean) -> Unit,
    remindLogPeriod: Boolean,
    onRemindLogPeriodChanged: (Boolean) -> Unit,
    averageCycleLength: Int?,
    averagePeriodLength: Int?,
    modifier: Modifier = Modifier
) {
    val settingsEditSheetState =
        rememberModalBottomSheetState(ModalBottomSheetValue.Hidden, skipHalfExpanded = true)
    val coroutineScope = rememberCoroutineScope()
    var settingsEditType by rememberSaveable { mutableStateOf(SettingsEditType.UNKNOWN) }

    BackHandler(
        enabled = settingsEditSheetState.isVisible,
        onBack = { coroutineScope.launch { settingsEditSheetState.hide() } }
    )

    fun showEditSheet() {
        coroutineScope.launch { settingsEditSheetState.show() }
    }

    CenteringModalBottomSheetLayout(
        sheetState = settingsEditSheetState,
        sheetContent = {
            BottomSheetHandle()

            SettingsEditSheetContent(
                settingsEditSheetState = settingsEditSheetState,
                coroutineScope = coroutineScope,
                menstrualCycleSettings = menstrualCycleSettings,
                onCycleLengthChanged = onCycleLengthChanged,
                onPeriodDurationChanged = onPeriodDurationChanged,
                settingsEditType = settingsEditType,
                onTrackingStopped = onTrackingStopped,
                averageCycleLength = averageCycleLength,
                averagePeriodLength = averagePeriodLength
            )
        },
        modifier = modifier
    ) {
        SettingsDetail(
            menstrualCycleSettings = menstrualCycleSettings,
            onEditCycleLength = {
                settingsEditType = SettingsEditType.CYCLE_LENGTH
                showEditSheet()
            },
            onEditPeriodDuration = {
                settingsEditType = SettingsEditType.PERIOD_DURATION
                showEditSheet()
            },
            showPredictions = showPredictions,
            onShowPredictionsChanged = onShowPredictionsChanged,
            notifyUpcomingPeriod = notifyUpcomingPeriod,
            onNotifyUpcomingPeriodChanged = onNotifyUpcomingPeriodChanged,
            remindLogPeriod = remindLogPeriod,
            onRemindLogPeriodChanged = onRemindLogPeriodChanged,
            onTryStopTracking = {
                settingsEditType = SettingsEditType.STOP_TRACKING
                showEditSheet()
            },
            modifier = Modifier.narrowContent()
        )
    }
}

@Composable
private fun SettingsDetail(
    menstrualCycleSettings: MenstrualCycleSettings,
    onEditCycleLength: () -> Unit,
    onEditPeriodDuration: () -> Unit,
    showPredictions: Boolean,
    onShowPredictionsChanged: (Boolean) -> Unit,
    notifyUpcomingPeriod: Boolean,
    onNotifyUpcomingPeriodChanged: (Boolean) -> Unit,
    remindLogPeriod: Boolean,
    onRemindLogPeriodChanged: (Boolean) -> Unit,
    onTryStopTracking: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .verticalScroll(rememberScrollState())
            .fillMaxHeight()
            .background(MaterialTheme.colors.surface)
    ) {
        val cycleLengthText = menstrualCycleSettings.cycleLength?.let {
            pluralStringResource(id = CR.plurals.value_days, it, it)
        } ?: stringResource(id = CR.string.not_sure)
        SettingsItem(
            title = stringResource(id = CR.string.cycle_length),
            value = cycleLengthText,
            onClick = onEditCycleLength
        )
        Divider()

        SettingsItem(
            title = stringResource(id = CR.string.period_length),
            value = pluralStringResource(
                id = CR.plurals.value_days,
                menstrualCycleSettings.periodDuration,
                menstrualCycleSettings.periodDuration
            ),
            onClick = onEditPeriodDuration
        )
        Divider()

        SwitchItem(
            title = stringResource(id = R.string.settings_menstrual_cycle_show_predictions),
            checked = showPredictions,
            onClick = { onShowPredictionsChanged(!showPredictions) }
        )
        Divider()

        if (showPredictions) {
            SwitchItem(
                title = stringResource(id = R.string.settings_menstrual_cycle_notify_upcoming_period),
                checked = notifyUpcomingPeriod,
                onClick = { onNotifyUpcomingPeriodChanged(!notifyUpcomingPeriod) }
            )
            Divider()

            SwitchItem(
                title = stringResource(id = R.string.settings_menstrual_cycle_remind_log_period),
                checked = remindLogPeriod,
                onClick = { onRemindLogPeriodChanged(!remindLogPeriod) }
            )
            Divider()
        }

        Text(
            text = stringResource(id = R.string.settings_menstrual_cycle_stop_tracking_title),
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.padding(MaterialTheme.spacing.medium)
        )

        Text(
            text = stringResource(id = R.string.settings_menstrual_cycle_stop_tracking_description),
            style = MaterialTheme.typography.body,
            modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium)
        )

        TextButton(
            onClick = { onTryStopTracking() },
            modifier = Modifier
                .align(Alignment.CenterHorizontally)
                .padding(top = MaterialTheme.spacing.medium)
        ) {
            Text(
                text = stringResource(id = R.string.settings_menstrual_cycle_stop_tracking)
                    .uppercase(Locale.getDefault()),
                style = MaterialTheme.typography.bodyBold,
                color = MaterialTheme.colors.primary
            )
        }

        Spacer(
            modifier = Modifier
                .weight(1F)
                .background(MaterialTheme.colors.background)
        )
    }
}

@Composable
private fun SwitchItem(
    title: String,
    checked: Boolean,
    onClick: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .heightIn(56.dp)
            .fillMaxWidth()
            .clickable {
                onClick(!checked)
            }
            .padding(
                start = MaterialTheme.spacing.medium,
                end = MaterialTheme.spacing.smaller,
                top = MaterialTheme.spacing.medium,
                bottom = MaterialTheme.spacing.medium
            ),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.weight(1f)
        )

        Switch(
            checked = checked,
            onCheckedChange = null,
            colors = MaterialTheme.colors.switchColors
        )
    }
}

@Composable
private fun SettingsItem(
    title: String,
    value: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .heightIn(56.dp)
            .fillMaxWidth()
            .clickable(onClick = onClick)
            .padding(MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.weight(1f)
        )

        Text(
            text = value,
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colors.primary
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun SettingsDetailPreview() {
    AppTheme {
        SettingsDetail(
            menstrualCycleSettings = MenstrualCycleSettings(
                cycleRegularity = MenstrualCycleRegularity.REGULAR,
                cycleLength = null,
                periodDuration = 4
            ),
            onEditCycleLength = {},
            onEditPeriodDuration = {},
            showPredictions = true,
            onShowPredictionsChanged = {},
            notifyUpcomingPeriod = false,
            onNotifyUpcomingPeriodChanged = {},
            remindLogPeriod = true,
            onRemindLogPeriodChanged = {},
            onTryStopTracking = {}
        )
    }
}
