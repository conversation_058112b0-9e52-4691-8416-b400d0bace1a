package com.stt.android.menstrualcycle

import android.content.Context
import android.content.Intent

enum class OnboardingDoneReason(val analyticsName: String) {
    PRODUCT_ONBOARDING("NewProductOnboarding"),
    PUSH_NOTIFICATION("PushNotification"),
    POP_UP("PopUp"),
    FIRST_PERIOD_LOGGED("FirstPeriodLogged"),
    SKIP("Skip"),
    SETTINGS("Settings"),
}

interface MenstrualCycleOnboardingNavigator {

    fun newOnboardingActivityIntent(context: Context, reason: OnboardingDoneReason): Intent

    companion object {
        const val EXTRA_TRY_LOG_MENSTRUAL_CYCLE = "com.stt.android.menstrualcycle.TRY_LOG"
        const val EXTRA_ANALYTICS_DONE_REASON =
            "com.stt.android.menstrualcycle.ANALYTICS_DONE_REASON"
    }
}
