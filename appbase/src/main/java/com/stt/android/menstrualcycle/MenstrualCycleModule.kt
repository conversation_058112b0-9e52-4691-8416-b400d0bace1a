package com.stt.android.menstrualcycle

import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.backgroundwork.WorkerKey
import dagger.Binds
import dagger.Module
import dagger.multibindings.IntoMap

@Module
abstract class MenstrualCycleModule {

    @Binds
    @IntoMap
    @WorkerKey(MenstrualCyclePredictionJob::class)
    abstract fun bindPredictionFactory(factory: MenstrualCyclePredictionJob.Factory): CoroutineWorkerAssistedFactory

    @Binds
    @IntoMap
    @WorkerKey(MenstrualCycleRemoteSyncJob::class)
    abstract fun bindRemoteSyncFactory(factory: MenstrualCycleRemoteSyncJob.Factory): CoroutineWorkerAssistedFactory
}
