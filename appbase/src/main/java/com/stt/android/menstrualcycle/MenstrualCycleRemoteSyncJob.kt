package com.stt.android.menstrualcycle

import android.content.Context
import androidx.work.Constraints
import androidx.work.CoroutineWorker
import androidx.work.ExistingWorkPolicy
import androidx.work.ListenableWorker
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequest
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.menstrualcycle.domain.MenstrualCycle
import com.stt.android.menstrualcycle.domain.MenstrualCycleLocalDataSource
import com.stt.android.menstrualcycle.domain.MenstrualCycleRemoteDataSource
import com.stt.android.menstrualcycle.domain.MenstrualCycleType
import com.stt.android.menstrualcycle.domain.invalid
import com.stt.android.utils.BrandFlavourConstants
import com.stt.android.utils.takeIfNotEmpty
import dagger.Lazy
import java.time.LocalDate
import javax.inject.Inject

class MenstrualCycleRemoteSyncJob(
    context: Context,
    params: WorkerParameters,
    private val menstrualCycleRemoteDataSource: MenstrualCycleRemoteDataSource,
    private val menstrualCycleLocalDataSource: MenstrualCycleLocalDataSource,
    private val workManager: Lazy<WorkManager>,
    private val menstrualCycleAnalyticsUtils: MenstrualCycleAnalyticsUtils,
) : CoroutineWorker(context, params) {

    override suspend fun doWork(): Result {
        if (!BrandFlavourConstants.PROVIDE_MC_FEATURE) return Result.success()

        val today = LocalDate.now()

        // 1. Load data from the cloud side.
        val invalidRemoteMenstrualCycles = mutableListOf<MenstrualCycle>()
        val remoteMenstrualCycles = mutableListOf<MenstrualCycle>()
        // TODO: Get the data of today and before today, waiting for the backend to fix bug.
        menstrualCycleRemoteDataSource.fetchMenstrualCycles().forEach { remote ->
            if (remote.includedDates.isEmpty() ||
                remote.includedDates.all { it.isAfter(today) } ||
                remoteMenstrualCycles.any { it.hasSameDataWith(remote) }
            ) {
                invalidRemoteMenstrualCycles.add(remote)
            } else {
                remoteMenstrualCycles.add(remote)
            }
        }
        invalidRemoteMenstrualCycles.takeIfNotEmpty()?.let {
            menstrualCycleRemoteDataSource.deleteMenstrualCycle(it.mapNotNull { remote -> remote.remoteKey })
        }

        // 2. Save new data that does not exist db, or update local when remoteKey is the same
        val localMenstrualCycles =
            menstrualCycleLocalDataSource.fetchBeforeAt(today, MenstrualCycleType.HISTORICAL)
        val pendingInsertMenstrualCycles = mutableListOf<MenstrualCycle>()
        val pendingUpdateMenstrualCycles = mutableListOf<MenstrualCycle>()
        val remoteKeys = mutableSetOf<String>()
        val remoteModifiedTimes = mutableMapOf<String, Long?>()
        remoteMenstrualCycles.forEach { remote ->
            remote.remoteKey?.let {
                remoteKeys.add(it)
                remoteModifiedTimes.put(it, remote.modifiedTime)
            }
            localMenstrualCycles.firstOrNull { it.remoteKey == remote.remoteKey }?.let { local ->
                if (compareModifiedTime(remote.modifiedTime, local.modifiedTime)) {
                    pendingUpdateMenstrualCycles.add(remote.copy(localId = local.localId))
                }
            } ?: pendingInsertMenstrualCycles.add(remote)
        }
        if (pendingInsertMenstrualCycles.isNotEmpty()) {
            menstrualCycleLocalDataSource.insert(pendingInsertMenstrualCycles)
        }
        if (pendingUpdateMenstrualCycles.isNotEmpty()) {
            menstrualCycleLocalDataSource.update(pendingUpdateMenstrualCycles)
        }

        // 3. Sync historical cycle data from before today to cloud.
        val historicalMenstrualCycles =
            menstrualCycleLocalDataSource.fetchBeforeAt(today, MenstrualCycleType.HISTORICAL)

        // If includedDates is empty, or remoteKey is not empty but the server does not have the remoteKey, delete data.
        val deletedMenstrualCycle = historicalMenstrualCycles.filter {
            it.invalid() || (it.remoteKey != null && it.remoteKey !in remoteKeys)
        }
        if (deletedMenstrualCycle.isNotEmpty()) {
            deletedMenstrualCycle.mapNotNull { it.remoteKey }.intersect(remoteKeys).takeIfNotEmpty()
                ?.let { menstrualCycleRemoteDataSource.deleteMenstrualCycle(it.toList()) }
            menstrualCycleLocalDataSource.delete(deletedMenstrualCycle)
        }

        val allMenstrualCycles = historicalMenstrualCycles.subtract(deletedMenstrualCycle.toSet())
        // As long as there is a logged period, the user is considered to be using the feature.
        menstrualCycleAnalyticsUtils.trackIsUsingFeature(allMenstrualCycles.isNotEmpty())
        allMenstrualCycles.filter {
            // Synchronize new or modified data to the cloud.
            it.remoteKey == null ||
                compareModifiedTime(it.modifiedTime, remoteModifiedTimes[it.remoteKey])
        }.takeIfNotEmpty()?.toList()?.let {
            val savedMenstrualCycles = mutableListOf<MenstrualCycle>()
            menstrualCycleRemoteDataSource.saveMenstrualCycles(it).forEach { remote ->
                it.firstOrNull { local ->
                    remote.hasSameDataWith(local)
                }?.let { local ->
                    savedMenstrualCycles.add(local.copy(remoteKey = remote.remoteKey))
                }
            }
            if (savedMenstrualCycles.isNotEmpty()) {
                menstrualCycleLocalDataSource.update(savedMenstrualCycles)
            }
        }

        // After the synchronization is complete, the prediction period need to re-predicted due to historical data changes.
        MenstrualCyclePredictionJob.schedule(workManager.get())

        return Result.success()
    }

    private fun compareModifiedTime(
        firstModifiedTime: Long?,
        secondModifiedTime: Long?
    ): Boolean {
        return when {
            firstModifiedTime == null -> false
            secondModifiedTime == null -> true
            else -> firstModifiedTime > secondModifiedTime
        }
    }

    class Factory @Inject constructor(
        private val menstrualCycleRemoteDataSource: MenstrualCycleRemoteDataSource,
        private val menstrualCycleLocalDataSource: MenstrualCycleLocalDataSource,
        private val workManager: Lazy<WorkManager>,
        private val menstrualCycleAnalyticsUtils: MenstrualCycleAnalyticsUtils,
    ) : CoroutineWorkerAssistedFactory {
        override fun create(context: Context, params: WorkerParameters): ListenableWorker {
            return MenstrualCycleRemoteSyncJob(
                context = context,
                params = params,
                menstrualCycleRemoteDataSource = menstrualCycleRemoteDataSource,
                menstrualCycleLocalDataSource = menstrualCycleLocalDataSource,
                workManager = workManager,
                menstrualCycleAnalyticsUtils = menstrualCycleAnalyticsUtils,
            )
        }
    }

    companion object {
        private const val TAG = "MenstrualCycleRemoteSyncJob"

        fun schedule(workManager: WorkManager) {
            workManager.enqueueUniqueWork(
                TAG,
                ExistingWorkPolicy.REPLACE,
                OneTimeWorkRequest.Builder(MenstrualCycleRemoteSyncJob::class.java)
                    .setConstraints(
                        Constraints.Builder()
                            .setRequiredNetworkType(NetworkType.CONNECTED)
                            .build()
                    )
                    .build()
            )
        }
    }
}
