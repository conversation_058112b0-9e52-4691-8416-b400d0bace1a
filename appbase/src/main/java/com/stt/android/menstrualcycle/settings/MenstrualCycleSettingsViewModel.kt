package com.stt.android.menstrualcycle.settings

import android.content.SharedPreferences
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.core.content.edit
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.work.WorkManager
import com.stt.android.controllers.UserSettingsController
import com.stt.android.controllers.UserSettingsController.UpdateListener
import com.stt.android.menstrualcycle.MenstrualCyclePredictionJob
import com.stt.android.menstrualcycle.domain.FetchMenstrualCycleAverageLengthUseCase
import com.stt.android.menstrualcycle.domain.LengthConstants.CYCLE_LENGTH_DEFAULT_INDEX
import com.stt.android.menstrualcycle.domain.LengthConstants.CYCLE_LENGTH_SELECTABLE
import com.stt.android.menstrualcycle.domain.LengthConstants.PERIOD_DURATION_DEFAULT_INDEX
import com.stt.android.menstrualcycle.domain.LengthConstants.PERIOD_DURATION_SELECTABLE
import com.stt.android.menstrualcycle.domain.toCalc
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_MENSTRUAL_CYCLE_NOTIFY_UPCOMING_PERIOD
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_MENSTRUAL_CYCLE_NOTIFY_UPCOMING_PERIOD_DEFAULT
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_MENSTRUAL_CYCLE_REMIND_LOG_PERIOD
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_MENSTRUAL_CYCLE_REMIND_LOG_PERIOD_DEFAULT
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS_DEFAULT
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_MENSTRUAL_CYCLE_STOP_TRACKING_TIMESTAMP
import dagger.Lazy
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class MenstrualCycleSettingsViewModel @Inject constructor(
    private val sharedPreferences: SharedPreferences,
    private val userSettingsController: UserSettingsController,
    private val workManager: Lazy<WorkManager>,
    private val fetchMenstrualCycleAverageLengthUseCase: FetchMenstrualCycleAverageLengthUseCase,
) : ViewModel(), UpdateListener,
    SharedPreferences.OnSharedPreferenceChangeListener {

    var menstrualCycleSettings by mutableStateOf(userSettingsController.settings.getMenstrualCycleSetting())
        private set

    var showPredictions by mutableStateOf(readShowPredictions())
        private set

    var averageCycleLength by mutableIntStateOf(0)
        private set

    var averagePeriodLength by mutableIntStateOf(0)
        private set

    var notifyUpcomingPeriod by mutableStateOf(readNotifyUpcomingPeriod())
        private set

    var remindLogPeriod by mutableStateOf(readRemindLogPeriod())
        private set

    init {
        userSettingsController.addUpdateListener(this)
        sharedPreferences.registerOnSharedPreferenceChangeListener(this)
    }

    fun fetchRealCycleLengthAndPeriodLength() {
        viewModelScope.launch {
            fetchMenstrualCycleAverageLengthUseCase(
                FetchMenstrualCycleAverageLengthUseCase.Params(
                    menstrualCycleSettings?.cycleLength,
                    menstrualCycleSettings?.cycleRegularity?.toCalc()
                )
            ).also {
                averageCycleLength = it?.averageCycleLength ?: 0
                averagePeriodLength = it?.averagePeriodLength ?: 0
            }
        }
    }

    fun onCycleLengthChanged(cycleLength: Int) {
        userSettingsController.storeSettings(
            userSettingsController.settings.setMenstrualCycleSettings(
                menstrualCycleSettings?.copy(cycleLength = cycleLength)
            )
        )
    }

    fun onPeriodDurationChanged(periodDuration: Int) {
        userSettingsController.storeSettings(
            userSettingsController.settings.setMenstrualCycleSettings(
                menstrualCycleSettings?.copy(periodDuration = periodDuration)
            )
        )
    }

    fun onShowPredictionsChanged(showPredictions: Boolean) {
        sharedPreferences.edit {
            putBoolean(KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS, showPredictions)
        }
    }

    fun onNotifyUpcomingPeriodChanged(notifyUpcomingPeriod: Boolean) {
        sharedPreferences.edit {
            putBoolean(KEY_MENSTRUAL_CYCLE_NOTIFY_UPCOMING_PERIOD, notifyUpcomingPeriod)
        }
    }

    fun onRemindLogPeriodChanged(remindLogPeriod: Boolean) {
        sharedPreferences.edit {
            putBoolean(KEY_MENSTRUAL_CYCLE_REMIND_LOG_PERIOD, remindLogPeriod)
        }
    }

    fun onTrackingStopped() {
        userSettingsController.storeSettings(
            userSettingsController.settings.setMenstrualCycleSettings(null)
        )
        sharedPreferences.edit {
            putLong(KEY_MENSTRUAL_CYCLE_STOP_TRACKING_TIMESTAMP, System.currentTimeMillis())
            putBoolean(KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS, false)
            putBoolean(KEY_MENSTRUAL_CYCLE_NOTIFY_UPCOMING_PERIOD, false)
            putBoolean(KEY_MENSTRUAL_CYCLE_REMIND_LOG_PERIOD, false)
        }
    }

    override fun onSettingsStoredToPreferences(didLocalChanges: Boolean) {
        menstrualCycleSettings = userSettingsController.settings.menstrualCycleSetting
        // refresh cycle length and period length.
        fetchRealCycleLengthAndPeriodLength()
        MenstrualCyclePredictionJob.schedule(workManager.get())
    }

    override fun onSharedPreferenceChanged(sharedPreferences: SharedPreferences, key: String?) {
        if (key == KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS) {
            showPredictions = readShowPredictions()
        }
        if (key == KEY_MENSTRUAL_CYCLE_NOTIFY_UPCOMING_PERIOD) {
            notifyUpcomingPeriod = readNotifyUpcomingPeriod()
        }
        if (key == KEY_MENSTRUAL_CYCLE_REMIND_LOG_PERIOD) {
            remindLogPeriod = readRemindLogPeriod()
        }
    }

    private fun readShowPredictions() = sharedPreferences.getBoolean(
        KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS,
        KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS_DEFAULT
    )

    private fun readNotifyUpcomingPeriod() = sharedPreferences.getBoolean(
        KEY_MENSTRUAL_CYCLE_NOTIFY_UPCOMING_PERIOD,
        KEY_MENSTRUAL_CYCLE_NOTIFY_UPCOMING_PERIOD_DEFAULT
    )

    private fun readRemindLogPeriod() = sharedPreferences.getBoolean(
        KEY_MENSTRUAL_CYCLE_REMIND_LOG_PERIOD,
        KEY_MENSTRUAL_CYCLE_REMIND_LOG_PERIOD_DEFAULT
    )

    override fun onCleared() {
        userSettingsController.removeUpdateListener(this)
        sharedPreferences.unregisterOnSharedPreferenceChangeListener(this)
        super.onCleared()
    }

    companion object {

        fun getCycleLengthSelectedIndex(cycleLength: Int?): Int {
            val index = cycleLength?.let {
                CYCLE_LENGTH_SELECTABLE.indexOf(it)
            }
            return if (index == null || index == -1) CYCLE_LENGTH_DEFAULT_INDEX else index
        }

        fun getPeriodDurationSelectedIndex(periodDuration: Int): Int {
            val index = PERIOD_DURATION_SELECTABLE.indexOf(periodDuration)
            return if (index == -1) PERIOD_DURATION_DEFAULT_INDEX else index
        }
    }
}
