package com.stt.android.menstrualcycle

import android.content.SharedPreferences
import com.stt.android.menstrualcycle.domain.MenstrualCycle
import com.stt.android.utils.BrandFlavourConstants
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_MENSTRUAL_CYCLE_STOP_TRACKING_TIMESTAMP
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.temporal.ChronoUnit
import javax.inject.Inject

class ShowCycleDayHelper @Inject constructor(
    sharedPreferences: SharedPreferences,
) {

    private val stopMenstrualCycleTrackingDate =
        sharedPreferences.getLong(KEY_MENSTRUAL_CYCLE_STOP_TRACKING_TIMESTAMP, 0)
            .takeIf { it > 0 }?.let {
                LocalDate.ofInstant(Instant.ofEpochMilli(it), ZoneId.systemDefault())
            }

    private fun shouldShowCycleDay(menstrualCycles: List<MenstrualCycle>, date: LocalDate) = when {
        stopMenstrualCycleTrackingDate == null && menstrualCycles.isEmpty() -> false
        stopMenstrualCycleTrackingDate == null -> true
        menstrualCycles.isEmpty() -> false
        else -> date.isBefore(stopMenstrualCycleTrackingDate)
    }

    fun getFromMenstrualCycleDays(menstrualCycles: List<MenstrualCycle>, date: LocalDate) =
        if (BrandFlavourConstants.PROVIDE_MC_FEATURE && shouldShowCycleDay(menstrualCycles, date)) {
            menstrualCycles.sortedBy { it.startDate }.lastOrNull {
                !it.startDate.isAfter(date)
            }?.let { last ->
                (ChronoUnit.DAYS.between(last.startDate, date).toInt() + 1)
                    .takeIf { it > 0 }?.toString() ?: NO_DAYS_FOUND_FROM_START_DATE
            } ?: NO_DAYS_FOUND_FROM_START_DATE
        } else {
            null // The user is not using menstrual cycle feature.
        }

    companion object {
        private const val NO_DAYS_FOUND_FROM_START_DATE = "--"
    }
}
