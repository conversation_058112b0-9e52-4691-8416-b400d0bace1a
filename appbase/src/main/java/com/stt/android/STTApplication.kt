package com.stt.android

import android.app.Application
import android.app.job.JobScheduler
import android.content.Context
import android.content.pm.PackageManager
import android.net.ConnectivityManager
import android.os.Build
import android.os.StrictMode
import android.os.StrictMode.ThreadPolicy
import android.os.StrictMode.VmPolicy
import android.view.Gravity
import androidx.annotation.CallSuper
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.SnapHelper
import androidx.tracing.trace
import androidx.work.Configuration
import coil3.imageLoader
import com.airbnb.epoxy.Carousel
import com.airbnb.epoxy.Carousel.SnapHelperFactory
import com.github.rubensousa.gravitysnaphelper.GravitySnapHelper
import com.stt.android.coroutines.LoggingExceptionHandler
import com.stt.android.di.MainProcessEntryPoint
import com.stt.android.domain.sml.reader.SmlReader
import com.stt.android.maps.MapTypeHelper
import com.stt.android.network.interfaces.ANetworkProvider
import com.stt.android.services.FetchStaticConfigFilesWorker
import com.stt.android.ui.tasks.PrepareWorkoutUiTask
import com.stt.android.utils.FileTimberTree
import com.stt.android.utils.STTConstants
import com.stt.android.workouts.RecordWorkoutService
import com.stt.android.workouts.autosave.AutoSaveOngoingWorkoutController
import dagger.hilt.android.EntryPointAccessors
import io.reactivex.plugins.RxJavaPlugins
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import rx.plugins.RxJavaHooks
import timber.log.Timber

abstract class STTApplication : BaseApplication(), Configuration.Provider {

    // "improper" injection. This is meant to be initialised and used by main process only
    val injection: MainProcessEntryPoint by lazy {
        trace("MainProcess inject") {
            initAtProcessStart()
            EntryPointAccessors.fromApplication<MainProcessEntryPoint>(this).apply {
                appComponent = this
                Timber.d("Main process injected")

                // Do Ormlite migration early in the app startup so that the User table in Room
                // is ready to be used for checking if user is logged in or not.
                val ormliteSchemaVersion = databaseHelper().readableDatabase.version
                Timber.d("OrmLite schema version $ormliteSchemaVersion")
                onMainProcessInjected()
            }
        }
    }

    private val applicationScope = CoroutineScope(SupervisorJob() + Dispatchers.Main + LoggingExceptionHandler)

    override val workManagerConfiguration: Configuration
        get() = injection.workManagerConfig()

    @CallSuper
    override fun onCreateMainProcess() = trace("onCreateMainProcess") {
        if (STTConstants.DEBUG) {
            enableStrictMode()
        }
        setupRxJavaErrorHandler()

        // We need to load dynamic maps before initializing maps.
        MapTypeHelper.loadDynamicMapsFromCache(this)
        injection.suuntoMaps().initialize(this)

        injection.appInitializers().init(this)

        // Check JobScheduler state before any job is scheduled
        checkPendingJobCount()
        // upgradePreferencesIfNeeded must be called before any preference is set
        LegacyAppPreferencesManager.upgradePreferencesIfNeeded(this)
        setActiveSubscriptionsSupport()
        LegacyAppPreferencesManager.storeCurrentAppVersion(this)
        prepareConnectivityManager()
        prepareWorkoutUi()
        autoRecoverWorkoutIfNeeded()

        // Day mode by default
        AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
        FetchStaticConfigFilesWorker.schedule(injection.workManager())
        injection.userSettingsTrackerForAnalytics().listenToUserSettingsChanges()
        setupEpoxyCarouselSnapHelper()
    }

    @CallSuper
    override fun onCreateSecondaryProcess() {
        setupRxJavaErrorHandler()
    }

    abstract fun onMainProcessInjected()

    private fun setupRxJavaErrorHandler() {
        RxJavaPlugins.setErrorHandler { handleRxJava2Error(it) }
        RxJavaHooks.setOnError { handleRxJavaError(it) }
    }

    private fun autoRecoverWorkoutIfNeeded() {
        applicationScope.launch(IO) {
            try {
                if (AutoSaveOngoingWorkoutController.autoSaveDataTimestamp(this@STTApplication) != 0L) {
                    try {
                        // In case of auto recovery, very likely it happens when the app is in background, so we
                        // can only start the foreground service on pre-14 devices.
                        // https://developer.android.com/develop/background-work/services/foreground-services#background-start-restriction-exemptions
                        // If after auto-recovery, user opens the app and goes back to a workout recording
                        // screen, a non-auto recovery action will be sent and we will show the notification.
                        val startIntent = RecordWorkoutService.newAutoRecoverIntent(this@STTApplication)
                        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                            ContextCompat.startForegroundService(this@STTApplication, startIntent)
                        } else {
                            startService(startIntent)
                        }
                    } catch (e: IllegalStateException) {
                        Timber.w(e, "Could not start RecordWorkoutService for AutoRecover")
                    }
                }
            } catch (e: Exception) {
                Timber.w(e, "Could not get autoSaveDataTimestamp")
            }
        }
    }

    /**
     * Sets the right value for [ .STTConstants#SUPPORTS_ACTIVE_SUBSCRIPTIONS][com.stt.android.utils]
     * flag
     * according to the android version and the device density bucket.
     */
    private fun setActiveSubscriptionsSupport() {
        STTConstants.SUPPORTS_ACTIVE_SUBSCRIPTIONS =
            resources.getBoolean(R.bool.support_active_subscriptions)
        Timber.d(
            "Are active subscription features supported? %s",
            STTConstants.SUPPORTS_ACTIVE_SUBSCRIPTIONS
        )
    }

    private fun prepareConnectivityManager() {
        val context = applicationContext
        val cm = context.getSystemService(CONNECTIVITY_SERVICE) as ConnectivityManager
        ANetworkProvider.setConnectivityManager(cm)
    }

    private fun prepareWorkoutUi() {
        PrepareWorkoutUiTask(this).execute()
    }

    public override fun onTrimMemoryMainProcess(level: Int) {
        try {
            if (level >= TRIM_MEMORY_MODERATE) {
                clearMemoryCaches()
            }
        } catch (ignored: Exception) {
            // it's likely to fail just because it fails to create the folder, so ignore it
        }
    }

    public override fun onLowMemoryMainProcess() {
        clearMemoryCaches()
    }

    private fun clearMemoryCaches() {
        injection.workoutDataLoaderController().clearCache()
        injection.mapSnapshotter().get().clearCache()
        SmlReader.clearCache()
        imageLoader.memoryCache?.clear()
    }

    fun logToFile(application: Application, loggingActive: Boolean) {
        if (fileTimberTree != null) {
            Timber.uproot(fileTimberTree!!)
            fileTimberTree = null
        }
        if (loggingActive) {
            fileTimberTree = FileTimberTree(application).also {
                Timber.plant(it)
            }
            Timber.d("------------- START LOG (%d) -------------", System.currentTimeMillis())
        }
    }

    fun toggleEasterEgg() {
        applicationScope.launch {
            injection.easterEgg().easterEggToggle(this@STTApplication)
        }
    }

    private fun enableStrictMode() {
        StrictMode.setThreadPolicy(
            ThreadPolicy.Builder()
                .detectAll()
                .permitDiskReads()
                .permitDiskWrites()
                .permitCustomSlowCalls()
                .penaltyLog()
                .build()
        )

        // Set VmPolicy in the same way as detectAll() would do, except don't enable detecting
        // untagged sockets. Otherwise the logs will be flooded with useless UntaggedSocketViolation
        // warnings originating from OkHttp. See https://github.com/square/okhttp/issues/3537
        val vmPolicyBuilder = VmPolicy.Builder()
            .detectActivityLeaks()
            .detectFileUriExposure()
            .detectLeakedClosableObjects()
            .detectLeakedRegistrationObjects()
            .detectLeakedSqlLiteObjects()
            .apply {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    detectIncorrectContextUse()
                    detectUnsafeIntentLaunch()
                }
            }
            .penaltyLog()
        try {
            vmPolicyBuilder.detectContentUriWithoutPermission()
        } catch (ignore: PackageManager.NameNotFoundException) {
        }
        StrictMode.setVmPolicy(vmPolicyBuilder.build())
    }

    /**
     * Check Android Job Scheduler pending jobs. If pending job count exceeds the maximum allowed
     * amount then cancel workers from the WorkManager.
     *
     * This is done to prevent the app from crashing due to exceeding the maximum amount of jobs
     * allowed for Android JobScheduler.
     */
    private fun checkPendingJobCount() {
        val androidJobScheduler = getSystemService(JOB_SCHEDULER_SERVICE) as JobScheduler
        val pendingJobCount = androidJobScheduler.allPendingJobs.size
        if (pendingJobCount >= PENDING_JOB_LIMIT) {
            Timber.d(
                "Android JobScheduler pending job count %d exceeds the maximum %d, cancelling jobs",
                pendingJobCount,
                PENDING_JOB_LIMIT
            )
            injection.workManager().cancelAllWork()
        }
    }

    private fun setupEpoxyCarouselSnapHelper() {
        // Affects all Epoxy Carousels. The default snap helper is LinearSnapHelper and
        // the feel of it was not great with top routes carousel.
        Carousel.setDefaultGlobalSnapHelperFactory(
            object : SnapHelperFactory() {
                override fun buildSnapHelper(context: Context): SnapHelper {
                    val snapHelper = GravitySnapHelper(Gravity.CENTER)
                    snapHelper.maxFlingSizeFraction = 0.3f
                    return snapHelper
                }
            }
        )
    }

    companion object {
        /**
         * Maximum amount of pending jobs allowed
         */
        private const val PENDING_JOB_LIMIT = 50
        private var fileTimberTree: FileTimberTree? = null

        private var appComponent: MainProcessEntryPoint? = null

        @JvmStatic
        fun getComponent() = appComponent!!
    }
}
