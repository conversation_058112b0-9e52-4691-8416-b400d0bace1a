package com.stt.android.usersettings

import android.app.Application
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.core.content.ContextCompat
import androidx.core.content.IntentCompat
import androidx.work.WorkManager
import com.stt.android.analytics.AnalyticsUUIDUpdater
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.data.usersettings.UserSettingsRemoteSyncJob
import com.stt.android.di.initializer.AppInitializerPostTermsApproval
import com.stt.android.domain.user.UserProfile
import com.stt.android.utils.STTConstants
import dagger.Lazy
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import timber.log.Timber
import java.time.Duration
import javax.inject.Inject
import javax.inject.Singleton

/**
 * App initializer that triggers user settings sync with the server on cold app starts (only when logged in)
 * and whenever user settings changes occur.
 * It also makes sure analytics user Ids are correctly initialised if the user is logged in.
 */
@Singleton
class UserSettingsSyncInitializer
@Inject constructor(
    private val currentUserController: CurrentUserController,
    private val userSettingsController: UserSettingsController,
    private val analyticsUUIDUpdater: AnalyticsUUIDUpdater,
    private val workManger: Lazy<WorkManager>,
    private val context: Context,
    private val dispatchers: CoroutinesDispatchers
) : AppInitializerPostTermsApproval, UserSettingsController.UpdateListener {

    private val scope = CoroutineScope(dispatchers.io)
    private val userProfileFlow = MutableStateFlow<UserProfile?>(null)

    private val receiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            Timber.d("UserSettingsSyncInitializer onReceiver.")
            IntentCompat.getParcelableExtra(
                intent,
                STTConstants.ExtraKeys.USER_PROFILE,
                UserProfile::class.java
            )?.let { userProfile ->
                userProfileFlow.tryEmit(userProfile)
            }
        }
    }

    init {
        registerUserProfileChangedReceiver()
        startUserProfileChangedObserver()
    }

    private fun registerUserProfileChangedReceiver() {
        ContextCompat.registerReceiver(
            context,
            receiver,
            IntentFilter(STTConstants.BroadcastActions.USER_PROFILE_CHANGE),
            ContextCompat.RECEIVER_EXPORTED
        )
    }

    override fun init(app: Application) {
        if (currentUserController.isLoggedIn) {
            UserSettingsRemoteSyncJob.enqueue(workManger.get(), shouldForceSettingsFetch())
            val analyticsUUID = userSettingsController.settings.analyticsUUID
            analyticsUUIDUpdater.updateUUID(analyticsUUID)
        }
        userSettingsController.addUpdateListener(this)
    }

    private fun startUserProfileChangedObserver() {
        scope.launch {
            userProfileFlow.collectLatest { profile ->
                try {
                    profile?.let {
                        userSettingsController.settings.setUserProfile(it).apply {
                            userSettingsController.storeSettings(this)
                        }
                    }
                } catch (e: Exception) {
                    Timber.w(e, "Catch exception while listening to user setting sync broadcast action")
                }
            }
        }
    }

    /**
     * Forcing fetch of settings at process start if they are at least 1 day old
     */
    private fun shouldForceSettingsFetch(): Boolean {
        val timeDiff = System.currentTimeMillis() - userSettingsController.settingsLastSavedTimestamp
        return Duration.ofMillis(timeDiff).toDays() > 1
    }

    override fun onSettingsStoredToPreferences(didLocalChanges: Boolean) {
        if (didLocalChanges) {
            Timber.d("onSettingsStoredToPreferences(true) event invoked")
            UserSettingsRemoteSyncJob.enqueue(workManger.get(), true)
        }
    }
}
