package com.stt.android.usersettings

import android.content.Context
import androidx.annotation.VisibleForTesting
import com.stt.android.BuildConfig
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsUserProperty
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.UserSettingsController
import com.stt.android.data.usersettings.MenstrualCycleSettings
import com.stt.android.domain.user.DEFAULT_MEASUREMENT_UNIT
import com.stt.android.domain.user.DomainMenstrualCycleSettings
import com.stt.android.domain.user.DomainUserSettings
import com.stt.android.domain.user.DomainUserTagAutomationSettings
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.user.MenstrualCycleRegularity
import com.stt.android.domain.user.ScreenBacklightSetting
import com.stt.android.domain.user.Sex
import com.stt.android.domain.user.UserSettings.DEFAULT_USER_SETTINGS_SHARE_PRIVACY
import com.stt.android.domain.user.UserSettingsDataSource
import com.stt.android.domain.user.mergeLocal
import com.stt.android.maps.MAP_TYPE_DEFAULT
import kotlinx.coroutines.withContext
import java.time.DayOfWeek
import javax.inject.Inject
import com.stt.android.domain.user.UserSettings as OldDomainUserSettings

class UserSettingsSharedPrefsDataSource
@Inject constructor(
    private val userSettingsController: UserSettingsController,
    private val context: Context,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    private val dispatchers: CoroutinesDispatchers
) : UserSettingsDataSource {

    override fun getPushApp(): String = BuildConfig.PUSH_APP

    override fun isLocallyChanged(): Boolean {
        return userSettingsController.settings.isLocallyChanged
    }

    override fun getUserSettings(): DomainUserSettings {
        return userSettingsController.settings.toDataSourceEntity()
    }

    override suspend fun saveUserSettings(remoteSettings: DomainUserSettings) {
        withContext(dispatchers.io) {
            val oldUserSettings = userSettingsController.settings
            val defaultCountry = OldDomainUserSettings.getDefaultCountry(context)
            val newUserSettings = remoteSettings.toOldDomainEntity(oldUserSettings, defaultCountry)
            userSettingsController.storeSettings(newUserSettings)
            sendSamplingBucketValueAnalyticsProperty(newUserSettings.samplingBucketValue)
        }
    }

    private fun sendSamplingBucketValueAnalyticsProperty(samplingBucketValue: Double) {
        amplitudeAnalyticsTracker.trackUserProperty(
            AnalyticsUserProperty.SAMPLING_BUCKET_VALUE,
            samplingBucketValue
        )
    }
}

fun OldDomainUserSettings.toDataSourceEntity(): DomainUserSettings {
    return DomainUserSettings(
        measurementUnit = this.measurementUnit.name,
        hrMaximum = this.hrMaximum,
        gender = this.gender.name,
        height = this.height,
        weight = this.weight,
        birthDate = this.birthDate,
        email = this.email?.takeIf { it.isNotBlank() },
        phoneNumber = this.phoneNumber?.takeIf { it.isNotBlank() },
        screenBacklightSetting = this.screenBacklight.name,
        gpsFiltering = this.isGpsFiltering,
        altitudeOffset = this.altitudeOffset,
        selectedMapType = this.selectedMapType.name,
        notifyNewFollower = this.notificationSettings.newFollowerEmailEnabled,
        notifyWorkoutComment = this.notificationSettings.workoutCommentEmailEnabled,
        notifyWorkoutFollowingShare = this.notificationSettings.workoutShareEmailEnabled,
        autoApproveFollowers = this.notificationSettings.autoApproveFollowersEnabled,
        emailDigest = this.notificationSettings.digestEmailEnabled,
        optinAccepted = this.optinAccepted,
        optinRejected = this.optinRejected,
        optinLastShown = this.optinLastShown,
        optinShowCount = this.optinShowCount,
        analyticsUUID = this.analyticsUUID,
        country = this.country,
        countrySubdivision = this.countrySubdivision,
        language = this.language,
        realName = this.realName,
        description = this.description,
        sharingFlagPreference = this.sharingFlagPreference,
        workoutShareNotificationEnabled = this.notificationSettings.workoutSharePushEnabled,
        workoutReactionNotificationEnabled = this.notificationSettings.workoutReactionPushEnabled,
        workoutCommentNotificationEnabled = this.notificationSettings.workoutCommentPushEnabled,
        newFollowerNotificationEnabled = this.notificationSettings.newFollowerPushEnabled,
        facebookFriendJoinNotificationEnabled = this.notificationSettings.facebookFriendJoinPushEnabled,
        hasOutboundPartnerConnections = this.hasOutboundPartnerConnections(),
        predefinedReplies = this.predefinedReplies.toList(),
        preferredTssCalculationMethods = this.preferredTssCalculationMethods.toMap(),
        firstDayOfTheWeek = this.firstDayOfTheWeek.value,
        tagAutomation = DomainUserTagAutomationSettings.fromMap(tagAutomation),
        favoriteSports = this.favoriteSports.toList(),
        motivations = this.motivations.toList(),
        disabledAppRatingSuggestions = this.disabledAppRatingSuggestions.toList(),
        automaticUpdateDisabledWatches = this.automaticUpdateDisabledWatches.toList(),
        samplingBucketValue = this.samplingBucketValue,
        privateAccount = this.notificationSettings.privateAccount,
        menstrualCycleSettings = (this.menstrualCycleSetting
            ?: invalidMenstrualCycleSettings()).toDomain(),
        hrRest = this.hrRest,
        combinedIntensityZones = this.combinedZones,
        lastAgeSaveTimestamp = this.lastAgeSaveTimestamp,
        lastGenderSaveTimestamp = this.lastGenderSaveTimestamp,
        lastWeightSaveTimestamp = this.lastWeightSaveTimestamp,
        lastHeightSaveTimestamp = this.lastHeightSaveTimestamp,
        lastMaxHRSaveTimestamp = this.lastMaxHRSaveTimestamp,
        lastRestHRSaveTimestamp = this.lastRestHRSaveTimestamp,
        newActivitySyncedNotificationEnabled = this.notificationSettings.newActivitySyncedLocalEnabled,
        showLocale = this.isShowLocale
    )
}

@VisibleForTesting
internal fun DomainUserSettings.toOldDomainEntity(
    oldSettings: OldDomainUserSettings,
    defaultCountry: String
): OldDomainUserSettings {
    val tempMapType = selectedMapType ?: MAP_TYPE_DEFAULT

    val notificationSettings = oldSettings.notificationSettings
        .toBuilder()
        .newFollowerEmailEnabled(notifyNewFollower)
        .workoutCommentEmailEnabled(notifyWorkoutComment)
        .workoutShareEmailEnabled(notifyWorkoutFollowingShare)
        .autoApproveFollowersEnabled(autoApproveFollowers)
        .digestEmailEnabled(emailDigest)
        .workoutCommentPushEnabled(this.workoutCommentNotificationEnabled)
        .workoutReactionPushEnabled(this.workoutReactionNotificationEnabled)
        .workoutSharePushEnabled(this.workoutShareNotificationEnabled)
        .facebookFriendJoinPushEnabled(this.facebookFriendJoinNotificationEnabled)
        .newFollowerPushEnabled(this.newFollowerNotificationEnabled)
        .newActivitySyncedLocalEnabled(this.newActivitySyncedNotificationEnabled)
        .setPrivateAccount(this.privateAccount)
        .build()

    val localCombinedZones = oldSettings.combinedZones

    return OldDomainUserSettings(
        country ?: defaultCountry,
        countrySubdivision ?: "",
        oldSettings.language, // backend value doesn't update app locale language
        measurementUnit?.let { MeasurementUnit.valueOf(it) } ?: DEFAULT_MEASUREMENT_UNIT,
        if (hrMaximum > 0) hrMaximum else OldDomainUserSettings.DEFAULT_MAX_HR,
        oldSettings.wheelCircumference,
        oldSettings.cadenceDataSource,
        gender?.let { Sex.valueOf(it) } ?: Sex.DEFAULT,
        height,
        weight,
        birthDate,
        analyticsUUID,
        email ?: "",
        phoneNumber ?: "",
        screenBacklightSetting?.let { ScreenBacklightSetting.valueOf(it) } ?: ScreenBacklightSetting.DEFAULT,
        oldSettings.isKeepAboveLock,
        gpsFiltering,
        altitudeOffset,
        tempMapType,
        false,
        oldSettings.altitudeSource,
        oldSettings.isAltitudeSourceSetByUser,
        notificationSettings,
        optinAccepted,
        optinRejected,
        optinLastShown,
        optinShowCount,
        realName,
        description,
        sharingFlagPreference ?: DEFAULT_USER_SETTINGS_SHARE_PRIVACY.toInt(),
        hasOutboundPartnerConnections,
        predefinedReplies.takeIf { it.isNotEmpty() }?.toTypedArray()
            ?: oldSettings.predefinedReplies,
        preferredTssCalculationMethods.takeIf { it.isNotEmpty() }?.toMap()
            ?: oldSettings.preferredTssCalculationMethods,
        firstDayOfTheWeek?.let { DayOfWeek.of(it) } ?: oldSettings.firstDayOfTheWeek,
        tagAutomation.takeIf { it != null }?.toMap() ?: oldSettings.tagAutomation,
        favoriteSports.takeIf { it.isNotEmpty() }?.toIntArray()
            ?: oldSettings.favoriteSports,
        motivations.takeIf { it.isNotEmpty() }?.toTypedArray()
            ?: oldSettings.motivations,
        disabledAppRatingSuggestions.takeIf { it.isNotEmpty() }?.toTypedArray()
            ?: oldSettings.disabledAppRatingSuggestions,
        automaticUpdateDisabledWatches.toTypedArray(),
        samplingBucketValue,
        menstrualCycleSettings?.cycleRegularity ?: MenstrualCycleRegularity.NOT_SURE,
        // If NOT SURE in onboarding, iOS set the cycle length is 0, we can delete the takeIf after iOS fixed.
        menstrualCycleSettings?.cycleLength?.takeIf { it != 0 } ?: OldDomainUserSettings.DEFAULT_MENSTRUAL_CYCLE_LENGTH.toInt(),
        menstrualCycleSettings?.periodDuration ?: OldDomainUserSettings.DEFAULT_MENSTRUAL_PERIOD_DURATION.toInt(),
        hrRest ?: 0,
        combinedIntensityZones?.hrZoneType?.mergeLocal(localCombinedZones.hrZoneType) ?: localCombinedZones.hrZoneType,
        combinedIntensityZones?.intensityZones?.mergeLocal(localCombinedZones.intensityZones) ?: localCombinedZones.intensityZones,
        oldSettings.lastAgeSaveTimestamp,
        oldSettings.lastGenderSaveTimestamp,
        oldSettings.lastWeightSaveTimestamp,
        oldSettings.lastHeightSaveTimestamp,
        oldSettings.lastMaxHRSaveTimestamp,
        oldSettings.lastRestHRSaveTimestamp,
        oldSettings.isShowLocale
    )
}

private fun MenstrualCycleSettings.toDomain() = DomainMenstrualCycleSettings(
    cycleRegularity = this.cycleRegularity,
    cycleLength = this.cycleLength ?: OldDomainUserSettings.DEFAULT_MENSTRUAL_CYCLE_LENGTH.toInt(),
    periodDuration = this.periodDuration
)

private fun invalidMenstrualCycleSettings() = MenstrualCycleSettings(
    MenstrualCycleRegularity.NOT_SURE,
    null,
    OldDomainUserSettings.DEFAULT_MENSTRUAL_PERIOD_DURATION.toInt()
)
