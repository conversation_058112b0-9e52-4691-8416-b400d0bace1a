package com.stt.android.usersettings;

import com.squareup.moshi.Moshi;
import com.stt.android.data.user.settings.DefaultQuestionnaireRepository;
import com.stt.android.domain.user.UserSettingsDataSource;
import com.stt.android.data.usersettings.UserSettingsRemoteSyncJobModule;
import com.stt.android.domain.user.settings.QuestionnaireRepository;
import com.stt.android.remote.AuthProvider;
import com.stt.android.remote.BaseUrl;
import com.stt.android.remote.SharedOkHttpClient;
import com.stt.android.remote.UserAgent;
import com.stt.android.remote.di.BrandOkHttpConfigFactory;
import com.stt.android.remote.di.RestApiFactory;
import com.stt.android.remote.usersettings.FcmTokenRegistrationRestApi;
import com.stt.android.remote.usersettings.UserNotificationsSettingsRestApi;
import com.stt.android.remote.usersettings.UserSettingsRestApi;
import dagger.Binds;
import dagger.Module;
import dagger.Provides;
import okhttp3.OkHttpClient;

@Module(includes = {
    UserSettingsRemoteSyncJobModule.class
})
public abstract class UserSettingsModule {
    @Binds
    public abstract UserSettingsDataSource bindUserSettingsDataSource(
        UserSettingsSharedPrefsDataSource userSettingsSharedPrefsDataSource);

    @Binds
    public abstract QuestionnaireRepository bindQuestionnaireRepository(
        DefaultQuestionnaireRepository repository
    );

    @Provides
    static UserSettingsRestApi provideUserSettingsRestApi(
        @SharedOkHttpClient OkHttpClient sharedClient,
        @BaseUrl String baseUrl,
        @UserAgent String userAgent,
        AuthProvider authProvider,
        Moshi moshi
    ) {
        return RestApiFactory.buildRestApi(
            sharedClient,
            baseUrl,
            UserSettingsRestApi.class,
            BrandOkHttpConfigFactory.getStOkHttpConfig(authProvider, userAgent),
            moshi);
    }

    @Provides
    static UserNotificationsSettingsRestApi provideUserNotificationsSettingsRestApi(
        @SharedOkHttpClient OkHttpClient sharedClient,
        @BaseUrl String baseUrl,
        @UserAgent String userAgent,
        AuthProvider authProvider,
        Moshi moshi) {
        return RestApiFactory.buildRestApi(
            sharedClient,
            baseUrl,
            UserNotificationsSettingsRestApi.class,
            BrandOkHttpConfigFactory.getStOkHttpConfig(authProvider, userAgent),
            moshi);
    }

    @Provides
    static FcmTokenRegistrationRestApi provideFcmTokenRegistrationRestApi(
        @SharedOkHttpClient OkHttpClient sharedClient,
        @BaseUrl String baseUrl,
        @UserAgent String userAgent,
        AuthProvider authProvider,
        Moshi moshi) {
        return RestApiFactory.buildRestApi(
            sharedClient,
            baseUrl,
            FcmTokenRegistrationRestApi.class,
            BrandOkHttpConfigFactory.getStOkHttpConfig(authProvider, userAgent),
            moshi);
    }
}
