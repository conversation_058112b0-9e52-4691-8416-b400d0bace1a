package com.stt.android.workoutcomparison

import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.exceptions.GhostMatchNotFoundException
import com.stt.android.exceptions.InitialGhostMatchNotFoundException

interface GhostTarget {

    @Throws(InitialGhostMatchNotFoundException::class)
    fun processNewLocation(currentWorkoutGeoPoint: WorkoutGeoPoint)

    fun getLastMatchedPosition(): WorkoutGeoPoint?

    @Throws(GhostMatchNotFoundException::class)
    fun calculateCurrentMatchTimeDifferenceInMilliseconds(referenceTimeInMilliseconds: Long): Long

    fun findByDuration(millisecondsInWorkout: Int): WorkoutGeoPoint
}
