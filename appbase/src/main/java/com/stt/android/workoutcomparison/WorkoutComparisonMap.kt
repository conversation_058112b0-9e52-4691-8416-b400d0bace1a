package com.stt.android.workoutcomparison

import android.content.res.Resources
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidViewBinding
import androidx.core.content.ContextCompat
import androidx.fragment.app.commit
import androidx.lifecycle.lifecycleScope
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.stt.android.R
import com.stt.android.databinding.WorkoutComparisonMapBinding
import com.stt.android.maps.MapType
import com.stt.android.maps.MarkerZPriority
import com.stt.android.maps.SuuntoBitmapDescriptor
import com.stt.android.maps.SuuntoBitmapDescriptorFactory
import com.stt.android.maps.SuuntoMap
import com.stt.android.maps.SuuntoMapOptions
import com.stt.android.maps.SuuntoMarker
import com.stt.android.maps.SuuntoMarkerOptions
import com.stt.android.maps.SuuntoSupportMapFragment
import com.stt.android.maps.newLatLngBounds
import com.stt.android.ui.map.RouteMarkerHelper
import kotlinx.coroutines.launch

@Composable
internal fun WorkoutComparisonMap(
    mapType: MapType,
    currentWorkoutRoute: List<LatLng>,
    otherWorkoutRoute: List<LatLng>,
    selectedComparison: WorkoutComparisonEntry,
    modifier: Modifier = Modifier,
) {
    AndroidViewBinding(
        modifier = modifier,
        factory = { inflater, parent, attachToParent ->
            WorkoutComparisonMapBinding.inflate(inflater, parent, attachToParent)
                .apply {
                    val mapFragment = WorkoutComparisonMapFragment.newInstance(
                        resources = root.context.resources,
                        mapType = mapType,
                    )
                    (root.context as? AppCompatActivity)?.supportFragmentManager
                        ?.commit(allowStateLoss = true) {
                            add(
                                fragmentContainerView,
                                mapFragment,
                                WorkoutComparisonMapFragment.FRAGMENT_TAG
                            )
                        }
                    mapFragment.initialize(
                        currentWorkoutRoute = currentWorkoutRoute,
                        otherWorkoutRoute = otherWorkoutRoute,
                        currentWorkoutLatLng = selectedComparison.currentWorkoutLatLng,
                        otherWorkoutLatLng = selectedComparison.otherWorkoutLatLng,
                    )
                }
        },
        update = {
            (root.context as? AppCompatActivity)
                ?.supportFragmentManager
                ?.findFragmentByTag(WorkoutComparisonMapFragment.FRAGMENT_TAG)
                ?.let { it as? WorkoutComparisonMapFragment }
                ?.update(
                    currentWorkoutLatLng = selectedComparison.currentWorkoutLatLng,
                    otherWorkoutLatLng = selectedComparison.otherWorkoutLatLng,
                )
        },
    )
}

class WorkoutComparisonMapFragment : SuuntoSupportMapFragment() {
    private var currentWorkoutMaker: SuuntoMarker? = null
    private var otherWorkoutMaker: SuuntoMarker? = null

    fun initialize(
        currentWorkoutRoute: List<LatLng>,
        otherWorkoutRoute: List<LatLng>,
        currentWorkoutLatLng: LatLng,
        otherWorkoutLatLng: LatLng,
    ) {
        lifecycleScope.launch {
            val bounds = LatLngBounds.Builder()
                .apply {
                    currentWorkoutRoute.forEach(::include)
                    otherWorkoutRoute.forEach(::include)
                }
                .build()

            val map = getMap()
            map.batchUpdate {
                val mapPadding = resources.getDimensionPixelSize(R.dimen.size_spacing_xxlarge)
                val cameraUpdate = newLatLngBounds(bounds, mapPadding)
                map.moveCamera(cameraUpdate)

                val context = context ?: return@batchUpdate
                RouteMarkerHelper.drawRouteWithColor(
                    context = context,
                    map = map,
                    latLngs = currentWorkoutRoute,
                    color = ContextCompat.getColor(context, R.color.map_route),
                )

                RouteMarkerHelper.drawRouteWithColor(
                    context = context,
                    map = map,
                    latLngs = otherWorkoutRoute,
                    color = ContextCompat.getColor(context, R.color.target_map_route),
                )

                currentWorkoutMaker = map.createMarker(
                    icon = SuuntoBitmapDescriptorFactory(requireContext()).forCurrentLocationDot(),
                    latLng = currentWorkoutLatLng,
                )

                otherWorkoutMaker = map.createMarker(
                    icon = SuuntoBitmapDescriptorFactory(requireContext()).forGhostLocationDot(),
                    latLng = otherWorkoutLatLng,
                )
            }
        }
    }

    fun update(
        currentWorkoutLatLng: LatLng,
        otherWorkoutLatLng: LatLng,
    ) {
        currentWorkoutMaker?.setPosition(currentWorkoutLatLng)
        otherWorkoutMaker?.setPosition(otherWorkoutLatLng)
    }

    private fun SuuntoMap.createMarker(
        icon: SuuntoBitmapDescriptor,
        latLng: LatLng
    ): SuuntoMarker? = addMarker(
        SuuntoMarkerOptions()
            .alpha(0.75F)
            .anchor(0.5F, 0.5F)
            .icon(icon)
            .position(latLng)
            .zPriority(MarkerZPriority.SELECTED_GEOPOINT)
    )

    companion object {
        const val FRAGMENT_TAG: String = "WorkoutComparisonMapFragment"

        fun newInstance(
            resources: Resources,
            mapType: MapType,
        ): WorkoutComparisonMapFragment {
            val options = SuuntoMapOptions()
                .mapType(mapType.name)
                .compassEnabled(false)
                .rotateGesturesEnabled(false)
                .scrollGesturesEnabled(false)
                .tiltGesturesEnabled(false)
                .zoomControlsEnabled(false)
                .zoomGesturesEnabled(false)
                .showMyLocationMarker(false)
                .attributionEnabled(resources.getBoolean(R.bool.maps_logo_enabled))
                .logoEnabled(resources.getBoolean(R.bool.maps_logo_enabled))
            return WorkoutComparisonMapFragment().apply {
                setOptions(options)
            }
        }
    }
}
