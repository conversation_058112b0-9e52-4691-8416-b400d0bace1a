package com.stt.android.workoutcomparison

import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import com.github.mikephil.charting.data.Entry
import com.google.android.gms.maps.model.LatLng
import com.stt.android.domain.workouts.ActivityGroup
import com.stt.android.maps.MapType
import com.suunto.algorithms.data.Length
import kotlin.time.Duration

data class WorkoutComparisonEntry(
    val distance: Length,
    val duration: Duration,
    val difference: Duration,
    val heartRate: Comparison,
    val speed: Comparison,
    val power: Comparison,
    val currentWorkoutLatLng: LatLng,
    val otherWorkoutLatLng: LatLng,
) {
    data class Comparison(
        val currentWorkoutValue: Double,
        val currentWorkoutAverageValue: Double,
        val otherWorkoutValue: Double,
        val otherWorkoutAverageValue: Double,
    )
}

data class WorkoutComparisonLine(
    @ColorRes val color: Int,
    val entries: List<Entry>,
) {
    // Entry doesn't implement hashCode() nor equals()
    override fun hashCode(): Int {
        var result = color.hashCode()
        entries.forEach { entry ->
            result = 31 * result + entry.x.hashCode()
            result = 31 * result + entry.y.hashCode()
        }
        return result
    }

    // Entry doesn't implement hashCode() nor equals()
    override fun equals(other: Any?): Boolean {
        if (other !is WorkoutComparisonLine) {
            return false
        }

        if (color != other.color) {
            return false
        }

        if (entries.size != other.entries.size) {
            return false
        }

        entries.forEachIndexed { index, entry ->
            if (!entry.equalTo(other.entries[index])) {
                return false
            }
        }

        return true
    }
}

data class WorkoutComparisonMap(
    val mapType: MapType,
    val currentWorkoutRoute: List<LatLng>,
    val otherWorkoutRoute: List<LatLng>,
)
sealed class WorkoutComparisonUiState {
    data object Loading : WorkoutComparisonUiState()

    data class Loaded(
        @DrawableRes val activityIcon: Int,
        val activityGroup: ActivityGroup,
        val activityTime: String,
        val activityDate: String,
        val otherActivityDate: String,
        val distanceUnit: String,
        val comparisonEntries: List<WorkoutComparisonEntry>,
        val comparisonLines: List<WorkoutComparisonLine>,
        val workoutComparisonMap: WorkoutComparisonMap?,
    ) : WorkoutComparisonUiState()

    data object Error : WorkoutComparisonUiState()
}
