package com.stt.android.workoutcomparison

import android.content.Context
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.github.mikephil.charting.data.Entry
import com.stt.android.R
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.UserSettingsController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.sml.FetchSmlUseCase
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.workout.WorkoutData
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.domain.workout.WorkoutHrEvent
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.exceptions.GhostMatchNotFoundException
import com.stt.android.home.diary.diarycalendar.activitygroups.ActivityTypeToGroupMapper
import com.stt.android.models.MapSelectionModel
import com.stt.android.ui.controllers.WorkoutDataLoaderController
import com.stt.android.ui.controllers.loadWorkout
import com.stt.android.ui.utils.TextFormatter
import com.stt.android.workouts.OngoingGhostTarget
import com.suunto.algorithms.data.Length.Companion.meters
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import kotlin.time.Duration.Companion.milliseconds

@HiltViewModel
open class WorkoutComparisonViewModel @Inject constructor(
    @ApplicationContext private val appContext: Context,
    private val savedStateHandle: SavedStateHandle,
    private val activityTypeToGroupMapper: ActivityTypeToGroupMapper,
    private val workoutDataLoaderController: WorkoutDataLoaderController,
    private val mapSelectionModel: MapSelectionModel,
    private val userSettingsController: UserSettingsController,
    private val fetchSmlUseCase: FetchSmlUseCase,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : ViewModel() {
    private val _uiState: MutableStateFlow<WorkoutComparisonUiState> = MutableStateFlow(WorkoutComparisonUiState.Loading)
    val uiState: StateFlow<WorkoutComparisonUiState> = _uiState.asStateFlow()
    protected open val loadOnInit: Boolean = true

    init {
        load()
    }

    fun load() {
        if (loadOnInit) {
            viewModelScope.launch {
                _uiState.value = WorkoutComparisonUiState.Loading
                _uiState.value = runSuspendCatching {
                    loadWorkoutComparison()
                }.getOrElse { e ->
                    Timber.w(e, "Failed to load workouts for comparison")
                    WorkoutComparisonUiState.Error
                }
            }
        }
    }

    protected suspend fun loadWorkoutComparison(): WorkoutComparisonUiState =
        withContext(coroutinesDispatchers.io) {
            val currentWorkoutHeader = getCurrentWorkoutHeader()
            val activityType = currentWorkoutHeader.activityType
            val otherWorkoutHeader = getOtherWorkoutHeader()
            val currentWorkoutDataAsync = async {
                workoutDataLoaderController.loadWorkout(getCurrentWorkoutHeader())
            }
            val otherWorkoutDataAsync = async {
                workoutDataLoaderController.loadWorkout(
                    getOtherWorkoutHeader()
                )
            }
            val currentWorkoutSmlAsync = async {
                fetchSmlUseCase.fetchSml(
                    getCurrentWorkoutHeader().id,
                    getCurrentWorkoutHeader().key,
                )
            }
            val otherWorkoutSml = fetchSmlUseCase.fetchSml(
                getOtherWorkoutHeader().id,
                getOtherWorkoutHeader().key,
            )
            val currentWorkoutData = currentWorkoutDataAsync.await()
            val otherWorkoutData = otherWorkoutDataAsync.await()
            val currentWorkoutSml = currentWorkoutSmlAsync.await()
            val ghostTarget = OngoingGhostTarget(getOtherWorkoutHeader(), otherWorkoutData)
            val (comparisonEntries, comparisonLines) = createComparisonEntriesAndLines(
                currentWorkoutData = currentWorkoutData,
                otherWorkoutData = otherWorkoutData,
                currentWorkoutSml = currentWorkoutSml,
                otherWorkoutSml = otherWorkoutSml,
                ghostTarget = ghostTarget
            )
            if (comparisonEntries.isEmpty() || comparisonLines.isEmpty()) {
                return@withContext WorkoutComparisonUiState.Error
            }

            WorkoutComparisonUiState.Loaded(
                activityIcon = activityType.iconId,
                activityGroup = activityTypeToGroupMapper
                    .activityTypeIdToGroup(activityType.id),
                activityTime = appContext.getString(
                    R.string.compare_workout_date_template,
                    TextFormatter.formatRelativeDateSpan(appContext.resources, currentWorkoutHeader.startTime),
                    TextFormatter.formatRelativeDateSpan(appContext.resources, otherWorkoutHeader.startTime),
                ),
                activityDate = TextFormatter.formatDate(appContext, currentWorkoutHeader.startTime, true),
                otherActivityDate = TextFormatter.formatDate(appContext, otherWorkoutHeader.startTime, true),
                distanceUnit = appContext.resources
                    .getString(userSettingsController.settings.measurementUnit.distanceUnit),
                comparisonEntries = comparisonEntries,
                comparisonLines = comparisonLines,
                workoutComparisonMap = WorkoutComparisonMap(
                    mapSelectionModel.selectedMapType,
                    currentWorkoutData.routePoints.map(WorkoutGeoPoint::getLatLng),
                    otherWorkoutData.routePoints.map(WorkoutGeoPoint::getLatLng)
                ),
            )
        }

    protected open fun getCurrentWorkoutHeader() = savedStateHandle.currentWorkoutHeader

    protected open fun getOtherWorkoutHeader(): WorkoutHeader {
        return savedStateHandle.otherWorkoutHeader
    }

    protected fun createComparisonEntriesAndLines(
        currentWorkoutData: WorkoutData,
        otherWorkoutData: WorkoutData,
        currentWorkoutSml: Sml?,
        otherWorkoutSml: Sml?,
        ghostTarget: GhostTarget
    ): Pair<List<WorkoutComparisonEntry>, List<WorkoutComparisonLine>> {
        val comparisonEntries = mutableListOf<WorkoutComparisonEntry>()
        val comparisonLines = mutableListOf<WorkoutComparisonLine>()
        var previousTimeDiffInMillis = 0.0
        val currentEntries = mutableListOf<Entry>()

        val currentWorkoutHrEvents = arrayListOf<WorkoutHrEvent>()
            .apply { currentWorkoutData.heartRateEvents?.let(::addAll) }
        var lastCurrentWorkoutHrEventIndex = 0
        var totalCurrentWorkoutHeartRate = 0
        val otherWorkoutHrEvents = arrayListOf<WorkoutHrEvent>()
            .apply { otherWorkoutData.heartRateEvents?.let(::addAll) }
        var lastOtherWorkoutHrEventIndex = 0
        var totalOtherWorkoutHeartRate = 0

        var currentWorkoutPowerCount = 0
        var currentWorkoutTotalPower = 0.0
        var otherWorkoutPowerCount = 0
        var otherWorkoutTotalPower = 0.0

        currentWorkoutData.routePoints.forEachIndexed { index, currentWorkoutGeoPoint ->
            try {
                ghostTarget.processNewLocation(currentWorkoutGeoPoint)
                val matchedGeoPoint = ghostTarget.getLastMatchedPosition() ?: return@forEachIndexed

                val rawTimeDiffInMillis = ghostTarget
                    .calculateCurrentMatchTimeDifferenceInMilliseconds(
                        currentWorkoutGeoPoint.millisecondsInWorkout.toLong()
                    )
                    .toDouble()
                val timeDiffInMillis = if (index == 0) {
                    rawTimeDiffInMillis
                } else {
                    (rawTimeDiffInMillis + previousTimeDiffInMillis) / 2.0
                }
                previousTimeDiffInMillis = timeDiffInMillis

                val entry = Entry(currentWorkoutGeoPoint.totalDistance.toFloat(), -timeDiffInMillis.toFloat())
                currentEntries.lastOrNull()
                    ?.let { lastEntry ->
                        if ((lastEntry.y >= 0.0F && entry.y < 0.0F) || (lastEntry.y < 0.0F && entry.y >= 0.0F)) {
                            comparisonLines.addEntriesIfNeeded(currentEntries)
                            currentEntries.clear()

                            // make sure the new segment starts from end of last segment
                            currentEntries.add(lastEntry)
                        }
                    }
                currentEntries.add(entry)

                while (lastCurrentWorkoutHrEventIndex < currentWorkoutHrEvents.lastIndex) {
                    val hrEvent = currentWorkoutHrEvents[lastCurrentWorkoutHrEventIndex]
                    if (hrEvent.millisecondsInWorkout > currentWorkoutGeoPoint.millisecondsInWorkout) {
                        break
                    }
                    totalCurrentWorkoutHeartRate += hrEvent.heartRate
                    lastCurrentWorkoutHrEventIndex++
                }

                while (lastOtherWorkoutHrEventIndex < otherWorkoutHrEvents.lastIndex) {
                    val hrEvent = otherWorkoutHrEvents[lastOtherWorkoutHrEventIndex]
                    if (hrEvent.millisecondsInWorkout > matchedGeoPoint.millisecondsInWorkout) {
                        break
                    }
                    totalOtherWorkoutHeartRate += hrEvent.heartRate
                    lastOtherWorkoutHrEventIndex++
                }

                val currentWorkoutPower = currentWorkoutSml?.findMatchedPower(currentWorkoutGeoPoint)
                val currentWorkoutAveragePower = currentWorkoutPower?.let {
                    currentWorkoutPowerCount++
                    currentWorkoutTotalPower += it
                    currentWorkoutTotalPower / currentWorkoutPowerCount
                }
                val otherWorkoutPower = otherWorkoutSml?.findMatchedPower(matchedGeoPoint)
                val otherWorkoutAveragePower = otherWorkoutPower?.let {
                    otherWorkoutPowerCount++
                    otherWorkoutTotalPower += it
                    otherWorkoutTotalPower / otherWorkoutPowerCount
                }

                val comparisonEntry = WorkoutComparisonEntry(
                    distance = currentWorkoutGeoPoint.totalDistance.meters,
                    duration = currentWorkoutGeoPoint.millisecondsInWorkout.milliseconds,
                    difference = timeDiffInMillis.milliseconds,
                    heartRate = WorkoutComparisonEntry.Comparison(
                        currentWorkoutValue = currentWorkoutHrEvents.getHeartRate(lastCurrentWorkoutHrEventIndex - 1),
                        currentWorkoutAverageValue = totalCurrentWorkoutHeartRate.safeDiv(lastCurrentWorkoutHrEventIndex),
                        otherWorkoutValue = otherWorkoutHrEvents.getHeartRate(lastOtherWorkoutHrEventIndex - 1),
                        otherWorkoutAverageValue = totalOtherWorkoutHeartRate.safeDiv(lastOtherWorkoutHrEventIndex),
                    ),
                    speed = WorkoutComparisonEntry.Comparison(
                        currentWorkoutValue = currentWorkoutGeoPoint.speedMetersPerSecond.toDouble(),
                        currentWorkoutAverageValue = currentWorkoutGeoPoint.avgSpeedMetersPerSecond,
                        otherWorkoutValue = matchedGeoPoint.speedMetersPerSecond.toDouble(),
                        otherWorkoutAverageValue = matchedGeoPoint.avgSpeedMetersPerSecond,
                    ),
                    power = WorkoutComparisonEntry.Comparison(
                        currentWorkoutValue = currentWorkoutPower ?: Double.NaN,
                        currentWorkoutAverageValue = currentWorkoutAveragePower ?: Double.NaN,
                        otherWorkoutValue = otherWorkoutPower ?: Double.NaN,
                        otherWorkoutAverageValue = otherWorkoutAveragePower ?: Double.NaN,
                    ),
                    currentWorkoutLatLng = currentWorkoutGeoPoint.latLng,
                    otherWorkoutLatLng = ghostTarget
                        .findByDuration(currentWorkoutGeoPoint.millisecondsInWorkout)
                        .latLng,
                )
                comparisonEntries.add(comparisonEntry)
            } catch (e: GhostMatchNotFoundException) {
                Timber.d(e, "Failed to find ghost match")
            }
        }

        // Make sure the remaining entries are added.
        comparisonLines.addEntriesIfNeeded(currentEntries)

        return comparisonEntries to comparisonLines
    }

    private companion object {
        val SavedStateHandle.currentWorkoutHeader: WorkoutHeader
            get() = requireNotNull(get(WorkoutComparisonActivity.CURRENT_WORKOUT_ARG))

        val SavedStateHandle.otherWorkoutHeader: WorkoutHeader
            get() = requireNotNull(get(WorkoutComparisonActivity.TARGET_WORKOUT_ARG))

        val WorkoutGeoPoint.avgSpeedMetersPerSecond: Double
            get() = if (millisecondsInWorkout > 0) {
                totalDistance / (millisecondsInWorkout / 1000.0)
            } else {
                0.0
            }

        fun Sml.findMatchedPower(workoutGeoPoint: WorkoutGeoPoint): Double? {
            val timestamp = workoutGeoPoint.timestamp
            return streamData.power
                .firstOrNull { point ->
                    point.timestamp >= timestamp
                }
                ?.value
                ?.toDouble()
        }

        fun List<WorkoutHrEvent>.getHeartRate(i: Int): Double =
            getOrNull(i)?.heartRate?.toDouble() ?: 0.0

        fun Int.safeDiv(divisor: Int): Double = if (divisor == 0) 0.0 else toDouble() / divisor

        fun MutableList<WorkoutComparisonLine>.addEntriesIfNeeded(entries: List<Entry>) {
            if (entries.isEmpty()) {
                return
            }

            val color = if (entries.last().y >= 0.0F) {
                R.color.ghost_target_ahead
            } else {
                R.color.ghost_target_behind_or_no_match
            }

            add(WorkoutComparisonLine(color, entries.toList()))
        }
    }
}
