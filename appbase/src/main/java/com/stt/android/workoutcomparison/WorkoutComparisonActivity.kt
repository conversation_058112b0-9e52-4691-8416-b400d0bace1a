package com.stt.android.workoutcomparison

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import androidx.appcompat.app.AppCompatActivity
import androidx.hilt.navigation.compose.hiltViewModel
import com.amersports.formatter.unit.jscience.JScienceUnitConverter
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.premium.PremiumRequiredToAccessHandler
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class WorkoutComparisonActivity : AppCompatActivity() {
    @Inject
    lateinit var premiumRequiredToAccessHandler: PremiumRequiredToAccessHandler

    @Inject
    lateinit var amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker

    @Inject
    lateinit var infoModelFormatter: InfoModelFormatter

    @Inject
    lateinit var unitConverter: JScienceUnitConverter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContentWithM3Theme {
            WorkoutComparisonScreen(
                onBackClicked = ::finish,
                infoModelFormatter = infoModelFormatter,
                unitConverter = unitConverter,
                viewModel = hiltViewModel(),
            )
        }

        intent.getStringExtra(SOURCE)
            ?.let { source ->
                amplitudeAnalyticsTracker.trackEvent(
                    AnalyticsEvent.WORKOUT_COMPARE_SCREEN,
                    AnalyticsProperties().put(AnalyticsEventProperty.SOURCE, source),
                )
            }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) {
            onBackPressedDispatcher.onBackPressed()
            return true
        }
        return super.onOptionsItemSelected(item)
    }

    companion object {
        const val CURRENT_WORKOUT_ARG = "CURRENT_WORKOUT_ARG"
        const val TARGET_WORKOUT_ARG = "TARGET_WORKOUT_ARG"
        private const val SOURCE = "ANALYTICS_SOURCE"

        fun newStartIntent(
            context: Context?,
            current: WorkoutHeader?,
            target: WorkoutHeader?,
            source: String?,
        ): Intent = Intent(context, WorkoutComparisonActivity::class.java)
            .putExtra(CURRENT_WORKOUT_ARG, current)
            .putExtra(TARGET_WORKOUT_ARG, target)
            .putExtra(SOURCE, source)
    }
}
