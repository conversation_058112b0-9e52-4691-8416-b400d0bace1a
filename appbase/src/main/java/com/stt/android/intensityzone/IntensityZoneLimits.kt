package com.stt.android.intensityzone

import com.soy.algorithms.intensity.IntensityZonesData
import com.stt.android.hr.HeartRateZone

data class IntensityZoneLimits(
     val zoneLimits: List<ZoneRange>
) {
    val asFloats: List<Float>
        get() = zoneLimits.map { it.start } + zoneLimits.last().end

    fun inWhichZone(value: Float): ZoneRange {
        return zoneLimits.firstOrNull { zoneRange ->
            zoneRange.inZone(value)
        } ?: zoneLimits.last()
    }

    companion object {
        fun createHrIntensityZoneLimits(
            heartRateMax: Int,
            hrZones: IntensityZonesData?
        ): IntensityZoneLimits {
            val limits = getHrLimits(heartRateMax, hrZones)
            require(limits.size == 6)

            return IntensityZoneLimits(
                listOf(
                    ZoneRange(IntensityZone.WARMUP, limits.first(), limits[1]),
                    ZoneRange(IntensityZone.ENDURANCE, limits[1], limits[2]),
                    ZoneRange(IntensityZone.AEROBIC, limits[2], limits[3]),
                    ZoneRange(IntensityZone.ANAEROBIC, limits[3], limits[4]),
                    ZoneRange(IntensityZone.PEAK, limits[4], limits[5])
                )
            )
        }

        private fun getHrLimits(heartRateMax: Int, hrZones: IntensityZonesData?): List<Float> =
            hrZones?.let { it ->
                val lowerLimits = listOf(
                    it.zone2LowerLimit,
                    it.zone3LowerLimit,
                    it.zone4LowerLimit,
                    it.zone5LowerLimit
                ).map { it * 60 } // Hz to bpm
                listOf(0f)
                    .plus(lowerLimits)
                    .plus(heartRateMax.toFloat())
            } ?: listOf(
                0,
                HeartRateZone.WARMUP.getHighBpm(heartRateMax),
                HeartRateZone.ENDURANCE.getHighBpm(heartRateMax),
                HeartRateZone.AEROBIC.getHighBpm(heartRateMax),
                HeartRateZone.ANAEROBIC.getHighBpm(heartRateMax),
                heartRateMax
            ).map { it.toFloat() }

        fun createAerobicZoneIntensityZoneLimits(): IntensityZoneLimits {
            return IntensityZoneLimits(
                listOf(
                    ZoneRange(IntensityZone.ENDURANCE, 1f, -0.19999f),
                    ZoneRange(IntensityZone.AEROBIC, -0.2f, -0.49999f),
                    ZoneRange(IntensityZone.PEAK, -0.5f, -1f)
                )
            )
        }
    }
}

data class ZoneRange(
    val intensityZone: IntensityZone,
    val start: Float,
    val end: Float
) {
    fun inZone(value: Float): Boolean {
        return value in start..<end
    }
}

data class ZoneRangeWithColor(
    val zoneRange: ZoneRange,
    val color: Int
)
