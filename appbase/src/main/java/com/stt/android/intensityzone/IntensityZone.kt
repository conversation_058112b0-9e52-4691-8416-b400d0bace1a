package com.stt.android.intensityzone

import android.content.Context
import androidx.annotation.ColorRes
import com.stt.android.core.R as CR

enum class IntensityZone(
    @ColorRes val color: Int
) {
    WARMUP(CR.color.heart_rate_1),
    ENDURANCE(CR.color.heart_rate_2),
    AEROBIC(CR.color.heart_rate_3),
    ANAEROBIC(CR.color.heart_rate_4),
    PEAK(CR.color.heart_rate_5);

    fun getColor(context: Context) = context.getColor(color)
}
