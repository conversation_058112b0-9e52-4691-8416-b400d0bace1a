package com.stt.android.newfeed

import android.content.res.Resources
import android.util.Size
import kotlin.math.min
import kotlin.math.roundToInt
import com.stt.android.core.R as CoreR

object FeedMapSizeCalculator {
    val EMPTY: Size = Size(0, 0)

    fun calculate(resources: Resources): Size {
        val displayMetrics = resources.displayMetrics
        val width = min(
            displayMetrics.widthPixels - 2 * resources.getDimensionPixelSize(CoreR.dimen.padding),
            resources.getDimensionPixelSize(CoreR.dimen.map_card_max_width),
        )
        val height = (width * 0.75).roundToInt()
        return Size(width, height)
    }
}
