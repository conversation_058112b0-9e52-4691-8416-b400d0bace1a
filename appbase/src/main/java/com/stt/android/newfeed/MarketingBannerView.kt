package com.stt.android.newfeed

import android.content.Context
import android.util.AttributeSet
import android.widget.FrameLayout
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.unit.dp
import com.airbnb.epoxy.AfterPropsSet
import com.airbnb.epoxy.ModelProp
import com.airbnb.epoxy.ModelView
import com.stt.android.home.dashboardv2.ui.banners.BannerPager

@ModelView(autoLayout = ModelView.Size.MATCH_WIDTH_WRAP_HEIGHT)
class MarketingBannerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
) : FrameLayout(context, attrs) {

    private val composeView = ComposeView(context).also {
        addView(it, LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT))
    }

    @set:[ModelProp]
    var data: MarketingBannerData? = null

    @AfterPropsSet
    fun bindProps() {
        data?.let {
            composeView.setContent {
                BannerPager(
                    modifier = Modifier.fillMaxWidth(),
                    paddingTop = 0.dp,
                    banners = it.banners,
                    onBannerClick = it.onClick,
                    onBannerClose = it.onClose,
                    onBannerExposure = it.onExposure,
                )
            }
        }
    }
}
