package com.stt.android.newfeed.binding

import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import com.stt.android.R
import com.stt.android.domain.user.ImageInformation
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.multimedia.sportie.SportieShareSource
import com.stt.android.utils.PermissionUtils
import com.stt.android.workouts.sharepreview.WorkoutSharePreviewActivity
import com.stt.android.workouts.sharepreview.customshare.WorkoutShareHelper
import pub.devrel.easypermissions.EasyPermissions

fun shareSportieClicked(
    context: Context,
    workoutHeader: WorkoutHeader?,
    imageInfo: ImageInformation?,
    watchName: String,
    workoutShareHelper: WorkoutShareHelper?
) {
    if (imageInfo == null || workoutHeader == null) {
        return
    }

    if (EasyPermissions.hasPermissions(context, *PermissionUtils.STORAGE_PERMISSIONS)) {
        if (workoutShareHelper?.showMultipleWorkoutShareWays() == true) {
            workoutShareHelper.toMultipleWorkoutShareWays(
                context,
                workoutHeader,
                imageInfo.indexInWorkoutHeader,
                watchName
            )
        } else {
            val (intent, options) = WorkoutSharePreviewActivity.newStartIntent(
                workoutHeader,
                context,
                imageInfo.indexInWorkoutHeader,
                SportieShareSource.FEED_SHARE_CAROUSEL
            )
            context.startActivity(intent, options.toBundle())
        }
    } else {
        context.activity?.let { activity ->
            PermissionUtils.requestPermissionsIfNeeded(
                activity,
                PermissionUtils.STORAGE_PERMISSIONS,
                context.resources.getString(R.string.storage_permission_rationale)
            )
        }
    }
}

private val Context.activity: Activity?
    get() = when (this) {
        is Activity -> this
        is ContextWrapper -> this.baseContext?.activity
        else -> null
    }
