package com.stt.android.newfeed

import android.content.res.Resources
import android.icu.text.MessageFormat
import com.stt.android.R
import com.stt.android.domain.achievements.AchievementProvider
import com.stt.android.domain.achievements.CumulativeAchievement
import com.stt.android.domain.achievements.PersonalBestAchievement
import java.util.Locale

object AchievementUtil {
    @JvmStatic
    fun createDisplayTextForPersonalBests(
        list: List<PersonalBestAchievement>,
        resources: Resources,
        activityName: String
    ): List<AchievementItem> {
        return list.mapNotNull {
            val title = when (it.timeCategory) {
                AchievementProvider.ALL_TIME -> {
                    createPersonalBestTextsForAllTime(resources, it, activityName)
                }
                AchievementProvider.YEAR -> {
                    createPersonalBestTextsForYear(resources, it, activityName)
                }
                AchievementProvider.MONTH -> {
                    createPersonalBestTextsForMonth(resources, it, activityName)
                }
                else -> null
            }

            if (title != null) {
                AchievementItem(
                    icon = R.drawable.achievement_trophy_icon,
                    title = title
                )
            } else {
                null
            }
        }
    }

    private fun createPersonalBestTextsForAllTime(
        resources: Resources,
        achievement: PersonalBestAchievement,
        activityName: String
    ): String? {
        return when (achievement.valueCategory) {
            AchievementProvider.FIRST_ACTIVITY_OF_TYPE -> {
                resources.getString(R.string.achievements_first_activity_of_any_type)
            }
            AchievementProvider.FASTEST_ACTIVITY -> {
                String.format(
                    Locale.getDefault(),
                    resources.getString(R.string.achievements_fastest_activity_of_type),
                    activityName
                )
            }
            AchievementProvider.FARTHEST_ACTIVITY -> {
                String.format(
                    Locale.getDefault(),
                    resources.getString(R.string.achievements_farthest_activity_of_type),
                    activityName
                )
            }
            else -> null
        }
    }

    private fun createPersonalBestTextsForYear(
        resources: Resources,
        achievement: PersonalBestAchievement,
        activityName: String
    ): String? {
        return when (achievement.valueCategory) {
            AchievementProvider.FASTEST_ACTIVITY -> {
                String.format(
                    Locale.getDefault(),
                    resources.getString(R.string.achievements_fastest_activity_of_type_year),
                    activityName
                )
            }
            AchievementProvider.FARTHEST_ACTIVITY -> {
                String.format(
                    Locale.getDefault(),
                    resources.getString(R.string.achievements_farthest_activity_of_type_year),
                    activityName
                )
            }
            else -> null
        }
    }

    private fun createPersonalBestTextsForMonth(
        resources: Resources,
        achievement: PersonalBestAchievement,
        activityName: String
    ): String? {
        return when (achievement.valueCategory) {
            AchievementProvider.FASTEST_ACTIVITY -> {
                String.format(
                    Locale.getDefault(),
                    resources.getString(R.string.achievements_fastest_activity_of_type_month),
                    activityName
                )
            }
            AchievementProvider.FARTHEST_ACTIVITY -> {
                String.format(
                    Locale.getDefault(),
                    resources.getString(R.string.achievements_farthest_activity_of_type_month),
                    activityName
                )
            }
            else -> null
        }
    }

    @JvmStatic
    fun createDisplayTextForCumulative(
        list: List<CumulativeAchievement>,
        resources: Resources,
        activityName: String
    ): List<AchievementItem> {
        return list.mapNotNull {
            val title = when (it.description) {
                AchievementProvider.ACTIVITY_TYPE_ONLY -> {
                    createCumulativeActivityTypeTexts(resources, it, activityName)
                }
                AchievementProvider.ACTIVITY_ONLY -> {
                    createCumulativeActivityTexts(resources, it)
                }
                AchievementProvider.FIRST_ACTIVITY_TYPE -> {
                    createFirstActivityTypeTexts(resources, it, activityName)
                }
                AchievementProvider.EARLY_BIRD -> {
                    String.format(
                        Locale.getDefault(),
                        resources.getString(R.string.achievements_early_bird_activitytype),
                        activityName
                    )
                }
                else -> null
            }
            if (title != null) {
                AchievementItem(
                    icon = R.drawable.achievement_star_icon,
                    title = title
                )
            } else {
                null
            }
        }
    }

    private fun createCumulativeActivityTypeTexts(
        resources: Resources,
        achievement: CumulativeAchievement,
        activityName: String
    ): String? {
        return when (achievement.activityCounts.timeCategory) {
            AchievementProvider.YEAR -> {
                String.format(
                    Locale.getDefault(),
                    resources.getString(R.string.achievements_cumulative_activitytype_year),
                    getOrdinalValue(achievement.activityCounts.activityTypeCount),
                    activityName
                )
            }
            AchievementProvider.MONTH -> {
                String.format(
                    Locale.getDefault(),
                    resources.getString(R.string.achievements_cumulative_activitytype_month),
                    getOrdinalValue(achievement.activityCounts.activityTypeCount),
                    activityName
                )
            }
            AchievementProvider.WEEK -> {
                String.format(
                    Locale.getDefault(),
                    resources.getString(R.string.achievements_cumulative_activitytype_count_week),
                    getOrdinalValue(achievement.activityCounts.currentCount),
                    activityName,
                    achievement.activityCounts.currentCount - achievement.activityCounts.previousCount
                )
            }
            else -> null
        }
    }

    private fun createCumulativeActivityTexts(
        resources: Resources,
        achievement: CumulativeAchievement
    ): String? {
        return when (achievement.activityCounts.timeCategory) {
            AchievementProvider.ALL_TIME -> {
                String.format(
                    Locale.getDefault(),
                    resources.getString(R.string.achievements_cumulative_activity),
                    getOrdinalValue(achievement.activityCounts.activityCount)
                )
            }
            AchievementProvider.YEAR -> {
                String.format(
                    Locale.getDefault(),
                    resources.getString(R.string.achievements_cumulative_activity_year),
                    getOrdinalValue(achievement.activityCounts.activityCount)
                )
            }
            AchievementProvider.WEEK -> {
                String.format(
                    Locale.getDefault(),
                    resources.getString(R.string.achievements_cumulative_activity_count_week),
                    getOrdinalValue(achievement.activityCounts.currentCount),
                    achievement.activityCounts.currentCount - achievement.activityCounts.previousCount
                )
            }
            else -> null
        }
    }

    private fun createFirstActivityTypeTexts(
        resources: Resources,
        achievement: CumulativeAchievement,
        activityName: String
    ): String? {
        return when (achievement.activityCounts.timeCategory) {
            AchievementProvider.ALL_TIME -> {
                String.format(
                    Locale.getDefault(),
                    resources.getString(R.string.achievements_cumulative_first_activitytype_ever),
                    activityName
                )
            }
            AchievementProvider.YEAR -> {
                String.format(
                    Locale.getDefault(),
                    resources.getString(R.string.achievements_cumulative_first_activitytype_year),
                    activityName
                )
            }
            AchievementProvider.MONTH -> {
                val firstInMonths = achievement.activityCounts.firstInCount
                if (firstInMonths != null) {
                    String.format(
                        Locale.getDefault(),
                        resources.getQuantityString(
                            R.plurals.achievements_cumulative_first_in_months,
                            firstInMonths
                        ),
                        activityName,
                        firstInMonths
                    )
                } else {
                    String.format(
                        Locale.getDefault(),
                        resources.getString(R.string.achievements_cumulative_first_activitytype_month),
                        activityName
                    )
                }
            }
            AchievementProvider.WEEK -> {
                val firstInWeeks = achievement.activityCounts.firstInCount
                if (firstInWeeks != null) {
                    String.format(
                        Locale.getDefault(),
                        resources.getQuantityString(
                            R.plurals.achievements_cumulative_first_in_weeks,
                            firstInWeeks
                        ),
                        activityName,
                        firstInWeeks
                    )
                } else {
                    null
                }
            }
            else -> null
        }
    }

    private fun getOrdinalValue(workoutCount: Int): String {
        val formatter = MessageFormat("{0,ordinal}", Locale.getDefault())
        return formatter.format(arrayOf(workoutCount)) ?: workoutCount.toString()
    }
}
