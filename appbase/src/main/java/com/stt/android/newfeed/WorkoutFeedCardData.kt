package com.stt.android.newfeed

import com.stt.android.domain.workouts.WorkoutHeader

data class WorkoutFeedCardData(
    val cardInfo: WorkoutCardInfo,
    val onLikeClicked: OnLikeClicked, // make sure not to pass a lambda here (it breaks equals())
    val onShareClicked: OnShareClicked, // make sure not to pass a lambda here (it breaks equals())
    val onWorkoutClicked: OnWorkoutClicked,
    val onAddCommentClicked: OnWorkoutClicked,
    val onAddPhotoClicked: OnAddPhotoClicked,
    val onPlayButtonClicked: OnWorkoutCardPlayButtonClicked,
    val onAddDescriptionAndTagsClicked: OnAddDescriptionAndTagsClicked,
    val onTagClicked: OnTagClicked,
) : FeedCardData() {
    val id = workoutHeader.id.toLong()

    val workoutHeader: WorkoutHeader
        get() = cardInfo.workoutHeader
}

fun WorkoutCardInfo.toWorkoutFeedCardData(
    onLikeClicked: OnLikeClicked,
    onShareClicked: OnShareClicked,
    onWorkoutClicked: OnWorkoutClicked,
    onAddCommentClicked: OnWorkoutClicked,
    onAddPhotoClicked: OnAddPhotoClicked,
    onWorkoutCardPlayButtonClicked: OnWorkoutCardPlayButtonClicked,
    onAddDescriptionAndTagsClicked: OnAddDescriptionAndTagsClicked,
    onTagClicked: OnTagClicked,
) = WorkoutFeedCardData(
    cardInfo = this,
    onLikeClicked = onLikeClicked,
    onShareClicked = onShareClicked,
    onWorkoutClicked = onWorkoutClicked,
    onAddCommentClicked = onAddCommentClicked,
    onAddPhotoClicked = onAddPhotoClicked,
    onPlayButtonClicked = onWorkoutCardPlayButtonClicked,
    onAddDescriptionAndTagsClicked = onAddDescriptionAndTagsClicked,
    onTagClicked = onTagClicked,
)
