package com.stt.android.newfeed

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import com.airbnb.epoxy.AfterPropsSet
import com.airbnb.epoxy.ModelProp
import com.airbnb.epoxy.ModelView
import com.stt.android.databinding.FeedTopBannerBinding
import com.stt.android.ui.utils.setOnClickListenerThrottled

@ModelView(autoLayout = ModelView.Size.MATCH_WIDTH_WRAP_HEIGHT)
class FeedTopBannerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {
    private val binding =
        FeedTopBannerBinding.inflate(LayoutInflater.from(context), this, true)

    @set:[ModelProp]
    var data: FeedTopBannerData? = null

    @AfterPropsSet
    fun bindProps() {
        val data = data
        if (data != null) {
            binding.feedTopBannerTitle.text = data.type.getTitle(context)
            binding.feedTopBannerBody.text = data.type.getBody(context)

            binding.root.setOnClickListenerThrottled {
                data.onClick(data.type)
            }

            binding.feedTopCloseButton.setOnClickListenerThrottled {
                data.onClose(data.type)
            }

            binding.root.isVisible = true
        } else {
            binding.root.isVisible = false
        }
    }
}
