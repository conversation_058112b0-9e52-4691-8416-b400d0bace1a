package com.stt.android.newfeed

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.annotation.AttrRes
import androidx.annotation.StyleRes
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Divider
import androidx.compose.material.FloatingActionButton
import androidx.compose.material.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.airbnb.epoxy.AfterPropsSet
import com.airbnb.epoxy.ModelProp
import com.airbnb.epoxy.ModelView
import com.stt.android.R
import com.stt.android.coil.placeholder
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.databinding.ViewholderFeedCardSportieBinding
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator
import com.stt.android.domain.workouts.extensions.SummaryExtension
import com.stt.android.multimedia.sportie.SportieImage
import com.stt.android.newfeed.binding.shareSportieClicked
import com.stt.android.utils.firstOfType
import com.stt.android.workouts.sharepreview.customshare.WorkoutShareHelper

@ModelView(autoLayout = ModelView.Size.MATCH_WIDTH_WRAP_HEIGHT)
class SportieCardView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    @AttrRes defStyleAttr: Int = 0,
    @StyleRes defStyleRes: Int = 0,
) : FrameLayout(context, attrs, defStyleAttr, defStyleRes) {
    @set:[ModelProp]
    lateinit var images: List<SportieImage>

    @set:[ModelProp(ModelProp.Option.DoNotHash)]
    lateinit var workoutShareHelper: WorkoutShareHelper

    @set:[ModelProp(ModelProp.Option.DoNotHash)]
    lateinit var workoutDetailsNavigator: WorkoutDetailsRewriteNavigator

    private val binding: ViewholderFeedCardSportieBinding = ViewholderFeedCardSportieBinding.inflate(
        LayoutInflater.from(context), this, true)

    @AfterPropsSet
    fun setup() {
        updateImages()
    }

    private fun updateImages() {
        // Do not show sportie cards until view has been laid out
        if (binding.composeView.width <= 0) {
            return
        }

        binding.composeView.setContentWithM3Theme {
            SportieCard(
                images = images,
                onClick = { sportieImage ->
                    val workoutHeader = sportieImage.workoutHeader
                    workoutDetailsNavigator.navigate(
                        context = context,
                        username = workoutHeader.username,
                        workoutId = workoutHeader.id,
                        workoutKey = workoutHeader.key,
                    )
                },
                onShareClick = { sportieImage ->
                    shareSportieClicked(
                        context = context,
                        workoutHeader = sportieImage.workoutHeader,
                        imageInfo = sportieImage.imageInformation,
                        watchName = sportieImage.workoutExtensions.firstOfType<SummaryExtension>()?.displayName.orEmpty(),
                        workoutShareHelper = workoutShareHelper,
                    )
                },
                modifier = Modifier.padding(top = MaterialTheme.spacing.medium),
            )
        }
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)

        updateImages()
    }
}

@Composable
private fun SportieCard(
    images: List<SportieImage>,
    onClick: (SportieImage) -> Unit,
    onShareClick: (SportieImage) -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
    ) {
        Divider()

        Text(
            text = stringResource(R.string.sportie_share_your_photos),
            modifier = Modifier.padding(MaterialTheme.spacing.medium),
            style = MaterialTheme.typography.bodyLargeBold,
        )

        LazyRow(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = MaterialTheme.spacing.medium)
                .height(256.dp),
        ) {
            items(images) { image ->
                Card(
                    modifier = Modifier
                        .padding(start = MaterialTheme.spacing.medium)
                        .width(200.dp)
                        .clickable { onClick(image) },
                    shape = RoundedCornerShape(16.dp),
                    elevation = 0.dp,
                ) {
                    Box {
                        AsyncImage(
                            model = ImageRequest.Builder(LocalContext.current)
                                .data(image.imageInformation.getFeedUri(LocalContext.current))
                                .placeholder(LocalContext.current, R.drawable.default_image_placeholder)
                                .crossfade(true)
                                .build(),
                            contentDescription = null,
                            modifier = Modifier.fillMaxSize(),
                            contentScale = ContentScale.Crop,
                        )

                        FloatingActionButton(
                            modifier = Modifier
                                .align(Alignment.BottomEnd)
                                .padding(MaterialTheme.spacing.medium)
                                .size(40.dp),
                            onClick = { onShareClick(image) },
                            backgroundColor = MaterialTheme.colorScheme.onPrimary,
                        ) {
                            Icon(
                                painterResource(R.drawable.share_outline),
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.nearBlack,
                            )
                        }
                    }
                }
            }

            item {
                Spacer(modifier = Modifier.width(MaterialTheme.spacing.medium))
            }
        }

        Divider()
    }
}
