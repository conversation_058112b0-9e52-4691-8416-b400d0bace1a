package com.stt.android.newfeed

import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.ui.components.workout.WorkoutShareInfo

typealias OnLikeClicked = (WorkoutCardInfo) -> Unit
typealias OnShareClicked = (WorkoutHeader, WorkoutShareInfo) -> Unit
typealias OnWorkoutClicked = (WorkoutHeader) -> Unit
typealias OnAddPhotoClicked = (WorkoutHeader) -> Unit
typealias OnWorkoutCardPlayButtonClicked = (WorkoutHeader) -> Unit
typealias OnAddDescriptionAndTagsClicked = (WorkoutHeader) -> Unit
typealias OnTagClicked = (tagName: String, isEditable: Boolean) -> Unit
typealias OnOpenPremiumPromotionClicked = (String) -> Unit

data class FeedDataContainer(
    val workoutCards: List<WorkoutFeedCardData>,
    val welcomeCard: WelcomeFeedCardData?,
    val exploreCard: ExploreCardData?,
    val sportieCard: SportieCardData?,
    val topBannerData: FeedTopBannerData?,
    val inlineAppViewCard: EmarsyInlineAppViewCard? = null,
    val filterTagData: FilterTagData? = null,
    val emptyFeedFollowData: FeedEmptyFollowCardData? = null,
    val marketingBannerData: MarketingBannerData? = null,
) {
    companion object {
        @JvmStatic
        val EMPTY = FeedDataContainer(
            workoutCards = emptyList(),
            welcomeCard = null,
            exploreCard = null,
            sportieCard = null,
            filterTagData = null,
            topBannerData = null
        )
    }
}
