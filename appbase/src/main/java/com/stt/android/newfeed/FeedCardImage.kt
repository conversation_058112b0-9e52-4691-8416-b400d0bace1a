package com.stt.android.newfeed

import android.util.Size
import androidx.annotation.DrawableRes
import com.stt.android.domain.user.ImageInformation
import com.stt.android.domain.user.VideoInformation
import com.stt.android.maps.MapSnapshotSpec

sealed class FeedCardImageData(
    open val mapSize: Size
)

data class PlaceholderImageData(
    @DrawableRes val placeholderImageResId: Int,
    override val mapSize: Size,
) : FeedCardImageData(mapSize)

data class WorkoutImageData(
    val imageInformation: ImageInformation,
    override val mapSize: Size,
) : FeedCardImageData(mapSize)

data class WorkoutVideoData(
    val videoInformation: VideoInformation,
    override val mapSize: Size,
) : FeedCardImageData(mapSize)

data class MapSnapshotData(
    val spec: MapSnapshotSpec,
    override val mapSize: Size,
) : FeedCardImageData(mapSize)
