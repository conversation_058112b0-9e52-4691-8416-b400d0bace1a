package com.stt.android.newfeed

import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.stt.android.R
import com.stt.android.common.KotlinEpoxyHolder
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.social.userprofile.UserProfileActivity
import com.stt.android.ui.components.workout.WorkoutCard
import com.stt.android.ui.components.workout.WorkoutCardViewData

@EpoxyModelClass
abstract class WorkoutCardModel : EpoxyModelWithHolder<WorkoutCardHolder>() {
    @EpoxyAttribute
    lateinit var workout: WorkoutCardInfo

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var onWorkoutClicked: OnWorkoutClicked

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var onAddPhotoClicked: OnAddPhotoClicked

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var onPlayButtonClicked: OnWorkoutCardPlayButtonClicked

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var onTagClicked: OnTagClicked

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var onAddCommentClicked: OnWorkoutClicked

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var onReactionClicked: () -> Unit

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var onShareClicked: OnShareClicked

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var onAddDescriptionAndTagsClicked: OnAddDescriptionAndTagsClicked

    override fun getDefaultLayout(): Int = R.layout.viewholder_feed_card_workout

    override fun bind(holder: WorkoutCardHolder) {
        holder.composeView.setContentWithM3Theme {
            WorkoutCard(
                viewData = workout.workoutCardViewData,
                onClick = { onWorkoutClicked(workout.workoutHeader) },
                modifier = Modifier.narrowContent()
                    .padding(
                        top = MaterialTheme.spacing.medium,
                        start = MaterialTheme.spacing.medium,
                        end = MaterialTheme.spacing.medium,
                    ),
                onUserClick = { username ->
                    val context = holder.composeView.context
                    context.startActivity(UserProfileActivity.newStartIntent(context, username, false))
                },
                onAddPhotoClick = { onAddPhotoClicked(workout.workoutHeader) },
                onPlayClick = { onPlayButtonClicked(workout.workoutHeader) },
                onTagClicked = onTagClicked,
                onAddCommentClick = { onAddCommentClicked(workout.workoutHeader) },
                onReactionClick = { onReactionClicked() },
                onShareClick = { onShareClicked(workout.workoutHeader, it) },
                onEditClick = { onAddDescriptionAndTagsClicked(workout.workoutHeader) },
            )
        }
    }

    override fun onViewDetachedFromWindow(holder: WorkoutCardHolder) {
        super.onViewDetachedFromWindow(holder)

        val hasVideo = workout.workoutCardViewData
            .coverInfo
            .any { it is WorkoutCardViewData.CoverInfo.Video }
        if (hasVideo) {
            holder.composeView.setContent {  }
        }
    }
}

class WorkoutCardHolder : KotlinEpoxyHolder() {
    val composeView: ComposeView by bind(R.id.compose_view)
}
