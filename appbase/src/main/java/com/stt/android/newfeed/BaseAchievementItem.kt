package com.stt.android.newfeed

import com.stt.android.domain.user.workout.SimilarWorkoutSummary
import com.stt.android.domain.workouts.WorkoutHeader
import java.util.concurrent.atomic.AtomicInteger

private val globalId = AtomicInteger(1)

sealed class BaseAchievementItem

data class AchievementItem(
    val id: Int = globalId.getAndIncrement(),
    val icon: Int,
    val title: String
) : BaseAchievementItem()

// Old "Fastest on this route" item
data class FastestOnThisRouteItem(
    val title: Int,
    val workoutHeader: WorkoutHeader?,
    val similarWorkoutSummary: SimilarWorkoutSummary?
) : BaseAchievementItem()
