package com.stt.android.newfeed

data class FilterTagData(
    var showNewInMe: Boolean = false,
    var showNewInFollowing: Boolean = false,
    var defaultSelected: FilterTag,
    val onAllClicked: (FilterTagData) -> Unit,
    val onMeClicked: (FilterTagData) -> Unit,
    val onFollowingClicked: (FilterTagData) -> Unit,
    val onSuuntoClicked: (FilterTagData) -> Unit
)

enum class FilterTag(val filterCode: Int) {
    ALL(0),
    Me(1),
    FOLLOWING(2),
    SUUNTO(3),
}

fun getFilterTagByCode(filterCode: Int): FilterTag {
    return when (filterCode) {
        FilterTag.ALL.filterCode -> FilterTag.ALL
        FilterTag.Me.filterCode -> FilterTag.Me
        FilterTag.FOLLOWING.filterCode -> FilterTag.FOLLOWING
        FilterTag.SUUNTO.filterCode -> FilterTag.SUUNTO
        else -> FilterTag.Me
    }
}
