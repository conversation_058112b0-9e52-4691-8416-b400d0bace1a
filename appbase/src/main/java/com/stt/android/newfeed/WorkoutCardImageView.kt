package com.stt.android.newfeed

import android.content.Context
import android.net.Uri
import android.util.AttributeSet
import android.util.Size
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.Group
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import coil3.dispose
import coil3.load
import coil3.request.crossfade
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.ui.AspectRatioFrameLayout
import com.google.android.exoplayer2.ui.PlayerView
import com.stt.android.R
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.coil.placeholder
import com.stt.android.databinding.WorkoutCardImageViewBinding
import com.stt.android.domain.review.ReviewState
import com.stt.android.domain.user.VideoInformation
import com.stt.android.maps.bindMapSnapshot
import com.stt.android.maps.clearMapSnapshot
import com.stt.android.maps.detach
import com.stt.android.multimedia.video.ExoPlayerHelper
import timber.log.Timber
import kotlin.math.roundToInt

class WorkoutCardImageView
@JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr), Player.Listener, DefaultLifecycleObserver {

    private val binding: WorkoutCardImageViewBinding = DataBindingUtil.inflate(
        LayoutInflater.from(context),
        R.layout.workout_card_image_view,
        this,
        true
    )

    private val pictureOrMap: ImageView = binding.pictureOrMap
    private val exoPlayerView: PlayerView = binding.exoPlayerView
    private val fullscreenVideo: ImageButton = binding.fullscreenVideo
    private val muteVideo: ImageButton = binding.muteVideo
    private val loadingSpinner: ProgressBar = binding.loadingSpinner
    private val contentReviewing: TextView = binding.contentReviewing
    private val contentIllegalViewGroup: Group = binding.contentIllegalViewGroup
    private var videoInformation: VideoInformation? = null
    private var previousImageData: FeedCardImageData? = null
    private var mapSize: Size? = null
    private var mediaUri: Uri? = null
    private var isMute = true

    private var amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker? = null

    private val player: Player?
        get() = exoPlayerView.player

    var lifecycle: Lifecycle? = null
    var userAgent: String? = null
    var ownWorkout: Boolean = false

    var scrollingState = RecyclerView.SCROLL_STATE_IDLE
        set(value) {
            field = value
            if (value == RecyclerView.SCROLL_STATE_IDLE && videoInformation != null) {
                initializeOrStopVideo()
            }
        }

    var visibilityPercentage = 0.0f
        set(value) {
            field = value
            if (value >= AUTOPLAY_MIN_VISIBILITY_PERCENTAGE && player?.playbackState == Player.STATE_READY) {
                startVideo()
            }
        }

    var useFixedHeight = false // Images in ExploreCardFragment have fixed height

    private val allowedAspectRationRange = if (context.resources.getBoolean(R.bool.wide_display)) {
        1.3f..2.33f
    } else {
        0.75f..2.33f
    }

    init {
        muteVideo.setOnClickListener {
            toggleMute()
        }
    }

    fun setFullscreenVideoClickListener(listener: OnClickListener) {
        fullscreenVideo.setOnClickListener(listener)
    }

    fun bind(imageData: FeedCardImageData, scaleType: ImageView.ScaleType? = null, amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker? = null) {
        val playingOrBuffering = player?.playbackState == Player.STATE_READY ||
            player?.playbackState == Player.STATE_BUFFERING
        if (imageData is WorkoutVideoData && videoInformation == imageData.videoInformation && playingOrBuffering) {
            // This video is already being played back. Keep playing instead of setting up
            // playback from scratch
            Timber.d("Video playback already ongoing for this video")
            return
        }

        videoInformation = null
        releaseVideoPlayer()

        lifecycle?.addObserver(this)
        this.amplitudeAnalyticsTracker = amplitudeAnalyticsTracker

        if (previousImageData != imageData) {
            // Image data has changed -> load new image
            previousImageData = imageData

            if (imageData !is MapSnapshotData) {
                // Make sure any pending snapshot loading is cancelled and image view is cleared
                pictureOrMap.clearMapSnapshot()
            }
            // revert to the default invisible state
            setContentIllegalViewVisible(false)
            setContentReviewingViewVisible(false, "")
            when (imageData) {
                is WorkoutImageData -> bindImage(imageData)
                is WorkoutVideoData -> bindVideo(imageData)
                is PlaceholderImageData -> bindPlaceholderImage(imageData)
                is MapSnapshotData -> bindMapSnapshot(imageData, scaleType)
            }
        }
    }

    fun unbind() {
        lifecycle?.removeObserver(this)

        // Abort any pending map snapshot loading, but keep showing current map snapshot, if any
        pictureOrMap.detach()
        videoInformation = null
        amplitudeAnalyticsTracker = null
        releaseVideoPlayer()
    }

    private fun setHeight(height: Int) {
        val myLayoutParams = layoutParams
        if (myLayoutParams != null && myLayoutParams.height != height) {
            myLayoutParams.height = height
            layoutParams = myLayoutParams
        }
    }

    /**
     * Bind an user attached workout image. The image will be loaded asynchronously using Coil.
     * The layout height for this view will be adjusted according to the aspect ratio and image
     * information.
     */
    private fun bindImage(workoutImageData: WorkoutImageData) {
        val imageInformation = workoutImageData.imageInformation
        pictureOrMap.visibility = View.VISIBLE
        pictureOrMap.scaleType = ImageView.ScaleType.CENTER_CROP
        hideVideoView()

        var viewWidth = pictureOrMap.width
        if (viewWidth == 0) {
            // It is safe to assume they're the same size
            viewWidth = workoutImageData.mapSize.width
        }

        val viewHeight = calculateImageViewHeight(
            Size(imageInformation.width, imageInformation.height),
            workoutImageData.mapSize,
        )

        setHeight(viewHeight)

        if (viewWidth > 0 && viewHeight > 0) {
            pictureOrMap.load(imageInformation.getFeedUri(context)) {
                listener(onError = { request, e -> onImageLoadFailed(e.throwable, request.data) })
            }
        }
        if (ownWorkout) {
            setImageReviewState(workoutImageData)
        }
    }

    private fun setImageReviewState(workoutImageData: WorkoutImageData) {
        setContentReviewingViewVisible(
            workoutImageData.imageInformation.reviewState == ReviewState.REVIEWING,
            contentReviewing.resources.getString(R.string.image_reviewing)
        )
        setContentIllegalViewVisible(workoutImageData.imageInformation.reviewState == ReviewState.FAIL)
    }

    private fun setVideoReviewState(reviewState: ReviewState?) {
        setContentReviewingViewVisible(
            reviewState == ReviewState.REVIEWING,
            contentReviewing.resources.getString(R.string.video_reviewing)
        )
        setContentIllegalViewVisible(reviewState == ReviewState.FAIL)
        if (reviewState == ReviewState.REVIEWING || reviewState == ReviewState.FAIL
        ) {
            hideVideoView()
        }
    }

    private fun setContentReviewingViewVisible(visible: Boolean, text: String) {
        if (visible) {
            contentReviewing.text = text
            contentReviewing.visibility = VISIBLE
        } else {
            contentReviewing.visibility = GONE
        }
    }
    private fun setContentIllegalViewVisible(visible: Boolean) {
        contentIllegalViewGroup.visibility = if (visible) VISIBLE else GONE
    }

    /**
     * Bind an activity placeholder image.
     */
    private fun bindPlaceholderImage(placeholderImageData: PlaceholderImageData) {
        hideVideoView()
        pictureOrMap.visibility = View.VISIBLE
        pictureOrMap.scaleType = ImageView.ScaleType.CENTER_CROP

        val defaultProfileOrMapHeight =
            context.resources.getDimensionPixelSize(R.dimen.summary_map_height)

        setHeight(defaultProfileOrMapHeight)

        pictureOrMap.load(placeholderImageData.placeholderImageResId) {
            crossfade(false)
            listener(onError = { request, e -> onImageLoadFailed(e.throwable, request.data) })
        }
    }

    private fun bindMapSnapshot(mapSnapshotData: MapSnapshotData, scaleType: ImageView.ScaleType?) {
        pictureOrMap.visibility = View.VISIBLE
        if (scaleType != null) {
            pictureOrMap.scaleType = scaleType
        }
        hideVideoView()
        setHeight(mapSnapshotData.mapSize.height)

        pictureOrMap.dispose()
        pictureOrMap.bindMapSnapshot(mapSnapshotData.spec)
    }

    private fun bindVideo(videoData: WorkoutVideoData) {
        val reviewState = videoData.videoInformation.reviewState
        if (reviewState == ReviewState.PASS) {
            // if the video review state is not pass, video will can't play and show
            // but you can see the thumbnail and show the text that video Review in Progress
            videoInformation = videoData.videoInformation
        }
        mapSize = videoData.mapSize

        pictureOrMap.visibility = View.VISIBLE
        exoPlayerView.visibility = View.INVISIBLE
        fullscreenVideo.visibility = View.VISIBLE
        muteVideo.visibility = View.VISIBLE
        loadingSpinner.visibility = View.GONE
        muteVideo.setImageResource(if (isMute) R.drawable.ic_speaker_off else R.drawable.ic_speaker_full)

        var viewWidth = pictureOrMap.width
        if (viewWidth == 0) {
            // It is safe to assume they're the same size
            viewWidth = videoData.mapSize.width
        }

        val viewHeight = calculateImageViewHeight(
            Size(videoData.videoInformation.width, videoData.videoInformation.height),
            videoData.mapSize
        )

        setHeight(viewHeight)

        if (viewWidth > 0 && viewHeight > 0) {
            val thumbnailUri = videoData.videoInformation.getThumbnailUri(context)
            if (thumbnailUri != null) {
                pictureOrMap.load(thumbnailUri) {
                    placeholder(pictureOrMap.context, R.color.feed_image_placeholder)
                    listener(onError = { request, e -> onImageLoadFailed(e.throwable, request.data) })
                }
            }
        }
        setVideoReviewState(reviewState)
    }

    private fun initializeOrStopVideo() {
        val userAgent = userAgent ?: return
        val videoInformation = videoInformation ?: return
        val uri = videoInformation.getUri(exoPlayerView.context) ?: return

        // initializes if needed
        if (exoPlayerView.player == null) {
            exoPlayerView.player = ExoPlayerHelper.createPlayer(context, userAgent).also {
                it.addListener(this)
            }
        }

        // prepares video source if needed
        if (uri != mediaUri) {
            loadingSpinner.visibility = View.VISIBLE

            mediaUri = uri
            ExoPlayerHelper.setMedia(exoPlayerView.player, uri, true)
            exoPlayerView.player?.prepare()

            val videoAspectRatio = videoInformation.width.toDouble() / videoInformation.height
            val viewAspectRatio = if (exoPlayerView.height > 0) {
                exoPlayerView.width.toDouble() / exoPlayerView.height
            } else {
                mapSize?.let { it.width.toDouble() / it.height }
                    ?: 1.0
            }

            if (videoAspectRatio < viewAspectRatio) {
                exoPlayerView.resizeMode = AspectRatioFrameLayout.RESIZE_MODE_FIXED_WIDTH
            } else {
                exoPlayerView.resizeMode = AspectRatioFrameLayout.RESIZE_MODE_FIXED_HEIGHT
            }

            isMute = true
            muteVideo.setImageResource(R.drawable.ic_speaker_off)
        }

        // We auto-play the video if at least half of the video view is visible, otherwise stop it.
        if (visibilityPercentage < AUTOPLAY_MIN_VISIBILITY_PERCENTAGE) {
            exoPlayerView.player?.let { it.playWhenReady = false }
        } else {
            startVideo()
        }

        amplitudeAnalyticsTracker?.trackEvent(AnalyticsEvent.LOADING_VIDEO, "Location", "Feed")
    }

    private fun hideVideoView() {
        exoPlayerView.visibility = View.GONE
        fullscreenVideo.visibility = View.GONE
        muteVideo.visibility = View.GONE
        loadingSpinner.visibility = View.GONE
    }

    private fun startVideo() {
        player?.volume = if (isMute) 0.0f else 1.0f
        player?.playWhenReady = true
    }

    private fun toggleMute() {
        isMute = !isMute
        muteVideo.setImageResource(
            if (isMute) R.drawable.ic_speaker_off else R.drawable.ic_speaker_full
        )
        player?.volume = if (isMute) 0.0f else 1.0f
    }

    private fun releaseVideoPlayer() {
        player?.run {
            removeListener(this@WorkoutCardImageView)
            stop()
            release()
        }
        exoPlayerView.player = null
        mediaUri = null
    }

    override fun onResume(owner: LifecycleOwner) {
        if (videoInformation != null && visibilityPercentage > AUTOPLAY_MIN_VISIBILITY_PERCENTAGE) {
            initializeOrStopVideo()
        }
    }

    override fun onPause(owner: LifecycleOwner) {
        releaseVideoPlayer()
    }

    override fun onDestroy(owner: LifecycleOwner) {
        lifecycle?.removeObserver(this)
    }

    private fun calculateImageViewHeight(imageSize: Size, mapSize: Size, ): Int {
        return if (useFixedHeight) {
            // Images in ExploreCardFragment have fixed height
            mapSize.height
        } else {
            // In feed, use dynamic height based on aspect ratio
            calculateImageViewHeight(
                imageSize.width,
                imageSize.height,
                mapSize.width,
                mapSize.height,
            )
        }
    }

    private fun calculateImageViewHeight(
        imageWidth: Int,
        imageHeight: Int,
        mapCacheWidth: Int,
        mapCacheHeight: Int
    ): Int {
        var viewWidth = pictureOrMap.width
        if (viewWidth == 0) {
            // It is safe to assume they're the same size
            viewWidth = mapCacheWidth
        }

        // TODO: 11.11.2021 Consider a solution that also takes the screen height in to account.
        // On landscape, the available height is a more limiting factor than the width.
        return if (imageWidth > 0 && imageHeight > 0) {
            val aspect = imageWidth.toFloat() / imageHeight.toFloat()
            (viewWidth.toFloat() / aspect.coerceIn(allowedAspectRationRange)).roundToInt()
        } else {
            // It is safe to assume they're the same size
            mapCacheHeight
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onPlayerStateChanged(playWhenReady: Boolean, playbackState: Int) {
        if (playbackState == Player.STATE_READY) {
            // We don't show the player until the video is ready, to avoid black screen while
            // initializing the video
            pictureOrMap.visibility = View.GONE
            exoPlayerView.visibility = View.VISIBLE
            loadingSpinner.visibility = View.GONE

            amplitudeAnalyticsTracker?.trackEvent(AnalyticsEvent.PLAY_VIDEO, "Location", "Feed")
        }
    }

    fun onImageLoadFailed(e: Throwable, data: Any) = post {
        // Intentionally use debug log level since this is not that serious of an error
        Timber.d(e, "onLoadFailed for data: $data")

        // Reset previous image data on error to try loading again on next bind
        previousImageData = null
    }

    companion object {
        private const val AUTOPLAY_MIN_VISIBILITY_PERCENTAGE = 50f
    }
}
