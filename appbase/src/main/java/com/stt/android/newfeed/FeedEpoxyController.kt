package com.stt.android.newfeed

import android.widget.LinearLayout
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import com.airbnb.epoxy.EpoxyModel
import com.airbnb.epoxy.EpoxyViewHolder
import com.airbnb.epoxy.stickyheader.StickyHeaderCallbacks
import com.emarsys.core.api.result.CompletionListener
import com.emarsys.di.emarsys
import com.emarsys.inapp.ui.InlineInAppView
import com.emarsys.mobileengage.iam.jsbridge.OnCloseListener
import com.stt.android.BuildConfig
import com.stt.android.FeedCardFilterTagBindingModel_
import com.stt.android.R
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.ViewStateEpoxyController
import com.stt.android.databinding.ViewholderFeedCardFilterTagBinding
import com.stt.android.databinding.ViewholderFeedEmptyFollowBinding
import com.stt.android.databinding.ViewholderInlineAppViewCardBinding
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator
import com.stt.android.domain.review.ReviewState
import com.stt.android.feedCardFilterTag
import com.stt.android.feedEmptyFollow
import com.stt.android.feedEmptyOther
import com.stt.android.home.dashboardnew.DashboardPagerCard
import com.stt.android.home.dashboardnew.dashboardPagerCard
import com.stt.android.inlineAppViewCard
import com.stt.android.ui.utils.setOnClickListenerThrottled
import com.stt.android.welcomeCard
import com.stt.android.workouts.sharepreview.customshare.WorkoutShareHelper
import timber.log.Timber
import java.lang.ref.WeakReference
import javax.inject.Inject
import androidx.core.view.isNotEmpty

typealias EmarsysInlineNeedListener = (Boolean) -> Unit

class FeedEpoxyController @Inject constructor(
    private val workoutShareHelper: WorkoutShareHelper,
) : ViewStateEpoxyController<FeedDataContainer>(), StickyHeaderCallbacks {
    var lifecycleOwner: LifecycleOwner? = null
        set(value) {
            if (field != value) {
                field?.lifecycle?.removeObserver(lifecycleObserver)
                field = value
                field?.lifecycle?.addObserver(lifecycleObserver)
            }
        }

    private val lifecycleObserver = object : DefaultLifecycleObserver {
        override fun onDestroy(owner: LifecycleOwner) {
            super.onDestroy(owner)
            fragmentManager = null
            rewriteNavigator = null
            lifecycleOwner = null
        }
    }

    var fragmentManager: FragmentManager? = null

    var rewriteNavigator: WorkoutDetailsRewriteNavigator? = null

    // Scrolling state is passed as a LiveData object in order to avoid going through the full
    // model building and diffing operation every time the scroll state is changed. Scrolling state
    // is used for video playback handling in WorkoutCardImageView.
    private val scrollingStateLiveData = MutableLiveData<Int>()

    init {
        isDebugLoggingEnabled = BuildConfig.DEBUG
    }

    fun setScrollingState(state: Int) {
        scrollingStateLiveData.value = state
    }

    override fun buildModels(viewState: ViewState<FeedDataContainer?>) {
        val cards = getFeedCardsInOrder(viewState.data)

        viewState.data?.topBannerData?.let {
            buildTopBanner(it)
        }

        buildDashboardCardModel()
        viewState.data?.marketingBannerData?.let {
            buildMarketingBanner(it)
        }
        viewState.data?.filterTagData?.let {
            buildFilterTagCardModel(it)
        }
        cards.forEach {
            when (it) {
                is WorkoutFeedCardData -> buildWorkoutCardModel(it)
                is WelcomeFeedCardData -> buildWelcomeCardModel()
                is ExploreCardData -> buildExploreCardModel(it)
                is SportieCardData -> buildSportieCardModel(it)
                is EmarsyInlineAppViewCard -> buildInlineAppViewCardModel(it)
                else -> Timber.w("Unsupported FeedCardData $it")
            }
        }
        if (cards.isEmpty() && viewState.data?.filterTagData != null) {
            if (viewState.data.filterTagData.defaultSelected == FilterTag.FOLLOWING) {
                viewState.data.emptyFeedFollowData?.let {
                    buildEmptyFollowCardModel(it)
                }
            } else {
                buildEmptyOtherCardModel()
            }
        }
    }

    private fun buildEmptyOtherCardModel() {
        feedEmptyOther {
            id("feed_empty_other")
        }
    }

    private fun buildEmptyFollowCardModel(emptyFollowCardData: FeedEmptyFollowCardData) {
        feedEmptyFollow {
            id("feed_empty_follow")
            onBind { _, view, _ ->
                val binding = view.dataBinding as ViewholderFeedEmptyFollowBinding
                binding.btnFindPeople.setOnClickListenerThrottled {
                    emptyFollowCardData.onFindPeopleClicked.invoke()
                }
            }
        }
    }

    private fun buildInlineAppViewCardModel(emarsyInlineAppViewCard: EmarsyInlineAppViewCard) {
        inlineAppViewCard {
            id("inline_app_view")
            onBind { _, view, _ ->
                val binding = view.dataBinding as ViewholderInlineAppViewCardBinding
                binding.inlineViewLayout.removeAllViews()
                binding.inlineViewLayout.addView(
                    InlineInAppView(binding.root.context.applicationContext).apply {
                        onCompletionListener = CompletionListener {
                            if (it != null) {
                                emarsyInlineAppViewCard.needListener(true)
                            }
                            binding.inlineDivider.isVisible = it == null
                        }
                        onAppEventListener = emarsys().jsOnAppEventListener
                        onCloseListener = object : OnCloseListener {
                            override fun invoke() {
                                emarsyInlineAppViewCard.needListener(false)
                            }
                        }
                        loadInApp(context.getString(R.string.emarsys_view_id))
                        layoutParams = LinearLayout.LayoutParams(
                            LinearLayout.LayoutParams.MATCH_PARENT,
                            LinearLayout.LayoutParams.WRAP_CONTENT
                        )
                    },
                    0
                )
            }
            onUnbind { _, view ->
                val binding = view.dataBinding as ViewholderInlineAppViewCardBinding
                if (binding.inlineViewLayout.isNotEmpty()) {
                    (binding.inlineViewLayout.getChildAt(0) as? InlineInAppView)?.clearListeners()
                }
                binding.inlineViewLayout.removeAllViews()
            }
        }
    }

    private fun InlineInAppView.clearListeners() {
        onCompletionListener = null
        onAppEventListener = null
        onCloseListener = null
    }

    private fun buildFilterTagCardModel(filterTagData: FilterTagData) {
        feedCardFilterTag {
            id("filter_tag")
            item(filterTagData)
            onBind { _, view, _ ->
                val binding = view.dataBinding as ViewholderFeedCardFilterTagBinding
                with(binding) {
                    when (filterTagData.defaultSelected) {
                        FilterTag.ALL -> chipFilterAll
                        FilterTag.Me -> chipFilterMe
                        FilterTag.FOLLOWING -> chipFilterFollowing
                        FilterTag.SUUNTO -> chipFilterSuunto
                    }.apply {
                        isChecked = true
                        setOtherFilterTagToUnChecked(this.id, binding)
                    }
                    chipFilterAll.setOnClickListenerThrottled(500) {
                        if (filterTagData.defaultSelected == FilterTag.ALL) return@setOnClickListenerThrottled
                        setOtherFilterTagToUnChecked(it.id, this)
                        filterTagData.onAllClicked.invoke(filterTagData)
                    }
                    chipFilterMe.setOnClickListenerThrottled(500) {
                        if (filterTagData.defaultSelected == FilterTag.Me) return@setOnClickListenerThrottled
                        setOtherFilterTagToUnChecked(it.id, this)
                        filterTagData.onMeClicked.invoke(filterTagData)
                    }
                    chipFilterFollowing.setOnClickListenerThrottled(500) {
                        if (filterTagData.defaultSelected == FilterTag.FOLLOWING) return@setOnClickListenerThrottled
                        setOtherFilterTagToUnChecked(it.id, this)
                        filterTagData.onFollowingClicked.invoke(filterTagData)
                    }
                    chipFilterSuunto.setOnClickListenerThrottled(500) {
                        if (filterTagData.defaultSelected == FilterTag.SUUNTO) return@setOnClickListenerThrottled
                        setOtherFilterTagToUnChecked(it.id, this)
                        filterTagData.onSuuntoClicked.invoke(filterTagData)
                    }
                }
            }
        }
    }

    private fun setOtherFilterTagToUnChecked(
        checkedId: Int,
        binding: ViewholderFeedCardFilterTagBinding
    ) {
        when (checkedId) {
            binding.chipFilterAll.id -> {
                binding.chipFilterMe.isChecked = false
                binding.chipFilterFollowing.isChecked = false
                binding.chipFilterSuunto.isChecked = false
                FilterTag.ALL
            }

            binding.chipFilterMe.id -> {
                binding.chipFilterAll.isChecked = false
                binding.chipFilterFollowing.isChecked = false
                binding.chipFilterSuunto.isChecked = false
                FilterTag.Me
            }

            binding.chipFilterFollowing.id -> {
                binding.chipFilterMe.isChecked = false
                binding.chipFilterAll.isChecked = false
                binding.chipFilterSuunto.isChecked = false
                FilterTag.FOLLOWING
            }

            binding.chipFilterSuunto.id -> {
                binding.chipFilterMe.isChecked = false
                binding.chipFilterFollowing.isChecked = false
                binding.chipFilterAll.isChecked = false
                FilterTag.SUUNTO
            }

            else -> {
                FilterTag.ALL
            }
        }
    }

    private fun buildDashboardCardModel() {
        // In case model is used in the context of ExploreCardFragment, lifecycle is null
        // and dashboard card view is not created
        val lifecycle = lifecycleOwner?.lifecycle ?: return
        val fragmentManager = fragmentManager ?: return

        dashboardPagerCard {
            id("dashboard")
            fragmentManagerRef(WeakReference(fragmentManager))

            onBind { _, view, _ -> view.onBind(lifecycle) }
            onUnbind { _, view -> view.onUnbind(lifecycle) }
        }
    }

    private fun getFeedCardsInOrder(container: FeedDataContainer?): List<FeedCardData> {
        if (container == null) {
            return emptyList()
        }

        val cards = mutableListOf<FeedCardData>()
        cards.addAll(container.workoutCards)
        val haveWorkouts = cards.isNotEmpty()

        container.welcomeCard?.let {
            if (!haveWorkouts) {
                // When user has no cards set welcome card to first position
                cards.add(0, it)
            }
        }

        if (haveWorkouts) {
            container.exploreCard?.run {
                cards.add(EXPLORE_CARD_POSITION.coerceAtMost(cards.size), this)
            }

            container.sportieCard?.run {
                cards.add(SPORTIE_CARD_POSITION.coerceAtMost(cards.size), this)
            }
        }

        container.inlineAppViewCard?.let { cards.add(0, it) }

        return cards
    }

    private fun buildWorkoutCardModel(workoutCard: WorkoutFeedCardData) {
        workoutCard {
            id(workoutCard.id)
            workout(workoutCard.cardInfo)
            onWorkoutClicked(workoutCard.onWorkoutClicked)
            onAddPhotoClicked(workoutCard.onAddPhotoClicked)
            onPlayButtonClicked(workoutCard.onPlayButtonClicked)
            onTagClicked(workoutCard.onTagClicked)
            onAddCommentClicked(workoutCard.onAddCommentClicked)
            onReactionClicked { workoutCard.onLikeClicked(workoutCard.cardInfo) }
            onShareClicked(workoutCard.onShareClicked)
            onAddDescriptionAndTagsClicked(workoutCard.onAddDescriptionAndTagsClicked)
        }
    }

    private fun buildExploreCardModel(exploreCard: ExploreCardData) {
        exploreCardView {
            id("explore")
            rewriteNavigator(requireNotNull(rewriteNavigator))
            workoutCards(exploreCard.workoutCards)
        }
    }

    private fun buildSportieCardModel(sportieCard: SportieCardData) {
        sportieCardView {
            id("sportie")
            images(sportieCard.images.filter { it.imageInformation.reviewState == ReviewState.PASS })
            workoutShareHelper(workoutShareHelper)
            workoutDetailsNavigator(requireNotNull(rewriteNavigator))
        }
    }

    private fun buildWelcomeCardModel() {
        welcomeCard {
            id("welcome")
        }
    }

    private fun buildTopBanner(bannerData: FeedTopBannerData) {
        feedTopBannerView {
            id("topBanner")
            data(bannerData)
        }
    }

    private fun buildMarketingBanner(data: MarketingBannerData) {
        marketingBannerView {
            id("marketingBanner")
            data(data)
        }
    }

    override fun onViewAttachedToWindow(holder: EpoxyViewHolder, model: EpoxyModel<*>) {
        super.onViewAttachedToWindow(holder, model)

        // Forward attach event to DashboardPagerCard so it can do fragment transactions safely
        (holder.itemView as? DashboardPagerCard)?.handleAttach()
    }

    companion object {
        private const val SPORTIE_CARD_POSITION = 2
        private const val EXPLORE_CARD_POSITION = 7
    }

    override fun isStickyHeader(position: Int): Boolean {
        return if (position >= 0 && position < adapter.itemCount) {
            adapter.getModelAtPosition(position) is FeedCardFilterTagBindingModel_
        } else {
            false
        }
    }
}
