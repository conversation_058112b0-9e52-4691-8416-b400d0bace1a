package com.stt.android.newfeed

import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.stt.android.cardlist.MapCard
import com.stt.android.domain.user.ReactionSummary
import com.stt.android.domain.user.User
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.ui.components.workout.WorkoutCardViewData

data class WorkoutCardInfo internal constructor(
    val user: User,
    val workoutHeader: WorkoutHeader,
    val likes: ReactionSummary?,
    val workoutCardViewData: WorkoutCardViewData,
    override val route: List<LatLng>? = null,
    override val activityRoutes: List<List<LatLng>>? = null,
    override val nonActivityRoutes: List<List<LatLng>>? = null,
    override val bounds: LatLngBounds? = null,
) : MapCard, FeedCardData() {
    fun toBuilder() = Builder(
        user = user,
        workoutHeader = workoutHeader,
        likes = likes,
        workoutCardViewData = workoutCardViewData,
    )

    override val polylineHashCode: Int
        get() = workoutHeader.polylineHashCode

    @Suppress("LongParameterList")
    class Builder internal constructor(
        private var user: User? = null,
        private var workoutHeader: WorkoutHeader? = null,
        private var likes: ReactionSummary? = null,
        private var workoutCardViewData: WorkoutCardViewData? = null,
    ) {
        fun user(value: User): Builder = apply { this.user = value }

        fun workoutHeader(value: WorkoutHeader): Builder = apply { this.workoutHeader = value }

        fun likes(value: ReactionSummary?): Builder = apply { this.likes = value }

        fun workoutCardViewData(value: WorkoutCardViewData): Builder = apply { this.workoutCardViewData = value }

        fun build(): WorkoutCardInfo = WorkoutCardInfo(
            user = user ?: error("user == null"),
            workoutHeader = workoutHeader ?: error("workoutHeader == null"),
            likes = likes,
            workoutCardViewData = workoutCardViewData ?: error("workoutCardViewData == null"),
        )
    }

    companion object {
        @JvmStatic
        fun builder(): Builder = Builder()
    }
}
