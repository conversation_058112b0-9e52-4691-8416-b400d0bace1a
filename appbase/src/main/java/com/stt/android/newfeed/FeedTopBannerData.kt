package com.stt.android.newfeed

import android.content.Context
import androidx.fragment.app.Fragment
import javax.inject.Inject

data class FeedTopBannerData(
    val type: FeedTopBannerType,
    val onClick: (FeedTopBannerType) -> Unit,
    val onClose: (FeedTopBannerType) -> Unit
)

sealed interface FeedTopBannerType {
    fun getTitle(context: Context): String
    fun getBody(context: Context): String
}

interface FeedTopBannerNavigator {
    fun register(fragment: Fragment)
    fun navigateToContent(context: Context, type: FeedTopBannerType)
}

class FeedTopBannerNavigatorNoOp @Inject constructor() : FeedTopBannerNavigator {
    override fun register(fragment: Fragment) {
        // Do nothing
    }

    override fun navigateToContent(context: Context, type: FeedTopBannerType) {
        // no-op
    }
}
