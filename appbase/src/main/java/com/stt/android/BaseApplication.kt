package com.stt.android

import android.app.Application
import androidx.annotation.CallSuper
import androidx.tracing.trace
import com.stt.android.exceptions.STTUncaughtExceptionHandler
import com.stt.android.utils.isMainProcess
import timber.log.Timber

abstract class BaseApplication : Application() {

    override fun onCreate() {
        /**
         * Since this application is made of multiple processes we need to be careful about which
         * process is currently being created. So please don't use this method unless you know what
         * you're doing.
         */
        super.onCreate()

        initAtProcessStart()

        if (isMainProcess) {
            onCreateMainProcess()
        } else {
            onCreateSecondaryProcess()
        }
    }

    /**
     * Initialises everything that needs to be ready at start of the process, no matter what process it is
     */
    @CallSuper
    protected open fun initAtProcessStart() = trace("initAtProcessStart") {
        synchronized(initAtProcessStartLock) {
            if (!initAtProcessStartDone) {
                initAtProcessStartDone = true
                LoggingInitializer().initializeTimberAtProcessStart()
                setupSTTUncaughtExceptionHandler()
            }
        }
    }

    /**
     * Since this application is made of multiple processes we need to be careful about which
     * process is currently being created. So please don't use this method unless you know what
     * you're doing.
     *
     */
    @Deprecated("Please use {@link #onTrimMemoryMainProcess(int)}")
    override fun onTrimMemory(level: Int) {
        super.onTrimMemory(level)
        if (isMainProcess) {
            onTrimMemoryMainProcess(level)
        }
    }

    /**
     * Since this application is made of multiple processes we need to be careful about which
     * process is currently being created. So please don't use this method unless you know what
     * you're doing.
     *
     */
    @Deprecated("Please use {@link #onLowMemoryMainProcess()}")
    override fun onLowMemory() {
        super.onLowMemory()
        if (isMainProcess) {
            Timber.w("onLowMemory - Main process")
            onLowMemoryMainProcess()
        } else {
            Timber.w("onLowMemory - Other process")
        }
    }

    /**
     * This is called when the overall system is running low on memory, and actively running
     * processes should trim their memory usage.
     *
     * @see android.app.Application.onLowMemory
     */
    protected abstract fun onLowMemoryMainProcess()

    /**
     * Called when the operating system has determined that it is a good time for a process to
     * trim unneeded memory from its process.
     *
     * @see android.app.Application.onTrimMemory
     */
    protected abstract fun onTrimMemoryMainProcess(level: Int)

    /**
     * Called when the application is starting and this is the main process
     */
    protected abstract fun onCreateMainProcess()

    /**
     * Called when the application is starting and this is NOT the main process
     */
    protected abstract fun onCreateSecondaryProcess()

    private fun setupSTTUncaughtExceptionHandler() {
        Thread.setDefaultUncaughtExceptionHandler(
            STTUncaughtExceptionHandler(Thread.getDefaultUncaughtExceptionHandler())
        )
    }

    companion object {
        private val initAtProcessStartLock = Any()
        private var initAtProcessStartDone = false
    }
}
