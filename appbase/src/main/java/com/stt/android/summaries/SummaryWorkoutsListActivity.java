package com.stt.android.summaries;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import com.stt.android.R;
import com.stt.android.home.diary.DiaryWorkoutListFragment;
import dagger.hilt.android.AndroidEntryPoint;
import java.util.ArrayList;

/**
 * Simple activity that holds a {@link DiaryWorkoutListFragment}.
 * <p><b>WARNING!</b> <em>this activity can only be used when we're sure that the user has an
 * active subscription</em></p>
 */
@AndroidEntryPoint
public class SummaryWorkoutsListActivity extends AppCompatActivity {
    @NonNull
    public static Intent newStartIntent(
        @NonNull Context context,
        @NonNull ArrayList<Integer> workoutIds,
        boolean hideGroupHeader,
        boolean isSubView,
        String analyticsViewId) {
        return new Intent(context, SummaryWorkoutsListActivity.class).putExtra(
                DiaryWorkoutListFragment.ARG_WORKOUT_IDS, workoutIds)
            .putExtra(DiaryWorkoutListFragment.ARG_HIDE_GROUP_HEADER, hideGroupHeader)
            .putExtra(DiaryWorkoutListFragment.ARG_ANALYTICS_VIEW_ID, analyticsViewId)
            .putExtra(DiaryWorkoutListFragment.ARG_SUB_VIEW, isSubView);
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.workouts_list_activity);

        // Attach the fragments only if it's the first time that this activity is created
        if (savedInstanceState == null) {
            Intent intent = getIntent();
            boolean hideGroupHeader = intent
                .getBooleanExtra(DiaryWorkoutListFragment.ARG_HIDE_GROUP_HEADER, false);
            String analyticsViewId = intent
                .getStringExtra(DiaryWorkoutListFragment.ARG_ANALYTICS_VIEW_ID);
            ArrayList<Integer> workoutIds = intent
                .getIntegerArrayListExtra(DiaryWorkoutListFragment.ARG_WORKOUT_IDS);
            boolean isSubView = intent
                .getBooleanExtra(DiaryWorkoutListFragment.ARG_SUB_VIEW, false);
            Fragment diaryFragment = DiaryWorkoutListFragment
                .newInstanceWithWorkoutIds(workoutIds, hideGroupHeader, analyticsViewId, isSubView);
            getSupportFragmentManager().beginTransaction()
                .add(R.id.listFragmentContainer, diaryFragment, DiaryWorkoutListFragment.FRAGMENT_TAG)
                .commit();
        }
    }
}
