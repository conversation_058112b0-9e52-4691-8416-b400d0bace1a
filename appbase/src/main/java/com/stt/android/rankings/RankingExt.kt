package com.stt.android.rankings

import android.content.res.Resources
import com.stt.android.R
import com.stt.android.domain.ranking.Ranking

fun Ranking.getDescription(resources: Resources): String? {
    val ranking = this.ranking
    val numberOfWorkouts = this.numberOfWorkouts
    if (ranking != null && numberOfWorkouts != null) {
        if (ranking == 1 && numberOfWorkouts > 1) {
            return resources.getString(R.string.achievement_route_first)
        } else if (ranking == 2 && numberOfWorkouts > 1) {
            return resources.getString(R.string.achievement_route_second)
        } else if (ranking == 3 && numberOfWorkouts > 2) {
            return resources.getString(R.string.achievement_route_third)
        }
    }
    return null
}
