package com.stt.android.analytics

import android.content.Context
import android.content.Intent
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.stt.android.di.ApplicationId
import com.stt.android.utils.STTConstants
import javax.inject.Inject

class AnalyticsUUIDUpdater
@Inject constructor(
    private val context: Context,
    private val emarsysAnalytics: EmarsysAnalytics,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    @ApplicationId private val appId: String
) {
    fun updateUUID(analyticsUUID: String) {
        FirebaseCrashlytics.getInstance().setUserId(analyticsUUID)
        emarsysAnalytics.setUserId()
        amplitudeAnalyticsTracker.setUUID(analyticsUUID)
        Firebase.crashlytics.setUserId(analyticsUUID)
        val intent = Intent(STTConstants.BroadcastActions.ANALYTICS_UUID_CHANGED)
        intent.setPackage(appId)
        intent.putExtra(STTConstants.ExtraKeys.ANALYTICS_UUID, analyticsUUID)
        context.sendBroadcast(intent)
    }
}
