package com.stt.android.analytics

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class AppOpenAnalyticsActivity : AppCompatActivity() {

    @Inject
    lateinit var analytics: AppOpenAnalytics

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val sourceType = intent.getStringExtra(EXTRA_SOURCE_TYPE)
        val sourceTypeDetail = intent.getStringExtra(EXTRA_SOURCE_TYPE_DETAIL)
        if (sourceType != null && sourceTypeDetail != null) {
            analytics.trackEvent(sourceType, sourceTypeDetail)
        }
        finish()
    }

    companion object {
        private const val EXTRA_SOURCE_TYPE = "extra:source_type"
        private const val EXTRA_SOURCE_TYPE_DETAIL = "extra:source_type_detail"

        fun newStartIntent(
            context: Context,
            sourceType: String,
            sourceTypeDetail: String,
        ) = Intent(context, AppOpenAnalyticsActivity::class.java).apply {
            putExtra(EXTRA_SOURCE_TYPE, sourceType)
            putExtra(EXTRA_SOURCE_TYPE_DETAIL, sourceTypeDetail)
        }
    }
}
