package com.stt.android.analytics

import android.content.SharedPreferences
import androidx.core.content.edit
import com.stt.android.analytics.AnalyticsEvent.APP_OPEN
import com.stt.android.analytics.AnalyticsEventProperty.APP_OPEN_IS_FIRST_TIME
import com.stt.android.analytics.AnalyticsEventProperty.APP_OPEN_RESUME_FROM_BACKGROUND
import com.stt.android.analytics.AnalyticsEventProperty.APP_OPEN_SOURCE_TYPE
import com.stt.android.analytics.AnalyticsEventProperty.APP_OPEN_SOURCE_TYPE_DETAIL
import com.stt.android.analytics.AnalyticsEventProperty.APP_OPEN_WATCH_MODEL
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.eventtracking.EventTracker
import com.stt.android.logs.VisibleActivityTracker
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_APP_OPEN_FIRST_TIME
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import timber.log.Timber

abstract class BaseAppOpenAnalytics(
    private val currentUserController: CurrentUserController,
    private val eventTracker: EventTracker,
    private val sharedPreferences: SharedPreferences,
    private val visibleActivityTracker: VisibleActivityTracker,
    private val dispatchers: CoroutinesDispatchers,
) {

    abstract suspend fun fetchWatchName(): String?

    @OptIn(DelicateCoroutinesApi::class)
    fun trackEvent(sourceType: String, sourceTypeDetail: String) {
        // Only track app open event if user is logged in
        if (!currentUserController.isLoggedIn) return

        GlobalScope.launch(dispatchers.io) {
            val firstTime = runCatching {
                sharedPreferences.getBoolean(KEY_APP_OPEN_FIRST_TIME, true).also {
                    if (it) {
                        sharedPreferences.edit { putBoolean(KEY_APP_OPEN_FIRST_TIME, false) }
                    }
                }
            }.getOrElse { false }
            val resumeFromBackground = !visibleActivityTracker.isForeground
            Timber.d("App Open: %s, %s, %s", sourceType, sourceTypeDetail, resumeFromBackground)
            eventTracker.trackEvent(
                APP_OPEN,
                buildMap {
                    fetchWatchName()?.let { put(APP_OPEN_WATCH_MODEL, it) }
                    put(APP_OPEN_IS_FIRST_TIME, firstTime.asString())
                    put(APP_OPEN_SOURCE_TYPE, sourceType)
                    put(APP_OPEN_SOURCE_TYPE_DETAIL, sourceTypeDetail)
                    put(APP_OPEN_RESUME_FROM_BACKGROUND, resumeFromBackground.asString())
                },
            )
        }
    }

    private fun Boolean.asString() = if (this) "True" else "False"
}
