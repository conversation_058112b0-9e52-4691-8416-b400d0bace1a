package com.stt.android.analytics

import com.stt.android.analytics.AnalyticsEventProperty.ACTIVITY_TYPE
import com.stt.android.analytics.AnalyticsEventProperty.CALORIES
import com.stt.android.analytics.AnalyticsEventProperty.CO2_EMISSIONS_REDUCED
import com.stt.android.analytics.AnalyticsEventProperty.DISTANCE_FROM_START_TO_END
import com.stt.android.analytics.AnalyticsEventProperty.DISTANCE_IN_METERS
import com.stt.android.analytics.AnalyticsEventProperty.DURATION_IN_MINUTES
import com.stt.android.analytics.AnalyticsEventProperty.HR_AVERAGE
import com.stt.android.analytics.AnalyticsEventProperty.HR_MAX
import com.stt.android.analytics.AnalyticsEventProperty.PUBLIC_TAG
import com.stt.android.analytics.AnalyticsEventProperty.TAG_ADD_METHOD
import com.stt.android.analytics.AnalyticsEventProperty.TAG_TYPE
import com.stt.android.analytics.AnalyticsEventProperty.TAG_TYPE_PUBLIC
import com.stt.android.analytics.AnalyticsEventProperty.WORKOUT_START_TIME
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.data.TimeUtils
import com.stt.android.data.toEndOfYearMilli
import com.stt.android.data.toStartOfYearMilli
import com.stt.android.domain.user.autoCommuteTaggingEnabled
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.home.dashboardnew.commute.GetCommuteWorkoutHeadersUseCase
import kotlinx.coroutines.flow.first
import timber.log.Timber
import java.time.LocalDate
import java.time.Year
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.roundToInt
import kotlin.math.roundToLong

@Singleton
class DefaultTagsAnalytics @Inject constructor(
    private val emarsysAnalytics: EmarsysAnalytics,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    private val userSettingsController: UserSettingsController,
    private val getCommuteWorkoutHeadersUseCase: GetCommuteWorkoutHeadersUseCase,
    private val currentUserController: CurrentUserController,
) : TagsAnalytics {
    override fun trackWorkoutTagAdded(
        suuntoTag: SuuntoTag,
        tagAddMethod: String,
        workoutHeader: WorkoutHeader
    ) {
        Timber.v("Sending Amplitude and Braze events: WorkoutTagAdded")
        buildCommonAnalyticsProperties(suuntoTag, workoutHeader)
            .apply {
                put(TAG_ADD_METHOD, tagAddMethod)

                emarsysAnalytics.trackEventWithProperties(AnalyticsEvent.WORKOUT_TAG_ADDED, this.map)
                amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.WORKOUT_TAG_ADDED, this)
            }
    }

    override fun trackWorkoutTagRemoved(suuntoTag: SuuntoTag, workoutHeader: WorkoutHeader) {
        Timber.v("Sending Amplitude event: WorkoutTagRemoved")
        buildCommonAnalyticsProperties(suuntoTag, workoutHeader)
            .apply {
                workoutHeader.calculateDistanceFromStartToEndOrNull()
                    ?.let { distanceFromStartToEnd ->
                        put(
                            DISTANCE_FROM_START_TO_END,
                            distanceFromStartToEnd.roundToInt()
                        )
                    }

                amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.WORKOUT_TAG_REMOVED, this)
            }
    }

    override suspend fun trackCommuteUserProperties() {
        Timber.v("Sending Amplitude and Braze events: trackCommuteUserProperties")
        val workoutHeaders = getCommuteWorkoutHeadersUseCase(
            GetCommuteWorkoutHeadersUseCase.Params(currentUserController.username)
        ).first()

        var totalCO2EmissionsReduced = 0.0
        var totalCO2EmissionsReducedLast30Days = 0.0
        var totalCO2EmissionsReducedThisYear = 0.0
        var totalCommuteWorkoutsLast30Days = 0
        val totalCommuteWorkouts: Int = workoutHeaders.size
        val tagCommutesAutomatically: Boolean = userSettingsController.settings.autoCommuteTaggingEnabled

        val last30DaysTime = LocalDate
            .now()
            .minusDays(30)
            .atStartOfDay(ZoneId.systemDefault())
            .toInstant()
            .toEpochMilli()

        val tomorrowTime = LocalDate.now()
            .plusDays(1)
            .atStartOfDay(ZoneId.systemDefault())
            .toInstant()
            .toEpochMilli()

        val currentYear = Year.now()
        val startOfCurrentYear = currentYear.toStartOfYearMilli()
        val endOfCurrentYear = currentYear.toEndOfYearMilli()

        workoutHeaders.forEach { workout ->
            totalCO2EmissionsReduced += workout.co2EmissionsReduced
            if (workout.startTime >= last30DaysTime &&
                workout.stopTime <= tomorrowTime
            ) {
                totalCO2EmissionsReducedLast30Days += workout.co2EmissionsReduced
                totalCommuteWorkoutsLast30Days++
            }
            if (workout.startTime >= startOfCurrentYear &&
                workout.stopTime <= endOfCurrentYear
            ) {
                totalCO2EmissionsReducedThisYear += workout.co2EmissionsReduced
            }
        }

        AnalyticsProperties().apply {
            put(AnalyticsUserProperty.CO2_EMISSIONS_REDUCED, totalCO2EmissionsReduced.roundToInt())
            put(
                AnalyticsUserProperty.CO2_EMISSIONS_REDUCED_LAST_30_DAYS,
                totalCO2EmissionsReducedLast30Days.roundToInt()
            )
            put(
                "${AnalyticsUserProperty.CO2_EMISSIONS_REDUCED}$currentYear",
                totalCO2EmissionsReducedThisYear.roundToInt()
            )
            put(AnalyticsUserProperty.TOTAL_COMMUTE_WORKOUTS, totalCommuteWorkouts)
            put(
                AnalyticsUserProperty.TOTAL_COMMUTE_WORKOUTS_LAST_30_DAYS,
                totalCommuteWorkoutsLast30Days
            )
            putOnOff(AnalyticsUserProperty.TAG_COMMUTES_AUTOMATICALLY, tagCommutesAutomatically)

            amplitudeAnalyticsTracker.trackUserProperties(this)
            emarsysAnalytics.trackUserProperties(this.map)
        }
    }

    private fun buildCommonAnalyticsProperties(
        suuntoTag: SuuntoTag,
        workoutHeader: WorkoutHeader
    ) = AnalyticsProperties().apply {
        put(TAG_TYPE, TAG_TYPE_PUBLIC)
        put(PUBLIC_TAG, suuntoTag.analyticsName)
        put(ACTIVITY_TYPE, workoutHeader.activityType.simpleName)
        put(DISTANCE_IN_METERS, workoutHeader.totalDistance)
        put(DURATION_IN_MINUTES, (workoutHeader.totalTime.toFloat() / 60.0f).roundToLong())
        put(
            WORKOUT_START_TIME,
            TimeUtils.epochToLocalZonedDateTime(workoutHeader.startTime)
                .format(DateTimeFormatter.ofPattern("HH:mm"))
        )
        put(CALORIES, workoutHeader.energyConsumption)
        put(HR_MAX, workoutHeader.heartRateMax.roundToInt())
        put(HR_AVERAGE, workoutHeader.heartRateAverage.toInt())
        put(CO2_EMISSIONS_REDUCED, workoutHeader.co2EmissionsReduced)
    }
}
