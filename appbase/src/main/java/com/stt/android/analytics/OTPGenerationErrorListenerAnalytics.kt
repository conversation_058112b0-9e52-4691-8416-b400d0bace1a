package com.stt.android.analytics

import com.stt.android.remote.otp.OTPGenerationErrorListener
import javax.inject.Inject

class OTPGenerationErrorListenerAnalytics
@Inject constructor(
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
) : OTPGenerationErrorListener {

    override fun notifyOTPGeneratedError(e: Exception) {
        val properties = AnalyticsProperties().put(AnalyticsEventProperty.ERROR_CAUSE, e.message)
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.TOTP_GENERATION_ERROR, properties)
    }
}
