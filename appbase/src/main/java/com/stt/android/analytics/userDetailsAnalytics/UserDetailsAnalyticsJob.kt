package com.stt.android.analytics.userDetailsAnalytics

import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.ListenableWorker
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.controllers.CurrentUserController
import javax.inject.Inject

class UserDetailsAnalyticsJob(
    private val userDetailsAnalyticsUtil: UserDetailsAnalyticsUtil,
    private val userController: CurrentUserController,
    appContext: Context,
    params: WorkerParameters
) : CoroutineWorker(appContext, params) {
    override suspend fun doWork(): Result {
        if (userController.currentUser.isLoggedIn) {
            userDetailsAnalyticsUtil.pushLocalInfoToAnalytics(userController.currentUser.username)
        }

        return Result.success()
    }

    class Factory
    @Inject constructor(
        private val userDetailsAnalyticsUtil: UserDetailsAnalyticsUtil,
        private val userController: CurrentUserController
    ) : CoroutineWorkerAssistedFactory {
        override fun create(context: Context, params: WorkerParameters): ListenableWorker {
            return UserDetailsAnalyticsJob(
                userDetailsAnalyticsUtil,
                userController,
                context,
                params
            )
        }
    }

    companion object {
        const val TAG = "UserDetailsAnalyticsJob"

        @JvmStatic
        fun schedule(workManager: WorkManager) {
            workManager.enqueue(
                OneTimeWorkRequestBuilder<UserDetailsAnalyticsJob>()
                    .addTag(TAG)
                    .build()
            )
        }
    }
}
