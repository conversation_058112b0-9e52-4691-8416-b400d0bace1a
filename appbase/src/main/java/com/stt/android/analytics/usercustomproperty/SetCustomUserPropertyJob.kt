package com.stt.android.analytics.usercustomproperty

import android.content.Context
import androidx.work.BackoffPolicy
import androidx.work.Constraints
import androidx.work.CoroutineWorker
import androidx.work.ExistingWorkPolicy
import androidx.work.ListenableWorker
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import com.google.gson.JsonArray
import com.google.gson.JsonObject
import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.user.customproperty.CustomPropertyDataSourceImpl
import com.stt.android.remote.customdata.CustomDataRestApi
import com.stt.android.worker.ifNotAlreadyScheduled
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject

class SetCustomUserPropertyJob(
    private val customDataRestApi: CustomDataRestApi,
    private val customPropertyDataSourceImpl: CustomPropertyDataSourceImpl,
    appContext: Context,
    params: WorkerParameters
) : CoroutineWorker(appContext, params) {

    override suspend fun doWork(): Result {
        return runSuspendCatching {
            val notSyncData = customPropertyDataSourceImpl.findNotSyncData()
            val paramsJson = JsonObject()
            notSyncData.forEach {
                when (it.valueType) {
                    StringValueType.type -> paramsJson.addProperty(
                        it.key,
                        it.value
                    )
                    IntValueType.type -> paramsJson.addProperty(
                        it.key,
                        it.value.toInt()
                    )
                    BooleanValueType.type -> paramsJson.addProperty(
                        it.key,
                        it.value.toBoolean()
                    )
                    FloatValueType.type -> paramsJson.addProperty(
                        it.key,
                        it.value.toFloat()
                    )
                    LongValueType.type -> paramsJson.addProperty(
                        it.key,
                        it.value.toLong()
                    )
                    ArrayValueType.type -> {
                        val splitValue = it.value.split(",")
                        val valueJsonArray = JsonArray()
                        splitValue.forEach { v ->
                            valueJsonArray.add(v)
                        }
                        paramsJson.add(it.key, valueJsonArray)
                    }
                }
            }
            Timber.d("sync user custom property to backend:$paramsJson")
            val result = customDataRestApi.setCustomData(
                paramsJson.toString().toRequestBody(jsonContentType)
            )
            if (result.payload == true) {
                notSyncData.forEach {
                    customPropertyDataSourceImpl.updateData(it.copy(isSynced = true))
                }
            }
            Result.success()
        }.getOrElse { e ->
            Timber.w(e, "set user custom property to backend fail")
            Result.success()
        }
    }

    class Factory
    @Inject constructor(
        private val customDataRestApi: CustomDataRestApi,
        private val customPropertyDataSourceImpl: CustomPropertyDataSourceImpl
    ) : CoroutineWorkerAssistedFactory {
        override fun create(context: Context, params: WorkerParameters): ListenableWorker {
            return SetCustomUserPropertyJob(
                customDataRestApi,
                customPropertyDataSourceImpl,
                context,
                params
            )
        }
    }

    companion object {
        const val TAG = "SetCustomUserPropertyJob"
        val jsonContentType = "application/json; charset=utf-8".toMediaType()

        // delay task,one minute
        private const val delayTime = 1L

        @JvmStatic
        fun schedule(workManager: WorkManager) {
            workManager.ifNotAlreadyScheduled(TAG) {
                workManager.enqueueUniqueWork(
                    TAG,
                    ExistingWorkPolicy.APPEND_OR_REPLACE,
                    OneTimeWorkRequestBuilder<SetCustomUserPropertyJob>()
                        .setConstraints(
                            Constraints.Builder()
                                .setRequiredNetworkType(NetworkType.CONNECTED)
                                .build()
                        )
                        .setInitialDelay(delayTime, TimeUnit.MINUTES)
                        .setBackoffCriteria(BackoffPolicy.EXPONENTIAL, 1, TimeUnit.MINUTES)
                        .build()
                )
            }
        }
    }
}
