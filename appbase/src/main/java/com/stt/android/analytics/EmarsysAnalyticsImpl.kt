package com.stt.android.analytics

import android.app.Activity
import android.app.Application
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import androidx.core.content.edit
import androidx.core.net.toUri
import com.emarsys.Emarsys
import com.emarsys.config.EmarsysConfig
import com.emarsys.mobileengage.api.event.EventHandler
import com.stt.android.BuildConfig
import com.stt.android.FeatureFlags
import com.stt.android.R
import com.stt.android.analytics.AnalyticsPropertyValue.AppOpenSourceType.PUSH_MESSAGE
import com.stt.android.analytics.usercustomproperty.ArrayValueType
import com.stt.android.analytics.usercustomproperty.BooleanValueType
import com.stt.android.analytics.usercustomproperty.CustomUserPropertySynchronizer
import com.stt.android.analytics.usercustomproperty.FloatValueType
import com.stt.android.analytics.usercustomproperty.IntValueType
import com.stt.android.analytics.usercustomproperty.LongValueType
import com.stt.android.analytics.usercustomproperty.StringValueType
import com.stt.android.controllers.CurrentUserController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.di.EmarsysCustomAttributePreferences
import com.stt.android.di.initializer.AppInitializerPostTermsApproval
import com.stt.android.domain.refreshable.Refreshable
import com.stt.android.launcher.DeepLinkIntentBuilder
import com.stt.android.remote.emarsys.EmarsysRestApi
import com.stt.android.utils.CustomTabsUtils
import com.stt.android.utils.STTConstants
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import org.json.JSONObject
import timber.log.Timber
import java.util.Locale
import java.util.concurrent.atomic.AtomicBoolean
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.CoroutineContext

@Singleton
class EmarsysAnalyticsImpl
@Inject constructor(
    private val app: Application,
    private val featureFlags: FeatureFlags,
    @EmarsysCustomAttributePreferences private val emarsysSharedPreferences: SharedPreferences,
    private val customUserPropertySynchronizer: CustomUserPropertySynchronizer,
    private val emarsysRestApi: EmarsysRestApi,
    private val currentUserController: CurrentUserController,
    private val intentBuilder: DeepLinkIntentBuilder,
    private val appOpenAnalytics: AppOpenAnalytics,
) : EmarsysAnalytics, AppInitializerPostTermsApproval, CoroutineScope, Refreshable, EventHandler {

    override val coroutineContext: CoroutineContext = Dispatchers.IO + SupervisorJob() +
        CoroutineExceptionHandler { _, t -> Timber.w(t, "Error in EmarsysAnalyticsImpl scope") }

    private val isInitialized = AtomicBoolean(false)

    override fun init(app: Application) {
        init(app, null)
    }

    private fun init(app: Application, onInitialized: (() -> Unit)?) {
        if (isEnabled() && !isInitialized.get()) {
            launch {
                var shouldCallOnInitialized = false
                synchronized(isInitialized) {
                    if (!isInitialized.get()) {
                        configEmarsys(app)
                        shouldCallOnInitialized = true
                        isInitialized.set(true)
                    }
                }

                if (shouldCallOnInitialized) {
                    onInitialized?.invoke()
                }
            }
        }
    }

    override fun setBirthYear(year: Int) {
        trackIntUserProperty(AnalyticsUserProperty.BIRTH_YEAR, year)
    }

    override fun trackStringUserProperty(userProperty: String, value: String) {
        customUserPropertySynchronizer.syncCustomUserProperty(
            userProperty,
            value,
            StringValueType
        )
    }

    override suspend fun refresh() {
        // let's try to reset contactId when refreshing, if needed
        setUserId()
    }

    override fun trackIntUserProperty(userProperty: String, value: Int) {
        customUserPropertySynchronizer.syncCustomUserProperty(
            userProperty,
            value.toString(),
            IntValueType
        )
    }

    override fun setUserId() {
        // userId might be set before emarsys is initialized, so let's make sure it is
        if (isInitialized.get()) {
            setUserIdAfterInitialization()
        } else {
            init(app, ::setUserIdAfterInitialization)
        }
    }

    private fun setUserIdAfterInitialization() {
        if (isEnabled()) {
            launch {
                runSuspendCatching {
                    val contactId = emarsysRestApi.fetchEmarsysContactId().payloadOrThrow()
                    // if contactId is updated, Emarsys need setContact again
                    if (getCurrentEmarsysId() != contactId) {
                        // it seems like emarsys doesn't like to set a new contact without clearing the
                        // previous one (even though in theory this should be supported)
                        runSuspendCatching {
                            Emarsys.clearContact { e -> setContactAfterClearing(e, contactId) }
                            contactId
                        }.onFailure { setContactAfterClearing(it, contactId) }
                    } else {
                        Result.success(null)
                    }
                }.onSuccess {
                    it.getOrNull()?.let { value ->
                        emarsysSharedPreferences.edit {
                            putBoolean(
                                STTConstants.EmarsysCustomAttributePreferences.SETCONTACT,
                                true
                            )
                                // saving contact id to shared preferences for debugging purposes and comparing with new emarysys id
                                .putString(
                                    STTConstants.EmarsysCustomAttributePreferences.CONTACT_ID,
                                    value
                                )
                        }
                    }
                }.onFailure {
                    Timber.w(it, "Failing to fetch and store emarsys contactId")
                }
            }
        }
    }

    private fun setContactAfterClearing(errorWhileClearing: Throwable?, contactId: String) {
        if (errorWhileClearing != null) {
            Timber.i(errorWhileClearing, "Error clearing emarsys contact, can be ignored")
        }
        val contactFieldId = app.resources.getInteger(R.integer.emarsys_contact_id)
        Emarsys.setContact(contactFieldId, contactId)
        Timber.d("Setting emarsys contactId: $contactId")
    }

    private fun getCurrentEmarsysId(): String {
        return emarsysSharedPreferences.getString(
            STTConstants.EmarsysCustomAttributePreferences.CONTACT_ID,
            ""
        ) ?: ""
    }

    override fun logout() {
        if (isEnabled() && isInitialized.get()) {
            Emarsys.clearContact()
            emarsysSharedPreferences.edit {
                putBoolean(STTConstants.EmarsysCustomAttributePreferences.SETCONTACT, false)
                    .putString(STTConstants.EmarsysCustomAttributePreferences.CONTACT_ID, "")
            }
        }
    }

    override fun trackBooleanUserProperty(userProperty: String, value: Boolean) {
        customUserPropertySynchronizer.syncCustomUserProperty(
            userProperty,
            value.toString(),
            BooleanValueType
        )
    }

    /**
     * Sends custom user attribute array to Braze servers
     *
     * @param userProperty name of a custom userProperty
     * @param values array of values for userProperty
     */
    override fun trackArrayUserProperty(userProperty: String, values: Array<String?>) {
        if (values.isNotEmpty()) {
            val valuesSb = StringBuilder()
            values.forEachIndexed { index, s ->
                valuesSb.append(s)
                if (index < values.lastIndex) {
                    valuesSb.append(",")
                }
            }
            customUserPropertySynchronizer.syncCustomUserProperty(
                userProperty,
                valuesSb.toString(),
                ArrayValueType
            )
        }
    }

    /**
     * Tracks list of user properties. Sending these properties to Braze servers is handled by
     * Braze SDK which tries to flush the data when Braze session is closed or a new one is
     * started
     *
     * @param properties list of user properties
     */
    override fun trackUserProperties(properties: Map<String, Any?>) {
        if (isEnabled() && isInitialized.get() && properties.isNotEmpty()) {
            setUserProperties(properties)
        }
    }

    private fun setUserProperties(properties: Map<String, Any?>) {
        for ((key, value) in properties) {
            when (value) {
                is String -> customUserPropertySynchronizer.syncCustomUserProperty(
                    key,
                    value,
                    StringValueType
                )

                is Int -> customUserPropertySynchronizer.syncCustomUserProperty(
                    key,
                    value.toString(),
                    IntValueType
                )

                is Boolean -> customUserPropertySynchronizer.syncCustomUserProperty(
                    key,
                    value.toString(),
                    BooleanValueType
                )

                is Float -> customUserPropertySynchronizer.syncCustomUserProperty(
                    key,
                    value.toString(),
                    FloatValueType
                )

                is Long -> customUserPropertySynchronizer.syncCustomUserProperty(
                    key,
                    value.toString(),
                    LongValueType
                )

                is List<*> -> trackArrayUserProperty(
                    key,
                    value.mapNotNull { it as? String }.toTypedArray()
                )
            }
        }
    }

    override fun trackEvent(@AnalyticsEvent.EventName event: String) {
        Timber.d("Emarsys Analytics：%s", event)
        if (isEnabled() && isInitialized.get()) {
            Emarsys.trackCustomEvent(event, null) { throwable ->
                if (throwable == null) {
                    Timber.d("Emarsys track event($event) success")
                } else {
                    Timber.w(throwable, "track event($event) fail:${throwable.message}")
                }
            }
        }
    }

    override fun trackEventWithProperties(
        @AnalyticsEvent.EventName event: String,
        properties: Map<String, Any?>
    ) {
        if (isEnabled() && isInitialized.get() && properties.isNotEmpty()) {
            Timber.d("Emarsys Analytics with properties：%s", event)
            Emarsys.trackCustomEvent(event, properties.toMapString()) {
                it?.let {
                    Timber.w(it, "track event($event) fail:${it.message}")
                }
            }
        }
    }

    override fun isEnabled(): Boolean {
        return featureFlags.isEmarsysEnabled
    }

    private fun configEmarsys(app: Application) {
        val config = EmarsysConfig(
            app,
            app.resources.getString(R.string.emarsys_application_code),
            verboseConsoleLoggingEnabled = BuildConfig.DEBUG
        )
        Emarsys.setup(config)
        Emarsys.inApp.setEventHandler(this)
        Emarsys.push.apply {
            setNotificationEventHandler(this@EmarsysAnalyticsImpl)
            setSilentMessageEventHandler(this@EmarsysAnalyticsImpl)
        }
        Timber.d("init Emarsys")
    }

    override fun handleEvent(context: Context, eventName: String, payload: JSONObject?) {
        runCatching {
            when (eventName) {
                DEEP_LINK_EVENT_NAME -> {
                    val url = payload?.optString(DEEP_LINK_PAYLOAD_URL)
                    if (url.isNullOrEmpty()) return
                    handleDeeplinkIntent(context, url)
                }
                // application event
                else -> {
                    // payload: {"url":"https://www.baidu.com","name":"customEvent"}
                    val externalUrl =
                        payload?.optString(STTConstants.EmarsysConstant.APPLICATION_EXTERNAL_URL_NAME)
                    // payload: {"deeplink":"","name":"customEvent"}
                    val deepLinkUrl =
                        payload?.optString(STTConstants.EmarsysConstant.APPLICATION_DEEPLINK_URL_NAME)
                    if (!externalUrl.isNullOrEmpty()) {
                        CustomTabsUtils.launchCustomTab(context, externalUrl.toUri())
                    }
                    if (!deepLinkUrl.isNullOrEmpty()) {
                        handleDeeplinkIntent(context, deepLinkUrl)
                    }
                    val customEventName =
                        payload?.optString(STTConstants.EmarsysConstant.APPLICATION_CUSTOM_EVENT_NAME)
                    if (!customEventName.isNullOrEmpty()) {
                        trackEvent(customEventName)
                        appOpenAnalytics.trackEvent(PUSH_MESSAGE, "Emarsys_$customEventName")
                    }
                }
            }
        }.onFailure {
            Timber.w(it, "Emarsys event cannot be processed.")
        }
    }

    private fun handleDeeplinkIntent(context: Context, deeplink: String) {
        val intent = getIntentFromDeeplinkUrl(context, deeplink) ?: return
        if (context !is Activity) {
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }
        context.startActivity(intent)
    }

    private fun getIntentFromDeeplinkUrl(context: Context, url: String): Intent? {
        val uri = url.toUri()
        val fragmentOrPathParts = intentBuilder.getFragmentsOrPathParts(uri)
        val type = fragmentOrPathParts.getOrNull(1)?.lowercase(Locale.ROOT) ?: ""
        return intentBuilder.getDeepLinkIntent(
            context,
            uri,
            currentUserController,
            fragmentOrPathParts,
            type
        )
    }

    companion object {
        private const val DEEP_LINK_EVENT_NAME = "DeepLink"
        private const val DEEP_LINK_PAYLOAD_URL = "url"
    }
}
