package com.stt.android.analytics.usercustomproperty

import androidx.work.WorkManager
import com.stt.android.coroutines.LoggingExceptionHandler
import com.stt.android.domain.user.CustomPropertyDataSource
import com.stt.android.domain.user.DomainCustomProperty
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.CoroutineContext

/**
 * Emarsys: Synchronize custom user properties to the backend
 */
@Singleton
class CustomUserPropertySynchronizer @Inject constructor(
    private val customPropertyDataSource: CustomPropertyDataSource,
    private val workManager: dagger.Lazy<WorkManager>,
) : CoroutineScope {

    override val coroutineContext: CoroutineContext
        get() = Dispatchers.Default + SupervisorJob() + LoggingExceptionHandler

    fun syncCustomUserProperty(key: String, value: String, valueType: ValueType) {
        launch {
            val previous = customPropertyDataSource.findDataByKey(key)
            if (previous == null) {
                customPropertyDataSource.saveData(
                    DomainCustomProperty(
                        key = key,
                        value = value,
                        valueType = valueType.type,
                        isSynced = false
                    )
                )
                SetCustomUserPropertyJob.schedule(workManager.get())
            } else if (previous.value != value) {
                // value changed
                customPropertyDataSource.updateData(
                    previous.copy(
                        value = value,
                        isSynced = false
                    )
                )
                SetCustomUserPropertyJob.schedule(workManager.get())
            }
        }
    }
}

sealed class ValueType(val type: String)
object StringValueType : ValueType("String")
object IntValueType : ValueType("Int")
object FloatValueType : ValueType("Float")
object LongValueType : ValueType("Long")
object BooleanValueType : ValueType("Boolean")
object ArrayValueType : ValueType("Array")
