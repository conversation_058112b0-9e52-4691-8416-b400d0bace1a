package com.stt.android.analytics

import android.app.Application
import com.google.android.play.core.review.ReviewManager
import com.google.android.play.core.review.ReviewManagerFactory
import com.google.android.play.core.review.testing.FakeReviewManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class ReviewManagerModule {

    companion object {

        @Provides
        fun provideReviewManager(app: Application): ReviewManager =
            if (BuildConfig.DEBUG) {
                FakeReviewManager(app)
            } else {
                ReviewManagerFactory.create(app)
            }
    }
}
