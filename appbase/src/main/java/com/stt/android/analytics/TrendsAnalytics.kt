package com.stt.android.analytics

import com.stt.android.domain.diary.models.DiaryPage
import com.stt.android.domain.diary.models.GraphDataType
import com.stt.android.domain.diary.models.GraphTimeRange

interface TrendsAnalytics {
    fun trackTrendsScreens(
        diaryPage: DiaryPage,
        timeRange: GraphTimeRange,
        source: String?
    )

    fun trackTrendsViewChanged(
        diaryPage: DiaryPage,
        previousDiaryPage: DiaryPage?,
        timeRange: GraphTimeRange
    )

    fun trackTrendsGraphSetupChanged(
        diaryPage: DiaryPage,
        primaryGraphTypeChanged: Boolean,
        primaryGraphType: GraphDataType?,
        secondaryGraphType: GraphDataType?,
        timeRange: GraphTimeRange
    )

    fun trackTrendsGraphPeriodChanged(
        diaryPage: DiaryPage,
        primaryGraphType: GraphDataType?,
        secondaryGraphType: GraphDataType?,
        timeRange: GraphTimeRange
    )

    fun trackTrendsGraphValueTapped(
        diaryPage: DiaryPage,
        primaryGraphType: GraphDataType?,
        secondaryGraphType: GraphDataType?,
        timeRange: GraphTimeRange
    )

    fun trackDiaryGraphValueTapped(analyticsProperties: AnalyticsProperties)

    fun getPrimaryGraphDataType(diaryPage: DiaryPage): GraphDataType?
    fun getSecondaryGraphDataType(diaryPage: DiaryPage): GraphDataType?
}
