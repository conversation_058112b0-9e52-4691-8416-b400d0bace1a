package com.stt.android.analytics

import com.stt.android.analytics.AnalyticsUserProperty.SUUNTO_SIGN_UP_AND_OFFERS_SUBSCRIPTION
import com.stt.android.data.marketingconsent.MarketingConsentAnalyticsTracker
import com.stt.android.exceptions.remote.HttpException
import javax.inject.Inject

class MarketingConsentAnalyticsTrackerImpl @Inject constructor(
    private val emarsysAnalytics: EmarsysAnalytics,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
) : MarketingConsentAnalyticsTracker {
    override fun trackOnSubscribed(isSubscribed: <PERSON><PERSON><PERSON>, context: String?) {
        val analyticsProperties = AnalyticsProperties()
        analyticsProperties.putYesNo(
            AnalyticsEventProperty.NEWS_AND_OFFERS_SUBSCRIBED,
            isSubscribed
        ).put(
            AnalyticsEventProperty.NEWS_AND_OFFERS_CONTEXT,
            context
        )
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.SUUNTO_SIGN_UP_AND_OFFERS_SUBSCRIPTION,
            analyticsProperties
        )
        emarsysAnalytics.trackEventWithProperties(
            AnalyticsEvent.SUUNTO_SIGN_UP_AND_OFFERS_SUBSCRIPTION,
            analyticsProperties.map
        )
        amplitudeAnalyticsTracker.trackUserProperty(SUUNTO_SIGN_UP_AND_OFFERS_SUBSCRIPTION, true)
    }

    override fun trackOnError(error: Throwable?) {
        val analyticsProperties = AnalyticsProperties()
        when (error) {
            is HttpException ->
                amplitudeAnalyticsTracker.trackEvent(
                    AnalyticsEvent.SUUNTO_SIGN_UP_AND_OFFERS_ERROR,
                    analyticsProperties.put(AnalyticsEventProperty.ERROR_TYPE, error.code)
                )
            else -> amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.SUUNTO_SIGN_UP_AND_OFFERS_ERROR)
        }
    }
}
