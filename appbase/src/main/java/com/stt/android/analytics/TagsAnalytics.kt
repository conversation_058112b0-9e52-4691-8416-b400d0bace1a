package com.stt.android.analytics

import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.tag.SuuntoTag

interface TagsAnalytics {
    fun trackWorkoutTagAdded(
        suuntoTag: SuuntoTag,
        tagAddMethod: String,
        workoutHeader: WorkoutHeader,
    )

    fun trackWorkoutTagRemoved(suuntoTag: SuuntoTag, workoutHeader: WorkoutHeader)

    suspend fun trackCommuteUserProperties()
}
