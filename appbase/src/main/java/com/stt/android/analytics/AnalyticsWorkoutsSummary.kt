package com.stt.android.analytics

import android.util.SparseIntArray
import kotlin.jvm.JvmStatic

data class AnalyticsWorkoutsSummary internal constructor(
    val currentYear: Int,
    val previousYear: Int,
    val totalWorkouts: Int,
    val totalWorkoutsCurrentYear: Int,
    val totalWorkoutsPreviousYear: Int,
    val totalDurationInMinutes: Int,
    val totalDurationInMinutesCurrentYear: Int,
    val totalDurationInMinutesPreviousYear: Int,
    val totalPictures: Int,
    val totalComments: Int,
    val totalLikes: Int,
    val totalPrivate: Int,
    val totalPublic: Int,
    val totalForFollowers: Int,
    val totalWithDescription: Int,
    val workoutCountsForPreviousDays: SparseIntArray,
    val totalWorkoutsCurrentWeek: Int,
    val totalWorkoutsCurrentMonth: Int
) {
    fun toBuilder(): Builder = Builder(
        currentYear = currentYear,
        previousYear = previousYear,
        totalWorkouts = totalWorkouts,
        totalWorkoutsCurrentYear = totalWorkoutsCurrentYear,
        totalWorkoutsPreviousYear = totalWorkoutsPreviousYear,
        totalDurationInMinutes = totalDurationInMinutes,
        totalDurationInMinutesCurrentYear = totalDurationInMinutesCurrentYear,
        totalDurationInMinutesPreviousYear = totalDurationInMinutesPreviousYear,
        totalPictures = totalPictures,
        totalComments = totalComments,
        totalLikes = totalLikes,
        totalPrivate = totalPrivate,
        totalPublic = totalPublic,
        totalForFollowers = totalForFollowers,
        totalWithDescription = totalWithDescription,
        workoutCountsForPreviousDays = workoutCountsForPreviousDays,
        totalWorkoutsCurrentWeek = totalWorkoutsCurrentWeek,
        totalWorkoutsCurrentMonth = totalWorkoutsCurrentMonth
    )

    @Suppress("LongParameterList")
    class Builder internal constructor(
        private var currentYear: Int = 0,
        private var previousYear: Int = 0,
        private var totalWorkouts: Int = 0,
        private var totalWorkoutsCurrentYear: Int = 0,
        private var totalWorkoutsPreviousYear: Int = 0,
        private var totalDurationInMinutes: Int = 0,
        private var totalDurationInMinutesCurrentYear: Int = 0,
        private var totalDurationInMinutesPreviousYear: Int = 0,
        private var totalPictures: Int = 0,
        private var totalComments: Int = 0,
        private var totalLikes: Int = 0,
        private var totalPrivate: Int = 0,
        private var totalPublic: Int = 0,
        private var totalForFollowers: Int = 0,
        private var totalWithDescription: Int = 0,
        private var workoutCountsForPreviousDays: SparseIntArray? = null,
        private var totalWorkoutsCurrentWeek: Int = 0,
        private var totalWorkoutsCurrentMonth: Int = 0
    ) {
        fun currentYear(value: Int): Builder = apply { this.currentYear = value }

        fun previousYear(value: Int): Builder = apply { this.previousYear = value }

        fun totalWorkouts(value: Int): Builder = apply { this.totalWorkouts = value }

        fun totalWorkoutsCurrentYear(value: Int): Builder =
            apply { this.totalWorkoutsCurrentYear = value }

        fun totalWorkoutsPreviousYear(value: Int): Builder =
            apply { this.totalWorkoutsPreviousYear = value }

        fun totalDurationInMinutes(value: Int): Builder = apply {
            this.totalDurationInMinutes =
                value
        }

        fun totalDurationInMinutesCurrentYear(value: Int): Builder =
            apply { this.totalDurationInMinutesCurrentYear = value }

        fun totalDurationInMinutesPreviousYear(value: Int): Builder =
            apply { this.totalDurationInMinutesPreviousYear = value }

        fun totalPictures(value: Int): Builder = apply { this.totalPictures = value }

        fun totalComments(value: Int): Builder = apply { this.totalComments = value }

        fun totalLikes(value: Int): Builder = apply { this.totalLikes = value }

        fun totalPrivate(value: Int): Builder = apply { this.totalPrivate = value }

        fun totalPublic(value: Int): Builder = apply { this.totalPublic = value }

        fun totalForFollowers(value: Int): Builder = apply { this.totalForFollowers = value }

        fun totalWithDescription(value: Int): Builder = apply {
            this.totalWithDescription =
                value
        }

        fun workoutCountsForPreviousDays(values: SparseIntArray): Builder =
            apply { this.workoutCountsForPreviousDays = values }

        fun totalWorkoutCurrentWeek(value: Int): Builder = apply {
            this.totalWorkoutsCurrentWeek = value
        }

        fun totalWorkoutCurrentMonth(value: Int): Builder = apply {
            this.totalWorkoutsCurrentMonth = value
        }

        fun build(): AnalyticsWorkoutsSummary =
            AnalyticsWorkoutsSummary(
                currentYear = currentYear,
                previousYear = previousYear,
                totalWorkouts = totalWorkouts,
                totalWorkoutsCurrentYear = totalWorkoutsCurrentYear,
                totalWorkoutsPreviousYear = totalWorkoutsPreviousYear,
                totalDurationInMinutes = totalDurationInMinutes,
                totalDurationInMinutesCurrentYear = totalDurationInMinutesCurrentYear,
                totalDurationInMinutesPreviousYear = totalDurationInMinutesPreviousYear,
                totalPictures = totalPictures,
                totalComments = totalComments,
                totalLikes = totalLikes,
                totalPrivate = totalPrivate,
                totalPublic = totalPublic,
                totalForFollowers = totalForFollowers,
                totalWithDescription = totalWithDescription,
                workoutCountsForPreviousDays = workoutCountsForPreviousDays
                    ?: error("workoutCountsForPreviousDays == null"),
                totalWorkoutsCurrentWeek = totalWorkoutsCurrentWeek,
                totalWorkoutsCurrentMonth = totalWorkoutsCurrentMonth
            )
    }

    companion object {
        @JvmStatic
        fun builder(): Builder = Builder()
    }
}
