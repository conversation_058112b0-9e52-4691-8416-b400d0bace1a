package com.stt.android.analytics

import android.app.Activity
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentSender
import android.net.Uri
import android.os.Build
import androidx.core.app.ShareCompat
import com.stt.android.R
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.eventtracking.EventTracker
import com.stt.android.multimedia.sportie.SportieHelper
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class ShareBroadcastReceiver : BroadcastReceiver() {

    @Inject
    lateinit var emarsysAnalytics: EmarsysAnalytics

    @Inject
    lateinit var firebaseAnalyticsTracker: FirebaseAnalyticsTracker

    @Inject
    lateinit var amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker

    @Inject
    lateinit var eventTracker: EventTracker

    override fun onReceive(context: Context, intent: Intent) {
        val target = intent.getParcelableExtra<ComponentName>(Intent.EXTRA_CHOSEN_COMPONENT)
        val pm = context.packageManager

        val shareTarget = try {
            pm.getApplicationInfo(target?.packageName ?: "", 0)
        } catch (ignored: Exception) {
            null
        }?.let { pm.getApplicationLabel(it) } ?: "(unknown)"

        val analyticsEvent: String = intent.getStringExtra(ANALYTICS_EVENT) ?: "Unknown"
        // annual report and other h5 share
        if (analyticsEvent == AnalyticsEvent.H5SHARE) {
            intent.getBundleExtra(EXTRA_ANALYTICS)?.let { analysisData ->
                eventTracker.trackEvent(analyticsEvent, analysisData.toMap().apply {
                    put(
                        AnalyticsEventProperty.SHARE_TARGET,
                        shareTarget
                    )
                })
            }
        } else {
            val properties =
                intent.getBundleExtra(EXTRA_ANALYTICS)?.toAnalyticsProperties()
                    ?: AnalyticsProperties()
            properties.put(AnalyticsEventProperty.TARGET, shareTarget)
            amplitudeAnalyticsTracker.trackEvent(analyticsEvent, properties)

            emarsysAnalytics.trackEventWithProperties(analyticsEvent, properties.map)

            if (analyticsEvent == AnalyticsEvent.SHARE_WORKOUT) {
                firebaseAnalyticsTracker.trackEvent(analyticsEvent, properties)
            }
        }
    }

    companion object {

        const val EXTRA_ANALYTICS = "EXTRA_ANALYTICS"
        const val ANALYTICS_EVENT = "ANALYTICS_EVENT"

        fun shareWorkoutImage(
            activity: Activity,
            uri: Uri,
            workoutHeader: WorkoutHeader,
            analyticsProperties: AnalyticsProperties
        ) {
            val shareIntent =
                SportieHelper.buildShareIntent(activity, activity.resources, workoutHeader, uri)
            shareWorkout(activity, shareIntent, analyticsProperties)
        }

        fun shareWorkoutVideo(
            activity: Activity,
            videoUri: Uri,
            analyticsProperties: AnalyticsProperties
        ) {
            val shareIntent = Intent(Intent.ACTION_SEND).apply {
                setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                setDataAndType(videoUri, activity.contentResolver.getType(videoUri))
                putExtra(Intent.EXTRA_STREAM, videoUri)
            }
            shareWorkout(activity, shareIntent, analyticsProperties)
        }

        fun shareSummary(
            activity: Activity,
            shareIntent: Intent,
            analyticsProperties: AnalyticsProperties
        ) {
            val receiverIntent = Intent(activity, ShareBroadcastReceiver::class.java).apply {
                putExtra(EXTRA_ANALYTICS, analyticsProperties.toBundle())
                putExtra(ANALYTICS_EVENT, AnalyticsEvent.SHARE_SUMMARY)
            }

            val pendingIntent = PendingIntent.getBroadcast(
                activity,
                0,
                receiverIntent,
                getPendingIntentFlags()
            )

            val chooserIntent = Intent.createChooser(
                shareIntent,
                activity.resources.getString(R.string.dialog_title_share),
                pendingIntent.intentSender
            )

            activity.startActivity(chooserIntent)
        }

        fun shareWorkout(
            activity: Activity,
            shareIntent: Intent,
            analyticsProperties: AnalyticsProperties
        ) {
            val receiverIntent = Intent(activity, ShareBroadcastReceiver::class.java).apply {
                putExtra(EXTRA_ANALYTICS, analyticsProperties.toBundle())
                putExtra(ANALYTICS_EVENT, AnalyticsEvent.SHARE_WORKOUT)
            }

            val pendingIntent = PendingIntent.getBroadcast(
                activity,
                0,
                receiverIntent,
                getPendingIntentFlags()
            )

            val chooserIntent = SportieHelper.buildChooserIntent(
                activity.resources,
                shareIntent,
                pendingIntent.intentSender
            )

            activity.startActivity(chooserIntent)
        }

        fun shareToSystem(
            activity: Activity,
            shareIntent: Intent,
            eventName: String,
            analyticsProperties: Map<String, Any>
        ) {
            val receiverIntent = Intent(activity, ShareBroadcastReceiver::class.java).apply {
                putExtra(EXTRA_ANALYTICS, analyticsProperties.toBundle())
                putExtra(ANALYTICS_EVENT, eventName)
            }

            val pendingIntent = PendingIntent.getBroadcast(
                activity,
                0,
                receiverIntent,
                getPendingIntentFlags()
            )

            val chooserIntent = SportieHelper.buildChooserIntent(
                activity.resources,
                shareIntent,
                pendingIntent.intentSender
            )

            activity.startActivity(chooserIntent)
        }

        @JvmStatic
        fun shareRoute(
            activity: Activity,
            shareLink: String,
            source: String
        ) {
            val analyticsProperties = AnalyticsProperties()
            analyticsProperties.apply {
                put(AnalyticsEventProperty.EXPORT_TYPE, AnalyticsPropertyValue.ExportType.ROUTE_GPX)
                put(AnalyticsEventProperty.SOURCE, source)
                put(AnalyticsEventProperty.RESULT, AnalyticsPropertyValue.ExportResult.OK)
            }

            val receiverIntent = Intent(activity, ShareBroadcastReceiver::class.java).apply {
                putExtra(EXTRA_ANALYTICS, analyticsProperties.toBundle())
                putExtra(ANALYTICS_EVENT, AnalyticsEvent.EXPORT_FILE)
            }

            val pendingIntent = PendingIntent.getBroadcast(
                activity,
                0,
                receiverIntent,
                getPendingIntentFlags()
            )

            val chooserIntent = buildShareChooserIntent(
                activity,
                shareLink,
                pendingIntent.intentSender
            )

            activity.startActivity(chooserIntent)
        }

        private fun getPendingIntentFlags() = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            PendingIntent.FLAG_CANCEL_CURRENT or PendingIntent.FLAG_MUTABLE
        } else {
            PendingIntent.FLAG_CANCEL_CURRENT
        }

        private fun buildShareRouteIntent(
            activity: Activity,
            routeShareLink: String
        ): Intent {
            val shareIntent = ShareCompat.IntentBuilder(activity)
                .setType("text/plain")
                .setText(routeShareLink)
                .intent
            shareIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            return shareIntent
        }

        private fun buildShareChooserIntent(
            activity: Activity,
            routeShareLink: String,
            intentSender: IntentSender
        ) = Intent.createChooser(
            buildShareRouteIntent(activity, routeShareLink),
            activity.resources.getString(R.string.dialog_title_select),
            intentSender
        )
    }
}
