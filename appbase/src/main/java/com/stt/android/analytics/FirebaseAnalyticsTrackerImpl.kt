package com.stt.android.analytics

import android.content.Context
import android.os.Bundle
import androidx.annotation.Size
import androidx.core.os.bundleOf
import com.google.firebase.analytics.FirebaseAnalytics
import com.stt.android.analytics.FirebaseAnalyticsTracker.Companion.isValidUserProperty
import com.stt.android.utils.FlavorUtils
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * All events must be between 1 and 20 letters long
 */
@Singleton
class FirebaseAnalyticsTrackerImpl @Inject constructor() : FirebaseAnalyticsTracker {
    // We are supposed to enable Firebase for China after user's approval on terms. However, we have
    // not enabled it since October 2022. Therefore, let us disable it for China.
    private val isFirebaseAnalyticsEnabled: Boolean = !FlavorUtils.isSuuntoAppChina

    private lateinit var fAnalytics: FirebaseAnalytics

    override fun init(context: Context) {
        if (::fAnalytics.isInitialized) {
            Timber.d("Tried to re-initialize FirebaseAnalyticsTracker")
            return
        }

        fAnalytics = FirebaseAnalytics.getInstance(context.applicationContext)
        fAnalytics.setAnalyticsCollectionEnabled(isFirebaseAnalyticsEnabled)
    }

    override fun trackEvent(
        @AnalyticsEvent.EventName
        @Size(min = 1L, max = 40L)
        eventName: String,
        propertyKey: String,
        propertyValue: Any?
    ) = trackEvent(eventName, bundleOf(propertyKey to propertyValue))

    override fun trackEvent(
        @AnalyticsEvent.EventName
        @Size(min = 1L, max = 40L)
        eventName: String,
        analyticsProperties: AnalyticsProperties
    ) = trackEvent(eventName, analyticsProperties.toBundle())

    override fun trackEvent(
        @AnalyticsEvent.EventName
        @Size(min = 1L, max = 40L)
        eventName: String,
        bundle: Bundle?
    ) {
        if (!::fAnalytics.isInitialized) {
            Timber.w("Tried to use FirebaseAnalyticsTracker before calling init, skipping trackEvent $eventName")
            return
        }
        if (!isFirebaseAnalyticsEnabled) return

        val parameters = bundle?.sanitiseParameters(eventName)
        val sanitisedEventName = sanitiseEventName(eventName)
        fAnalytics.logEvent(sanitisedEventName, parameters)
    }

    private fun Bundle.sanitiseParameters(eventName: String): Bundle {
        for (paramKey in keySet()) {
            @Suppress("DEPRECATION")
            val paramValue = this[paramKey]
            remove(paramKey)
            val key = sanitiseParameterKey(eventName, paramKey)
            // all google examples seem to use only String, Long and Double parameters, so let's stick to those
            when (paramValue) {
                is String -> putString(key, sanitiseParameterValue(eventName, paramValue))
                is Int -> putLong(key, paramValue.toLong())
                is Boolean -> putString(key, paramValue.toYesNo())
                is Float -> putDouble(key, paramValue.toDouble())
                is Double -> putDouble(key, paramValue)
                is Long -> putLong(key, paramValue)
                else -> putString(key, sanitiseParameterValue(eventName, paramValue.toString()))
            }
        }
        return this
    }

    private fun sanitiseEventName(eventName: String) =
        if (eventName.length > 40) {
            val shortened = eventName.take(40)
            Timber.w("Event $eventName is too long for firebase, shortened to $shortened")
            shortened
        } else {
            eventName
        }

    private fun sanitiseParameterKey(eventName: String, paramKey: String): String =
        if (paramKey.length > 40) {
            Timber.w("Event $eventName has parameter key $paramKey which is too long (max 40 characters), shortening")
            paramKey.take(40)
        } else {
            paramKey
        }

    private fun sanitiseParameterValue(eventName: String, paramValue: String): String =
        if (paramValue.length > 100) {
            Timber.w("Event $eventName has parameter value which is too long (max 100 characters), shortening")
            paramValue.take(100)
        } else {
            paramValue
        }

    override fun setUserId(userAnalyticsUUID: String) {
        if (!::fAnalytics.isInitialized) {
            Timber.w("Tried to use FirebaseAnalyticsTracker before calling init, skipping setUserId")
            return
        }
        if (!isFirebaseAnalyticsEnabled) return

        fAnalytics.setUserId(userAnalyticsUUID)
    }

    /**
     * There are max 25 custom user properties in Firebase Analytics, let's be careful which one we choose to send!
     */
    override fun trackUserProperty(userProperty: String, @Size(min = 0L, max = 36L) value: String) {
        if (!::fAnalytics.isInitialized) {
            Timber.w("Tried to use FirebaseAnalyticsTracker before calling init, skipping trackStringUserProperty $userProperty")
            return
        }
        if (!isFirebaseAnalyticsEnabled) return

        if (!isValidUserProperty(userProperty)) {
            Timber.w("User property $userProperty is not in valid format for firebase")
            return
        }
        val propertyValue = if (value.length > 36) {
            Timber.w("User property $userProperty has value which is too long (max 36 characters), shortening")
            value.take(36)
        } else {
            value
        }
        fAnalytics.setUserProperty(userProperty, propertyValue)
    }

    override fun logout() {
        if (!::fAnalytics.isInitialized) {
            Timber.w("Tried to use FirebaseAnalyticsTracker before calling init, skipping logout")
            return
        }
        fAnalytics.setUserId(null)
        fAnalytics.resetAnalyticsData()
    }
}
