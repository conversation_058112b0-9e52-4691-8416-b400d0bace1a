package com.stt.android.analytics.userDetailsAnalytics

import android.content.SharedPreferences
import androidx.core.util.forEach
import com.google.firebase.analytics.FirebaseAnalytics
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsUserProperty
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.analytics.FirebaseAnalyticsTracker
import com.stt.android.analytics.TagsAnalytics
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.di.AnalyticsSharedPreferences
import com.stt.android.home.people.PeopleController
import com.stt.android.usecases.startup.AppStatRepository
import com.stt.android.utils.STTConstants
import io.reactivex.Completable
import io.reactivex.Single
import io.reactivex.schedulers.Schedulers
import timber.log.Timber
import java.util.Locale
import javax.inject.Inject

class UserDetailsAnalyticsUtil
@Inject constructor(
    private val workoutHeaderController: WorkoutHeaderController,
    private val peopleController: PeopleController,
    @AnalyticsSharedPreferences val sharedPreferences: SharedPreferences,
    private val emarsysAnalytics: EmarsysAnalytics,
    private val firebaseAnalyticsTracker: FirebaseAnalyticsTracker,
    private val appStatRepository: AppStatRepository,
    private val tagsAnalytics: TagsAnalytics,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
) {
    suspend fun pushLocalInfoToAnalytics(sessionOwnerUsername: String) {
        val analyticsCompletables = listOf(
            loadAndPushWorkoutInfoToAnalytics(sessionOwnerUsername),
            loadAndPushFollowInfoToAnalytics()
        )

        runSuspendCatching {
            Completable.mergeDelayError(analyticsCompletables).blockingAwait()
            tagsAnalytics.trackCommuteUserProperties()
            appStatRepository.initialUserDetailsSentToAnalytics = true
        }.onFailure { e ->
            Timber.w(e, "Error occurs when pushing local info to analytics")
        }
    }

    private data class WorkoutAnalyticsValue(
        val key: String,
        val value: Int?,
        val sendToBraze: Boolean,
        val sendToFirebase: Boolean = false
    )

    private fun loadAndPushWorkoutInfoToAnalytics(sessionOwnerUserName: String): Completable {
        return Single.fromCallable {
            workoutHeaderController.getUserAnalyticsWorkoutSummary(sessionOwnerUserName)
        }.flatMapCompletable { analyticsWorkoutsSummary ->
            val currentYear: Int = analyticsWorkoutsSummary.currentYear
            val previousYear: Int = analyticsWorkoutsSummary.previousYear

            val analyticsValues = mutableListOf(
                // Total number of workouts
                WorkoutAnalyticsValue(
                    key = AnalyticsUserProperty.TOTAL_WORKOUTS,
                    value = analyticsWorkoutsSummary.totalWorkouts,
                    sendToBraze = true,
                    sendToFirebase = true
                ),
                // Total number of pictures
                WorkoutAnalyticsValue(
                    key = AnalyticsUserProperty.TOTAL_WORKOUT_PHOTOS,
                    value = analyticsWorkoutsSummary.totalPictures,
                    sendToBraze = true
                ),
                // Total number of comments
                WorkoutAnalyticsValue(
                    key = AnalyticsUserProperty.TOTAL_COMMENTS_RECEIVED,
                    value = analyticsWorkoutsSummary.totalComments,
                    sendToBraze = true
                ),
                // Total number of likes
                WorkoutAnalyticsValue(
                    key = AnalyticsUserProperty.TOTAL_LIKES_RECEIVED,
                    value = analyticsWorkoutsSummary.totalLikes,
                    sendToBraze = true
                ),
                // Number of private workouts
                WorkoutAnalyticsValue(
                    key = AnalyticsUserProperty.TOTAL_WORKOUTS_TO_PRIVATE,
                    value = analyticsWorkoutsSummary.totalPrivate,
                    sendToBraze = true
                ),
                // Number of public workouts
                WorkoutAnalyticsValue(
                    key = AnalyticsUserProperty.TOTAL_WORKOUTS_TO_PUBLIC,
                    value = analyticsWorkoutsSummary.totalPublic,
                    sendToBraze = true
                ),
                // Number of workouts to followers
                WorkoutAnalyticsValue(
                    key = AnalyticsUserProperty.TOTAL_WORKOUTS_TO_FOLLOWERS,
                    value = analyticsWorkoutsSummary.totalForFollowers,
                    sendToBraze = true
                ),
                // Total duration of all workouts
                WorkoutAnalyticsValue(
                    key = AnalyticsUserProperty.TOTAL_WORKOUTS_DURATION_IN_MINUTES,
                    value = analyticsWorkoutsSummary.totalDurationInMinutes,
                    sendToBraze = true
                ),
                // Total number of workouts with description
                WorkoutAnalyticsValue(
                    key = AnalyticsUserProperty.TOTAL_WORKOUTS_WITH_DESCRIPTION,
                    value = analyticsWorkoutsSummary.totalWithDescription,
                    sendToBraze = true
                ),
                // Total number of workouts for current year
                WorkoutAnalyticsValue(
                    key = "${AnalyticsUserProperty.TOTAL_WORKOUTS}$currentYear",
                    value = analyticsWorkoutsSummary.totalWorkoutsCurrentYear,
                    sendToBraze = true
                ),
                // Total duration of workouts in minutes for current year
                WorkoutAnalyticsValue(
                    key = "${AnalyticsUserProperty.TOTAL_DURATION}$currentYear",
                    value = analyticsWorkoutsSummary.totalDurationInMinutesCurrentYear,
                    sendToBraze = false
                ),
                // Total number of workouts for previous year
                WorkoutAnalyticsValue(
                    key = "${AnalyticsUserProperty.TOTAL_WORKOUTS}$previousYear",
                    value = analyticsWorkoutsSummary.totalWorkoutsPreviousYear,
                    sendToBraze = false
                ),
                // Total duration of workouts in minutes for previous year
                WorkoutAnalyticsValue(
                    key = "${AnalyticsUserProperty.TOTAL_DURATION}$previousYear",
                    value = analyticsWorkoutsSummary.totalDurationInMinutesPreviousYear,
                    sendToBraze = false
                ),
                // Total number of workouts for current week
                WorkoutAnalyticsValue(
                    key = AnalyticsUserProperty.TOTAL_WORKOUTS_CURRENT_WEEK,
                    value = analyticsWorkoutsSummary.totalWorkoutsCurrentWeek,
                    sendToBraze = true
                ),
                // Total number of workouts for current month
                WorkoutAnalyticsValue(
                    key = AnalyticsUserProperty.TOTAL_WORKOUTS_CURRENT_MONTH,
                    value = analyticsWorkoutsSummary.totalWorkoutsCurrentMonth,
                    sendToBraze = true
                )
            )

            // Total number of workouts during last X days
            analyticsWorkoutsSummary.workoutCountsForPreviousDays.forEach { numDays, numWorkouts ->
                val userProperty = String.format(
                    Locale.US,
                    AnalyticsUserProperty.TOTAL_WORKOUTS_WITHIN_LAST_X_DAYS,
                    numDays
                )
                analyticsValues.add(
                    WorkoutAnalyticsValue(
                        key = userProperty,
                        value = numWorkouts,
                        sendToBraze = true
                    )
                )
            }
            try {
                trackUserPropertiesAndSaveChangedValues(analyticsValues)
            } catch (e: Throwable) {
                Timber.w(e, "Error occurred when pushing workout info to analytics")
            }

            Completable.complete()
        }.subscribeOn(Schedulers.io())
    }

    private fun loadAndPushFollowInfoToAnalytics(): Completable {
        return Single.fromCallable {
            peopleController.followCountSummary
        }.flatMapCompletable { followSummary ->
            // Map analytics user properties to values
            val analyticsValues = mutableListOf(
                // Total followings
                WorkoutAnalyticsValue(
                    key = AnalyticsUserProperty.TOTAL_FOLLOWS,
                    value = followSummary.followingCount,
                    sendToBraze = true
                ),
                // Total followers
                WorkoutAnalyticsValue(
                    key = AnalyticsUserProperty.TOTAL_FOLLOWERS,
                    value = followSummary.followersCount,
                    sendToBraze = true
                )
            )

            try {
                trackUserPropertiesAndSaveChangedValues(analyticsValues)
            } catch (e: Throwable) {
                Timber.w(e, "Error occurred when pushing workout info to analytics")
            }

            Completable.complete()
        }
    }

    private fun trackUserPropertiesAndSaveChangedValues(
        analyticsValues: List<WorkoutAnalyticsValue>
    ) {
        // Check last sent analytics value from SharedPreferences so we update only
        // changed values to analytics services
        val amplitudeProperties = mutableMapOf<String, Int>()
        val brazeProperties = mutableMapOf<String, Int>()
        val firebaseProperties = mutableMapOf<String, Int>()
        val updatedSharedPreferencesValues = mutableMapOf<String, Int>()

        analyticsValues.forEach { (key, value, sendToBraze, sendToFirebase) ->
            value?.let {
                // If new value is set and it is different from previous value, add new user
                // property value to analyticsProperties and add the related SharedPreferences
                // key to updatedSharedPreferencesValues.
                //
                // updatedSharedPreferencesValues will be written to SharedPreferences once
                // user properties have been tracked. This is used to keep track of previous
                // values and avoid unneeded updates.
                val sharedPreferencesKey = mapUserPropertyToSharedPreferencesKey(key)
                if (sharedPreferencesKey != null) {
                    if (sharedPreferences.getInt(sharedPreferencesKey, -1) != value) {
                        amplitudeProperties[key] = value
                        if (sendToBraze) brazeProperties[key] = value
                        if (sendToFirebase) firebaseProperties[key] = value
                        updatedSharedPreferencesValues[sharedPreferencesKey] = value
                    }
                } else {
                    // this should not happen, but just in case we update only amplitude
                    amplitudeProperties[key] = value
                }
            }
        }

        amplitudeAnalyticsTracker.trackUserProperties(amplitudeProperties)
        emarsysAnalytics.trackUserProperties(brazeProperties)
        for ((key, value) in firebaseProperties) {
            // int properties don't work well with firebase, let's track an event with value property
            firebaseAnalyticsTracker.trackEvent(key, FirebaseAnalytics.Param.VALUE, value)
        }

        // Save changed analytics values to SharedPreferences
        sharedPreferences.edit().apply {
            updatedSharedPreferencesValues.entries.forEach { (sharedPreferencesKey, value) ->
                putInt(sharedPreferencesKey, value)
            }
            apply()
        }
    }

    private fun mapUserPropertyToSharedPreferencesKey(
        userProperty: String
    ): String? {
        // Map "TotalWorkoutsLast#Days" user properties
        for (numDays in AnalyticsUserProperty.TOTAL_WORKOUTS_WITHIN_LAST_DAYS_PERIODS) {
            val prop = String.format(
                Locale.US,
                AnalyticsUserProperty.TOTAL_WORKOUTS_WITHIN_LAST_X_DAYS,
                numDays
            )

            if (prop == userProperty) {
                return String.format(
                    Locale.US,
                    STTConstants.AnalyticsPreferences.KEY_LAST_TOTAL_WORKOUTS_WITHIN_X_DAYS,
                    numDays
                )
            }
        }

        // Map workout counts and durations for each year (TotalWorkouts2019, etc)
        YEAR_REGEX.find(userProperty)?.groupValues?.getOrNull(1)?.let {
            if (userProperty.startsWith(AnalyticsUserProperty.TOTAL_WORKOUTS)) {
                return STTConstants.AnalyticsPreferences.KEY_LAST_TOTAL_WORKOUTS + "_$it"
            } else if (userProperty.startsWith(AnalyticsUserProperty.TOTAL_DURATION)) {
                return STTConstants.AnalyticsPreferences.KEY_LAST_TOTAL_DURATION_IN_MINUTES + "_$it"
            }
        }

        // Map other user properties, including workout counts for this year and previous year
        return when (userProperty) {
            AnalyticsUserProperty.TOTAL_WORKOUTS ->
                STTConstants.AnalyticsPreferences.KEY_LAST_TOTAL_WORKOUTS
            AnalyticsUserProperty.TOTAL_WORKOUT_PHOTOS ->
                STTConstants.AnalyticsPreferences.KEY_LAST_TOTAL_PICTURES
            AnalyticsUserProperty.TOTAL_COMMENTS_RECEIVED ->
                STTConstants.AnalyticsPreferences.KEY_LAST_TOTAL_COMMENTS
            AnalyticsUserProperty.TOTAL_LIKES_RECEIVED ->
                STTConstants.AnalyticsPreferences.KEY_LAST_TOTAL_LIKES
            AnalyticsUserProperty.TOTAL_WORKOUTS_TO_PRIVATE ->
                STTConstants.AnalyticsPreferences.KEY_LAST_TOTAL_PRIVATE_WORKOUTS
            AnalyticsUserProperty.TOTAL_WORKOUTS_TO_PUBLIC ->
                STTConstants.AnalyticsPreferences.KEY_LAST_TOTAL_PUBLIC_WORKOUTS
            AnalyticsUserProperty.TOTAL_WORKOUTS_TO_FOLLOWERS ->
                STTConstants.AnalyticsPreferences.KEY_LAST_TOTAL_FOR_FOLLOWERS_WORKOUTS
            AnalyticsUserProperty.TOTAL_WORKOUTS_DURATION_IN_MINUTES ->
                STTConstants.AnalyticsPreferences.KEY_LAST_TOTAL_DURATION_IN_MINUTES
            AnalyticsUserProperty.TOTAL_WORKOUTS_WITH_DESCRIPTION ->
                STTConstants.AnalyticsPreferences.KEY_LAST_TOTAL_WORKOUTS_WITH_DESCRIPTION
            AnalyticsUserProperty.TOTAL_FOLLOWERS ->
                STTConstants.AnalyticsPreferences.KEY_LAST_TOTAL_FOLLOWERS
            AnalyticsUserProperty.TOTAL_FOLLOWS ->
                STTConstants.AnalyticsPreferences.KEY_LAST_TOTAL_FOLLOWINGS
            else -> {
                Timber.w("Unable to map user property: $userProperty")
                null
            }
        }
    }

    companion object {
        private val YEAR_REGEX = Regex(".*(\\d{4})$")
    }
}
