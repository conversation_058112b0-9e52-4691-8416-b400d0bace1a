package com.stt.android.analytics.notificationAnalytics

import android.app.NotificationManager
import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.ListenableWorker
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import com.stt.android.R
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsUserProperty
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import javax.inject.Inject

class NotificationsAnalyticsJob(
    private val emarsysAnalytics: EmarsysAnalytics,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    private val appContext: Context,
    params: WorkerParameters
) : CoroutineWorker(appContext, params) {

    override suspend fun doWork(): Result {
        val notificationManager =
            appContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        for (channel in notificationManager.notificationChannels) {
            if (channel.importance != 0) {
                addUserProperty(channel.name, true)
            } else {
                addUserProperty(channel.name, false)
            }
        }

        return Result.success()
    }

    class Factory
    @Inject constructor(
        val emarsysAnalytics: EmarsysAnalytics,
        val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    ) : CoroutineWorkerAssistedFactory {
        override fun create(context: Context, params: WorkerParameters): ListenableWorker {
            return NotificationsAnalyticsJob(
                emarsysAnalytics,
                amplitudeAnalyticsTracker,
                context,
                params
            )
        }
    }

    private fun addUserProperty(channelName: CharSequence, value: Boolean) {
        with(appContext.resources) {
            when (channelName) {
                getString(com.stt.android.core.R.string.notification_channel_activity_recording) ->
                    trackProperty(AnalyticsUserProperty.PUSH_SETTING_ACTIVITY_RECORDING, value)

                getString(R.string.notification_channel_my_activity_likes) ->
                    trackProperty(AnalyticsUserProperty.PUSH_SETTING_LIKES, value)

                getString(R.string.notification_channel_my_activity_comments) ->
                    trackProperty(AnalyticsUserProperty.PUSH_SETTING_COMMENTS, value)

                getString(R.string.notification_channel_personal_achievements) ->
                    trackProperty(AnalyticsUserProperty.PUSH_SETTING_PERSONAL_ACHIEVEMENTS, value)

                getString(R.string.notification_channel_new_followers) ->
                    trackProperty(AnalyticsUserProperty.PUSH_SETTING_NEW_FOLLOWERS, value)

                getString(R.string.notification_channel_facebook_friend_joined) ->
                    trackProperty(AnalyticsUserProperty.PUSH_SETTING_FRIENDS_JOIN, value)

                getString(R.string.notification_channel_friend_activity_shared) ->
                    trackProperty(AnalyticsUserProperty.PUSH_SETTING_FRIENDS_SHARED_ACTIVITY, value)

                getString(R.string.notification_channel_friend_activity_commented) ->
                    trackProperty(AnalyticsUserProperty.PUSH_SETTING_FRIENDS_COMMENTED_ACTIVITY, value)

                getString(R.string.notification_channel_critical_information) ->
                    trackProperty(AnalyticsUserProperty.PUSH_SETTING_CRITICAL_INFORMATION, value)

                getString(R.string.notification_channel_app_updates) ->
                    trackProperty(AnalyticsUserProperty.PUSH_SETTING_APP_UPDATES, value)

                getString(R.string.notification_channel_events_and_challenges) ->
                    trackProperty(AnalyticsUserProperty.PUSH_SETTING_EVENTS_AND_CHALLENGES, value)

                getString(R.string.notification_channel_updates_from_community) ->
                    trackProperty(AnalyticsUserProperty.PUSH_SETTING_UPDATES_FROM_COMMUNITY, value)
            }
        }
    }

    private fun trackProperty(userProperty: String, value: Boolean) {
        amplitudeAnalyticsTracker.trackUserProperty(userProperty, value)
        emarsysAnalytics.trackBooleanUserProperty(userProperty, value)
    }

    companion object {
        const val TAG = "NotificationsAnalyticsJob"

        @JvmStatic
        fun schedule(workManager: WorkManager) {
            workManager.enqueue(
                OneTimeWorkRequestBuilder<NotificationsAnalyticsJob>()
                    .addTag(TAG)
                    .build()
            )
        }
    }
}
