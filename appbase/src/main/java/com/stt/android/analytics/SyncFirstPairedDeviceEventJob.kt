package com.stt.android.analytics

import android.content.Context
import android.content.SharedPreferences
import android.text.TextUtils
import androidx.core.content.edit
import androidx.work.BackoffPolicy
import androidx.work.Constraints
import androidx.work.CoroutineWorker
import androidx.work.Data
import androidx.work.ExistingWorkPolicy
import androidx.work.ListenableWorker
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import com.stt.android.FirstPairedDeviceType
import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.di.FirstPairedDevicePreferences
import com.stt.android.eventtracking.EventTracker
import com.stt.android.locationinfo.FetchLocationInfoUseCase
import com.stt.android.remote.firstdevicepaired.SyncFirstPairedDeviceEventRestApi
import com.stt.android.utils.STTConstants
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject

class SyncFirstPairedDeviceEventJob(
    private val syncFirstPairedDeviceEventRestApi: SyncFirstPairedDeviceEventRestApi,
    private val firstPairedDevicePreferences: SharedPreferences,
    private val emarsysAnalytics: EmarsysAnalytics,
    private val fetchLocationInfoUseCase: FetchLocationInfoUseCase,
    private val eventTracker: EventTracker,
    appContext: Context,
    params: WorkerParameters
) : CoroutineWorker(appContext, params) {
    // if over retry times, stop this job
    private var retryTimes = 0

    override suspend fun doWork(): Result {
        return try {
            val serialNumber = inputData.getString(KEY_SERIAL_NUMBER)
            val deviceName = inputData.getString(KEY_DEVICE_NAME)
            val type = inputData.getString(KEY_TYPE)
            if (!TextUtils.isEmpty(serialNumber) &&
                !TextUtils.isEmpty(deviceName) &&
                !TextUtils.isEmpty(type)
            ) {
                val place = runSuspendCatching {
                    fetchLocationInfoUseCase.fetchLocationInfo()
                }.getOrNull()
                val firstPairedDeviceEventResult =
                    syncFirstPairedDeviceEventRestApi.setFirstPairedDeviceEvent(
                        serialNumber!!,
                        deviceName!!,
                        type!!,
                        country = place?.country,
                        region = place?.province,
                        city = place?.city
                    )

                val key = String.format(
                    STTConstants.FirstPairedDevicePreferences.KEY_FIRST_PAIRED_DEVICE,
                    type,
                    serialNumber
                )
                firstPairedDevicePreferences.edit { putBoolean(key, true) }
                if (firstPairedDeviceEventResult.payload == true) {
                    val properties = HashMap<String, String>().apply {
                        put(AnalyticsEventProperty.SUUNTO_WATCH_MODEL, deviceName)
                    }
                    emarsysAnalytics.trackEventWithProperties(
                        AnalyticsEvent.FIRST_PAIRED_DEVICE,
                        properties
                    )
                    val dataProperties = HashMap<String, String>().apply {
                        put(AnalyticsEventProperty.SUUNTO_WATCH_MODEL, deviceName)
                        put(AnalyticsEventProperty.SN, serialNumber)
                        put(AnalyticsEventProperty.CITY, place?.city ?: "")
                    }
                    eventTracker.trackEvent(AnalyticsEvent.FIRST_PAIRED, dataProperties)
                }
                Timber.d("sync device first paired event result: ${firstPairedDeviceEventResult.payload}")
            }
            Result.success()
        } catch (e: Exception) {
            Timber.w(e, "sync device first paired event to backend fail")
            if (retryTimes > MAX_RETRY_TIMES)
                Result.success()
            else {
                retryTimes++
                Result.retry()
            }
        }
    }

    class Factory
    @Inject constructor(
        private val syncFirstPairedDeviceEventRestApi: SyncFirstPairedDeviceEventRestApi,
        @FirstPairedDevicePreferences
        private val firstPairedDevicePreferences: SharedPreferences,
        private val emarsysAnalytics: EmarsysAnalytics,
        private val fetchLocationInfoUseCase: FetchLocationInfoUseCase,
        private val eventTracker: EventTracker,
    ) : CoroutineWorkerAssistedFactory {
        override fun create(context: Context, params: WorkerParameters): ListenableWorker {
            return SyncFirstPairedDeviceEventJob(
                syncFirstPairedDeviceEventRestApi,
                firstPairedDevicePreferences,
                emarsysAnalytics,
                fetchLocationInfoUseCase,
                eventTracker,
                context,
                params
            )
        }
    }

    companion object {
        private const val KEY_SERIAL_NUMBER = "serial_number"
        private const val KEY_DEVICE_NAME = "device_name"
        private const val KEY_TYPE = "type"
        const val TAG = "SyncDeviceFirstPairedEventJob"
        private const val MAX_RETRY_TIMES = 3

        fun enqueue(
            workManager: WorkManager,
            serialNumber: String,
            deviceName: String,
            type: FirstPairedDeviceType
        ) {
            Timber.d("Enqueuing SyncDeviceFirstPairedEventJob")
            workManager.enqueueUniqueWork(
                "${TAG}_${serialNumber}",
                ExistingWorkPolicy.REPLACE,
                OneTimeWorkRequestBuilder<SyncFirstPairedDeviceEventJob>()
                    .setConstraints(
                        Constraints.Builder()
                            .setRequiredNetworkType(NetworkType.CONNECTED)
                            .build()
                    )
                    .setBackoffCriteria(BackoffPolicy.EXPONENTIAL, 1, TimeUnit.MINUTES)
                    .setInputData(
                        Data.Builder().putString(KEY_SERIAL_NUMBER, serialNumber)
                            .putString(KEY_DEVICE_NAME, deviceName)
                            .putString(KEY_TYPE, type.type)
                            .build()
                    )
                    .build()
            )
        }

        fun getWorkoutRequest(
            serialNumber: String,
            deviceName: String,
            type: FirstPairedDeviceType
        ) = OneTimeWorkRequestBuilder<SyncFirstPairedDeviceEventJob>()
            .setConstraints(
                Constraints.Builder()
                    .setRequiredNetworkType(NetworkType.CONNECTED)
                    .build()
            )
            .setBackoffCriteria(BackoffPolicy.EXPONENTIAL, 1, TimeUnit.MINUTES)
            .setInputData(
                Data.Builder().putString(KEY_SERIAL_NUMBER, serialNumber)
                    .putString(KEY_DEVICE_NAME, deviceName)
                    .putString(KEY_TYPE, type.type)
                    .build()
            )
            .build()
    }
}

