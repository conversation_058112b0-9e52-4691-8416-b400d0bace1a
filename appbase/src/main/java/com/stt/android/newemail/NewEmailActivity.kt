package com.stt.android.newemail

import android.content.Context
import android.content.Intent
import com.stt.android.R
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class NewEmailActivity : BaseComposeActivity() {

    companion object {

        const val EXTRA_EMAIL_KEY = "email"
        fun newStartIntent(context: Context): Intent {
            return Intent(context, NewEmailActivity::class.java)
        }
    }

    override fun getNavGraph(): Int {
        return R.navigation.new_email_nav_graph
    }
}
