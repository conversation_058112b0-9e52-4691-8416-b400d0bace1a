package com.stt.android.newemail

import androidx.annotation.StringRes
import androidx.core.util.PatternsCompat
import com.stt.android.R

sealed class EmailError(@StringRes val resId: Int) {
    data object EmailIsError : EmailError(R.string.invalid_email)
    data object EmailHasBeenRegistered : EmailError(R.string.email_has_been_registered)
    data object EmailHasNotChanged : EmailError(R.string.input_new_email)
    data object TooManyRequests : EmailError(R.string.error_429)
}

fun isEmailValid(email: String): Boolean =
    PatternsCompat.EMAIL_ADDRESS.matcher(email).matches()

const val VERIFICATION_CODE_LENGTH = 6
