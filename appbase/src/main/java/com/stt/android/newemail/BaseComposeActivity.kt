package com.stt.android.newemail

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.annotation.NavigationRes
import androidx.appcompat.app.AppCompatActivity
import androidx.navigation.NavController
import androidx.navigation.findNavController
import androidx.navigation.fragment.NavHostFragment
import com.stt.android.R
import com.stt.android.window.setFlagsAndColors
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
abstract class BaseComposeActivity : AppCompatActivity() {
    private val handler = Handler(Looper.getMainLooper())
    private val navigationListener =
        NavController.OnDestinationChangedListener { _, destination, _ ->
            // Change system UI colors via handler so that the fragment transaction for the
            // navigation has a chance to start first
            handler.post {
                updateStatusBarAndNavigationBarColor(darkMode = false)
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        title = "" // No title, just centered toolbar with logo
        // Assume dark navigation bar and status bar for splash intro screen. Update based on
        // navigation events after onStart.
        updateStatusBarAndNavigationBarColor(darkMode = false)
        setContentView(R.layout.activity_base_compose_china)
        val navHostFragment = supportFragmentManager.findFragmentById(R.id.main_content) as NavHostFragment
        navHostFragment.navController.apply {
            val newNavGraph = navInflater.inflate(getNavGraph())
            setGraph(newNavGraph, getStartDestinationArgs())
        }
    }

    override fun onSupportNavigateUp(): Boolean =
        findNavController(R.id.main_content).navigateUp() || super.onSupportNavigateUp()

    override fun onStart() {
        super.onStart()
        findNavController(R.id.main_content).addOnDestinationChangedListener(navigationListener)
    }

    override fun onStop() {
        super.onStop()

        findNavController(R.id.main_content).removeOnDestinationChangedListener(
            navigationListener
        )
    }

    private fun updateStatusBarAndNavigationBarColor(darkMode: Boolean) {
        window.setFlagsAndColors(darkMode = darkMode)
    }

    @NavigationRes
    abstract fun getNavGraph(): Int

    open fun getStartDestinationArgs(): Bundle? {
        return null
    }
}
