package com.stt.android.social.personalrecord

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Chip
import androidx.compose.material.ChipDefaults
import androidx.compose.material.Divider
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import com.stt.android.R
import com.stt.android.compose.component.SuuntoTopBar
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.cloudyGrey
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.LoadingContent
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.domain.achievements.PersonalRecordType
import com.stt.android.domain.achievements.RecordItemDomain

@Suppress("ktlint:compose:vm-forwarding-check")
@Composable
fun PersonalRecordsScreen(
    onCloseClicked: () -> Unit,
    onItemClick: (RecordItemDomain) -> Unit,
    modifier: Modifier = Modifier,
    personalRecordsViewModel: PersonalRecordsViewModel = hiltViewModel(),
) {
    Scaffold(
        modifier = modifier,
        topBar = {
            SuuntoTopBar(
                title = stringResource(R.string.personal_records),
                onNavigationClick = onCloseClicked,
            )
        },
    ) { paddingValues ->
        ContentBody(
            paddingValues,
            personalRecordsViewModel,
            onItemClick = onItemClick
        )
    }
}

@Composable
private fun ContentBody(
    paddingValues: PaddingValues,
    personalRecordsViewModel: PersonalRecordsViewModel,
    onItemClick: (RecordItemDomain) -> Unit,
) {
    val uiState by personalRecordsViewModel.uiState.collectAsState()

    Column(
        modifier = Modifier
            .padding(paddingValues)
            .fillMaxSize()
            .narrowContent()
            .background(MaterialTheme.colors.surface)
    ) {

        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))

        WorkoutsFilterSection(
            workouts = listOf(
                CoreActivityType.RUNNING,
                CoreActivityType.TRAIL_RUNNING,
                CoreActivityType.CYCLING
            ),
            selectedWorkout = personalRecordsViewModel.selectedActivityType,
            onFilterClick = {
                personalRecordsViewModel.onFilterTagChanged(it)
            }
        )
        when (uiState) {
            is PersonalRecordsViewState.Loaded -> {
                RecordList(
                    (uiState as PersonalRecordsViewState.Loaded).recordList,
                    onItemClick = onItemClick
                )
            }

            is PersonalRecordsViewState.Error -> {}
            is PersonalRecordsViewState.Loading -> LoadingContent(true)
        }
    }
}

@Composable
private fun RecordList(
    recordList: List<RecordItemDomain>,
    onItemClick: (RecordItemDomain) -> Unit,
) {
    LazyColumn {
        items(
            items = recordList,
            key = { item -> item.personalRecordType.titleId }
        ) {
            PersonalRecordItem(
                title = stringResource(it.personalRecordType.titleId),
                value = it.value,
                date = it.timestamp ?: stringResource(id = R.string.no_record),
                onClick = {
                    onItemClick.invoke(it)
                }
            )
            Divider(color = MaterialTheme.colors.dividerColor)
        }
    }
}

@Composable
fun WorkoutsFilterSection(
    workouts: List<CoreActivityType>,
    selectedWorkout: CoreActivityType,
    onFilterClick: (CoreActivityType) -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .height(MaterialTheme.spacing.xxxxlarge),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        workouts.forEach { workout ->
            FilterChip(
                label = stringResource(id = workout.nameRes),
                iconId = workout.icon,
                backgroundColorResId = workout.color,
                isSelected = workout == selectedWorkout,
                onFilterClicked = {
                    onFilterClick.invoke(workout)
                }
            )
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun FilterChip(
    label: String,
    iconId: Int,
    backgroundColorResId: Int,
    isSelected: Boolean,
    onFilterClicked: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Chip(
        onClick = onFilterClicked,
        colors = ChipDefaults.chipColors(
            backgroundColor = if (isSelected) MaterialTheme.colors.primary else MaterialTheme.colors.cloudyGrey,
            contentColor = Color.White
        ),
        modifier = modifier.padding(
            vertical = MaterialTheme.spacing.xsmall,
            horizontal = MaterialTheme.spacing.xxxsmall
        ),
    ) {
        Icon(
            painter = painterResource(id = iconId),
            contentDescription = label,
            modifier = Modifier
                .size(MaterialTheme.spacing.large)
                .background(
                    shape = CircleShape,
                    color = colorResource(id = backgroundColorResId)
                )
        )
        Spacer(modifier = Modifier.width(MaterialTheme.spacing.xxsmall))
        Text(
            text = label,
            style = MaterialTheme.typography.body2,
            color = if (isSelected) Color.White else MaterialTheme.colors.nearBlack,
        )
    }
}

@Composable
fun PersonalRecordItem(
    title: String,
    value: String?,
    date: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
            .padding(MaterialTheme.spacing.medium)
            .fillMaxWidth()
            .heightIn(min = MaterialTheme.spacing.xxlarge)
            .clickable(
                enabled = value != null,
                interactionSource = remember { MutableInteractionSource() },
                indication = null,
            ) { onClick.invoke() }
    ) {
        Column {
            Text(
                title,
                color = MaterialTheme.colors.nearBlack,
                style = MaterialTheme.typography.body
            )
            Text(
                date,
                color = MaterialTheme.colors.darkGrey,
                style = MaterialTheme.typography.body
            )
        }
        Row(verticalAlignment = Alignment.CenterVertically) {
            value?.let {
                Text(
                    it,
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colors.primary
                )
                Spacer(modifier = Modifier.width(MaterialTheme.spacing.medium))
                Icon(
                    painter = painterResource(id = R.drawable.icon_arrow_right),
                    tint = MaterialTheme.colors.nearBlack,
                    contentDescription = "",
                    modifier = Modifier.size(MaterialTheme.spacing.xsmaller)
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun PersonalRecordsScreenPreview() {
    AppTheme {
        Surface {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .narrowContent()
                    .background(MaterialTheme.colors.surface)
            ) {

                Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))

                WorkoutsFilterSection(
                    workouts = listOf(
                        CoreActivityType.RUNNING,
                        CoreActivityType.TRAIL_RUNNING,
                        CoreActivityType.CYCLING
                    ),
                    selectedWorkout = CoreActivityType.RUNNING,
                    onFilterClick = { }
                )
                RecordList(
                    listOf(
                        RecordItemDomain(
                            personalRecordType = PersonalRecordType.LONGEST_DISTANCE,
                            value = "102 km",
                            timestamp = "2.5.2024",
                            workoutHeader = null
                        ),

                        RecordItemDomain(
                            personalRecordType = PersonalRecordType.HIGHEST_CLIMB,
                            value = "3000m",
                            timestamp = "12.5.2024",
                            workoutHeader = null
                        ),

                        RecordItemDomain(
                            personalRecordType = PersonalRecordType.HIGHEST_ALTITUDE,
                            value = "6500m",
                            timestamp = "10.5.2024",
                            workoutHeader = null
                        ),
                    ),
                    onItemClick = {}
                )
            }
        }
    }
}
