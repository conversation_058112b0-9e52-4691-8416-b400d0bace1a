package com.stt.android.social.notifications.list

import android.content.Context
import android.content.Intent
import android.content.res.Resources
import android.view.View
import android.widget.Toast
import androidx.annotation.ColorInt
import androidx.core.content.ContextCompat
import androidx.core.net.toUri
import coil3.load
import coil3.request.transformations
import coil3.transform.CircleCropTransformation
import com.stt.android.R
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsPropertyValue.NotificationMessageType
import com.stt.android.coil.placeholderWithFallback
import com.stt.android.common.ui.BaseBindableItem
import com.stt.android.databinding.FeedEventItemBinding
import com.stt.android.domain.notifications.DomainNotification
import com.stt.android.eventtracking.EventTracker
import com.stt.android.ui.utils.TextFormatter
import timber.log.Timber
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.format.FormatStyle
import com.stt.android.R as BaseR
import com.stt.android.core.R as CoreR

class NotificationItem(
    private val notification: DomainNotification,
    private val eventTracker: EventTracker,
    private val onNotificationOpened: (String) -> Unit,
) : BaseBindableItem<FeedEventItemBinding>() {
    private lateinit var context: Context
    private lateinit var resources: Resources

    override fun getLayout() = R.layout.feed_event_item

    override fun bind(viewBinding: FeedEventItemBinding, position: Int) {
        super.bind(viewBinding, position)
        context = viewBinding.root.context
        resources = viewBinding.root.resources

        // Load user profile image
        viewBinding.profileImage.load(notification.imageUrl) {
            transformations(CircleCropTransformation())
            placeholderWithFallback(
                viewBinding.profileImage.context,
                CoreR.drawable.ic_default_profile_image_light
            )
        }
    }

    /**
     * Handle click on the base item
     */
    fun onItemClick(view: View) {
        try {
            @Suppress("UnsafeImplicitIntentLaunch")
            val intent = Intent(Intent.ACTION_VIEW).apply {
                setData(notification.appUrl.toUri())
            }
            context.startActivity(intent)

            onNotificationOpened(notification.id)
            sendAnalyticsEvent(notification.type)
        } catch (e: Exception) {
            Timber.w(e, "Error while opening app URL from notification")
            Toast.makeText(context, BaseR.string.error_0, Toast.LENGTH_SHORT).show()
        }
    }

    fun getFeedTime(): String {
        val millis = notification.timestamp
        val date = LocalDate.ofInstant(Instant.ofEpochMilli(millis), ZoneId.systemDefault())
        val today = LocalDate.now()
        return when (date) {
            today -> TextFormatter.formatRelativeDateSpan(context.resources, millis).toString()
            today.minusDays(1L) -> context.getString(R.string.yesterday)
            else -> DateTimeFormatter.ofLocalizedDate(FormatStyle.SHORT).format(date)
        }
    }

    fun getFeedMessage(): String = notification.text

    @ColorInt
    fun getTextColor(): Int = if (notification.read) {
        ContextCompat.getColor(context, R.color.dark_gray)
    } else {
        ContextCompat.getColor(context, R.color.accent)
    }

    private fun sendAnalyticsEvent(notificationType: String) {
        val type = when (notificationType) {
            NOTIFICATION_TYPE_COMMENT_SINGLE,
            NOTIFICATION_TYPE_COMMENT_MULTIPLE,
            NOTIFICATION_TYPE_COMMENT_ALSO,
            NOTIFICATION_TYPE_COMMENT_OWN -> NotificationMessageType.COMMENTS

            NOTIFICATION_TYPE_FOLLOW,
            NOTIFICATION_TYPE_FOLLOW_REQUEST,
            NOTIFICATION_TYPE_FOLLOW_ACCEPT -> NotificationMessageType.NEW_FOLLOWERS

            NOTIFICATION_TYPE_LIKE -> NotificationMessageType.LIKES
            NOTIFICATION_TYPE_FACEBOOK -> NotificationMessageType.FACEBOOK
            else -> return
        }
        eventTracker.trackEvent(
            AnalyticsEvent.NOTIFICATIONS_PAGE_CLICK,
            mapOf(AnalyticsEventProperty.NOTIFICATION_MESSAGE_TYPE to type),
        )
    }

    companion object {
        private const val NOTIFICATION_TYPE_COMMENT_SINGLE = "COMMENT_TEXT_SINGLE"
        private const val NOTIFICATION_TYPE_COMMENT_MULTIPLE = "COMMENT_TEXT_MULTIPLE"
        private const val NOTIFICATION_TYPE_COMMENT_ALSO = "COMMENT_TEXT_ALSO"
        private const val NOTIFICATION_TYPE_COMMENT_OWN = "COMMENT_TEXT_OWN"
        private const val NOTIFICATION_TYPE_FOLLOW = "FOLLOW"
        private const val NOTIFICATION_TYPE_FOLLOW_REQUEST = "FOLLOW_REQUEST"
        private const val NOTIFICATION_TYPE_FOLLOW_ACCEPT = "FOLLOW_ACCEPT"
        private const val NOTIFICATION_TYPE_LIKE = "REACTION"
        private const val NOTIFICATION_TYPE_FACEBOOK = "NEW_USER"
    }
}
