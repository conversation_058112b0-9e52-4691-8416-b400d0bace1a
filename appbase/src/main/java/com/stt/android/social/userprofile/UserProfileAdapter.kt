package com.stt.android.social.userprofile

import android.text.TextUtils
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.Filter
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListUpdateCallback
import androidx.recyclerview.widget.RecyclerView
import com.stt.android.FeatureFlags
import com.stt.android.R
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator
import com.stt.android.domain.report.block.BlockStatus
import com.stt.android.domain.user.ImageInformation
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.user.User
import com.stt.android.domain.user.subscription.CurrentPremiumSubscriptionStatus
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.social.userprofile.UserProfileViewModel.Companion.PROFILE_IMAGES_COUNT
import com.stt.android.ui.components.PictureThumbnailView
import com.stt.android.ui.components.workout.WorkoutCardViewModel
import timber.log.Timber
import java.util.Locale

class UserProfileAdapter(
    val activity: BaseUserProfileActivity,
    private val userDetailPresenter: UserDetailPresenter,
    private val currentUserController: CurrentUserController,
    pictureScrollPosition: Int,
    private val userSettingsController: UserSettingsController,
    private val workoutDetailsRewriteNavigator: WorkoutDetailsRewriteNavigator,
    private val loadUser: () -> Unit,
    private val onDescriptionClicked: () -> Unit,
    private val onRealNameClicked: () -> Unit,
    private val featureFlags: FeatureFlags,
    private val workoutCardViewModel: WorkoutCardViewModel,
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private lateinit var inflater: LayoutInflater
    private lateinit var filter: RecyclerViewAdapterFilter

    private var filterViewHolder: WorkoutFilterViewHolder? = null
    private var user: User? = null
    private var blockStatus: BlockStatus? = null
    private var activitySummaryData: UserActivitySummaryData? = null
    private var measurementUnit: MeasurementUnit? = null
    private val images = mutableListOf<ImageInformation>()
    private val workoutsSource = mutableListOf<WorkoutHeader>()
    private val workoutsFilter = mutableListOf<WorkoutHeader>()

    private var followRequestsCount = 0
    private var currentPremiumSubscriptionStatus: CurrentPremiumSubscriptionStatus? = null
    private var newVersionRedDotVisible = false
    private val positionThumbnails
        get() = if (hasImages()) 1 else -1
    private val positionWorkoutsAggregateData
        get() = if (hasImages()) 2 else 1
    private val positionFilterPart
        get() = if (hasImages()) 3 else 2
    private val positionNavigationPart
        get() = if (hasImages()) 4 else 3
    private val workoutsStartPosition: Int
        get() = if (hasImages()) 5 else 4
    var pictureScrollPosition: Int = 0
        private set

    private var searchCharSequence: CharSequence = ""

    init {
        this.pictureScrollPosition = pictureScrollPosition
    }

    override fun getItemViewType(position: Int): Int {
        return when (position) {
            POSITION_USER_DETAIL -> VIEW_TYPE_USER_DETAIL
            positionThumbnails -> VIEW_TYPE_THUMBNAILS
            positionWorkoutsAggregateData -> VIEW_TYPE_USER_ACTIVITY_SUMMARY
            positionNavigationPart -> VIEW_TYPE_NAVIGATION
            positionFilterPart-> VIEW_TYPE_FILTER
            else -> VIEW_TYPE_WORKOUTS
        }
    }

    private fun hasImages(): Boolean {
        return images.size > 0
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        this.inflater = LayoutInflater.from(parent.context)
        this.filter = getFilter()
        Timber.w("OnCreateViewHolder in UserProfileAdapter")
        return when (viewType) {
            VIEW_TYPE_USER_DETAIL -> {
                UserDetailViewHolder(
                    activity,
                    userDetailPresenter,
                    currentUserController,
                    DataBindingUtil.inflate(
                        inflater,
                        R.layout.item_user_profile_detail,
                        parent,
                        false
                    ),
                    userSettingsController,
                    loadUser,
                    onDescriptionClicked = onDescriptionClicked,
                    onRealNameClicked = onRealNameClicked
                )
            }
            VIEW_TYPE_THUMBNAILS -> UserPictureViewHolder(
                inflater.inflate(R.layout.picture_thumbnail_view, parent, false),
                pictureScrollPosition,
                object : UserPictureViewHolder.Callback {
                    override fun scrollPositionUpdated(position: Int) {
                        pictureScrollPosition = position
                    }
                }
            )
            VIEW_TYPE_USER_ACTIVITY_SUMMARY -> UserActivitySummaryViewHolder(
                activity,
                DataBindingUtil.inflate(
                    inflater,
                    R.layout.item_user_activity_summary,
                    parent,
                    false
                )
            )
            VIEW_TYPE_NAVIGATION -> UserProfileNavigationViewHolder(
                listener = activity,
                currentUserController = currentUserController,
                binding = DataBindingUtil.inflate(
                    inflater,
                    R.layout.item_user_profile_navigation,
                    parent,
                    false,
                ),
                featureFlags
            )
            VIEW_TYPE_FILTER -> {
                WorkoutFilterViewHolder(
                    activity,
                    currentUserController = currentUserController,
                    inflater,
                    parent
                ).apply {
                    filterViewHolder = this
                }
            }
            VIEW_TYPE_WORKOUTS -> UserWorkoutViewHolder(
                context = activity,
                workoutCardViewModel = workoutCardViewModel,
            )
            else -> throw IllegalStateException("Unknown view type $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (getItemViewType(position)) {
            VIEW_TYPE_USER_DETAIL -> (holder as UserDetailViewHolder).bind(user, blockStatus)
            VIEW_TYPE_THUMBNAILS -> (holder.itemView as PictureThumbnailView).showThumbnails(images)
            VIEW_TYPE_USER_ACTIVITY_SUMMARY -> (holder as UserActivitySummaryViewHolder).bind(
                activitySummaryData,
                measurementUnit
            )
            VIEW_TYPE_NAVIGATION -> {
                (holder as UserProfileNavigationViewHolder).bind(
                    user,
                    activity.checkNetworkConnection(),
                    followRequestsCount,
                    currentPremiumSubscriptionStatus,
                    newVersionRedDotVisible,
                )
            }
            VIEW_TYPE_FILTER -> (holder as WorkoutFilterViewHolder).bind(
                user,
                workoutsSource.size,
                searchCharSequence
            ) {
                this.searchCharSequence = it
                filterList(it)
            }
            VIEW_TYPE_WORKOUTS -> (holder as UserWorkoutViewHolder)
                .bind(workoutsFilter[position - workoutsStartPosition], workoutDetailsRewriteNavigator)
            else -> throw IllegalStateException("Unknown view type " + holder.itemViewType)
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        position: Int,
        payloads: MutableList<Any>
    ) {
        if (getItemViewType(position) == VIEW_TYPE_NAVIGATION && payloads.isNotEmpty()) {
            for (payload in payloads) {
                when (payload) {
                    PAYLOAD_BADGE -> {
                        // Change only the visibility of unseen requests badge
                        (holder as UserProfileNavigationViewHolder).bindBadge(
                            followRequestsCount
                        )
                    }

                    PAYLOAD_PREMIUM_SUBSCRIPTION_STATUS -> {
                        // Change only the visibility of ST subscription status text
                        (holder as UserProfileNavigationViewHolder).bindCurrentPremiumSubscriptionStatus(
                            currentPremiumSubscriptionStatus
                        )
                    }

                    PAYLOAD_NEW_APP_VERSION -> {
                        (holder as UserProfileNavigationViewHolder).showNewAppVersionRedDot(
                            newVersionRedDotVisible
                        )
                    }
                }
            }
        } else {
            onBindViewHolder(holder, position)
        }
    }

    override fun onViewRecycled(holder: RecyclerView.ViewHolder) {
        when (holder.itemViewType) {
            VIEW_TYPE_USER_DETAIL -> (holder as UserDetailViewHolder).unbind()
            VIEW_TYPE_THUMBNAILS -> {
                // do nothing
            }
            VIEW_TYPE_USER_ACTIVITY_SUMMARY -> {
                // do nothing
            }
            VIEW_TYPE_NAVIGATION -> {
                // do nothing
            }
            VIEW_TYPE_WORKOUTS -> {
                // do nothing
            }
            VIEW_TYPE_FILTER -> {
                (holder as WorkoutFilterViewHolder).unbind()
            }
            else -> throw IllegalStateException("Unknown view type " + holder.itemViewType)
        }

        super.onViewRecycled(holder)
    }

    override fun getItemCount(): Int {
        return getWithoutWorkoutsItemCount()+ workoutsFilter.size
    }
    private fun getWithoutWorkoutsItemCount(): Int {
        var count =
            4 // VIEW_TYPE_USER_DETAIL + WORKOUTS_AGGREGATE_DATA + VIEW_TYPE_NAVIGATION +VIEW_TYPE_FILTER
        if (hasImages()) {
            ++count // VIEW_TYPE_THUMBNAILS
        }
        return count
    }


    fun setUser(
        user: User,
        blockStatus: BlockStatus
    ) {
        this.blockStatus = blockStatus
        this.user = user
        notifyItemChanged(POSITION_USER_DETAIL, user)
        notifyItemChanged(positionFilterPart)
        notifyItemChanged(positionNavigationPart)

    }

    fun setUserActivitySummaryData(data: UserActivitySummaryData, measurementUnit: MeasurementUnit) {
        this.activitySummaryData = data
        val measurementUnitChanged = this.measurementUnit != null && this.measurementUnit != measurementUnit
        this.measurementUnit = measurementUnit
        notifyItemChanged(positionWorkoutsAggregateData, data)

        if (measurementUnitChanged && workoutsFilter.isNotEmpty()) {
            notifyItemRangeChanged(
                workoutsStartPosition,
                workoutsFilter.size
            )
        }
    }

    fun addPictures(newImages: List<ImageInformation>) {
        val maxNumberToShow = PROFILE_IMAGES_COUNT
        val newImagesCount = newImages.size
        if (newImagesCount == 0 && images.size == 0 || images == newImages) {
            // nothing new, abort the mission
            return
        }

        if (hasImages()) {
            if (newImagesCount >= maxNumberToShow) {
                // we already have max amount of images to show so replace current images
                images.clear()
                images.addAll(newImages)
            } else if (newImages.isNotEmpty()) {
                // we already have some images, let's add the ones we don't have
                for (image in newImages) {
                    if (!images.contains(image)) {
                        images.add(image)
                    }
                }

                // Let's delete extras
                val iter = images.iterator()
                while (iter.hasNext()) {
                    val image = iter.next()
                    if (!newImages.contains(image)) {
                        iter.remove()
                    }
                }
            }

            if (images.size == 0) {
                notifyItemRemoved(positionThumbnails)
            } else {
                notifyItemChanged(positionThumbnails)
            }
        } else {
            // we didn't have any images before, but now we have
            images.addAll(newImages)
            notifyItemInserted(positionThumbnails)
        }
    }

    fun removeWorkouts(workoutIds: List<Int>) {
        for (workoutId in workoutIds) {
            removeWorkout(workoutId)
        }
    }

    fun removeWorkout(workoutId: Int) {
        val iterSource = workoutsSource.iterator()
        while (iterSource.hasNext()) {
            val header = iterSource.next()
            if (workoutId == header.id) {
                iterSource.remove()
                break
            }
        }

        val iter = workoutsFilter.iterator()
        while (iter.hasNext()) {
            val header = iter.next()
            if (workoutId == header.id) {
                val pos = workoutsFilter.indexOf(header) + workoutsStartPosition
                iter.remove()
                notifyItemRemoved(pos)
                break
            }
        }
    }

    fun updateWorkouts(workoutHeaders: List<WorkoutHeader>): Boolean {
        var shouldReloadAggregates = false
        for (header in workoutHeaders) {
            if (updateWorkout(header)) {
                shouldReloadAggregates = true
            }
        }
        return shouldReloadAggregates
    }

    fun updateWorkout(workoutHeader: WorkoutHeader): Boolean {
        var shouldReloadAggregates = false
        val iterSource = workoutsSource.listIterator()
        while (iterSource.hasNext()) {
            val header = iterSource.next()
            if (header.id == workoutHeader.id) {
                iterSource.set(workoutHeader)
                break
            }
        }
        val iter = workoutsFilter.listIterator()
        while (iter.hasNext()) {
            val header = iter.next()
            if (header.id == workoutHeader.id) {
                val pos = workoutsFilter.indexOf(header) + workoutsStartPosition
                iter.set(workoutHeader)
                notifyItemChanged(pos)
                if (workoutHeader.sharingFlags != header.sharingFlags) {
                    shouldReloadAggregates = true
                }
                break
            }
        }
        return shouldReloadAggregates
    }

    fun addWorkouts(newWorkouts: List<WorkoutHeader>) {
        val newWorkoutCount = newWorkouts.size
        if (newWorkoutCount == 0 || newWorkoutCount == workoutsSource.size) {
            // nothing new, abort the mission
            return
        }
        if (workoutsSource.size == 0) { // show search box
            notifyItemChanged(positionFilterPart)
        }
        if (workoutsSource.size > 0) {
            filter.cancel = true
            // no filter
            if (TextUtils.isEmpty(searchCharSequence) && workoutsSource.size == workoutsFilter.size) {
                // we already have some workouts, let's see if we get more
                // 1) for new workouts that are newer than current ones
                val currentLatest = workoutsSource[0].startTime
                var i = 0
                while (i < newWorkoutCount) {
                    val workoutHeader = newWorkouts[i]
                    if (workoutHeader.startTime > currentLatest) {
                        workoutsSource.add(i, workoutHeader)
                        workoutsFilter.add(i, workoutHeader)
                    } else {
                        break
                    }
                    ++i
                }
                if (i > 0) {
                    notifyItemRangeInserted(workoutsStartPosition, i)
                }
                // 2) for new workouts that are older than current ones
                val currentOldest = workoutsSource[workoutsSource.size - 1].startTime
                while (i < newWorkoutCount) {
                    val workoutHeader = newWorkouts[i]
                    if (workoutHeader.startTime < currentOldest) {
                        break
                    }
                    ++i
                }

                if (i < newWorkoutCount) {
                    val count = workoutsSource.size
                    workoutsSource.addAll(newWorkouts.subList(i, newWorkoutCount))
                    workoutsFilter.addAll(newWorkouts.subList(i, newWorkoutCount))
                    notifyItemRangeInserted(count + 1, newWorkoutCount - i)
                }
            } else {
                workoutsSource.addAll(newWorkouts)
                filterList(searchCharSequence)
            }
        } else {
            workoutsSource.addAll(newWorkouts)
            workoutsFilter.addAll(newWorkouts)
            notifyItemRangeInserted(workoutsStartPosition, newWorkouts.size)
        }
    }

    fun showUnseenFollowRequestsBadge(count: Int) {
        followRequestsCount = count
        notifyItemChanged(positionNavigationPart, PAYLOAD_BADGE)
    }

    fun showNewAppVersionRedDot() {
        newVersionRedDotVisible = true
        notifyItemChanged(positionNavigationPart, PAYLOAD_NEW_APP_VERSION)
    }

    fun updatePremiumSubscriptionStatus(
        subscriptionStatus: CurrentPremiumSubscriptionStatus?
    ) {
        currentPremiumSubscriptionStatus = subscriptionStatus
        notifyItemChanged(positionNavigationPart, PAYLOAD_PREMIUM_SUBSCRIPTION_STATUS)
    }

    private fun filterList(constraint: CharSequence) {
        if (TextUtils.isEmpty(constraint)) {
            workoutsFilter.clear()
            workoutsFilter.addAll(workoutsSource)
            notifyItemRangeChanged(
                workoutsStartPosition, workoutsFilter.size
            )
        } else {
            filter.cancel = false
            filter.filter(constraint)
        }
    }
    private fun getFilter(): RecyclerViewAdapterFilter {
        return object : RecyclerViewAdapterFilter() {
            override fun performFiltering(constraint: CharSequence): FilterResults {
                val filterResults = FilterResults()
                if (cancel) {
                    return filterResults
                }
                filterViewHolder?.callbackFilterStart(constraint)
                val result = filterOriginalValues(constraint, workoutsSource)
                filterResults.values = result
                filterResults.count = result.size
                return filterResults
            }
            private fun filterOriginalValues(
                constraint: CharSequence,
                valuesToFilter: List<WorkoutHeader>
            ): MutableList<WorkoutHeader> {
                val searchTerms = constraint.toString()
                    .trim { it <= ' ' }
                    .lowercase(Locale.getDefault())
                    .split(" ".toRegex())
                    .dropLastWhile { it.isEmpty() }
                    .toTypedArray()
                val newValues = mutableListOf<WorkoutHeader>()
                for (value in valuesToFilter) {
                    if (value.applyFilter(searchTerms, activity.resources)) {
                        newValues.add(value)
                    }
                }
                return newValues
            }


            override fun publishResults(p0: CharSequence, result: FilterResults) {
                if (cancel || result.values == null) {
                    return
                }
                filterViewHolder?.callbackFilterComplete(result.count)
                calculateDiffUpdateWorkoutData(result.values as List<WorkoutHeader>)

            }
        }
    }

    private fun calculateDiffUpdateWorkoutData(filterResult: List<WorkoutHeader>) {
        val callback: DiffUtil.Callback = DiffCallback(
            workoutsFilter,
            filterResult
        )
        val newDataList = DiffUtil.calculateDiff(callback)
        workoutsFilter.clear()
        workoutsFilter.addAll(filterResult)
        val workoutStartIndex = getWithoutWorkoutsItemCount()
        newDataList.dispatchUpdatesTo(object : ListUpdateCallback {
            override fun onInserted(position: Int, count: Int) {
                notifyItemRangeInserted(workoutStartIndex + position, count)
            }

            override fun onRemoved(position: Int, count: Int) {
                notifyItemRangeRemoved(workoutStartIndex + position, count)
            }

            override fun onMoved(fromPosition: Int, toPosition: Int) {
                notifyItemMoved(workoutStartIndex + fromPosition, workoutStartIndex + toPosition)
            }

            override fun onChanged(position: Int, count: Int, payload: Any?) {
                // No changed, only filter
            }
        })
    }

    private class DiffCallback(
        private val mOldList: List<WorkoutHeader>,
        private val mNewList: List<WorkoutHeader>
    ) :
        DiffUtil.Callback() {
        override fun getOldListSize(): Int {
            return mOldList.size
        }

        override fun getNewListSize(): Int {
            return mNewList.size
        }

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return mOldList[oldItemPosition].id == mNewList[newItemPosition].id
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return mOldList[oldItemPosition] == mNewList[newItemPosition]
        }
    }

    companion object {
        private const val VIEW_TYPE_USER_DETAIL = 0
        private const val VIEW_TYPE_THUMBNAILS = 1
        private const val VIEW_TYPE_USER_ACTIVITY_SUMMARY = 2
        private const val VIEW_TYPE_WORKOUTS = 3
        private const val VIEW_TYPE_NAVIGATION = 4
        private const val VIEW_TYPE_FILTER = 5
        internal const val POSITION_USER_DETAIL = 0
        private const val PAYLOAD_BADGE = "payload_badge"
        private const val PAYLOAD_PREMIUM_SUBSCRIPTION_STATUS = "payload_premium_subscription_status"
        private const val PAYLOAD_NEW_APP_VERSION = "payload_new_app_version"
    }

    internal abstract class RecyclerViewAdapterFilter : Filter() {
        @Volatile
        var cancel = false
    }

}
