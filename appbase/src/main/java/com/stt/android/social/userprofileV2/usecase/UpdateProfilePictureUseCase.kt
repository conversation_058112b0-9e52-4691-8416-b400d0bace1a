package com.stt.android.social.userprofileV2.usecase

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.util.Size
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.BackendController
import com.stt.android.controllers.CurrentUserController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.UserSession
import com.stt.android.domain.user.User
import com.stt.android.multimedia.MediaStoreUtils
import com.stt.android.ui.utils.BitmapUtils
import com.stt.android.utils.FileUtils
import com.stt.android.utils.STTConstants
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import javax.inject.Inject

abstract class UpdateProfilePictureUseCase(
    private val currentUserController: CurrentUserController,
    private val backendController: BackendController,
    private val fileUtils: FileUtils,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) {
    protected abstract val tempFileName: String
    protected abstract val cropSize: Size
    protected abstract fun pushBackend(
        backendController: BackendController,
        userSession: UserSession,
        absolutePath: String,
    )

    val tempProfilePictureFile: File
        @Throws(IllegalStateException::class)
        get() = fileUtils.getCachedFilePath(STTConstants.DIRECTORY_MISC, tempFileName)

    operator fun invoke(data: Intent, context: Context) = flow {
        emit(UploadImageState(isLoading = true))
        runSuspendCatching {
            // some app returns the URI in data.getData(), while others simply put the cropped
            // image file in the path set by the MediaStore.EXTRA_OUTPUT extra in the request Intent
            val uri: Uri = data.data ?: Uri.fromFile(tempProfilePictureFile)
            // some picture chooser apps will not respect mime types and still return GIFs'
            if (MediaStoreUtils.isSupportedImageMimeType(context, uri)) {
                runSuspendCatching {
                    emit(
                        UploadImageState(
                            isLoading = false,
                            user = uploadProfilePicture(context, uri),
                        )
                    )
                }.onFailure { e ->
                    Timber.w(e, "Failed to upload profile picture")
                    emit(
                        UploadImageState(
                            isLoading = false,
                            error = e,
                        )
                    )
                }
            } else {
                emit(
                    UploadImageState(
                        isLoading = false,
                        error = IllegalArgumentException("Unsupported image type"),
                    )
                )
            }
        }.onFailure { e ->
            Timber.w(e, "Failed to decode profile image")
            emit(
                UploadImageState(
                    isLoading = false,
                    error = e,
                )
            )
        }
    }

    private suspend fun uploadProfilePicture(context: Context, pictureUri: Uri): User? =
        withContext(coroutinesDispatchers.io) {
            val bitmap = BitmapUtils.decodeAndRotate(
                context,
                pictureUri,
                cropSize.width,
                cropSize.height,
            ) ?: throw FileNotFoundException("Picture not found, URI: $pictureUri")

            val profilePicture = tempProfilePictureFile
            ByteArrayOutputStream().use { baos ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, 90, baos)
                FileOutputStream(profilePicture).use { fos ->
                    fos.write(baos.toByteArray())
                }
            }

            currentUserController.session?.let { userSession ->
                pushBackend(backendController, userSession, profilePicture.absolutePath)
                val user = backendController.fetchSessionUser(userSession).user
                currentUserController.store(user)
                user
            }
        }
}

data class UploadImageState(
    val isLoading: Boolean = false,
    val user: User? = null,
    val error: Throwable? = null
)

class UpdateAvatarUseCase @Inject constructor(
    currentUserController: CurrentUserController,
    backendController: BackendController,
    fileUtils: FileUtils,
    coroutinesDispatchers: CoroutinesDispatchers,
) : UpdateProfilePictureUseCase(
    currentUserController,
    backendController,
    fileUtils,
    coroutinesDispatchers,
) {
    override val tempFileName: String = "temp_profile.jpg"
    override val cropSize: Size = CropProfilePictureParams.CLICK_AVATAR.run { Size(width, height) }

    override fun pushBackend(
        backendController: BackendController,
        userSession: UserSession,
        absolutePath: String,
    ) {
        backendController.pushUserProfileImage(userSession, absolutePath)
    }
}

class UpdateCoverUseCase @Inject constructor(
    currentUserController: CurrentUserController,
    backendController: BackendController,
    fileUtils: FileUtils,
    coroutinesDispatchers: CoroutinesDispatchers,
) : UpdateProfilePictureUseCase(
    currentUserController,
    backendController,
    fileUtils,
    coroutinesDispatchers,
) {
    override val tempFileName: String = "temp_cover_photo.jpg"
    override val cropSize: Size = CropProfilePictureParams.CLICK_COVER.run { Size(width, height) }

    override fun pushBackend(
        backendController: BackendController,
        userSession: UserSession,
        absolutePath: String,
    ) {
        backendController.pushUserCoverImage(userSession, absolutePath)
    }
}
