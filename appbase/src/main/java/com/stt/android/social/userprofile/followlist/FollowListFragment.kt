package com.stt.android.social.userprofile.followlist

import android.os.Bundle
import android.view.View
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import com.stt.android.R
import com.stt.android.common.ui.observeNotNull
import com.stt.android.common.viewstate.ViewStateListFragment2
import com.stt.android.social.userprofile.UserProfileActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class FollowListFragment : ViewStateListFragment2<FollowListData, FollowListViewModel>() {
    override val viewModel: FollowListViewModel by viewModels()

    override val layoutId = R.layout.fragment_follow_list

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val followListType = arguments?.getString(FOLLOW_TYPE)?.run {
            FollowListType.valueOf(this)
        }
        val username = arguments?.getString(USERNAME)
        if (username != null && followListType != null) {
            viewModel.loadData(followListType, username)
        }
        viewModel.userClickedEvent.observeNotNull(viewLifecycleOwner) {
            openUserProfileActivity(it)
        }
    }

    fun openUserProfileActivity(username: String) {
        startActivity(UserProfileActivity.newStartIntent(requireContext(), username, false))
    }

    companion object {
        const val FRAGMENT_TAG = "com.stt.android.social.userprofile.followlist.FollowListFragment"

        fun newInstance(followListType: String, username: String): FollowListFragment {
            return FollowListFragment().apply {
                arguments = bundleOf(
                    FOLLOW_TYPE to followListType,
                    USERNAME to username
                )
            }
        }
    }
}
