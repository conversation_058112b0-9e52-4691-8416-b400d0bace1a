package com.stt.android.social.userprofile.followlist

import android.content.Context
import androidx.annotation.StringRes
import com.stt.android.R
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.ViewStateEpoxyController
import com.stt.android.di.FragmentContext
import com.stt.android.followListHeaderItem
import com.stt.android.followListItem
import javax.inject.Inject

class FollowListController
@Inject constructor(
    @FragmentContext val context: Context
) : ViewStateEpoxyController<FollowListData?>() {
    override fun buildModels(viewState: ViewState<FollowListData?>) {
        viewState.data?.let { data ->
            val titleId = getFollowListTitleId(data.followListType)
            followListHeaderItem {
                id("headerItem")
                title(context.getString(titleId))
            }
            data.userFollowStatusList.forEach { userFollowStatus ->
                followListItem {
                    id(userFollowStatus.username)
                    userFollowStatus(userFollowStatus)
                    showFollowButton(userFollowStatus.username != data.currentUsername)
                    onFollowClickListener { model, _, _, _ ->
                        data.onFollowButtonClicked.invoke(
                            data.followListType,
                            data.username,
                            model.userFollowStatus()
                        )
                    }
                    onUserClickListener { model, _, _, _ ->
                        data.onUserClicked.invoke(
                            data.followListType,
                            data.username,
                            model.userFollowStatus()
                        )
                    }
                }
            }
        }
        super.buildModels(viewState)
    }

    @StringRes
    private fun getFollowListTitleId(followListType: FollowListType): Int = when (followListType) {
        FollowListType.FOLLOWERS -> R.string.followers
        FollowListType.FOLLOWING -> R.string.following
    }
}
