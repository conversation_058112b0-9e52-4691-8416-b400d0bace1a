package com.stt.android.social.userprofileV2

import androidx.annotation.StringRes
import com.stt.android.domain.report.block.BlockStatus
import com.stt.android.domain.user.User
import com.stt.android.social.friends.Friend

data class UserProfileState(
    val user: User,
    val isCurrentUser: <PERSON><PERSON><PERSON>,
    val blockStatus: BlockStatus,
    val friend: Friend? = null,
    val uploadingAvatar: Boolean = false,
)

sealed interface UserProfileEvent

data object FollowerRemoved : UserProfileEvent
data object UserReported : UserProfileEvent
data object UserDuplicateReported : UserProfileEvent
data class UserProfileError(val throwable: Throwable) : UserProfileEvent

data class ConfirmDialogEvent(
    @StringRes val title: Int,
    @StringRes val message: Int,
    @StringRes val confirmText: Int,
    val onConfirm: () -> Unit,
) : UserProfileEvent
