package com.stt.android.social.userprofile

import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.stt.android.R
import com.stt.android.ui.components.PictureThumbnailView

class UserPictureViewHolder(
    view: View,
    firstVisibleItem: Int,
    callback: Callback
) : RecyclerView.ViewHolder(view) {

    interface Callback {
        /**
         * Called when thumbnail list scroll position was updated
         * @param position Position of the first completely visible item
         */
        fun scrollPositionUpdated(position: Int)
    }

    private val pictureThumbnailView = view.findViewById<PictureThumbnailView>(R.id.pictureThumbnails)

    init {
        val layoutManager = pictureThumbnailView.recyclerView.layoutManager
        layoutManager?.scrollToPosition(firstVisibleItem)

        pictureThumbnailView.recyclerView.addOnScrollListener(object :
            RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    if (layoutManager is LinearLayoutManager) {
                        callback.scrollPositionUpdated(layoutManager.findFirstCompletelyVisibleItemPosition())
                    }
                }
            }
        })
    }
}
