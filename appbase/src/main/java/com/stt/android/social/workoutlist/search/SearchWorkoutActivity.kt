package com.stt.android.social.workoutlist.search

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.databinding.ComposeMapSnapshotterBinding
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.maps.MapSnapshotter
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class SearchWorkoutActivity : AppCompatActivity() {
    @Inject
    lateinit var rewriteNavigator: WorkoutDetailsRewriteNavigator

    @Inject
    lateinit var mapSnapshotter: MapSnapshotter

    private val binding: ComposeMapSnapshotterBinding by lazy {
        ComposeMapSnapshotterBinding.inflate(layoutInflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                mapSnapshotter.runSnapshotterEngine(this@SearchWorkoutActivity)
            }
        }
        setContentView(binding.root)
        binding.composeView.setContentWithM3Theme {
            SearchWorkoutScreen(
                onBackClicked = ::finish,
                openWorkout = { workoutHeader ->
                    rewriteNavigator.navigate(
                        context = this@SearchWorkoutActivity,
                        username = workoutHeader.username,
                        workoutId = workoutHeader.id,
                        workoutKey = workoutHeader.key,
                    )
                }
            )
        }
    }

    @Composable
    fun SearchWorkoutScreen(
        onBackClicked: () -> Unit,
        openWorkout: (WorkoutHeader) -> Unit,
        modifier: Modifier = Modifier,
        viewModel: SearchWorkoutViewModel = hiltViewModel(),
    ) {
        val viewState by viewModel.viewState.collectAsState()
        SearchWorkoutContent(
            viewState = viewState,
            onBackClick = onBackClicked,
            onQueryChange = viewModel::onQueryChange,
            openWorkout = openWorkout,
            modifier = modifier,
        )
    }

    companion object {
        internal const val KEY_USERNAME = "username"

        @JvmStatic
        fun newStartIntent(context: Context, username: String?, trackPageName: String): Intent {
            return Intent(context, SearchWorkoutActivity::class.java)
                .putExtra(KEY_USERNAME, username)
                .putExtra(AnalyticsEventProperty.PAGE_NAME, trackPageName)
        }
    }
}
