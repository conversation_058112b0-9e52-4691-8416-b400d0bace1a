package com.stt.android.social.notifications.list

import com.emarsys.mobileengage.api.inbox.Message
import org.json.JSONObject
import timber.log.Timber
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId

typealias onInboxItemClick = (EmarsysInboxItem, EmarsysAction) -> (Unit)

data class EmarsysInboxItem(
    val id: String,
    val title: String,
    val body: String,
    val imageUrl: String?,
    val actions: List<EmarsysAction>? = null,
    val onInboxItemClick: onInboxItemClick? = null,
    val onInboxItemLongClick: ((EmarsysInboxItem) -> Unit)? = null,
    val onInboxItemExposure: ((EmarsysInboxItem) -> Unit)? = null,
    val tags: List<String>? = null,
    val messageType: EmarsysInboxType,
    // unix timestamp
    val receivedAtTime: Long,
    // clickDelete = true, It will be deleted when user clicked
    val clickDelete: Boolean = false,
    val formattedDateTime: String,
    val source: EmarsysInboxSource,
) {

    val unOpened: Boolean
        get() = tags?.contains(InboxMessageTag.OPENED.tag) != true
    val unSeen: Boolean
        get() = tags?.contains(InboxMessageTag.SEEN.tag) != true
    val deleted: Boolean
        get() = tags?.contains(InboxMessageTag.DELETED.tag) == true
}

enum class EmarsysInboxType {
    TEXT,
    BIG_IMAGE,
    ANNUAL_REPORT,
    H5,
}

enum class EmarsysInboxSource {
    EMARSYS,
    SUUNTO,
}

fun Message.getType(): EmarsysInboxType {
    return when {
        properties?.get("type") == "annualReport" -> EmarsysInboxType.ANNUAL_REPORT
        properties?.get("type") == "H5" -> EmarsysInboxType.H5
        !imageUrl.isNullOrEmpty() -> EmarsysInboxType.BIG_IMAGE
        else -> EmarsysInboxType.TEXT
    }
}

/**
 * if this inbox message is test message, it will be deleted after five minutes
 */
fun Message.testMessage(): Boolean {
    return properties?.get("hide") == "test"
}

fun Message.clickDeleteMessage(): Boolean {
    return properties?.get("hide") == "del"
}

/**
 * Parse the expiry date from the Inbox message's JSON settings, if available.
 * This lets us hide expired messages—especially when the Recipient Source is Automation in Emarsys,
 * since the built-in message expiry option cannot be used.
 */
fun Message.isPastCustomExpiryDate(): Boolean {
    val customExpiryDate = getCustomExpiryDate() ?: return false
    return LocalDateTime.now().isAfter(customExpiryDate)
}

/**
 * Parse the expiry date from the Inbox message's JSON settings, if available.
 * This lets us hide expired messages—especially when the Recipient Source is Automation in Emarsys,
 * since the built-in message expiry option cannot be used.
 */
private fun Message.getCustomExpiryDate(): LocalDateTime? {
    val messageJson = properties?.get("message") ?: return null
    val expiryDate = JSONObject(messageJson).getString("expiry_date") ?: return null
    return try {
        val instant = Instant.parse(expiryDate)
        LocalDateTime.ofInstant(instant, ZoneId.systemDefault())
    } catch (e: Exception) {
        Timber.w(e, "Parsing expiry_date from Emarsys message failed. expiry_date:$expiryDate")
        null
    }
}

data class EmarsysAction(
    val id: String,
    val title: String,
    val type: EmarsysActionType,
    val url: String,
    val eventName: String? = null
)

enum class EmarsysActionType {
    OPEN_EXTERNAL_URL,
    DEEP_LINK,
    OPEN_ANNUAL_REPORT_URL,
    H5,
}
