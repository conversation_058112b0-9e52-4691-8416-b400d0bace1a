package com.stt.android.social.notifications.inbox

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import androidx.activity.viewModels
import androidx.core.net.toUri
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.OnScrollListener
import com.stt.android.R
import com.stt.android.analytics.MessageSource
import com.stt.android.common.ui.ViewModelActivity2
import com.stt.android.controllers.CurrentUserController
import com.stt.android.databinding.ActivityMarketingInboxBinding
import com.stt.android.home.marketing.MarketingH5Activity
import com.stt.android.launcher.DeepLinkIntentBuilder
import com.stt.android.social.notifications.InboxMessageHandler
import com.stt.android.social.notifications.list.EmarsysActionType
import com.stt.android.ui.utils.WideScreenPaddingDecoration
import com.stt.android.utils.CustomTabsUtils
import dagger.hilt.android.AndroidEntryPoint
import java.util.Locale
import javax.inject.Inject

@AndroidEntryPoint
class MarketingInboxActivity : ViewModelActivity2() {

    override val viewModel: MarketingInboxHolderViewModel by viewModels()

    private val viewDataBinding: ActivityMarketingInboxBinding get() = requireBinding()

    @Inject
    lateinit var marketingInboxController: MarketingInboxController

    @Inject
    lateinit var currentUserController: CurrentUserController

    @Inject
    lateinit var intentBuilder: DeepLinkIntentBuilder

    @Inject
    lateinit var inboxMessageHandler: InboxMessageHandler

    override fun getLayoutResId() = R.layout.activity_marketing_inbox

    public override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setSupportActionBar(viewDataBinding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowTitleEnabled(false)
        }
        initRecyclerView()
        viewModel.inboxDataEvent.observe(this) {
            marketingInboxController.setData(it)
        }
        val messageSource = getMessageSource()
        viewModel.inboxClickEvent.observe(this) {
            when (it.type) {
                EmarsysActionType.OPEN_ANNUAL_REPORT_URL -> {
                    inboxMessageHandler.openAnnualReport(this, it.url, messageSource?.name ?: "")
                }

                EmarsysActionType.H5 -> {
                    startActivity(
                        MarketingH5Activity.newIntent(
                            context = this,
                            url = it.url,
                            messageSource = MessageSource.INBOX,
                        )
                    )
                }

                EmarsysActionType.OPEN_EXTERNAL_URL -> {
                    launchBrowser(it.url)
                }

                EmarsysActionType.DEEP_LINK -> {
                    val uri = it.url.toUri()
                    val fragmentOrPathParts = intentBuilder.getFragmentsOrPathParts(uri)
                    val type = fragmentOrPathParts.getOrNull(1)?.lowercase(Locale.ROOT) ?: ""
                    val intent = intentBuilder.getDeepLinkIntent(
                        this,
                        uri,
                        currentUserController,
                        fragmentOrPathParts,
                        type
                    ) ?: return@observe
                    startActivity(intent)
                }
            }
        }
        viewDataBinding.list.addOnScrollListener(object : OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                val layoutManager = recyclerView.layoutManager
                if (layoutManager is LinearLayoutManager) {
                    val firstVisibleItemPosition =
                        layoutManager.findFirstCompletelyVisibleItemPosition()
                    val lastVisibleItemPosition =
                        layoutManager.findLastCompletelyVisibleItemPosition()
                    val readPosRange = firstVisibleItemPosition..lastVisibleItemPosition
                    viewModel.updateMessageReadState(readPosRange)
                }
            }
        })
    }

    private fun getMessageSource(): MessageSource? {
        val messageSource = intent.getStringExtra(KEY_MESSAGE_SOURCE)
        return when {
            messageSource?.isNotEmpty() == true -> MessageSource.entries.find { it.name == messageSource }
            // Tencent push message
            intent.data.toString().contains("com.tpns.push") -> MessageSource.PUSH
            // from deeplink by default
            else -> MessageSource.POPUP
        }
    }

    private fun initRecyclerView() {
        viewDataBinding.list.apply {
            adapter = marketingInboxController.adapter
            setHasFixedSize(true)
            addItemDecoration(WideScreenPaddingDecoration(resources, theme))
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                // This activity has multiple parents, so up navigation equals back button press.
                onBackPressedDispatcher.onBackPressed()
                true
            }

            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun launchBrowser(urlString: String) {
        CustomTabsUtils.launchCustomTab(this, urlString.toUri())
    }

    companion object {
        private const val KEY_MESSAGE_SOURCE = "key_message_source"

        fun newIntent(context: Context): Intent {
            return Intent(context, MarketingInboxActivity::class.java)
        }

        @JvmStatic
        fun newIntent(context: Context, source: MessageSource): Intent {
            return Intent(context, MarketingInboxActivity::class.java).apply {
                putExtra(KEY_MESSAGE_SOURCE, source.name)
            }
        }
    }
}
