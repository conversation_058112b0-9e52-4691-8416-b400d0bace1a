package com.stt.android.social.userprofile

import android.net.Uri
import android.os.Bundle
import com.stt.android.home.people.FollowStatusView
import kotlinx.coroutines.CoroutineScope
import java.io.File

interface UserDetailView : FollowStatusView {

    val animationBundleForProfileImage: Bundle?

    fun showEditPicture()

    fun hideEditPicture()

    fun onEditProfileImageClicked(width: Int, height: Int, tempProfilePictureFile: File)

    fun onProfileImageClicked(profileImageUrl: String)

    fun onPictureUpdated(picture: Uri)

    fun onPictureUploaded()

    fun onPictureUploadFailed(errorCode: Int)

    fun onUserProfileLoadFailed()

    fun hideFollowActions()

    fun onStartUploadPicture()

    fun getCoroutineScope(): CoroutineScope
}
