package com.stt.android.social.userprofile;

import android.content.Context;
import com.stt.android.analytics.AmplitudeAnalyticsTracker;
import com.stt.android.common.coroutines.CoroutinesDispatchers;
import com.stt.android.controllers.BackendController;
import com.stt.android.controllers.CurrentUserController;
import com.stt.android.controllers.SessionController;
import com.stt.android.controllers.UserSettingsController;
import com.stt.android.controllers.WorkoutHeaderController;
import com.stt.android.follow.UserFollowStatus;
import com.stt.android.home.people.PeopleController;
import com.stt.android.utils.FileUtils;
import dagger.Module;
import dagger.Provides;
import dagger.hilt.InstallIn;
import dagger.hilt.android.components.ActivityComponent;
import dagger.hilt.android.scopes.ActivityScoped;
import javax.inject.Named;
import rx.subjects.Subject;

@Module
@InstallIn(ActivityComponent.class)
public class UserProfileModule {

    @Provides
    @ActivityScoped
    public UserDetailPresenter provideUserDetailPresenter(Context applicationContext,
        SessionController sessionController, CurrentUserController currentUserController,
        UserSettingsController userSettingsController,
        WorkoutHeaderController workoutHeaderController, BackendController backendController,
        FileUtils fileUtils, AmplitudeAnalyticsTracker amplitudeAnalyticsTracker, PeopleController peopleController,
        @Named("FOLLOWING") Subject<UserFollowStatus, UserFollowStatus> followingSubject,
        @Named("FOLLOWERS") Subject<UserFollowStatus, UserFollowStatus> followersSubject,
        CoroutinesDispatchers coroutinesDispatchers) {
        return new UserDetailPresenter(applicationContext, sessionController, currentUserController,
            userSettingsController, workoutHeaderController, backendController, amplitudeAnalyticsTracker, fileUtils,
            peopleController, followingSubject.asObservable().onBackpressureBuffer(),
            followersSubject.asObservable().onBackpressureBuffer(), coroutinesDispatchers);
    }
}
