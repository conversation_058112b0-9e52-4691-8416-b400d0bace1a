package com.stt.android.social.userprofileV2

import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.stt.android.R
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.user.UserGearLatest
import com.stt.android.domain.workout.ActivityType
import com.suunto.connectivity.suuntoconnectivity.device.ProductType

data class SettingMenuInfo(
    val type: SettingMenuType,
    @DrawableRes val icon: Int,
    val title: String,
)

enum class SettingMenuType {
    HEADPHONES,
    SETTING,
    FIND_PEOPLE,
    FEEDBACK,
    REPAIR_SERVICE,
    PARTNER_SERVICE,
    SUPPORT,
    CHAT_BOT,
    CONTACT_CUSTOMER_SERVICE,
}

enum class MoreMenu(@StringRes val label: Int) {
    MAP(R.string.map),
    REMOVE_FOLLOWER(R.string.remove),
    BLOCK(R.string.block),
    UNBLOCK_USER(R.string.unBlock),
    REPORT(R.string.report)
}

data class DeviceType(@DrawableRes val icon: Int?, val deviceName: String)

fun UserGearLatest.toDeviceType(): DeviceType = DeviceType(
    icon = ProductType.valueOf(productType).deviceIconFromProductType(),
    deviceName = displayName
)

private fun ProductType.deviceIconFromProductType(): Int = when (this) {
    ProductType.SPORT_WATCH,
    ProductType.DIVE_WATCH -> R.drawable.ic_watch_basic_outline
    ProductType.DIVE_COMPUTER -> R.drawable.ic_device_model_eon
    ProductType.BIKE_COMPUTER -> R.drawable.ic_device_model_bike
    ProductType.SPORT_EARPHONE -> R.drawable.headphone_outline
}

data class WorkoutSummaryStats(
    val totalDistanceSum: Double,
    val totalTimeSum: Double,
    val totalNumberOfWorkoutsSum: Int,
    val totalDays: Int,
    val activityTypeStats: List<TopActivity>
)

data class FollowCountStats(
    val followersCount: Int,
    val followingCount: Int,
)

data class TopActivity(
    val activityType: ActivityType,
    val duration: Double,
    val distance: Double,
    val progress: Float,
    val measurementUnit: MeasurementUnit = MeasurementUnit.IMPERIAL
)
