package com.stt.android.social.workoutlist.ui

import android.annotation.SuppressLint
import com.stt.android.core.R  as CoreR
import androidx.annotation.StringRes
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ScrollableTabRow
import androidx.compose.material3.Tab
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.stt.android.R
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.material3.bodyXLarge
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.home.dashboardv2.ui.widgets.common.formatWidgetDurationTitle
import com.stt.android.social.userprofileV2.TopActivity
import com.stt.android.social.userprofileV2.WorkoutSummaryStats
import com.stt.android.ui.components.workout.WorkoutCard
import com.stt.android.ui.utils.TextFormatter
import kotlinx.coroutines.launch
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material3.Icon
import androidx.compose.material3.SuggestionChip
import androidx.compose.material3.SuggestionChipDefaults
import androidx.compose.material3.Surface
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.platform.LocalWindowInfo
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.pluralStringResource
import androidx.compose.ui.text.style.TextAlign
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.cloudyGrey
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.compose.widgets.CustomHeightTabIndicator
import com.stt.android.domain.workout.ActivityType
import com.stt.android.social.friends.composables.EmptyView
import com.stt.android.social.workoutlist.AllWorkoutViewModel.ViewData
import com.stt.android.social.workoutlist.DateHeader
import com.stt.android.social.workoutlist.OperationHistory
import com.stt.android.social.workoutlist.WorkoutPost
import com.stt.android.ui.components.workout.WorkoutCardViewModel
import com.stt.android.utils.isPositive
import kotlinx.coroutines.flow.drop

@SuppressLint("ConfigurationScreenWidthHeight")
@Composable
fun WorkoutListTab(
    viewState: ViewData,
    openWorkout: (WorkoutHeader) -> Unit,
    onRetryClicked: () -> Unit,
    modifier: Modifier = Modifier,
    lazyListState: LazyListState = rememberLazyListState(),
    measurementUnit: MeasurementUnit = MeasurementUnit.METRIC,
    nestedScrollConnection: NestedScrollConnection? = null,
    workoutSummaryStats: WorkoutSummaryStats? = null,
    onWorkoutPostClick: ((index: Int, workoutPost: WorkoutPost) -> Unit)? = null,
    lastOperationHistory: OperationHistory? = null,
    onTabSelected: (Int) -> Unit = {},
    onFilterSelected: (String) -> Unit = {},
) {
    val noShowingWorkout =
        viewState is ViewData.Loaded && viewState.dateAndWorkouts.none { it.value.any() }
    val noWorkout = noShowingWorkout && (workoutSummaryStats == null || workoutSummaryStats.totalNumberOfWorkoutsSum == 0)
    val photos = (viewState as? ViewData.Loaded)?.posts ?: emptyList()
    val tabs = Tab.entries.toTypedArray().mapNotNull {
        if (it == Tab.POSTS && (photos.none() || noShowingWorkout)) null else it
    }

    val initialPage = remember(lastOperationHistory, tabs) {
        (lastOperationHistory?.lastTabIndex ?: 0).coerceIn(0, tabs.lastIndex)
    }
    val pagerState = rememberPagerState(initialPage = initialPage) { tabs.size }
    LaunchedEffect(lastOperationHistory, tabs.size) {
        val page = lastOperationHistory?.lastTabIndex ?: pagerState.currentPage
        pagerState.scrollToPage(page.coerceIn(0, tabs.lastIndex))
    }

    val allCategories = (viewState as? ViewData.Loaded)?.dateAndWorkouts?.keys?.toList() ?: emptyList()
    val (selectedCategory, setSelectedCategory) = remember(allCategories, lastOperationHistory?.lastWorkoutFilter) {
        val selectedFilter = if (
            lastOperationHistory?.lastWorkoutFilter != null &&
            allCategories.contains(lastOperationHistory.lastWorkoutFilter)
        ) {
            lastOperationHistory.lastWorkoutFilter
        } else {
            allCategories.firstOrNull() ?: ""
        }
        mutableStateOf(selectedFilter)
    }

    val coroutineScope = rememberCoroutineScope()
    val density = LocalDensity.current
    val screenHeight = LocalWindowInfo.current.containerSize.height
    val statusBarHeight = WindowInsets.statusBars.getTop(density)
    val navigationBarHeight = WindowInsets.navigationBars.getBottom(density)
    val tabHeight = 48.dp
    val topBarHeight = 52.dp
    val availableHeight = with(density) {
        (screenHeight - statusBarHeight - topBarHeight.toPx() - tabHeight.toPx() - navigationBarHeight).toDp()
    }
    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(MaterialTheme.colorScheme.surface)
    ) {
        if (!noWorkout) {
            ScrollableTabRow(
                selectedTabIndex = pagerState.currentPage.coerceIn(0, tabs.lastIndex),
                modifier = Modifier.height(tabHeight),
                containerColor = Color.Transparent,
                contentColor = MaterialTheme.colorScheme.primary,
                edgePadding = 0.dp,
                divider = {},
                indicator = { tabPositions ->
                    CustomHeightTabIndicator(
                        tabPositions = tabPositions,
                        selectedTabIndex = pagerState.currentPage,
                    )
                }
            ) {
                tabs.forEachIndexed { index, tab ->
                    Tab(
                        selected = pagerState.currentPage == index,
                        onClick = {
                            coroutineScope.launch {
                                pagerState.animateScrollToPage(index)
                            }
                        },
                        text = {
                            Text(
                                text = stringResource(tab.title),
                                style = MaterialTheme.typography.bodyLargeBold,
                            )
                        },
                        selectedContentColor = MaterialTheme.colorScheme.primary,
                        unselectedContentColor = MaterialTheme.colorScheme.darkGrey,
                    )
                }
            }
            HorizontalDivider()
        }
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(availableHeight)
        ) {
            HorizontalPager(
                state = pagerState,
                modifier = Modifier
                    .fillMaxSize()
                    .narrowContentWithBgColors(
                        backgroundColor = MaterialTheme.colorScheme.surface,
                        outerBackgroundColor = MaterialTheme.colorScheme.background,
                    )
            ) { page ->
                when (tabs[page]) {
                    Tab.ACTIVITIES -> ActivitiesContent(
                        viewState = viewState,
                        onRetryClicked = onRetryClicked,
                        openWorkout = openWorkout,
                        onFilterSelected = {
                            setSelectedCategory(it)
                            onFilterSelected(it)
                        },
                        workoutSummaryStats = workoutSummaryStats,
                        lazyListState = lazyListState,
                        measurementUnit = measurementUnit,
                        nestedScrollConnection = nestedScrollConnection,
                        lastSelectedFilter = selectedCategory,
                    )

                    Tab.POSTS -> WorkoutPostsContent(
                        workoutPosts = photos,
                        onWorkoutPostClick = onWorkoutPostClick,
                        nestedScrollConnection = nestedScrollConnection,
                    )
                }
            }
        }
    }
    LaunchedEffect(pagerState) {
        snapshotFlow { pagerState.currentPage }
            .drop(1)
            .collect { pageIndex ->
                onTabSelected(pageIndex)
            }
    }
}

@Composable
@Suppress("ktlint:compose:vm-forwarding-check")
fun ActivitiesContentLoaded(
    dateAndWorkouts: Map<String, List<Any>>,
    openWorkout: (WorkoutHeader) -> Unit,
    modifier: Modifier = Modifier,
    lazyListState: LazyListState = rememberLazyListState(),
    measurementUnit: MeasurementUnit = MeasurementUnit.METRIC,
    workoutSummaryStats: WorkoutSummaryStats? = null,
    nestedScrollConnection: NestedScrollConnection? = null,
    showFilter: Boolean = false,
    showDate: Boolean = false,
    lastSelectedFilter: String = "",
    onFilterSelected: (String) -> Unit = {},
) {
    val nestedScrollModifier = if (nestedScrollConnection != null) {
        Modifier.nestedScroll(nestedScrollConnection)
    } else {
        Modifier
    }

    val categories = dateAndWorkouts.keys.toList()

    LaunchedEffect(lastSelectedFilter) {
        lazyListState.scrollToItem(0)
    }

    val resources = LocalContext.current.resources

    Column(modifier = modifier.background(MaterialTheme.colorScheme.surface)) {
        if (showFilter) {
            Row(
                modifier = Modifier
                    .horizontalScroll(state = rememberScrollState())
                    .fillMaxWidth()
                    .height(64.dp)
                    .padding(horizontal = MaterialTheme.spacing.medium),
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                categories.forEach { category ->
                    Category(
                        text = ActivityType.values().find { it.id == category.toIntOrNull() }
                            ?.getLocalizedName(resources) ?: category,
                        selected = category == lastSelectedFilter,
                        onClick = {
                            onFilterSelected(category)
                        },
                    )
                }
            }
        }

        LazyColumn(
            state = lazyListState,
            modifier = Modifier
                .fillMaxSize()
                .narrowContent()
                .then(nestedScrollModifier),
        ) {
            val selectedItems = dateAndWorkouts[lastSelectedFilter] ?: emptyList()
            val notAllShown = (workoutSummaryStats?.totalNumberOfWorkoutsSum ?: 0) >
                selectedItems.count { it is WorkoutHeader }
            if (workoutSummaryStats != null && selectedItems.isNotEmpty()) {
                item(key = "workout_stats") {
                    WorkoutStats(
                        unit = measurementUnit,
                        workoutSummaryStats = workoutSummaryStats,
                    )
                }
            }

            if (showDate) {
                val dateHeaderIndices =
                    selectedItems.indices.filter { selectedItems[it] is DateHeader }
                dateHeaderIndices.forEachIndexed { i, headerIndex ->
                    val nextHeaderIndex = dateHeaderIndices.getOrNull(i + 1) ?: selectedItems.size
                    val dateHeader = selectedItems[headerIndex] as DateHeader

                    stickyHeader(key = "sticky_${dateHeader.dateText}") {
                        DateHeader(
                            itemData = dateHeader,
                            modifier = Modifier.background(MaterialTheme.colorScheme.surface)
                        )
                    }

                    val workoutsInSection = selectedItems.subList(headerIndex + 1, nextHeaderIndex)
                        .filterIsInstance<WorkoutHeader>()

                    items(
                        items = workoutsInSection,
                        key = WorkoutHeader::id,
                    ) { workoutHeader ->
                        WorkoutCard(
                            workoutHeader = workoutHeader,
                            onClick = { openWorkout(workoutHeader) },
                            modifier = Modifier.padding(
                                vertical = MaterialTheme.spacing.small,
                                horizontal = MaterialTheme.spacing.medium,
                            ),
                            configuration = WorkoutCardViewModel.Configuration(
                                showCoverMap = true,
                                showCoverImage = true,
                                showWeather = true,
                                showUser = false,
                            ),
                        )
                    }
                }
            } else {
                items(
                    items = selectedItems.filterIsInstance<WorkoutHeader>(),
                    key = WorkoutHeader::id,
                ) { workoutHeader ->
                    WorkoutCard(
                        workoutHeader = workoutHeader,
                        onClick = { openWorkout(workoutHeader) },
                        modifier = Modifier.padding(
                            vertical = MaterialTheme.spacing.small,
                            horizontal = MaterialTheme.spacing.medium,
                        ),
                        configuration = WorkoutCardViewModel.Configuration(
                            showCoverMap = true,
                            showCoverImage = true,
                            showWeather = true,
                            showUser = false,
                        ),
                    )
                }

                if (notAllShown) {
                    item("only_show_public_activities") {
                        Text(
                            text = stringResource(R.string.only_show_public_activities),
                            color = MaterialTheme.colorScheme.secondary,
                            textAlign = TextAlign.Center,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(
                                    vertical = MaterialTheme.spacing.medium,
                                )
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun DateHeader(itemData: DateHeader, modifier: Modifier = Modifier) {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
            .fillMaxWidth()
            .padding(
                vertical = MaterialTheme.spacing.small,
                horizontal = MaterialTheme.spacing.medium,
            ),
    ) {
        Text(
            text = itemData.dateText,
            style = MaterialTheme.typography.body,
        )
        Text(
            text = pluralStringResource(
                R.plurals.workouts_plural,
                itemData.workoutCount,
                itemData.workoutCount,
            ),
            style = MaterialTheme.typography.body,
        )
    }
}

@Composable
private fun ActivitiesContent(
    viewState: ViewData,
    openWorkout: (WorkoutHeader) -> Unit,
    onRetryClicked: () -> Unit,
    lastSelectedFilter: String,
    onFilterSelected: (String) -> Unit,
    modifier: Modifier = Modifier,
    lazyListState: LazyListState = rememberLazyListState(),
    measurementUnit: MeasurementUnit = MeasurementUnit.METRIC,
    workoutSummaryStats: WorkoutSummaryStats? = null,
    nestedScrollConnection: NestedScrollConnection? = null,
) {
    when (viewState) {
        is ViewData.Loading -> ActivitiesLoadingScreen(modifier)
        is ViewData.Loaded -> {
            if (viewState.dateAndWorkouts.none { it.value.any() }) {
                if (nestedScrollConnection != null) { // Show as item in list
                    Column(modifier = modifier.fillMaxSize()) {
                        if (workoutSummaryStats?.totalNumberOfWorkoutsSum.isPositive()) {
                            WorkoutStats(
                                unit = measurementUnit,
                                workoutSummaryStats = workoutSummaryStats,
                            )
                        }
                        EmptyView(
                            icon = R.drawable.ic_svg_no_activity,
                            tips = R.string.only_show_public_activities,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = MaterialTheme.spacing.large),
                        )
                    }
                } else {
                    EmptyView(
                        icon = R.drawable.ic_svg_no_activity,
                        tips = R.string.no_activities_no_tracking,
                        modifier = modifier.fillMaxSize(),
                    )
                }
            } else {
                ActivitiesContentLoaded(
                    dateAndWorkouts = viewState.dateAndWorkouts,
                    openWorkout = openWorkout,
                    modifier = if (nestedScrollConnection != null) {
                        modifier.nestedScroll(nestedScrollConnection)
                    } else modifier,
                    lazyListState = lazyListState,
                    measurementUnit = measurementUnit,
                    workoutSummaryStats = workoutSummaryStats,
                    nestedScrollConnection = nestedScrollConnection,
                    showFilter = viewState.showFilterAndDate,
                    showDate = viewState.showFilterAndDate,
                    lastSelectedFilter = lastSelectedFilter,
                    onFilterSelected = onFilterSelected,
                )
            }
        }

        is ViewData.Error -> AllWorkoutErrorScreen(
            onRetryClicked = onRetryClicked,
            modifier = modifier,
        )
    }
}

@Composable
private fun AllWorkoutErrorScreen(
    onRetryClicked: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
    ) {
        Text(
            text = stringResource(R.string.error_0),
            style = MaterialTheme.typography.bodyXLarge,
        )

        Spacer(Modifier.height(MaterialTheme.spacing.small))

        PrimaryButton(
            text = stringResource(R.string.retry),
            onClick = onRetryClicked,
        )
    }
}

@Composable
private fun ActivitiesLoadingScreen(
    modifier: Modifier = Modifier,
) {
    Box(modifier = modifier.fillMaxSize()) {
        CircularProgressIndicator(
            modifier = Modifier
                .align(Alignment.Center)
                .size(48.dp),
            color = MaterialTheme.colorScheme.primary,
        )
    }
}

@Composable
private fun WorkoutPostsContent(
    workoutPosts: List<WorkoutPost>,
    modifier: Modifier = Modifier,
    onWorkoutPostClick: ((index: Int, photo: WorkoutPost) -> Unit)? = null,
    nestedScrollConnection: NestedScrollConnection? = null,
) {
    val nestedScrollModifier = if (nestedScrollConnection != null) {
        Modifier.nestedScroll(nestedScrollConnection)
    } else {
        Modifier
    }
    Box(modifier = modifier.fillMaxSize()) {
        LazyVerticalGrid(
            columns = GridCells.Fixed(2),
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.surface)
                .then(nestedScrollModifier),
            contentPadding = PaddingValues(MaterialTheme.spacing.small),
        ) {
            itemsIndexed(workoutPosts, key = { _, post -> post.id }) { index, workoutPost ->
                val uri = workoutPost.image?.getThumbnailUri(LocalContext.current)
                    ?: workoutPost.video?.getThumbnailUri(LocalContext.current)
                Box(
                    modifier = Modifier.clickableThrottleFirst {
                        onWorkoutPostClick?.invoke(index, workoutPost)
                    }
                ) {
                    AsyncImage(
                        model = ImageRequest.Builder(LocalContext.current)
                            .data(uri)
                            .crossfade(true)
                            .build(),
                        modifier = Modifier
                            .padding(MaterialTheme.spacing.small)
                            .height(256.dp)
                            .fillMaxWidth()
                            .clip(MaterialTheme.shapes.large),
                        contentDescription = null,
                        contentScale = ContentScale.Crop,
                    )
                    if (workoutPost.video != null) {
                        Box(
                            modifier = Modifier
                                .size(40.dp)
                                .align(Alignment.Center)
                                .background(
                                    brush = Brush.radialGradient(
                                        colors = listOf(
                                            MaterialTheme.colorScheme.mediumGrey,
                                            Color.Transparent,
                                        ),
                                    )
                                ),
                        ) {
                            Icon(
                                painter = painterResource(R.drawable.ic_play_fill),
                                contentDescription = null,
                                modifier = Modifier
                                    .align(Alignment.Center)
                                    .size(MaterialTheme.iconSizes.medium),
                                tint = Color.White,
                            )
                        }
                    }
                }
            }
        }
    }
}

private enum class Tab(
    @StringRes val title: Int,
) {
    ACTIVITIES(R.string.home_activities),
    POSTS(R.string.posts),
}

@Composable
fun WorkoutStats(
    unit: MeasurementUnit,
    modifier: Modifier = Modifier,
    workoutSummaryStats: WorkoutSummaryStats? = null,
) {
    val placeholder = AnnotatedString("-")
    val distance = workoutSummaryStats?.totalDistanceSum?.let {
        buildAnnotatedString {
            append(TextFormatter.formatDistanceRounded(unit.toDistanceUnit(it)))
            append(" ")
            withStyle(SpanStyle(fontSize = MaterialTheme.typography.bodySmall.fontSize)) {
                append(stringResource(unit.distanceUnit))
            }
        }

    } ?: placeholder
    val days = workoutSummaryStats?.totalDays?.let { AnnotatedString(it.toString()) } ?: placeholder
    val activityCount =
        workoutSummaryStats?.totalNumberOfWorkoutsSum?.let { AnnotatedString(it.toString()) }
            ?: placeholder
    val duration =
        workoutSummaryStats?.totalTimeSum?.formatWidgetDurationTitle(LocalContext.current)
            ?: placeholder
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(
                horizontal = MaterialTheme.spacing.medium,
                vertical = MaterialTheme.spacing.large,
            ),
        horizontalArrangement = Arrangement.SpaceBetween,
    ) {
        StatItem(
            value = activityCount,
            label = stringResource(R.string.goal_activities)
        )
        StatItem(
            value = distance,
            label = stringResource(R.string.distance),
        )
        StatItem(
            value = duration,
            label = stringResource(R.string.duration)
        )
        StatItem(
            value = days,
            label = stringResource(CoreR.string.days)
        )
    }
}

@Composable
private fun StatItem(
    value: AnnotatedString, label: String,
    modifier: Modifier = Modifier,
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        Text(
            text = value, fontWeight = FontWeight.Bold, fontSize = 16.sp
        )
        Text(
            text = label, color = Color.Gray, fontSize = 12.sp
        )
    }
}

@Composable
private fun Category(
    text: String,
    selected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    SuggestionChip(
        onClick = onClick,
        label = {
            Text(
                modifier = Modifier.defaultMinSize(minWidth = 24.dp),
                text = text,
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center,
            )
        },
        modifier = modifier.height(32.dp),
        enabled = !selected,
        shape = RoundedCornerShape(32.dp),
        colors = SuggestionChipDefaults.suggestionChipColors()
            .copy(
                disabledContainerColor = MaterialTheme.colorScheme.primary,
                disabledLabelColor = MaterialTheme.colorScheme.onPrimary,
            ),
        border = if (selected) null else BorderStroke(1.dp, MaterialTheme.colorScheme.cloudyGrey),
    )
}

@Preview(showBackground = true)
@Composable
private fun WorkoutStatsPreview() {
    M3AppTheme {
        Surface {
            WorkoutStats(
                unit = MeasurementUnit.IMPERIAL,
                workoutSummaryStats = WorkoutSummaryStats(
                    totalDistanceSum = 333.3,
                    totalTimeSum = 10.0,
                    totalNumberOfWorkoutsSum = 10000,
                    totalDays = 333,
                    listOf<TopActivity>()
                )
            )
        }
    }
}

@Preview
@Composable
private fun DateHeaderPreview() {
    M3AppTheme {
        DateHeader(DateHeader("March 2025", 10))
    }
}
