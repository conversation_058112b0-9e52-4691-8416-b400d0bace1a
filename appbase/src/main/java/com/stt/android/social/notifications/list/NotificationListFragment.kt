package com.stt.android.social.notifications.list

import android.os.Bundle
import android.view.View
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.RecyclerView
import com.stt.android.R
import com.stt.android.ThemeColors
import com.stt.android.common.ui.ListFragment2
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class NotificationListFragment : ListFragment2<NotificationListViewModel>() {

    override val viewModel: NotificationListViewModel by viewModels()

    private val viewDataBinding: ViewDataBinding get() = requireBinding()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val backgroundColor = ThemeColors.resolveColor(requireContext(), R.attr.suuntoBackground)
        viewDataBinding.root.findViewById<RecyclerView>(R.id.list).apply {
            itemAnimator = null
        }.setBackgroundColor(backgroundColor)
    }

    override fun onStart() {
        super.onStart()
        viewModel.loadData()
    }
}
