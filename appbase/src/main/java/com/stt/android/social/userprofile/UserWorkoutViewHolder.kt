package com.stt.android.social.userprofile

import android.content.Context
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.recyclerview.widget.RecyclerView
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.ui.components.workout.WorkoutCard
import com.stt.android.ui.components.workout.WorkoutCardViewModel

internal class UserWorkoutViewHolder(
    private val context: Context,
    private val workoutCardViewModel: WorkoutCardViewModel,
) : RecyclerView.ViewHolder(ComposeView(context)) {
    private val composeView: ComposeView = itemView as ComposeView

    fun bind(
        workoutHeader: WorkoutHeader,
        workoutDetailsRewriteNavigator: WorkoutDetailsRewriteNavigator,
    ) {
        composeView.setContentWithM3Theme {
            WorkoutCard(
                workoutHeader = workoutHeader,
                viewModel = workoutCardViewModel,
                onClick = {
                    workoutDetailsRewriteNavigator.navigate(
                        context = context,
                        username = workoutHeader.username,
                        workoutId = workoutHeader.id,
                        workoutKey = workoutHeader.key,
                    )
                },
                configuration = WorkoutCardViewModel.Configuration(
                    maxDescriptionLines = 1,
                ),
                modifier = Modifier.padding(
                    vertical = MaterialTheme.spacing.small,
                    horizontal = MaterialTheme.spacing.medium,
                ),
            )
        }
    }
}
