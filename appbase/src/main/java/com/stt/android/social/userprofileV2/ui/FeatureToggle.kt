package com.stt.android.social.userprofileV2.ui

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import com.stt.android.BuildConfig

@Composable
internal fun FeatureToggle(
    onFeatureToggleClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .padding(top = 25.dp, end = 40.dp)
            .zIndex(3f),
        contentAlignment = Alignment.TopEnd
    ) {
        val color = if (BuildConfig.DEBUG) {
            Color.Gray.copy(alpha = 0.5f)
        } else {
            Color.Transparent
        }
        Box(
            modifier = Modifier
                .size(80.dp)
                .background(color = Color.Transparent)
                .border(BorderStroke(0.5.dp, color))
                .clickable(
                    interactionSource = null,
                    enabled = true,
                    indication = null,
                    onClick = onFeatureToggleClick
                )
        )
    }
}
