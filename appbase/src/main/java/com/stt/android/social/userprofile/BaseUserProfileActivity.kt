package com.stt.android.social.userprofile

import android.app.Activity
import android.app.NotificationManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.NavUtils
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.snackbar.Snackbar
import com.helpshift.support.Support
import com.stt.android.FeatureFlags
import com.stt.android.R
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue.ProfilePictureAddedNewOrChanged.PROFILE_PICTURE_CHANGED
import com.stt.android.analytics.AnalyticsPropertyValue.ProfilePictureAddedNewOrChanged.PROFILE_PICTURE_NEW
import com.stt.android.appversion.AppVersionViewModel
import com.stt.android.common.ui.ErrorEvent
import com.stt.android.common.ui.SimpleDialogFragment
import com.stt.android.common.ui.observeK
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.core.utils.EventThrottler
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.databinding.UserProfileActivityBinding
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator
import com.stt.android.domain.review.ReviewState
import com.stt.android.domain.user.ImageInformation
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.user.User
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.extensions.openAppSettings
import com.stt.android.help.LoadSupportMetadataUseCase
import com.stt.android.home.explore.WorkoutMapNavigator
import com.stt.android.home.people.formatRevokeFollowerMessage
import com.stt.android.home.settings.SettingsActivity
import com.stt.android.network.interfaces.NetworkStatus
import com.stt.android.remote.appversion.shouldShowRedDot
import com.stt.android.social.following.PeopleActivity
import com.stt.android.social.personalrecord.PersonalRecordsActivity
import com.stt.android.social.userprofile.followlist.FollowListActivity
import com.stt.android.social.userprofile.followlist.FollowListType
import com.stt.android.ui.components.StringDialogValueType
import com.stt.android.ui.components.StringValueDialogFragment
import com.stt.android.ui.components.workout.WorkoutCardViewModel
import com.stt.android.ui.utils.WideScreenPaddingDecoration
import com.stt.android.utils.PermissionUtils
import com.stt.android.utils.STTConstants
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@Deprecated("Use com.stt.android.social.userprofileV2.BaseUserProfileActivity")
abstract class BaseUserProfileActivity :
    AppCompatActivity(),
    View.OnClickListener,
    SimpleDialogFragment.Callback,
    NavigationClickListener,
    StringValueDialogFragment.StringValueSelectedListener {

    private val userProfileViewModel: UserProfileViewModel by viewModels()

    class Navigator
    @Inject constructor() : UserProfileNavigator {
        override fun openUserProfile(context: Context, username: String) {
            context.startActivity(newStartIntent(context, username, false))
        }
    }

    @Inject
    lateinit var workoutMapNavigator: WorkoutMapNavigator

    @Inject
    lateinit var userDetailPresenter: UserDetailPresenter

    @Inject
    lateinit var currentUserController: CurrentUserController

    @Inject
    lateinit var userSettingsController: UserSettingsController

    @Inject
    lateinit var networkStatus: NetworkStatus

    @Inject
    lateinit var workoutDetailsRewriteNavigator: WorkoutDetailsRewriteNavigator

    @Inject
    lateinit var localBroadcastManager: LocalBroadcastManager

    @Inject
    lateinit var loadSupportMetadataUseCase: LoadSupportMetadataUseCase

    @Inject
    lateinit var featureFlags: FeatureFlags

    private val workoutCardViewModel: WorkoutCardViewModel by viewModels()

    private val appVersionViewModel: AppVersionViewModel by viewModels()

    protected val clickEventThrottler = EventThrottler()

    private var userProfileAdapter: UserProfileAdapter? = null
    protected var hideSettings = false
    lateinit var binding: UserProfileActivityBinding
    // handle permission
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { isGranted: Map<String, @JvmSuppressWildcards Boolean>? ->
        // reject and don't ask again. jump to system setting
        val allGranted = isGranted?.values?.all { it }
        if (allGranted == false && !shouldShowRational()) {
            openAppSettings()
        }
        if (allGranted == true) {
            userDetailPresenter.informProfileImageClick()
        }

        Timber.d("storage permission request result:%s", isGranted)
    }

    private val workoutDeletedReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            if (intent.hasExtra(STTConstants.ExtraKeys.WORKOUT_ID)) {
                val workoutId = intent.getIntExtra(STTConstants.ExtraKeys.WORKOUT_ID, -1)
                removeWorkout(workoutId)
            }
        }
    }

    private val workoutUpdatedReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            if (intent.hasExtra(STTConstants.ExtraKeys.WORKOUT_HEADER)) {
                val workoutHeader =
                    intent.getParcelableExtra<WorkoutHeader>(STTConstants.ExtraKeys.WORKOUT_HEADER)
                workoutHeader?.let {
                    updateWorkout(it)
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.user_profile_activity)

        val intent = intent
        val user = intent.getParcelableExtra<User>(KEY_USER)
        var userName: String? = user?.username
        if (userName.isNullOrEmpty()) {
            userName = intent.getStringExtra(KEY_USER_NAME)

            if (intent.getBooleanExtra(KEY_FROM_NOTIFICATION, false)) {
                // cancels the notification
                val nm = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                userName?.let {
                    nm.cancel(it.hashCode())
                }
            }
        }
        check(!userName.isNullOrEmpty()) { "Missing both user name and user parcelable" }

        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.setDisplayShowTitleEnabled(true)

        val picturePosition = savedInstanceState?.getInt(KEY_PICTURE_SCROLL_POSITION, 0) ?: 0

        binding.recyclerView.layoutManager = LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        userProfileAdapter = UserProfileAdapter(
            activity = this,
            userDetailPresenter = userDetailPresenter,
            currentUserController = currentUserController,
            pictureScrollPosition = picturePosition,
            userSettingsController = userSettingsController,
            workoutDetailsRewriteNavigator = workoutDetailsRewriteNavigator,
            loadUser = { userProfileViewModel.loadUser() },
            onDescriptionClicked = { onDescriptionClicked() },
            onRealNameClicked = { onRealNameClicked() },
            featureFlags = featureFlags,
            workoutCardViewModel = workoutCardViewModel,
        )
        binding.recyclerView.adapter = userProfileAdapter
        binding.recyclerView.addItemDecoration(WideScreenPaddingDecoration(resources, theme))

        registerBroadcastReceivers()
        observeViewModel()
    }

    override fun onResume() {
        super.onResume()
        appVersionViewModel.checkAppUpdateIfNeeded()
    }

    private fun shouldShowRational(): Boolean {
        return PermissionUtils.STORAGE_PERMISSIONS.any {
            shouldShowRequestPermissionRationale(it)
        }
    }

    fun showRequestPermissionConfirmationDialog() {
        AlertDialog.Builder(this)
            .setTitle(R.string.request_permission)
            .setMessage(R.string.request_storage_permission_purpose)
            .setPositiveButton(
                R.string.allow
            ) { _, _ ->
                requestPermissionLauncher.launch(PermissionUtils.STORAGE_PERMISSIONS)
            }
            .setNegativeButton(
                R.string.cancel
            ) { dialog, _ -> dialog.dismiss() }
            .show()
    }

    private fun registerBroadcastReceivers() {
        val intentFilter = IntentFilter(STTConstants.BroadcastActions.WORKOUT_DELETED)
        localBroadcastManager.registerReceiver(workoutDeletedReceiver, intentFilter)
        val updateIntentFilter = IntentFilter(STTConstants.BroadcastActions.WORKOUT_UPDATED)
        localBroadcastManager.registerReceiver(workoutUpdatedReceiver, updateIntentFilter)
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                launch {
                    userProfileViewModel.loadedUserState.collect {
                        it?.let {
                            when (it) {
                                is UserProfileState.Data -> onUserLoaded(it.data!!)
                                is UserProfileState.Error -> onUserLoadFailed(it.error)
                                is UserProfileState.Loading -> onUserLoading()
                            }
                        }
                    }
                }

                launch {
                    userProfileViewModel.loadedImages.collect { imageInformations ->
                        //  only show the pictures that review state is passed
                        onPicturesLoaded(imageInformations.filter { it.reviewState == ReviewState.PASS })
                    }
                }

                launch {
                    userProfileViewModel.loadedUserActivitySummaryData.collect {
                        it?.let {
                            onUserActivitySummaryDataLoaded(it, userProfileViewModel.measurementUnit)
                        }
                    }
                }

                launch {
                    userProfileViewModel.loadedPendingFollowRequestCount.collect {
                        onFollowRequestCountLoaded(it)
                    }
                }

                launch {
                    userProfileViewModel.uiEvent.collect {
                        when (it) {
                            is UiEvent.OnBlockUserFailed -> onBlockOrReportUserFailed(it.errorEvent)
                            is UiEvent.OnReportUserFailed -> onBlockOrReportUserFailed(it.errorEvent)
                            is UiEvent.OnUserBlocked -> onUserBlocked()
                            is UiEvent.OnUserUnBlocked -> onUserUnBlocked()
                            is UiEvent.OnUserReported -> showUserReportedDialog()
                            is UiEvent.HideRevokeFollowerAction -> invalidateOptionsMenu()
                            is UiEvent.OnRevokeFollowerFailed -> onRevokeFollowerFailed()
                            is UiEvent.OpenFollowListScreen -> openFollowListScreen(
                                it.followListType,
                                it.username
                            )
                            is UiEvent.HideSettingsMenu -> hideSettingsMenu()
                            is UiEvent.RemoveWorkout -> removeWorkout(it.workoutHeaderId)
                            is UiEvent.UpdateWorkout -> updateWorkout(it.workoutHeader)
                        }
                    }
                }

                launch {
                    userProfileViewModel.showRevokeFollowerAction.collect {
                        invalidateOptionsMenu()
                    }
                }

                launch {
                    userProfileViewModel.premiumSubscriptionStatus.collect {
                        userProfileAdapter?.updatePremiumSubscriptionStatus(it)
                    }
                }
            }
        }

        // fix https://suunto.tpondemand.com/entity/174205-the-activity-list-is-not-updated,
        // loadedWorkouts need not be included in repeatOnLifecycle(Lifecycle.State.STARTED),
        // otherwise when workout is deleted or updated, it will collect the original list,
        // no database changes are observed here.
        lifecycleScope.launch {
            userProfileViewModel.loadedWorkouts.collect(::onWorkoutsLoaded)
        }

        appVersionViewModel.newVersionLive.observeK(this) {
            if (it.shouldShowRedDot) {
                onNewAppVersionAvailable()
            }
        }
    }

    @Deprecated("Deprecated in Java")
    public override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == STTConstants.RequestCodes.PICK_USER_PROFILE_PICTURE) {
            if (resultCode == Activity.RESULT_OK) {
                data?.let {
                    startCrop(it)
                }
            }
        } else {
            super.onActivityResult(requestCode, resultCode, data)
        }
    }
    val cropImageLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == RESULT_OK) {
                sendProfileImageEditedAnalytics(userProfileViewModel.hasUserImage())
                result.data?.let {
                    lifecycleScope.launch { userDetailPresenter.updateProfilePicture(it) }
                }
            }
        }

    private fun startCrop(data: Intent) {
        cropImageLauncher.launch(
            userDetailPresenter.buildProfilePictureCropIntent(
                this, data
            )
        )
    }
    private fun onUserLoading() {
        binding.loadingSpinner.visibility = View.VISIBLE
    }

    private fun onUserLoaded(user: User) {
        binding.loadingSpinner.visibility = View.GONE
        invalidateOptionsMenu()
        userProfileAdapter?.setUser(user, userProfileViewModel.blockStatus)
    }

    private fun onUserLoadFailed(e: Throwable) {
        Snackbar.make(
            binding.root,
            R.string.error_generic, // todo replace with localised message from network error once that is implemented with new error mapping
            Snackbar.LENGTH_SHORT
        ).show()
    }

    private fun onPicturesLoaded(images: List<ImageInformation>) {
        userProfileAdapter?.addPictures(images)
    }

    fun removeWorkout(workoutId: Int) {
        userProfileAdapter?.removeWorkout(workoutId)
    }

    fun updateWorkout(workoutHeader: WorkoutHeader) {
        userProfileAdapter?.let {
            if (it.updateWorkout(workoutHeader)) {
                userProfileViewModel.reloadAggregatedWorkouts()
            }
        }
    }

    private fun onWorkoutsLoaded(workouts: List<WorkoutHeader>) {
        userProfileAdapter?.addWorkouts(workouts)
    }

    private fun onFollowRequestCountLoaded(count: Int) {
        userProfileAdapter?.showUnseenFollowRequestsBadge(count)
    }

    private fun onNewAppVersionAvailable() {
        userProfileAdapter?.showNewAppVersionRedDot()
    }

    private fun openWorkoutMap(user: User) {
        workoutMapNavigator.startWorkoutMapActivity(
            this,
            user,
            AnalyticsEvent.USER_PROFILE_SCREEN,
            true
        )
    }

    private fun hideSettingsMenu() {
        hideSettings = true
        invalidateOptionsMenu()
    }

    override fun onSupportClicked() {
        if (!clickEventThrottler.checkAcceptEvent()) return

        lifecycleScope.launch {
            runSuspendCatching {
                val apiConfig = loadSupportMetadataUseCase.run()
                Support.showFAQs(this@BaseUserProfileActivity, apiConfig)
            }.onFailure { e ->
                Timber.w(e, "Failed to load valid subscription")
            }
        }
    }

    override fun onSettingsClicked() {
        if (!clickEventThrottler.checkAcceptEvent()) return
        startActivity(SettingsActivity.newStartIntent(this))
    }

    override fun onFollowersClicked() {
        if (!clickEventThrottler.checkAcceptEvent()) return
        startActivity(PeopleActivity.newIntent(this))
    }

    override fun onFollowCountClicked(followListType: FollowListType, followCount: Int) {
        if (!clickEventThrottler.checkAcceptEvent()) return
        val username = userProfileViewModel.user?.username ?: return
        if (currentUserController.username == username) {
            when (followListType) {
                FollowListType.FOLLOWING -> startActivity(PeopleActivity.newIntent(context = this, showFollowingTab = true))
                FollowListType.FOLLOWERS -> startActivity(PeopleActivity.newIntent(context = this, showPendingRequests = true))
            }
        } else if (followCount > 0) {
            userProfileViewModel.informOpenFollowListScreen(followListType, username)
        }
    }

    override fun onFeedbackClicked() {
    }

    override fun onPersonalRecord() {
        if (!clickEventThrottler.checkAcceptEvent()) return
        startActivity(PersonalRecordsActivity.newIntent(this, ANALYTICS_USER_PROFILE))
    }

    private fun onRevokeFollowerFailed() {
        Snackbar.make(binding.recyclerView, R.string.error_generic, Snackbar.LENGTH_LONG).show()
    }

    private fun onBlockOrReportUserFailed(e: ErrorEvent) {
        val snackbar = Snackbar.make(
            binding.recyclerView,
            R.string.report_content_error,
            Snackbar.LENGTH_INDEFINITE
        )
        snackbar.setAction(getString(R.string.dismiss)) { snackbar.dismiss() }
        snackbar.show()
    }

    private fun onUserBlocked() {
        userProfileViewModel.user?.let { userProfileAdapter?.setUser(it, userProfileViewModel.blockStatus) }
        invalidateOptionsMenu()
        showUserBlockedDialog()
    }

    private fun onUserUnBlocked() {
        userProfileViewModel.user?.let { userProfileAdapter?.setUser(it, userProfileViewModel.blockStatus) }
        invalidateOptionsMenu()
    }

    override fun onClick(v: View) {
        if (isTaskRoot) {
            NavUtils.getParentActivityIntent(this)?.let {
                startActivity(
                    it.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP)
                )
            }
        }
        finish()
    }

    override fun onDestroy() {
        userDetailPresenter.dropView()
        localBroadcastManager.unregisterReceiver(workoutDeletedReceiver)
        localBroadcastManager.unregisterReceiver(workoutUpdatedReceiver)
        super.onDestroy()
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        if (!userProfileViewModel.isBlockStatusInitialized()) {
            // user is not loaded yet, don't draw the option menu
            return false
        }

        menuInflater.inflate(R.menu.user_profile_activity, menu)
        menu.findItem(R.id.revokeFollower).isVisible = userProfileViewModel.showRevokeFollowerAction.value

        val blockStatus = userProfileViewModel.blockStatus
        if (hideSettings) {
            menu.findItem(R.id.blockUser).isVisible = blockStatus.isUserBlocked != true
            menu.findItem(R.id.unBlockUser).isVisible = blockStatus.isUserBlocked == true
            menu.findItem(R.id.reportUser).isVisible = true
        } else {
            menu.findItem(R.id.blockUser).isVisible = false
            menu.findItem(R.id.unBlockUser).isVisible = false
            menu.findItem(R.id.reportUser).isVisible = false
        }
        menu.findItem(R.id.openMap).isVisible = blockStatus.isBlockedByUser != true
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            R.id.openMap -> {
                userProfileViewModel.user?.let {
                    openWorkoutMap(it)
                }
                true
            }
            R.id.revokeFollower -> {
                setShowRevokeFollowerConfirmation()
                true
            }
            R.id.blockUser -> {
                showBlockUserConfirmationDialog()
                true
            }
            R.id.unBlockUser -> {
                showUnBlockUserConfirmationDialog()
                true
            }
            R.id.reportUser -> {
                showReportUserConfirmationDialog()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        userProfileAdapter?.let {
            outState.putInt(KEY_PICTURE_SCROLL_POSITION, it.pictureScrollPosition)
        }
    }

    private fun setShowRevokeFollowerConfirmation() {
        val latestFollowerStatus = userDetailPresenter.latestFollowerStatus
        if (latestFollowerStatus == null) {
            Timber.w("Cannot ask follower revoke confirmation, latestFollowerStatus=null")
            return
        }

        if (supportFragmentManager.findFragmentByTag(CONFIRM_REVOKE_DIALOG_TAG) != null) {
            Timber.d("Already showing revoke confirmation")
            return
        }

        val title = getString(R.string.revoke_dialog_title)
        val message = formatRevokeFollowerMessage(
            resources,
            latestFollowerStatus
        )

        val simpleDialogFragment = SimpleDialogFragment.newInstance(
            message,
            title,
            getString(R.string.revoke_dialog_confirm),
            getString(R.string.cancel)
        )

        simpleDialogFragment.show(supportFragmentManager, CONFIRM_REVOKE_DIALOG_TAG)
    }

    private fun showBlockUserConfirmationDialog() {
        val singleDialogFragment = SimpleDialogFragment.newInstance(
            getString(R.string.block_user_message),
            getString(R.string.block_user_title, userProfileViewModel.user?.realNameOrUsername),
            getString(R.string.block),
            getString(R.string.cancel)
        )
        singleDialogFragment.show(supportFragmentManager, CONFIRM_BLOCK_USER_DIALOG_TAG)
    }

    private fun showUserBlockedDialog() {
        val singleDialogFragment = SimpleDialogFragment.newInstance(
            getString(R.string.user_unblock_hint),
            getString(R.string.user_blocked, userProfileViewModel.user?.realNameOrUsername),
            getString(R.string.ok),
            null
        )
        singleDialogFragment.show(supportFragmentManager, USER_BLOCKED_DIALOG_TAG)
    }

    private fun showReportUserConfirmationDialog() {
        val singleDialogFragment = SimpleDialogFragment.newInstance(
            getString(R.string.report_user_message),
            getString(R.string.report_user_title, userProfileViewModel.user?.realNameOrUsername),
            getString(R.string.report),
            getString(R.string.cancel)
        )
        singleDialogFragment.show(supportFragmentManager, CONFIRM_REPORT_USER_DIALOG_TAG)
    }

    private fun showUserReportedDialog() {
        val singleDialogFragment = SimpleDialogFragment.newInstance(
            getString(R.string.user_reported_message),
            getString(R.string.user_reported_title, userProfileViewModel.user?.realNameOrUsername),
            getString(R.string.ok),
            null
        )
        singleDialogFragment.show(supportFragmentManager, USER_REPORTED_DIALOG_TAG)
    }

    internal fun showUnBlockUserConfirmationDialog() {
        val singleDialogFragment = SimpleDialogFragment.newInstance(
            getString(R.string.unBlock_user_message),
            getString(R.string.unBlock_user_title, userProfileViewModel.user?.realNameOrUsername),
            getString(R.string.unBlock),
            getString(R.string.cancel)
        )
        singleDialogFragment.show(supportFragmentManager, CONFIRM_UNBLOCK_USER_DIALOG_TAG)
    }

    override fun onDialogButtonPressed(tag: String?, which: Int) {
        if (CONFIRM_REVOKE_DIALOG_TAG == tag && which == DialogInterface.BUTTON_POSITIVE) {
            val latestFollowerStatus = userDetailPresenter.latestFollowerStatus
            if (latestFollowerStatus != null) {
                userProfileViewModel.revokeFollower(latestFollowerStatus)
            } else {
                Timber.w("Cannot revoke follower, latestFollowerStatus=null")
            }
        }

        if (CONFIRM_BLOCK_USER_DIALOG_TAG == tag && which == DialogInterface.BUTTON_POSITIVE) {
            userProfileViewModel.blockUser()
        }

        if (CONFIRM_UNBLOCK_USER_DIALOG_TAG == tag && which == DialogInterface.BUTTON_POSITIVE) {
            userProfileViewModel.unblockUser()
        }

        if (CONFIRM_REPORT_USER_DIALOG_TAG == tag && which == DialogInterface.BUTTON_POSITIVE) {
            userProfileViewModel.reportUser()
        }
    }

    override fun onDialogDismissed(tag: String?) {
        // pass
    }

    private fun onUserActivitySummaryDataLoaded(
        data: UserActivitySummaryData,
        measurementUnit: MeasurementUnit
    ) {
        userProfileAdapter?.setUserActivitySummaryData(data, measurementUnit)
    }

    fun openFollowListScreen(followListType: FollowListType, username: String) {
        startActivity(FollowListActivity.newIntent(this, followListType, username))
    }

    fun checkNetworkConnection(): Boolean = networkStatus.hasNetworkConnection()

    private fun sendProfileImageEditedAnalytics(userImageExists: Boolean) {
        userDetailPresenter.trackEvent(
            AnalyticsEvent.PROFILE_PICTURE_EDITED,
            AnalyticsProperties()
                .put(
                    AnalyticsEventProperty.PROFILE_PICTURE_EDITED_NEW_OR_CHANGED,
                    if (userImageExists) PROFILE_PICTURE_CHANGED else PROFILE_PICTURE_NEW
                )
        )
    }

    private fun onDescriptionClicked() {
        if (!clickEventThrottler.checkAcceptEvent()) return
        val description: String = userSettingsController.settings.description ?: ""
        val stringValueDialogFragment = StringValueDialogFragment.newInstance(
            title = getString(R.string.profileDescription),
            message = getString(R.string.profileDescriptionMaxCharacters),
            stringValueType = StringDialogValueType.USER_DESCRIPTION,
            initialValue = description
        )

        stringValueDialogFragment.setStringValueSelectedListener(this)
        stringValueDialogFragment.show(supportFragmentManager, EDIT_DESCRIPTION_TAG)
    }

    private fun onRealNameClicked() {
        if (!clickEventThrottler.checkAcceptEvent()) return
        val realName: String = userSettingsController.settings.realName ?: ""
        val stringValueDialogFragment = StringValueDialogFragment.newInstance(
            title = getString(R.string.profileRealName),
            message = "",
            stringValueType = StringDialogValueType.USER_REAL_NAME,
            initialValue = realName
        )

        stringValueDialogFragment.setStringValueSelectedListener(this)
        stringValueDialogFragment.show(supportFragmentManager, EDIT_REAL_NAME_TAG)
    }

    override fun valueUpdatedFromDialog(tag: String?, value: String) {
        if (tag == EDIT_DESCRIPTION_TAG) {
            userProfileViewModel.updateUserProfileDescription(value)
        } else if (tag == EDIT_REAL_NAME_TAG) {
            userProfileViewModel.updateUserProfileRealName(value)
        }
        userProfileAdapter?.notifyItemChanged(UserProfileAdapter.POSITION_USER_DETAIL)
    }

    companion object {
        const val KEY_USER = "com.stt.android.KEY_USER"
        const val KEY_USER_NAME = "com.stt.android.KEY_USER_NAME"
        private const val KEY_FROM_NOTIFICATION = "com.stt.android.KEY_FROM_NOTIFICATION"
        private const val KEY_PICTURE_SCROLL_POSITION = "picture_scroll_position"
        private const val CONFIRM_REVOKE_DIALOG_TAG = "confirm_revoke_dlg"
        private const val CONFIRM_BLOCK_USER_DIALOG_TAG = "confirm_block_user_dlg"
        private const val CONFIRM_UNBLOCK_USER_DIALOG_TAG = "confirm_unblock_user_dlg"
        private const val CONFIRM_REPORT_USER_DIALOG_TAG = "confirm_report_user_dlg"
        private const val USER_REPORTED_DIALOG_TAG = "user_reported_dlg"
        private const val USER_BLOCKED_DIALOG_TAG = "user_blocked_dlg"
        private const val EDIT_REAL_NAME_TAG = "EDIT_REAL_NAME_TAG"
        private const val EDIT_DESCRIPTION_TAG = "EDIT_DESCRIPTION_TAG"
        private const val ANALYTICS_USER_PROFILE = "UserProfile"

        @JvmStatic
        fun newStartIntent(
            context: Context,
            userName: String,
            fromNotification: Boolean
        ): Intent {
            return com.stt.android.social.userprofileV2.BaseUserProfileActivity.newStartIntent(
                context,
                userName,
                fromNotification
            )
        }

        @JvmStatic
        fun newStartIntent(context: Context, user: User): Intent {
            return com.stt.android.social.userprofileV2.BaseUserProfileActivity.newStartIntent(
                context, user
            )
        }
    }
}
