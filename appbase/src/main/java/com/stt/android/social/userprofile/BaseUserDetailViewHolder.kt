package com.stt.android.social.userprofile

import android.content.Intent
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.view.View
import android.view.ViewTreeObserver
import android.widget.TextView
import androidx.core.app.ActivityOptionsCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import coil3.load
import coil3.request.transformations
import coil3.transform.CircleCropTransformation
import com.google.android.material.snackbar.Snackbar
import com.stt.android.BuildConfig
import com.stt.android.R
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.coil.placeholderWithFallback
import com.stt.android.common.ui.SimpleDialogFragment
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.databinding.ItemUserProfileDetailBinding
import com.stt.android.domain.STTErrorCodes
import com.stt.android.domain.report.block.BlockStatus
import com.stt.android.domain.user.User
import com.stt.android.featuretoggle.OpenFeatureToggleHandler
import com.stt.android.featuretoggle.OpenFeatureToggleHandlerV2
import com.stt.android.follow.FollowDirection
import com.stt.android.follow.FollowStatus
import com.stt.android.follow.UserFollowStatus
import com.stt.android.home.people.FollowActionViewHelper
import com.stt.android.multimedia.MediaStoreUtils
import com.stt.android.ui.utils.BitmapUtils
import com.stt.android.utils.STTConstants
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File
import com.stt.android.core.R as CR

abstract class BaseUserDetailViewHolder(
    val activity: BaseUserProfileActivity,
    private val userDetailPresenter: UserDetailPresenter,
    private val currentUserController: CurrentUserController,
    val binding: ItemUserProfileDetailBinding,
    private val userSettingsController: UserSettingsController,
    private val loadUser: () -> Unit,
    private val onDescriptionClicked: () -> Unit,
    private val onRealNameClicked: () -> Unit,
) : RecyclerView.ViewHolder(binding.root), UserDetailView {

    private var userFollowStatusForFollowingDirection: UserFollowStatus? = null

    var user: User? = null
    private var blockStatus: BlockStatus? = null

    init {
        setFollowActionListeners()
        val openFeature = OpenFeatureToggleHandlerV2()
        binding.featureToggleArea.setOnClickListener {
            openFeature.onClick(activity)
        }
        if (BuildConfig.DEBUG) {
            binding.featureToggleArea.setBackgroundResource(R.drawable.dashed_border)
            binding.featureToggleArea.alpha = 0.3f
        }
    }

    private fun sendProfileImageAnalytics(profilePictureExists: Boolean) {
        userDetailPresenter.trackEvent(
            AnalyticsEvent.PROFILE_PICTURE_BUTTON,
            AnalyticsProperties()
                .putYesNo(
                    AnalyticsEventProperty.PROFILE_PICTURE_ALREADY_EXISTS,
                    profilePictureExists
                )
        )
    }

    fun bind(
        user: User?,
        blockStatus: BlockStatus?
    ) {
        this.user = user
        this.blockStatus = blockStatus
        if (user == null) {
            if (!activity.checkNetworkConnection()) {
                showNoConnectionSnackBar()
            }
            return
        }

        userDetailPresenter.takeView(this)
        userDetailPresenter.loadUserProfile(user, blockStatus)

        if (!activity.checkNetworkConnection()) {
            showOffLineUser(user)
            showNoConnectionSnackBar()
            return
        } else {
            showUser(user)
        }
    }

    private fun showOffLineUser(user: User) {
        if (user.username == currentUserController.currentUser.username) {
            binding.profileEditGroup.visibility = View.VISIBLE

            userSettingsController.settings.description?.let {
                binding.profileDescription.text = it
            }

            userSettingsController.settings.realName?.let {
                binding.fullName.text = it
            }

            if (user.profileImageUrl != null) {
                showProfileImage(user)
            }
        } else {
            binding.profileEditGroup.visibility = View.GONE
            binding.fullName.apply {
                setPadding(this.paddingLeft, this.paddingTop, this.paddingRight, 0)
                text = user.realNameOrUsername
            }
            binding.profileDescription.apply {
                setPadding(
                    this.paddingLeft,
                    resources.getDimension(R.dimen.size_spacing_xxsmall).toInt(),
                    this.paddingRight,
                    this.paddingBottom
                )
            }
            setDescriptionText(user.description ?: "")
            showProfileImage(user)
        }
    }

    private fun showUser(user: User) {
        if (user.username == currentUserController.currentUser.username) {
            binding.profileEditGroup.visibility = View.VISIBLE

            binding.profileDescriptionBackground.setOnClickListener { onDescriptionClicked() }
            setDescriptionText(userSettingsController.settings.description ?: "")

            binding.profileNameBackground.setOnClickListener { onRealNameClicked() }
            setFullNameText(userSettingsController.settings.realName)

            if (user.profileImageUrl == null) {
                binding.addProfileImage.setOnClickListener {
                    sendProfileImageAnalytics(false)
                    onChangeProfileImage(true)
                }
            } else {
                showProfileImage(user)
                binding.profileImage.setOnClickListener {
                    sendProfileImageAnalytics(true)
                    onChangeProfileImage(true)
                }
            }
        } else {
            binding.profileEditGroup.visibility = View.GONE
            binding.fullName.apply {
                setPadding(this.paddingLeft, this.paddingTop, this.paddingRight, 0)
                text = user.realNameOrUsername
            }
            binding.profileDescription.apply {
                setPadding(
                    this.paddingLeft,
                    resources.getDimension(R.dimen.size_spacing_xxsmall).toInt(),
                    this.paddingRight,
                    this.paddingBottom
                )
            }
            setDescriptionText(user.description ?: "")
            showProfileImage(user)
            binding.profileImage.setOnClickListener {
                onChangeProfileImage(false)
            }
        }
    }

    abstract fun onChangeProfileImage(needRequestPermission: Boolean)

    private fun setDescriptionText(value: String) {
        binding.profileDescription.apply {
            visibility = View.VISIBLE
            if (user?.username == currentUserController.currentUser.username) {
                if (value.isNotBlank()) {
                    setTextWithEditIcon(value, this)
                } else {
                    setTextWithEditIcon(
                        activity.resources.getString(R.string.profileDescription),
                        this,
                        true
                    )
                }
            } else if (value.isNotBlank()) {
                text = value
            } else {
                binding.profileDescription.visibility = View.GONE
            }
        }
    }

    private fun setFullNameText(value: String) {
        if (value.isNotBlank()) {
            setTextWithEditIcon(value, binding.fullName)
        } else {
            val name = user?.realNameOrUsername ?: ""
            setTextWithEditIcon(name, binding.fullName)
        }
    }

    private fun showProfileImage(user: User) {
        hideEditPicture()

        binding.profileImage.load(user.profileImageUrl) {
            transformations(CircleCropTransformation())
            placeholderWithFallback(binding.profileImage.context, CR.drawable.ic_default_profile_image_light)
        }
    }

    private fun setTextWithEditIcon(text: String, textView: TextView, isHint: Boolean = false) {
        if (isHint) {
            textView.text = null
            textView.hint = text
        } else {
            textView.hint = null
            textView.text = text
        }

        textView.compoundDrawablePadding =
            activity.resources.getDimension(R.dimen.size_spacing_xsmall).toInt()

        val editIcon: Drawable? = activity.getDrawable(R.drawable.ic_edit_xsmall)
        textView.setCompoundDrawablesWithIntrinsicBounds(null, null, editIcon, null)
    }

    fun unbind() {
        userDetailPresenter.dropViewIfSame(this)
    }

    override fun hideEditPicture() {
        binding.profileImage.visibility = View.VISIBLE
        binding.addProfileImage.visibility = View.INVISIBLE
    }

    override fun showEditPicture() {
        binding.profileImage.visibility = View.INVISIBLE
        binding.addProfileImage.visibility = View.VISIBLE
    }

    override fun onEditProfileImageClicked(width: Int, height: Int, tempProfilePictureFile: File) {
        val intent = Intent(Intent.ACTION_GET_CONTENT).addCategory(Intent.CATEGORY_OPENABLE)
            .putExtra("aspectX", 1)
            .putExtra("aspectY", 1)
            .putExtra("outputX", width)
            .putExtra("outputY", height)
            .putExtra(MediaStore.EXTRA_OUTPUT, Uri.fromFile(tempProfilePictureFile))
            .putExtra("outputFormat", toString())
            .putExtra(Intent.EXTRA_MIME_TYPES, MediaStoreUtils.SUPPORTED_IMAGE_MIME_TYPES)
            // exclude gif images
            .setType("*/*")

        activity.startActivityForResult(
            intent,
            STTConstants.RequestCodes.PICK_USER_PROFILE_PICTURE
        )
    }

    override fun onProfileImageClicked(profileImageUrl: String) {
        // TODO: When this view is rewritten and performance is better, add shared element
        //  transition back to profile image and UserFullScreenProfilePictureActivity.
        val intent = UserFullScreenProfilePictureActivity.newStartIntent(
            activity,
            profileImageUrl
        )
        activity.startActivity(intent)
    }

    override fun onPictureUpdated(picture: Uri) {
        binding.profileImage.let {
            it.viewTreeObserver.addOnPreDrawListener(object : ViewTreeObserver.OnPreDrawListener {
                override fun onPreDraw(): Boolean {
                    it.viewTreeObserver?.removeOnPreDrawListener(this)

                    activity.lifecycleScope.launch {
                        runSuspendCatching {
                            val profileImage = BitmapUtils
                                .decodeAndRotate(activity, picture, it.width, it.height)
                                ?: return@runSuspendCatching
                            hideEditPicture()
                            it.load(profileImage) {
                                transformations(CircleCropTransformation())
                            }
                        }.onFailure { e ->
                            Timber.w(e, "Failed to update user profile image")
                        }
                    }

                    return false
                }
            })
        }
    }

    override fun onStartUploadPicture() {
        binding.profileImageLoadingSpinner.visibility = View.VISIBLE
    }

    override fun onPictureUploaded() {
        // update profile image
        loadUser.invoke()
        binding.profileImageLoadingSpinner.visibility = View.GONE
    }

    override fun onPictureUploadFailed(errorCode: Int) {
        binding.profileImageLoadingSpinner.visibility = View.GONE
        val errorMessage = when (errorCode) {
            STTErrorCodes.PROFILE_IMAGE_AUDIT_FAILED.code -> {
                itemView.context.getString(R.string.review_failed)
            }
            STTErrorCodes.FILE_SIZE_EXCEEDS_LIMIT.code -> {
                itemView.context.getString(R.string.image_out_of_size)
            }
            else -> {
                itemView.context.getString(R.string.error_0)
            }
        }
       Snackbar.make(
            activity.binding.root,
            errorMessage,
            Snackbar.LENGTH_SHORT
        ).show()
    }

    override fun onUserProfileLoadFailed() {
        user?.let {
            SimpleDialogFragment.newInstance(
                message = itemView.context.getString(R.string.error_0)
            ).show(activity.supportFragmentManager, "PROFILE_LOAD_FAILED")
        }
    }

    override val animationBundleForProfileImage: Bundle? =
        ActivityOptionsCompat.makeSceneTransitionAnimation(
            activity,
            binding.profileImage,
            activity.resources.getString(R.string.profile_image_transition_name)
        ).toBundle()

    override fun hideFollowActions() {
        binding.followIcon.visibility = View.GONE
        binding.followText.visibility = View.GONE
    }

    override fun updateStatus(userFollowStatus: UserFollowStatus) {
        if (blockStatus?.isUserBlocked == true) {
            binding.followText.setText(R.string.unBlock)
            binding.followIcon.visibility = View.GONE
        } else {
            when (userFollowStatus.direction) {
                FollowDirection.FOLLOWING -> {
                    this.userFollowStatusForFollowingDirection = userFollowStatus
                    FollowActionViewHelper.updateFollowActionContainerState(
                        binding.followIcon,
                        binding.followText,
                        userFollowStatus
                    )
                }
                else -> Timber.d("Unknown follower update status")
            }
        }
    }

    override fun showUserProfile(username: String) {
        // do nothing, should never come here
    }

    override fun showUnfollowDialog(userFollowStatus: UserFollowStatus) {
        FollowActionViewHelper.showUnfollowDialog(activity, userDetailPresenter, userFollowStatus)
    }

    override fun showActionError(
        userFollowStatus: UserFollowStatus,
        tryAgainAction: View.OnClickListener
    ) {
        FollowActionViewHelper.showActionError(
            this,
            binding.followText,
            userFollowStatus,
            tryAgainAction
        )
    }

    override fun showError(errorCode: STTErrorCodes) {
        FollowActionViewHelper.showError(binding.followText, errorCode)
    }

    override fun showEmptyView() {
        // do nothing
    }

    override fun getCoroutineScope(): CoroutineScope = activity.lifecycleScope

    private fun setFollowActionListeners() {
        binding.followText.setOnClickListener {
            if (blockStatus?.isUserBlocked == true) {
                activity.showUnBlockUserConfirmationDialog()
            } else {
                if (userFollowStatusForFollowingDirection != null) {
                    when (userFollowStatusForFollowingDirection?.currentUserFollowStatus) {
                        FollowStatus.FOLLOWING ->
                            // Currently FOLLOWING so the user wants to stop following
                            userDetailPresenter.askToUnfollow(
                                userFollowStatusForFollowingDirection
                            )
                        FollowStatus.UNFOLLOWING ->
                            // Currently not following so the user wants to start following
                            userDetailPresenter.follow(
                                userFollowStatusForFollowingDirection,
                                AnalyticsPropertyValue.FollowSourceProperty.PROFILE_VIEW
                            )
                        FollowStatus.PENDING ->
                            // Currently pending approval by other user, let's unfollow
                            userDetailPresenter.unfollow(userFollowStatusForFollowingDirection)
                        else ->
                            // No other status make sense so log a warning
                            userFollowStatusForFollowingDirection?.let {
                                Timber.w(
                                    IllegalArgumentException(
                                        "Invalid follow status ${it.currentUserFollowStatus}"
                                    )
                                )
                            }
                    }
                }
            }
        }
    }

    private fun showNoConnectionSnackBar() {
        val snackbar = Snackbar.make(
            activity.binding.root,
            R.string.no_network_error,
            Snackbar.LENGTH_INDEFINITE
        )
        snackbar.setAction(R.string.retry_action) {
            snackbar.dismiss()
            if (activity.checkNetworkConnection()) {
                loadUser()
            } else {
                itemView.postDelayed({
                    snackbar.show()
                }, 1000)
            }
        }
        snackbar.show()
    }
}
