package com.stt.android.social.notifications.inbox

import com.airbnb.epoxy.TypedEpoxyController
import com.stt.android.social.notifications.list.EmarsysInboxItem
import javax.inject.Inject

class MarketingInboxController @Inject constructor() :
    TypedEpoxyController<List<EmarsysInboxItem>>() {

    override fun buildModels(data: List<EmarsysInboxItem>?) {
        data?.forEach {
            inboxBigImageItemView {
                id(it.id)
                item(it)
            }
        }
    }
}
