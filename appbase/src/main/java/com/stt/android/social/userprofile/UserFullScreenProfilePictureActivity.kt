package com.stt.android.social.userprofile

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.content.res.AppCompatResources
import androidx.databinding.DataBindingUtil
import coil3.DrawableImage
import coil3.Image
import coil3.ImageDrawable
import coil3.asImage
import coil3.load
import coil3.request.error
import com.stt.android.R
import com.stt.android.databinding.ActivityUserFullscreenProfilePictureBinding

class UserFullScreenProfilePictureActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val binding: ActivityUserFullscreenProfilePictureBinding = DataBindingUtil.setContentView(
            this,
            R.layout.activity_user_fullscreen_profile_picture
        )
        binding.userProfileImage.load(intent.getStringExtra(KEY_IMAGE_URL)) {
            error(
                image = AppCompatResources.getDrawable(
                    this@UserFullScreenProfilePictureActivity,
                    R.drawable.default_avatar
                )?.asImage()
            )
        }
    }

    companion object {
        private const val KEY_IMAGE_URL = "com.stt.android.KEY_IMAGE_URL"

        fun newStartIntent(context: Context, imageUrl: String): Intent {
            return Intent(context, UserFullScreenProfilePictureActivity::class.java).putExtra(
                KEY_IMAGE_URL,
                imageUrl
            )
        }
    }
}
