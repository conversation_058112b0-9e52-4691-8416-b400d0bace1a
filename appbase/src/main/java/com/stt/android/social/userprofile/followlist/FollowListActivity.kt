package com.stt.android.social.userprofile.followlist

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.stt.android.R
import com.stt.android.databinding.ActivityFollowListBinding
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class FollowListActivity : AppCompatActivity() {

    private lateinit var binding: ActivityFollowListBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityFollowListBinding.inflate(layoutInflater)
        setContentView(binding.root)
        openFollowListFragment()
        setupActionBar()
    }

    private fun openFollowListFragment() {
        val followListType = intent.extras?.getString(FOLLOW_TYPE) ?: return
        val username = intent.extras?.getString(USERNAME) ?: return
        val followListFragment = FollowListFragment.newInstance(followListType, username)
        supportFragmentManager.beginTransaction()
            .replace(R.id.follow_list_fragment_container, followListFragment, FollowListFragment.FRAGMENT_TAG)
            .commit()
    }

    private fun setupActionBar() {
        val followListType = intent.extras?.getString(FOLLOW_TYPE)?.run {
            FollowListType.valueOf(this)
        }
        val followListTitle = when (followListType) {
            FollowListType.FOLLOWING -> R.string.following
            FollowListType.FOLLOWERS, null -> R.string.followers
        }
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayShowHomeEnabled(false)
            setDisplayHomeAsUpEnabled(true)
            title = resources.getString(followListTitle)
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }

    companion object {
        @JvmStatic
        fun newIntent(context: Context, followListType: FollowListType, username: String) =
            Intent(context, FollowListActivity::class.java).apply {
                putExtra(FOLLOW_TYPE, followListType.name)
                putExtra(USERNAME, username)
            }
    }
}
