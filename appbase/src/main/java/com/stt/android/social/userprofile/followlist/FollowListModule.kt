package com.stt.android.social.userprofile.followlist

import com.stt.android.common.viewstate.ViewStateEpoxyController
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.android.components.FragmentComponent

@Module
@InstallIn(FragmentComponent::class)
abstract class FollowListModule {

    @Binds
    abstract fun bindController(controller: FollowListController): ViewStateEpoxyController<FollowListData?>
}
