package com.stt.android.social.notifications.inbox

import android.content.Context
import android.util.AttributeSet
import android.widget.FrameLayout
import androidx.appcompat.app.AlertDialog
import androidx.compose.ui.platform.ComposeView
import com.airbnb.epoxy.AfterPropsSet
import com.airbnb.epoxy.ModelProp
import com.airbnb.epoxy.ModelView
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.social.notifications.list.EmarsysInboxItem

@ModelView(autoLayout = ModelView.Size.MATCH_WIDTH_WRAP_HEIGHT)
class InboxBigImageItemView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
) : FrameLayout(context, attrs) {

    private val composeView = ComposeView(context).also {
        addView(it, LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT))
    }

    @set:[ModelProp]
    var item: EmarsysInboxItem? = null

    @AfterPropsSet
    fun bindProps() {
        item?.let { item ->
            composeView.setContent {
                M3AppTheme {
                    InboxBigImageItem(
                        item = item,
                        onClick = { item.onInboxItemClick?.invoke(item, it) },
                        onLongClick = { showDeleteDialog { item.onInboxItemLongClick?.invoke(item) } },
                        onExposure = { item.onInboxItemExposure?.invoke(item) },
                    )
                }
            }
        }
    }

    private fun showDeleteDialog(onConfirm: () -> Unit) {
        AlertDialog.Builder(context)
            .setTitle(R.string.marketing_inbox_delete_dialog_title)
            .setPositiveButton(android.R.string.ok) { _, _ -> onConfirm() }
            .setNegativeButton(android.R.string.cancel, null)
            .show()
    }
}
