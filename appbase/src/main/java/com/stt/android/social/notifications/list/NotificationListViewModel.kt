package com.stt.android.social.notifications.list

import android.annotation.SuppressLint
import androidx.lifecycle.viewModelScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.ui.LoadingViewModel
import com.stt.android.controllers.BaseFeedController
import com.stt.android.controllers.FeedController
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.notifications.DomainNotification
import com.stt.android.domain.notifications.MarkNotificationsAsReadUseCase
import com.stt.android.domain.notifications.ReadNotificationsUseCase
import com.stt.android.eventtracking.EventTracker
import com.xwray.groupie.Section
import dagger.hilt.android.lifecycle.HiltViewModel
import hu.akarnokd.rxjava.interop.RxJavaInterop
import io.reactivex.Observable
import io.reactivex.Scheduler
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.rx2.asObservable
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject

@HiltViewModel
class NotificationListViewModel @Inject constructor(
    private val feedController: FeedController,
    private val readNotificationsUseCase: ReadNotificationsUseCase,
    private val markNotificationsAsReadUseCase: MarkNotificationsAsReadUseCase,
    private val coroutinesDispatchers: CoroutinesDispatchers,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    private val eventTracker: EventTracker,
) : LoadingViewModel(ioThread, mainThread) {
    private val emarsysSection: Flow<Section?> get() =
        feedController.emarsysInboxItems
            .map { emarsysInboxItems ->
                if (emarsysInboxItems.isNotEmpty()) {
                    Section(buildMarketingInboxItem(emarsysInboxItems))
                } else {
                    null
                }
            }
            .flowOn(coroutinesDispatchers.computation)

    private val notifications: Flow<List<DomainNotification>> get() =
        readNotificationsUseCase.invoke()
            .take(1) // Do not listen to updates, because we're marking all as read and don't want to update UI.
            .onEach { notifications ->
                viewModelScope.launch { markNotificationsAsReadUseCase.invoke(notifications) }
            }

    private val readNotificationIds: MutableStateFlow<Set<String>> = MutableStateFlow(emptySet())

    private val notificationSection: Flow<Section?> get() =
        combine(
            notifications,
            readNotificationIds,
        ) { notifications, readNotificationIds ->
            val notificationItems = notifications.map { notification ->
                NotificationItem(
                    notification = notification.copy(
                        read = notification.read || readNotificationIds.contains(notification.id),
                    ),
                    eventTracker = eventTracker,
                    onNotificationOpened = { notificationId ->
                        this.readNotificationIds.update { current ->
                            current.toMutableSet()
                                .apply { add(notificationId) }
                        }
                    }
                )
            }
            if (notificationItems.isNotEmpty()) {
                Section(NotificationHeaderItem(), notificationItems)
            } else {
                null
            }
        }

    init {
        listenUpdateFeedMessage()
    }

    @SuppressLint("BinaryOperationInTimber")
    override fun loadData() {
        if (isLoading.value == true) return
        notifyDataLoading()

        combine(
            emarsysSection,
            notificationSection,
        ) { emarsysSection, notificationSection ->
            val list = buildList {
                emarsysSection?.let(::add)
                notificationSection?.let(::add)
                if (isEmpty()) {
                    add(Section(NotificationEmptyItem()))
                }
            }
            notifyDataLoaded(list)
        }.launchIn(viewModelScope)
    }

    private fun updateMessageSeenCount() {
        val fetchMarketingFeedUnreadCount = feedController.emarsysInboxItems.asObservable()
        disposables.add(
            fetchMarketingFeedUnreadCount.subscribeOn(ioThread)
                .observeOn(mainThread).subscribe({ emarsysInboxItems ->
                    if (emarsysInboxItems.isNotEmpty()) {
                        val marketingInbox = Section(buildMarketingInboxItem(emarsysInboxItems))
                        val datas = datasetLiveData.value
                        // remove inbox message count data
                        val otherData = datas?.drop(1)
                        val sections = buildList {
                            add(marketingInbox)
                            otherData?.let { addAll(it) }
                        }
                        notifyDataLoaded(sections)
                    }
                }, {
                    Timber.w(it, "Error: get emarsys inbox message fail")
                })
        )
    }

    private fun buildMarketingInboxItem(inboxItems: List<EmarsysInboxItem>): MarketingInboxItem {
        val newestMessage = inboxItems.maxByOrNull { it.receivedAtTime }
        val feedUnSeenCount: Int = inboxItems.count { it.unSeen }
        return MarketingInboxItem(
            newestMessage?.title,
            newestMessage?.formattedDateTime,
            feedUnSeenCount,
            eventTracker,
        )
    }

    private fun listenUpdateFeedMessage() {
        val updateObservable = RxJavaInterop.toV2Observable(feedController.feedUpdatedObservable)
        disposables.add(
            updateObservable.subscribeOn(ioThread)
                .observeOn(mainThread).subscribe({
                    if (it == BaseFeedController.EMARSYS_MESSAGE_UPDATE) {
                        delayGetMessageSeenCount()
                    }
                }, {
                    Timber.w(it, "Error while listening for feed updates")
                })
        )
    }

    /**
     * Emarsys cannot update the tag in time, so there is a two-second delay for
     * sending the update message.
     * but when there are too many messages to set tag, even a two-second delay does not guarantee that Emarsy will set all message tag successfully
     */
    private fun delayGetMessageSeenCount() {
        disposables.add(
           Observable.timer(2,TimeUnit.SECONDS).subscribe {
               updateMessageSeenCount()
           }
        )
    }
}
