package com.stt.android.social.userprofile.followlist

import com.stt.android.follow.UserFollowStatus

typealias OnItemClickHandler = (FollowListType, String, UserFollowStatus) -> Unit

data class FollowListData(
    val onFollowButtonClicked: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    val onUserClicked: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    val userFollowStatusList: List<UserFollowStatus>,
    val followListType: FollowListType,
    val username: String,
    val currentUsername: String
)

enum class FollowListType {
    FOLLOWING,
    FOLLOWERS
}

const val FOLLOW_TYPE = "com.stt.android.social.userprofile.followlist.FOLLOW_TYPE"
const val USERNAME = "com.stt.android.social.userprofile.followlist.USERNAME"
