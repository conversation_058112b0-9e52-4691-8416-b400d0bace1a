package com.stt.android.social.userprofile.followlist

import androidx.lifecycle.LiveData
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.common.viewstate.LoadingStateViewModel
import com.stt.android.data.session.CurrentUser
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.user.follow.FetchOtherUserFollowInfoUseCase
import com.stt.android.follow.FollowStatus
import com.stt.android.follow.UserFollowStatus
import com.stt.android.home.people.PeopleController
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.utils.toV2
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.rx2.await
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class FollowListViewModel
@Inject constructor(
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    private val fetchOtherUserFollowInfoUseCase: FetchOtherUserFollowInfoUseCase,
    private val peopleController: PeopleController,
    private val currentUser: CurrentUser,
) : LoadingStateViewModel<FollowListData>(ioThread, mainThread) {

    private val _userClickedEvent = SingleLiveEvent<String>()
    val userClickedEvent: LiveData<String> = _userClickedEvent

    fun loadData(followListType: FollowListType, username: String) = launch(io) {
        try {
            notifyLoading()
            val followInfo = fetchOtherUserFollowInfoUseCase(username)
            val followUserList = when (followListType) {
                FollowListType.FOLLOWERS -> followInfo.followers
                FollowListType.FOLLOWING -> followInfo.followings
            }
            val userFollowStatusList =
                peopleController.loadUfsListFromDbForFollowUser(followUserList)
                    .sortedWith { o1, o2 ->
                        o1.realNameOrUsername.compareTo(o2.realNameOrUsername, true)
                    }

            delay(500)
            notifyDataLoaded(
                FollowListData(
                    onFollowButtonClicked = ::onFollowButtonClick,
                    onUserClicked = ::onUserClicked,
                    userFollowStatusList = userFollowStatusList,
                    currentUsername = currentUser.getUsername(),
                    username = username,
                    followListType = followListType
                )
            )
        } catch (e: Exception) {
            notifyError(e)
        }
    }

    private fun onFollowButtonClick(
        followListType: FollowListType,
        username: String,
        userFollowStatus: UserFollowStatus
    ) {
        launch {
            try {
                when (userFollowStatus.status) {
                    FollowStatus.FOLLOWING -> unfollow(userFollowStatus)
                    FollowStatus.PENDING -> unfollow(userFollowStatus)
                    FollowStatus.REJECTED -> unfollow(userFollowStatus)
                    FollowStatus.UNFOLLOWING -> follow(userFollowStatus)
                    FollowStatus.FAILED -> follow(userFollowStatus)
                    FollowStatus.FRIENDS -> unfollow(userFollowStatus)
                }
                loadData(followListType, username)
            } catch (e: Exception) {
                Timber.w(e, "Error attempting to follow user")
            }
        }
    }

    private fun onUserClicked(followListType: FollowListType, username: String, userFollowStatus: UserFollowStatus) {
        _userClickedEvent.value = userFollowStatus.username
    }

    private suspend fun follow(userFollowStatus: UserFollowStatus) = withContext(io) {
        peopleController.follow(
            userFollowStatus,
            AnalyticsPropertyValue.FollowSourceProperty.PROFILE_VIEW
        )
            .toV2()
            .await()
    }

    private suspend fun unfollow(userFollowStatus: UserFollowStatus) = withContext(io) {
        peopleController.unfollow(userFollowStatus).toV2().await()
    }

    override fun retryLoading() = Unit
}
