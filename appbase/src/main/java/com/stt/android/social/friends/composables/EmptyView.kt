package com.stt.android.social.friends.composables

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.spacing
import com.stt.android.R
import com.stt.android.compose.theme.mediumGrey

@Composable
fun EmptyView(
    @DrawableRes icon: Int,
    @StringRes tips: Int,
    modifier: Modifier = Modifier,
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier,
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        ) {
            Icon(
                painter = painterResource(icon),
                contentDescription = null,
                modifier = Modifier.size(134.dp),
                tint = MaterialTheme.colorScheme.mediumGrey,
            )
            Text(
                text = stringResource(tips),
                color = MaterialTheme.colorScheme.secondary,
                modifier = Modifier.padding(bottom = MaterialTheme.spacing.xlarge),
            )
        }
    }
}

@Preview
@Composable
private fun EmptyViewPreview() {
    M3AppTheme {
        EmptyView(
            icon = R.drawable.ic_empty_friends,
            tips = R.string.no_followers,
            modifier = Modifier.fillMaxWidth().padding(top = 155.dp),
        )
    }
}
