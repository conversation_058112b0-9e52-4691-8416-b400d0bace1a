package com.stt.android.social.userprofile

import android.annotation.SuppressLint
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.ui.ErrorEvent
import com.stt.android.common.ui.RxViewModel
import com.stt.android.controllers.BackendController
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.PicturesController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.controllers.loadWorkouts
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.report.ReportUserUseCase
import com.stt.android.domain.report.block.BlockStatus
import com.stt.android.domain.report.block.BlockUserUseCase
import com.stt.android.domain.report.block.GetUserBlockStatusUseCase
import com.stt.android.domain.report.block.UnblockUserUseCase
import com.stt.android.domain.user.GetUserByUsernameUseCase
import com.stt.android.domain.user.ImageInformation
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.user.User
import com.stt.android.domain.user.UserWorkoutSummary
import com.stt.android.domain.user.follow.FetchOtherUserFollowCountSummaryUseCase
import com.stt.android.domain.user.follow.FollowCountSummary
import com.stt.android.domain.user.follow.IsFolloweeUseCase
import com.stt.android.domain.user.subscription.CurrentPremiumSubscriptionStatus
import com.stt.android.domain.user.subscription.GetCurrentPremiumSubscriptionStatusUseCase
import com.stt.android.domain.workouts.FetchWorkoutStatsUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.follow.UserFollowStatus
import com.stt.android.home.people.PeopleController
import com.stt.android.social.userprofile.BaseUserProfileActivity.Companion.KEY_USER
import com.stt.android.social.userprofile.BaseUserProfileActivity.Companion.KEY_USER_NAME
import com.stt.android.social.userprofile.followlist.FollowListType
import com.stt.android.utils.toV2
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import io.reactivex.rxkotlin.subscribeBy
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

@HiltViewModel
class UserProfileViewModel @Inject constructor(
    private val getUserBlockStatusUseCase: GetUserBlockStatusUseCase,
    private val currentUserController: CurrentUserController,
    private val picturesController: PicturesController,
    private val backendController: BackendController,
    private val workoutHeaderController: WorkoutHeaderController,
    private val peopleController: PeopleController,
    private val fetchWorkoutStatsUseCase: FetchWorkoutStatsUseCase,
    private val fetchOtherUserFollowCountSummaryUseCase: FetchOtherUserFollowCountSummaryUseCase,
    private val getUserByUsernameUseCase: GetUserByUsernameUseCase,
    private val isFolloweeUseCase: IsFolloweeUseCase,
    private val blockUserUseCase: BlockUserUseCase,
    private val unblockUserUseCase: UnblockUserUseCase,
    private val reportUserUseCase: ReportUserUseCase,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    savedStateHandle: SavedStateHandle,
    private val userSettingsController: UserSettingsController,
    getCurrentPremiumSubscriptionStatusUseCase: GetCurrentPremiumSubscriptionStatusUseCase,
    coroutinesDispatchers: CoroutinesDispatchers,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler
) : RxViewModel(ioThread, mainThread, coroutinesDispatchers) {
    override val coroutineContext: CoroutineContext
        get() = Dispatchers.Main + SupervisorJob()
    var user: User? = null
        private set
    private val userName: String
    lateinit var blockStatus: BlockStatus
        private set

    // track reports for lifetime of VM to prevent API spam
    private var usernameReported = false

    // This is used in BaseUserProfileActivity#onCreateOptionsMenu
    // To ensure blockStatus is initialized before inflating the menu
    // Block status is needed to show correct block/unblock menu options
    fun isBlockStatusInitialized(): Boolean = ::blockStatus.isInitialized

    private val _loadedUserState = MutableStateFlow<UserProfileState<User?>?>(null)
    val loadedUserState: StateFlow<UserProfileState<User?>?> = _loadedUserState.asStateFlow()

    private val _loadedImages = MutableStateFlow<List<ImageInformation>>(emptyList())
    val loadedImages: StateFlow<List<ImageInformation>> = _loadedImages.asStateFlow()

    private val _loadedWorkouts = MutableStateFlow<List<WorkoutHeader>>(emptyList())
    val loadedWorkouts: StateFlow<List<WorkoutHeader>> = _loadedWorkouts.asStateFlow()

    private val _loadedUserWorkoutSummary = MutableStateFlow<UserWorkoutSummary?>(null)
    val loadedUserWorkoutSummary: StateFlow<UserWorkoutSummary?> =
        _loadedUserWorkoutSummary.asStateFlow()

    private val _loadedFollowCountSummary = MutableStateFlow<FollowCountSummary?>(null)
    val loadedFollowCountSummary: StateFlow<FollowCountSummary?> =
        _loadedFollowCountSummary.asStateFlow()

    private val _loadedFollowRelationship = MutableStateFlow<Pair<Boolean, Boolean>?>(null)

    private val _loadedPendingFollowRequestCount = MutableStateFlow(0)
    val loadedPendingFollowRequestCount: StateFlow<Int> =
        _loadedPendingFollowRequestCount.asStateFlow()

    private val _loadedUserActivitySummaryData = MutableStateFlow<UserActivitySummaryData?>(null)
    val loadedUserActivitySummaryData: StateFlow<UserActivitySummaryData?> =
        _loadedUserActivitySummaryData.asStateFlow()

    private val _uiEvent = Channel<UiEvent>()
    val uiEvent = _uiEvent.receiveAsFlow()

    private val _showRevokeFollowerAction = MutableStateFlow(false)
    val showRevokeFollowerAction: StateFlow<Boolean> = _showRevokeFollowerAction.asStateFlow()

    val measurementUnit: MeasurementUnit
        get() = userSettingsController.settings.measurementUnit

    private var loadWorkoutsJob: Job? = null
    private var loadUserJob: Job? = null

    init {
        user = savedStateHandle[KEY_USER]
        val name = user?.username
            ?.takeUnless(String::isEmpty)
            ?: savedStateHandle[KEY_USER_NAME]
        check(!name.isNullOrEmpty()) { "Missing both user name and user parcelable" }
        userName = name
        loadUser()
    }

    val premiumSubscriptionStatus: StateFlow<CurrentPremiumSubscriptionStatus?> = if (currentUserController.username == userName) {
        getCurrentPremiumSubscriptionStatusUseCase().flowOn(io)
    } else {
        flowOf(
            CurrentPremiumSubscriptionStatus(
                userSubscription = null,
                hasFreeTrialAvailable = false
            )
        )
    }.stateIn(
        scope = this,
        started = SharingStarted.WhileSubscribed(stopTimeoutMillis = 5000L),
        initialValue = null
    )

    internal fun loadUser() {
        viewModelScope.launch {
            runSuspendCatching {
                if (!::blockStatus.isInitialized) {
                    blockStatus = getUserBlockStatusUseCase(userName)
                }
                internalLoadUser()
            }.onFailure { e ->
                Timber.d(e, "Failed to load user")
                _loadedUserState.value = UserProfileState.Error(e)
            }
        }
    }

    private fun internalLoadUser() {
        if (currentUserController.username == userName) {
            user = currentUserController.currentUser
            onUserLoaded()
            loadFollowRequestCount()
            return
        }

        // Not the current user
        sendUiEvent(UiEvent.HideSettingsMenu)

        user?.let {
            onUserLoaded()
            return
        }

        loadUserJob?.cancel()
        loadUserJob = getUserByUsernameUseCase.getUserByUsername(userName)
            .catch { e ->
                Timber.w(e, "Error during load of user")
                _loadedUserState.value = UserProfileState.Error(e)
            }
            .onEach { user ->
                this.user = user
                onUserLoaded()
            }
            .launchIn(viewModelScope)
    }

    private fun onUserLoaded() {
        _loadedUserState.value = UserProfileState.Data(user)
        if (!blockStatus.isBlockedByUser) {
            loadImages()
            loadWorkouts()
        }
        sendUserProfileScreenEvent()
        loadAggregatedWorkouts()
        loadFollowCountSummary()
    }

    private fun loadImages() {
        flow {
            val local = picturesController.findByUserName(userName, PROFILE_IMAGES_COUNT.toLong())
            emit(local)

            if (currentUserController.username == userName) {
                return@flow
            }

            // Not current user, also fetch from backend.
            val session = currentUserController.session

            // backend consider since as ">="
            val since = (local.firstOrNull()?.timestamp ?: -1L) + 1L
            val remote = backendController.fetchUserPictures(session, userName, since)
                .also { saveImagesIfNeeded(it) }
            emit(remote)
        }.catch { e ->
            Timber.w(e, "Unable to load images for user profile view.")
        }.onEach { images ->
            _loadedImages.update { it.plus(images) }
        }
            .flowOn(io)
            .launchIn(viewModelScope)
    }

    private suspend fun saveImagesIfNeeded(images: List<ImageInformation>) {
        if (!isFolloweeUseCase.isFollowee(userName)) {
            return
        }

        runSuspendCatching {
            // i love my friends, so cache the data
            picturesController.store(images)
        }.onFailure { e ->
            Timber.w(e, "Error while storing followee images")
        }
    }

    private fun loadWorkouts() {
        loadWorkoutsJob?.cancel()
        loadWorkoutsJob = workoutHeaderController.loadWorkouts(userName)
            .onEach { workouts -> _loadedWorkouts.value = workouts }
            .catch { e -> Timber.w(e, "Unable to load workouts for user profile view.") }
            .launchIn(viewModelScope)
    }

    private fun loadAggregatedWorkouts() {
        if (blockStatus.isBlockedByUser) {
            return
        }

        viewModelScope.launch(io) {
            runSuspendCatching {
                val userWorkoutSummary = if (currentUserController.username == userName) {
                    workoutHeaderController.getUserWorkoutSummary(userName)
                } else {
                    val stats = fetchWorkoutStatsUseCase(userName)
                    UserWorkoutSummary(
                        stats.totalNumberOfWorkoutsSum,
                        stats.totalDistanceSum,
                        stats.totalTimeSum,
                        stats.totalEnergyConsumptionKCalSum
                    )
                }
                _loadedUserWorkoutSummary.value = userWorkoutSummary
                onUserActivitySummaryDataLoaded()
            }.onFailure { e ->
                Timber.w(e, "Error during load of aggregated data")
                _loadedUserState.value = UserProfileState.Error(e)
            }
        }
    }

    private fun loadFollowCountSummary() {
        if (blockStatus.isBlockedByUser) {
            return
        }

        viewModelScope.launch(io) {
            runSuspendCatching {
                val followCountSummary= if (currentUserController.username == userName) {
                    peopleController.followCountSummary
                } else {
                    fetchOtherUserFollowCountSummaryUseCase(userName)
                }
                _loadedFollowCountSummary.value = followCountSummary
                onUserActivitySummaryDataLoaded()
            }.onFailure { e ->
                Timber.w(e, "Error during load of user follow count")
                _loadedUserState.value = UserProfileState.Error(e)
            }
        }
    }

    private fun loadFollowRequestCount() {
        viewModelScope.launch(io) {
            runSuspendCatching {
                _loadedPendingFollowRequestCount.value =
                    peopleController.loadPendingFollowRequestCount().toInt()
            }.onFailure { e ->
                Timber.w(e, "Failed to load pending follow request count")
            }
        }
    }

    private fun onUserActivitySummaryDataLoaded() {
        val workoutSummary = _loadedUserWorkoutSummary.value
        val followCounts = _loadedFollowCountSummary.value
        val relationship = _loadedFollowRelationship.value

        _loadedUserActivitySummaryData.update {
            UserActivitySummaryData(
                totalDistanceMeters = workoutSummary?.totalDistance ?: 0.0,
                totalWorkoutCount = workoutSummary?.totalWorkouts ?: 0,
                followersCount = followCounts?.followersCount ?: 0,
                followingCount = followCounts?.followingCount ?: 0,
                isFollower = relationship?.first,
                isFollowing = relationship?.second
            )
        }
    }

    private fun sendUserProfileScreenEvent() {
        val currentUser = currentUserController.username == userName
        if (currentUser) {
            sendAmplitudeEvent(AnalyticsEventProperty.SELF)
            return
        }

        user?.let { user ->
            viewModelScope.launch(io) {
                runSuspendCatching {
                    val relationship = peopleController.getFollowRelationship(user)
                    if (relationship.first) {
                        // Profile is following currently logged in user
                        _showRevokeFollowerAction.value = true
                    }
                    _loadedFollowRelationship.value = relationship
                    onUserActivitySummaryDataLoaded()
                    sendAmplitudeEvent(peopleController.getFollowRelationshipValueForAnalytics(relationship))
                }.onFailure(Timber::w)
            }
        }
    }

    private fun sendAmplitudeEvent(targetRelationship: String) {
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.USER_PROFILE_SCREEN,
            AnalyticsProperties()
                .put(
                    AnalyticsEventProperty.TARGET_ACCOUNT_TYPE,
                    AnalyticsPropertyValue
                        .TargetAccountType.NORMAL
                )
                .put(AnalyticsEventProperty.TARGET_RELATIONSHIP, targetRelationship)
        )
    }

    fun blockUser() {
        viewModelScope.launch {
            runSuspendCatching {
                blockUserUseCase(userName)
            }.onSuccess {
                blockStatus = blockStatus.copy(isUserBlocked = true)
                _showRevokeFollowerAction.update { false }
                _uiEvent.send(UiEvent.OnUserBlocked)
                sendUserBlockedEvent()
            }.onFailure {
                Timber.w(it, "Error blocking user")
                _uiEvent.send(UiEvent.OnBlockUserFailed(ErrorEvent.get(it::class)))
            }
        }
    }

    fun reportUser() {
        if (usernameReported) {
            Timber.d("$userName has already been reported.")
            return
        }
        viewModelScope.launch {
            runSuspendCatching {
                reportUserUseCase(userName)
            }.onSuccess {
                usernameReported = true
                _uiEvent.send(UiEvent.OnUserReported)
                sendUserReportedEvent()
            }.onFailure {
                Timber.w(it, "Error reporting user")
                _uiEvent.send(UiEvent.OnReportUserFailed(ErrorEvent.get(it::class)))
            }
        }
    }

    private fun sendUserBlockedEvent() {
        fetchRelationshipStatusAndThen(
            onSuccess = {
                amplitudeAnalyticsTracker.trackEvent(
                    AnalyticsEvent.USER_BLOCKED,
                    AnalyticsProperties()
                        .put(AnalyticsEventProperty.TARGET_RELATIONSHIP, it)
                        .put(AnalyticsEventProperty.BLOCKED_USER_ID, userName)
                )
            },
            onError = {
                Timber.w(it, "sendUserBlockedEvent")
            }
        )
    }

    private fun sendUserReportedEvent() {
        fetchRelationshipStatusAndThen(
            onSuccess = {
                amplitudeAnalyticsTracker.trackEvent(
                    AnalyticsEvent.USER_REPORTED,
                    AnalyticsProperties()
                        .put(AnalyticsEventProperty.TARGET_RELATIONSHIP, it)
                        .put(AnalyticsEventProperty.BLOCKED_USER_ID, userName)
                )
            },
            onError = {
                Timber.w(it, "sendUserReportedEvent")
            }
        )
    }

    private fun fetchRelationshipStatusAndThen(
        onSuccess: (String) -> Unit,
        onError: (Throwable) -> Unit
    ) {
        viewModelScope.launch(io) {
            runSuspendCatching {
                user?.let(peopleController::getFollowRelationshipValueForAnalytics)
                    ?.let(onSuccess)
            }.onFailure(onError)
        }
    }

    fun unblockUser() {
        viewModelScope.launch {
            runSuspendCatching {
                unblockUserUseCase(userName)
            }.onSuccess {
                blockStatus = blockStatus.copy(isUserBlocked = false)
                disposables.add(
                    peopleController.hasFollowing()
                        .toV2()
                        .subscribeOn(ioThread)
                        .observeOn(mainThread)
                        .subscribeBy(onSuccess = {
                            sendUiEvent(UiEvent.OnUserUnBlocked)
                        }, onError = {
                            Timber.w(it, "hasFollowing check has failed")
                        })
                )
                sendUserUnBlockedEvent()
            }.onFailure {
                _uiEvent.send(UiEvent.OnBlockUserFailed(ErrorEvent.get(it::class)))
            }
        }
    }

    private fun sendUserUnBlockedEvent() {
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.USER_UNBLOCKED,
            AnalyticsProperties()
                .put(AnalyticsEventProperty.UNBLOCKED_USER_ID, userName)
        )
    }

    internal fun reloadAggregatedWorkouts() {
        user?.let { loadAggregatedWorkouts() }
    }

    fun hasUserImage(): Boolean = user?.profileImageUrl?.isNotBlank() ?: false

    @SuppressLint("CheckResult")
    internal fun revokeFollower(userFollowStatus: UserFollowStatus) {
        peopleController.revokeFollower(userFollowStatus)
            .toV2()
            .subscribeOn(ioThread)
            .observeOn(mainThread)
            .subscribeBy(onComplete = {
                _showRevokeFollowerAction.value = false
                sendUiEvent(UiEvent.HideRevokeFollowerAction)
            }, onError = { throwable ->
                Timber.w(throwable, "Failed to revoke follower")
                print(throwable)
                sendUiEvent(UiEvent.OnRevokeFollowerFailed)
            })
    }

    internal fun informOpenFollowListScreen(followListType: FollowListType, username: String) {
        user?.let { user ->
            viewModelScope.launch(io) {
                val (isFollower, isFollowing) = peopleController.getFollowRelationship(user)
                if (isFollower || isFollowing) {
                    sendUiEvent(UiEvent.OpenFollowListScreen(followListType, username))
                }
            }
        }
    }

    private fun sendUiEvent(uiEvent: UiEvent) {
        viewModelScope.launch {
            _uiEvent.send(uiEvent)
        }
    }

    override fun onCleared() {
        super.onCleared()
        coroutineContext.cancel()
    }

    fun updateUserProfileDescription(newDescription: String) {
        sendDescriptionAnalytics(hasDescription = !userSettingsController.settings.description.isNullOrBlank())
        userSettingsController.storeSettings(
            userSettingsController.settings.setProfileDescription(newDescription)
        )
    }

    fun updateUserProfileRealName(newRealName: String) {
        userSettingsController.storeSettings(
            userSettingsController.settings.setRealName(newRealName)
        )
    }

    private fun sendDescriptionAnalytics(hasDescription: Boolean) {
        if (hasDescription) {
            amplitudeAnalyticsTracker.trackEvent(
                AnalyticsEvent.PROFILE_DESCRIPTION_EDIT
            )
        } else {
            amplitudeAnalyticsTracker.trackEvent(
                AnalyticsEvent.PROFILE_DESCRIPTION_ADD
            )
        }
    }

    companion object {
        const val PROFILE_IMAGES_COUNT = 100
    }
}

sealed class UserProfileState<out T> {
    data class Data<T>(val data: T) : UserProfileState<T>()
    data object Loading : UserProfileState<Nothing>()
    data class Error(val error: Throwable) : UserProfileState<Nothing>()
}

sealed class UiEvent {
    data class OnBlockUserFailed(val errorEvent: ErrorEvent) : UiEvent()
    data class OnReportUserFailed(val errorEvent: ErrorEvent) : UiEvent()
    data class OpenFollowListScreen(val followListType: FollowListType, val username: String) :
        UiEvent()

    data class RemoveWorkout(val workoutHeaderId: Int) : UiEvent()
    data class UpdateWorkout(val workoutHeader: WorkoutHeader) : UiEvent()
    data object OnUserBlocked : UiEvent()
    data object OnUserUnBlocked : UiEvent()
    data object OnUserReported : UiEvent()
    data object HideRevokeFollowerAction : UiEvent()
    data object OnRevokeFollowerFailed : UiEvent()
    data object HideSettingsMenu : UiEvent()
}
