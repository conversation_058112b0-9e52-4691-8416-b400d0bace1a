package com.stt.android.social.workoutlist.ui

import android.view.LayoutInflater
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.compose.LocalLifecycleOwner
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.google.android.exoplayer2.ui.AspectRatioFrameLayout
import com.stt.android.R
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.newdesign.widgets.PrimaryButton
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.spacing
import com.stt.android.databinding.ViewExoPlayerViewBinding
import com.stt.android.social.workoutlist.WorkoutPost
import com.stt.android.utils.rememberExoPlayer

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PostViewerScreen(
    workoutPosts: List<WorkoutPost>,
    initialPostIndex: Int,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier,
    onCheckActivities: (WorkoutPost) -> Unit = {}
) {
    if (workoutPosts.none()) return
    BackHandler {
        onBackClick()
    }
    val pagerState = rememberPagerState(initialPage = initialPostIndex) { workoutPosts.size }

    LaunchedEffect(workoutPosts.size) {
        pagerState.scrollToPage(pagerState.currentPage.coerceIn(0..workoutPosts.lastIndex))
    }

    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color.Black)
    ) {
        HorizontalPager(
            state = pagerState,
            modifier = Modifier.fillMaxSize(),
        ) { page ->
            val post = workoutPosts[page]
            post.image?.let {
                AsyncImage(
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(it.getFeedUri(LocalContext.current))
                        .crossfade(true)
                        .build(),
                    contentDescription = null,
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Fit,
                )
            } ?: post.video?.let {
                WorkoutPostVideo(
                    workoutPost = post,
                    modifier = Modifier.fillMaxSize(),
                )
            }
        }
        CenterAlignedTopAppBar(
            title = {
                Text("${pagerState.currentPage + 1}/${workoutPosts.size}")
            },
            navigationIcon = {
                SuuntoIconButton(
                    icon = SuuntoIcons.ActionBack,
                    onClick = onBackClick,
                    contentDescription = stringResource(R.string.back),
                )
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color.Transparent,
                titleContentColor = MaterialTheme.colorScheme.surface,
                navigationIconContentColor = MaterialTheme.colorScheme.surface,
            ),
            modifier = Modifier.statusBarsPadding()
        )

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.Transparent)
                .padding(MaterialTheme.spacing.medium)
                .navigationBarsPadding()
                .align(Alignment.BottomCenter)
        ) {
            if (workoutPosts[pagerState.currentPage.coerceIn(0..workoutPosts.lastIndex)].workoutKey != null) {
                PrimaryButton(
                    text = stringResource(R.string.check_activities).uppercase(),
                    onClick = {
                        onCheckActivities.invoke(workoutPosts[pagerState.currentPage])
                    },
                    modifier = Modifier.fillMaxWidth(),
                )
            }
        }
    }
}


@Composable
private fun WorkoutPostVideo(
    workoutPost: WorkoutPost,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current
    val uri = workoutPost.video?.getUri(context) ?: return
    val lifecycleOwner = LocalLifecycleOwner.current
    val player = rememberExoPlayer(context, lifecycleOwner, workoutPost.userAgent, uri)
    AndroidView(
        factory = {
            ViewExoPlayerViewBinding.inflate(LayoutInflater.from(context))
                .root
                .apply {
                    resizeMode = AspectRatioFrameLayout.RESIZE_MODE_FIT
                    useController = false
                    this.player = player
                }
        },
        modifier = modifier,
    )
}
