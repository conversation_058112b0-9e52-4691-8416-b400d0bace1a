package com.stt.android.social.userprofile

import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams.WRAP_CONTENT
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.stt.android.controllers.CurrentUserController
import com.stt.android.databinding.ItemUserProfileNavigationBinding
import com.stt.android.domain.user.User
import com.stt.android.domain.user.subscription.CurrentPremiumSubscriptionStatus

abstract class BaseUserProfileNavigationViewHolder(
    val listener: NavigationClickListener,
    val currentUserController: CurrentUserController,
    val binding: ItemUserProfileNavigationBinding
) : RecyclerView.ViewHolder(binding.root) {

    open fun bind(
        user: User?,
        isOnline: Boolean,
        followRequestsCount: Int = 0,
        currentPremiumSubscriptionStatus: CurrentPremiumSubscriptionStatus? = null,
        newVersionRedDotVisible: Boolean = false,
    ) {
        if (user?.username == currentUserController.currentUser.username || !isOnline) {
            showItem()
            setupNavigationItems()
            bindBadge(followRequestsCount)
            bindCurrentPremiumSubscriptionStatus(currentPremiumSubscriptionStatus)
            showNewAppVersionRedDot(newVersionRedDotVisible)
            setupMyHeadsetItem()
        } else {
            hideItem()
        }
    }

    protected open fun setupMyHeadsetItem() {}

    protected open fun setupNavigationItems() {
        binding.followerContainer.setOnClickListener {
            listener.onFollowersClicked()
        }
        binding.settings.setOnClickListener {
            listener.onSettingsClicked()
        }
        binding.support.setOnClickListener {
            listener.onSupportClicked()
        }
    }

    private fun showItem() {
        binding.root.visibility = View.VISIBLE
        val params: ViewGroup.LayoutParams = itemView.layoutParams
        params.height = WRAP_CONTENT
        itemView.layoutParams = params
    }

    private fun hideItem() {
        binding.root.visibility = View.GONE
        val params: ViewGroup.LayoutParams = itemView.layoutParams
        params.height = 0
        itemView.layoutParams = params
    }

    fun bindBadge(count: Int) {
        if (count > 0) {
            binding.notificationBadgeText.visibility = View.VISIBLE
            binding.notificationBadgeText.text = count.toString()
        } else {
            binding.notificationBadgeText.visibility = View.GONE
        }
    }

    open fun bindCurrentPremiumSubscriptionStatus(
        currentPremiumSubscriptionStatus: CurrentPremiumSubscriptionStatus?
    ) {
        // Do nothing
    }

    fun showNewAppVersionRedDot(visible: Boolean) {
        binding.ivNewVersionRedDot.isVisible = visible
    }
}
