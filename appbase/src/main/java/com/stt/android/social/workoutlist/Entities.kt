package com.stt.android.social.workoutlist

import com.stt.android.domain.review.ReviewState
import com.stt.android.domain.user.ImageInformation
import com.stt.android.domain.user.VideoInformation

data class DateHeader(
    val dateText: String,
    val workoutCount: Int,
)

data class OperationHistory(
    val lastTabIndex: Int,
    val lastWorkoutFilter: String,
)

data class WorkoutPost(
    val userAgent: String,
    val image: ImageInformation? = null,
    val video: VideoInformation? = null,
) {
    val id: Int
        get() = image?.id ?: video?.id ?: 0

    val username: String
        get() = image?.username ?: video?.username ?: ""

    val workoutKey: String?
        get() = image?.workoutKey ?: video?.workoutKey

    val workoutId: Int?
        get() = image?.workoutId ?: video?.workoutId

    val timestamp: Long
        get() = image?.timestamp ?: video?.timestamp ?: 0L

    val reviewState: ReviewState
        get() = image?.reviewState ?: video?.reviewState ?: ReviewState.PASS
}
