package com.stt.android.social.workoutlist.search

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.M3SearchBar
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.social.workoutlist.ui.ActivitiesContentLoaded

@Composable
fun SearchWorkoutContent(
    viewState: SearchWorkoutViewState,
    onBackClick: () -> Unit,
    onQueryChange: (String) -> Unit,
    openWorkout: (WorkoutHeader) -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier) {
        M3SearchBar(
            query = viewState.keyword,
            onQueryChange = onQueryChange,
            placeholder = stringResource(R.string.search_workouts_hint),
            onCancel = onBackClick,
            cancelText = stringResource(R.string.cancel),
            modifier = Modifier.background(MaterialTheme.colorScheme.surface),
        )
        Box(
            modifier = Modifier
                .fillMaxSize()
                .narrowContentWithBgColors(
                    backgroundColor = MaterialTheme.colorScheme.surface,
                    outerBackgroundColor = MaterialTheme.colorScheme.background,
                )
        ) {
            when {
                viewState.searching -> {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = MaterialTheme.spacing.large),
                        contentAlignment = Alignment.TopCenter,
                    ) {
                        CircularProgressIndicator()
                    }
                }

                viewState.dateAndWorkouts.any() -> {
                    ActivitiesContentLoaded(
                        dateAndWorkouts = buildMap { put("", viewState.dateAndWorkouts) },
                        openWorkout = openWorkout,
                        showDate = true,
                    )
                }

                viewState.keyword.isNotBlank() -> {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 200.dp),
                        contentAlignment = Alignment.TopCenter,
                    ) {
                        Text(
                            text = stringResource(R.string.search_phone_contacts_empty),
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.secondary,
                        )
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun SearchWorkoutContentPreview() {
    M3AppTheme {
        SearchWorkoutContent(
            viewState = SearchWorkoutViewState("", emptyList(), false),
            onBackClick = {},
            onQueryChange = {},
            openWorkout = {},
        )
    }
}
