package com.stt.android.social.userprofile

import android.content.Context
import android.graphics.drawable.Drawable
import android.os.Handler
import android.os.Looper
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.View.FOCUS_FORWARD
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.TextView
import androidx.appcompat.content.res.AppCompatResources
import androidx.recyclerview.widget.RecyclerView
import com.stt.android.R
import com.stt.android.ThemeColors.resolveColor
import com.stt.android.controllers.CurrentUserController
import com.stt.android.domain.user.User
import com.stt.android.ui.components.RightDrawableClickableEditText
import timber.log.Timber
import java.util.concurrent.atomic.AtomicInteger

internal class WorkoutFilterViewHolder(
    private val context: Context,
    val currentUserController: CurrentUserController,
    inflater: LayoutInflater,
    parent: ViewGroup
) : RecyclerView.ViewHolder(inflater.inflate(R.layout.workout_filter_view, parent, false)),
    TextWatcher, RightDrawableClickableEditText.DrawableTouchListener,
    TextView.OnEditorActionListener {

    private var filterTextView: RightDrawableClickableEditText? = null
    private var filterValueCallback: ((CharSequence) -> Unit)? = null
    private val runningFilters = AtomicInteger(0)
    private val delayedFilterHandler = Handler(Looper.getMainLooper())

    init {
        filterTextView = itemView.findViewById(R.id.filterValue)
    }

    fun bind(
        user: User?,
        workoutSize: Int,
        filterValue: CharSequence,
        filterValueCallback: (CharSequence) -> Unit
    ) {
        if (workoutSize == 0 || user == null || user.username == currentUserController.currentUser.username) {
            hideItem()
        } else {
            showItem()
            this.filterValueCallback = filterValueCallback
            filterTextView?.setText(filterValue)
            filterTextView?.addTextChangedListener(this)
            filterTextView?.setDrawableTouchListener(this)
            filterTextView?.setOnEditorActionListener(this)
        }
    }

    private fun showItem() {
        itemView.visibility = View.VISIBLE
        val params: ViewGroup.LayoutParams = itemView.layoutParams
        params.height = ViewGroup.LayoutParams.WRAP_CONTENT
        itemView.layoutParams = params
    }

    private fun hideItem() {
        itemView.visibility = View.GONE
        val params: ViewGroup.LayoutParams = itemView.layoutParams
        params.height = 0
        itemView.layoutParams = params
    }


    fun unbind() {
        filterTextView?.removeTextChangedListener(this)
        filterTextView?.removeDrawableTouchListener()
        delayedFilterHandler.removeCallbacksAndMessages(null)
        filterTextView?.setOnEditorActionListener(null)

    }

    private fun resetFilter() {
        filterTextView?.setText("")
    }

    fun callbackFilterStart(constraint: CharSequence) {
        itemView.post {
            val runningFiltersLeft = runningFilters.incrementAndGet()
            Timber.d(
                "callbackFilterStart Filtering count: %d; constraint: %s",
                runningFiltersLeft,
                constraint
            )
            if (runningFiltersLeft == 1) {
                // Hide the clear text to avoid progress spinner overlapping it
                setClearFilterTextDrawable(false)
            }
        }
    }

    fun callbackFilterComplete(count: Int) {
        itemView.post {
            val runningFiltersLeft = runningFilters.decrementAndGet()
            // Hide the spinner and show the right text view drawable if there is no filter left running
            if (runningFiltersLeft == 0) {
                setClearFilterTextDrawable(!TextUtils.isEmpty(filterTextView?.text))
                // If there are no matches inform the user
                if (count == 0) {
                    // In android 4.4 the setError stores the previous drawable so we have to force
                    // the clear
                    // text icon before calling setError.
                    // See https://code.google.com/p/android/issues/detail?id=64593

                    filterTextView?.error = context.getString(R.string.no_matches_found);
                } else {
                    filterTextView?.error = null;
                }
            }
            Timber.d("callbackFilterComplete Filtering count: %d", runningFiltersLeft)
        }
    }

    private fun setClearFilterTextDrawable(showClearTextButton: Boolean) {
        Timber.d(
            "WorkoutFilterViewHolder.setClearFilterTextDrawable: %s",
            showClearTextButton
        )
        // Using getCompoundDrawables() requires that drawable_Left_ is defined in the layout
        val leftDrawable = filterTextView!!.compoundDrawables[0]
        if (showClearTextButton) {
            // Show the drawable to empty the text
            val rightDrawable =
                AppCompatResources.getDrawable(context, R.drawable.ic_cancel_cross)
            rightDrawable?.setTint(resolveColor(context, android.R.attr.textColorSecondary))
            setFilterTextViewDrawables(leftDrawable, rightDrawable)
        } else {
            // Remove the right drawable
            setFilterTextViewDrawables(leftDrawable, null)
        }
    }

    private fun setFilterTextViewDrawables(leftDrawable: Drawable?, rightDrawable: Drawable?) {
        filterTextView?.setCompoundDrawablesWithIntrinsicBounds(
            leftDrawable, null, rightDrawable,
            null
        )
    }

    override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
    }

    override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
    }

    override fun afterTextChanged(filterTerms: Editable?) {
        val filterTask = Runnable {
            filterTerms?.let { filterValueCallback?.invoke(it) }
        }
        // Remove any pending filter task
        delayedFilterHandler.removeCallbacksAndMessages(null)
        delayedFilterHandler.postDelayed(filterTask, FILTER_DELAY)
        // Show the clear filter text button if there's some text in the filter and it's the
        // first filter task
        if (runningFilters.get() == 0) {
            setClearFilterTextDrawable(!TextUtils.isEmpty(filterTerms))
        }
    }

    override fun onDrawableTouch() {
        resetFilter()
    }

    companion object {
        const val FILTER_DELAY: Long = 500
    }

    override fun onEditorAction(textView: TextView?, actionId: Int, keyEvent: KeyEvent?): Boolean {
        if (actionId == EditorInfo.IME_ACTION_SEARCH) {
            val view: View? = textView?.focusSearch(View.FOCUS_DOWN)
            if (view != null) {
                if (!view.requestFocus(FOCUS_FORWARD)) {
                    hideSoftInput()
                    return true
                }
            }
            return false
        }
        return false
    }

    private fun hideSoftInput() {
        filterTextView?.let {
            val imm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            imm.hideSoftInputFromWindow(it.windowToken, 0)
        }
    }
}
