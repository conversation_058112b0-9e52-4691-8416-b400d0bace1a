package com.stt.android.social.friends.composables

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.stt.android.social.friends.Friend

@Composable
fun FriendsListView(
    friends: List<Friend>,
    onFriendClick: (Friend) -> Unit,
    onStatusClick: (Friend) -> Unit,
    modifier: Modifier = Modifier,
    headerView: LazyListScope.() -> Unit = {},
    emptyView: @Composable BoxScope.() -> Unit = {},
    editing: Boolean = false,
    selectedFriends: List<Friend> = emptyList(),
    alwaysShowHeaderView: Boolean = false,
) {
    Box(modifier = modifier) {
        if (friends.any() || alwaysShowHeaderView) {
            LazyColumn {
                headerView()
                items(items = friends, key = { it.toString() }) { friend ->
                    FriendView(
                        friend = friend,
                        onClick = { onFriendClick(friend) },
                        onStatusClick = { onStatusClick(friend) },
                        editing = editing,
                        selected = selectedFriends.contains(friend),
                    )
                }
            }
        } else {
            emptyView()
        }
    }
}
