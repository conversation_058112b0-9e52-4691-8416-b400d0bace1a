package com.stt.android.social.notifications

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.databinding.NotificationActivityBinding
import com.stt.android.eventtracking.EventTracker
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class NotificationActivity : AppCompatActivity() {

    private lateinit var binding: NotificationActivityBinding

    @Inject
    lateinit var eventTracker: EventTracker

    public override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = NotificationActivityBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
        }

        eventTracker.trackEvent(AnalyticsEvent.INBOX_PAGE_EXPOSURE)
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressedDispatcher.onBackPressed()
        return true
    }

    companion object {
        @JvmStatic
        fun newStartIntent(context: Context): Intent {
            return Intent(context, NotificationActivity::class.java)
        }
    }
}
