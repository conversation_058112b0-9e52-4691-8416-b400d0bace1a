package com.stt.android.social.userprofile

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.BackendController
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.SessionController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.STTErrorCodes
import com.stt.android.domain.report.block.BlockStatus
import com.stt.android.domain.user.User
import com.stt.android.exceptions.BackendException
import com.stt.android.follow.FollowDirection
import com.stt.android.follow.FollowStatus
import com.stt.android.follow.UserFollowStatus
import com.stt.android.home.people.FollowStatusPresenter
import com.stt.android.home.people.PeopleController
import com.stt.android.multimedia.MediaStoreUtils
import com.stt.android.ui.crop.CropImageActivity
import com.stt.android.ui.utils.BitmapUtils
import com.stt.android.utils.FileUtils
import com.stt.android.utils.STTConstants
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import rx.Observable
import rx.Subscriber
import rx.android.schedulers.AndroidSchedulers
import rx.schedulers.Schedulers
import timber.log.Timber
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream

class UserDetailPresenter(
    val applicationContext: Context,
    val sessionController: SessionController,
    val currentUserController: CurrentUserController,
    val userSettingsController: UserSettingsController,
    val workoutHeaderController: WorkoutHeaderController,
    val backendController: BackendController,
    val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    val fileUtils: FileUtils,
    peopleController: PeopleController,
    followingObservable: Observable<UserFollowStatus>,
    /**
     * Observable subscribes to UserFollowStatus updates in
     * [FollowDirection.FOLLOWER] direction. Observable for [FollowDirection.FOLLOWING]
     * direction is handled in super class which is [FollowStatusPresenter].
     */
    private val followerObservable: Observable<UserFollowStatus>?,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : FollowStatusPresenter<UserDetailView>(peopleController, followingObservable) {
    private var user: User? = null
    var latestFollowerStatus: UserFollowStatus? = null
        private set // Is the user following currently logged in user

    private val isCurrentUserLoggedIn: Boolean
        get() = currentUserController.isLoggedIn

    private val tempProfilePictureFile: File
        @Throws(IllegalStateException::class)
        get() = fileUtils.getCachedFilePath(STTConstants.DIRECTORY_MISC, "temp_profile.jpg")

    override fun onViewTaken() {
        super.onViewTaken()
        if (followerObservable != null) {
            subscription.add(
                followerObservable.subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(object : Subscriber<UserFollowStatus>() {
                        override fun onCompleted() {
                        }

                        override fun onError(e: Throwable) {
                            Timber.w(e, "Unable to handle follow status update")
                        }

                        override fun onNext(userFollowStatus: UserFollowStatus) {
                            onUserFollowStatusUpdate(userFollowStatus)
                        }
                    })
            )
        }
    }

    override fun onUserFollowStatusUpdate(userFollowStatus: UserFollowStatus) {
        if (user != null && user?.username == userFollowStatus.username) {
            val v = getView()
            v?.updateStatus(userFollowStatus)

            if (userFollowStatus.direction == FollowDirection.FOLLOWER && userFollowStatus.status == FollowStatus.FOLLOWING) {
                latestFollowerStatus = userFollowStatus
            }
        }
    }

    fun loadUserProfile(user: User, blockStatus: BlockStatus?) {
        this.user = user
        val v = getView()
        if (v != null) {
            if (blockStatus?.isBlockedByUser == true) {
                v.hideFollowActions()
            }
            if (isCurrentUserLoggedIn && user.isCurrentUser) {
                v.showEditPicture()
                v.hideFollowActions()
            } else {
                v.hideEditPicture()
            }
        }

        if (user.isCurrentUser) {
            this.user = currentUserController.currentUser
        } else {
            peopleController.loadUfsByUserLocally(user)
        }
    }

    private val User.isCurrentUser: Boolean get() = currentUserController.username == username

    private fun informEditProfilePicture() {
        getView()?.onEditProfileImageClicked(
            PROFILE_IMAGE_WIDTH,
            PROFILE_IMAGE_HEIGHT,
            tempProfilePictureFile
        )
    }

    fun buildProfilePictureCropIntent(context: Context, data: Intent): Intent {
        val uri: Uri = data.data ?: Uri.fromFile(tempProfilePictureFile)
        return CropImageActivity.newStartIntent(
            context, uri, PROFILE_IMAGE_WIDTH, PROFILE_IMAGE_HEIGHT, true,
            tempProfilePictureFile
        )
    }

    suspend fun updateProfilePicture(data: Intent) = runSuspendCatching {
        // some app returns the URI in data.getData(), while others simply put the cropped
        // image file in the path set by the MediaStore.EXTRA_OUTPUT extra in the request Intent
        val uri: Uri = data.data ?: Uri.fromFile(tempProfilePictureFile)

        // some picture chooser apps will not respect mime types and still return GIFs'
        if (MediaStoreUtils.isSupportedImageMimeType(applicationContext, uri)) {
            getView()?.onStartUploadPicture()

            getView()?.getCoroutineScope()?.launch {
                runSuspendCatching {
                    uploadProfilePicture(uri)
                    getView()?.onPictureUploaded()
                }.onFailure { e ->
                    Timber.w(e, "Failed to upload profile picture")
                    if (e is BackendException) {
                        getView()?.onPictureUploadFailed(e.error.code)
                    } else {
                        getView()?.onPictureUploadFailed(STTErrorCodes.UNKNOWN.code)
                    }
                }
            }
        } else {
            getView()?.onPictureUploadFailed(STTErrorCodes.UNKNOWN.code)
        }
    }.onFailure { e ->
        Timber.e(e, "Failed to decode profile image")
    }

    private suspend fun uploadProfilePicture(pictureUri: Uri) = withContext(coroutinesDispatchers.io) {
        val bitmap = BitmapUtils.decodeAndRotate(
            applicationContext,
            pictureUri,
            PROFILE_IMAGE_WIDTH,
            PROFILE_IMAGE_HEIGHT
        ) ?: throw FileNotFoundException("Picture not found, URI: $pictureUri")

        val profilePicture = tempProfilePictureFile
        ByteArrayOutputStream().use { baos ->
            bitmap.compress(Bitmap.CompressFormat.JPEG, 90, baos)

            FileOutputStream(profilePicture).use { fos ->
                fos.write(baos.toByteArray())
            }
        }

        currentUserController.session
            ?.let { userSession ->
                backendController.pushUserProfileImage(userSession, profilePicture.absolutePath)

                val user = backendController.fetchSessionUser(userSession).user
                currentUserController.store(user)
            }
    }

    fun informProfileImageClick() {
        user?.let {
            if (it.isCurrentUser) {
                if (isCurrentUserLoggedIn) {
                    informEditProfilePicture()
                }
            } else {
                val profileImageUrl = it.fullSizeProfileImageUrl
                if (!profileImageUrl.isNullOrEmpty()) {
                    getView()?.onProfileImageClicked(profileImageUrl)
                }
            }
        }
    }

    fun trackEvent(eventName: String, properties: AnalyticsProperties) {
        amplitudeAnalyticsTracker.trackEvent(eventName, properties)
    }

    companion object {
        private const val PROFILE_IMAGE_WIDTH = 512
        private const val PROFILE_IMAGE_HEIGHT = 512
    }
}
