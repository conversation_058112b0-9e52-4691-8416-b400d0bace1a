package com.stt.android.social.notifications.list

import android.view.View
import com.stt.android.R
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsPropertyValue.NotificationMessageType
import com.stt.android.analytics.MessageSource
import com.stt.android.common.ui.ClickableItem
import com.stt.android.databinding.FeedInboxItemBinding
import com.stt.android.eventtracking.EventTracker
import com.stt.android.social.notifications.inbox.MarketingInboxActivity

class MarketingInboxItem(
    val title: String?,
    val dateTime: String?,
    val unreadCount: Int,
    val eventTracker: EventTracker,
) : ClickableItem<FeedInboxItemBinding>() {

    override fun getLayout() = R.layout.feed_inbox_item

    override fun onClick(v: View) {
        v.context?.startActivity(MarketingInboxActivity.newIntent(v.context, MessageSource.INBOX))
        eventTracker.trackEvent(
            AnalyticsEvent.NOTIFICATIONS_PAGE_CLICK,
            mapOf(AnalyticsEventProperty.NOTIFICATION_MESSAGE_TYPE to NotificationMessageType.NEWS),
        )
    }
}
