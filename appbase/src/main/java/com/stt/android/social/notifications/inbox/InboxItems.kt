package com.stt.android.social.notifications.inbox

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.social.notifications.list.EmarsysAction
import com.stt.android.social.notifications.list.EmarsysActionType
import com.stt.android.social.notifications.list.EmarsysInboxItem
import com.stt.android.social.notifications.list.EmarsysInboxSource
import com.stt.android.social.notifications.list.EmarsysInboxType

/**
 * Composable for
 * [EmarsysInboxType.TEXT],
 * [EmarsysInboxType.BIG_IMAGE],
 * [EmarsysInboxType.ANNUAL_REPORT]
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun InboxBigImageItem(
    item: EmarsysInboxItem,
    onClick: (EmarsysAction) -> Unit,
    onLongClick: () -> Unit,
    onExposure: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val action = item.actions?.getOrNull(0)
    Column(
        modifier = modifier
            .padding(
                horizontal = MaterialTheme.spacing.medium,
                vertical = MaterialTheme.spacing.small,
            )
            .fillMaxWidth()
            .clip(RoundedCornerShape(16.dp))
            .border(
                width = 1.dp,
                color = MaterialTheme.colorScheme.lightGrey,
                shape = RoundedCornerShape(16.dp),
            )
            .combinedClickable(
                onClick = { action?.let { onClick(it) } },
                onLongClick = onLongClick,
            )
            .background(MaterialTheme.colorScheme.surface),
    ) {
        when (item.messageType) {
            EmarsysInboxType.BIG_IMAGE,
            EmarsysInboxType.ANNUAL_REPORT -> {
                val imageUrl = item.imageUrl
                if (!imageUrl.isNullOrBlank()) {
                    AsyncImage(
                        modifier = Modifier
                            .fillMaxWidth()
                            .aspectRatio(4f / 3f),
                        model = imageUrl,
                        contentDescription = null,
                        contentScale = ContentScale.Crop,
                    )
                }
            }

            else -> Unit
        }
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.spacing.medium),
        ) {
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(6.dp),
            ) {
                Text(
                    modifier = Modifier
                        .padding(end = MaterialTheme.spacing.medium)
                        .fillMaxWidth(),
                    text = item.title,
                    style = MaterialTheme.typography.bodyLargeBold,
                    color = MaterialTheme.colorScheme.nearBlack,
                )
                Text(
                    text = item.body,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.nearBlack,
                )
            }
            Image(
                modifier = Modifier.align(Alignment.TopEnd),
                painter = painterResource(if (item.unOpened) R.drawable.ic_message_unread else R.drawable.ic_message_read),
                contentDescription = null,
            )
        }
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .defaultMinSize(minHeight = 56.dp)
                .padding(all = MaterialTheme.spacing.medium),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            if (action != null) {
                Text(
                    modifier = Modifier.weight(1f),
                    text = action.title.trim(),
                    style = MaterialTheme.typography.bodyLargeBold,
                    color = MaterialTheme.colorScheme.primary,
                )
            }
            Text(
                text = item.formattedDateTime,
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colorScheme.darkGrey,
            )
        }
    }

    LaunchedEffect(item.id) {
        onExposure()
    }
}

@Preview
@Composable
private fun InboxBigImageItemPreview() {
    M3AppTheme {
        InboxBigImageItem(
            item = EmarsysInboxItem(
                id = "test3",
                title = "Title Title Title Title",
                body = "Body Body Body Body Body Body Body Body Body Body Body Body Body Body",
                imageUrl = null,
                messageType = EmarsysInboxType.TEXT,
                receivedAtTime = System.currentTimeMillis() / 1000,
                clickDelete = true,
                actions = listOf(
                    EmarsysAction(
                        id = "action",
                        title = "Action",
                        type = EmarsysActionType.OPEN_EXTERNAL_URL,
                        url = "",
                    )
                ),
                formattedDateTime = "25/24/01",
                source = EmarsysInboxSource.EMARSYS,
            ),
            onClick = {},
            onLongClick = {},
            onExposure = {},
        )
    }
}
