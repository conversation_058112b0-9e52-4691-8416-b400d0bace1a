package com.stt.android.social.userprofile

import android.app.Activity
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.stt.android.R
import com.stt.android.databinding.ItemUserActivitySummaryBinding
import com.stt.android.domain.user.DEFAULT_MEASUREMENT_UNIT
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.social.userprofile.followlist.FollowListType
import com.stt.android.ui.utils.TextFormatter
import com.stt.android.core.R as CR

internal class UserActivitySummaryViewHolder(
    val activity: Activity,
    val binding: ItemUserActivitySummaryBinding
) : RecyclerView.ViewHolder(binding.root) {

    private val colorLightGray = ContextCompat.getColor(activity, R.color.light_gray)
    private val colorNearBlack = ContextCompat.getColor(activity, CR.color.near_black)

    fun bind(data: UserActivitySummaryData?, measurementUnit: MeasurementUnit?) {
        val unit = measurementUnit ?: DEFAULT_MEASUREMENT_UNIT

        binding.totalDistance.text = data?.totalDistanceMeters?.let {
            TextFormatter.formatDistanceRounded(unit.toDistanceUnit(it))
        } ?: "-"
        binding.totalDistanceTitle.text = activity.resources.getString(unit.distanceUnitFull)

        binding.workoutCount.text = data?.totalWorkoutCount?.toString() ?: "-"
        binding.workoutCountTitle.text = activity.resources.getQuantityText(R.plurals.workouts_plural_without_quantity, data?.totalWorkoutCount ?: 0)

        binding.followerCount.text = data?.followersCount?.toString(radix = 10) ?: "-"
        binding.followingCount.text = data?.followingCount?.toString(radix = 10) ?: "-"

        binding.followersClickArea.setOnClickListener {
            val followersCount = data?.followersCount ?: 0
            (activity as? NavigationClickListener)?.onFollowCountClicked(FollowListType.FOLLOWERS, followersCount)
        }
        binding.followingClickArea.setOnClickListener {
            val followingCount = data?.followingCount ?: 0
            (activity as? NavigationClickListener)?.onFollowCountClicked(FollowListType.FOLLOWING, followingCount)
        }

        (data?.isFollower != false || data.isFollowing != false).let { enabled ->
            setFollowerAreaEnabled(enabled)
            setFollowingAreaEnabled(enabled)
        }
    }

    private fun setFollowerAreaEnabled(enabled: Boolean) {
        val textColor = if (enabled) colorNearBlack else colorLightGray
        binding.followerCount.setTextColor(textColor)
        binding.followerCountTitle.setTextColor(textColor)
        binding.followersClickArea.isClickable = enabled
    }

    private fun setFollowingAreaEnabled(enabled: Boolean) {
        val textColor = if (enabled) colorNearBlack else colorLightGray
        binding.followingCount.setTextColor(textColor)
        binding.followingCountTitle.setTextColor(textColor)
        binding.followingClickArea.isClickable = enabled
    }
}

data class UserActivitySummaryData(
    val totalDistanceMeters: Double? = null,
    val totalWorkoutCount: Int? = null,
    val followersCount: Int? = null,
    val followingCount: Int? = null,
    val isFollower: Boolean? = null,
    val isFollowing: Boolean? = null,
)
