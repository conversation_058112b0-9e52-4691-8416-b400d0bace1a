package com.stt.android.social.friends.composables

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.stt.android.R
import com.stt.android.compose.widgets.ConfirmationDialog

@Composable
fun UnfollowConfirmDialog(
    onConfirm: () -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    ConfirmationDialog(
        text = stringResource(id = R.string.dialog_unfollow_title),
        confirmButtonText = stringResource(id = R.string.unfollow_dialog_confirm),
        cancelButtonText = stringResource(id = R.string.cancel),
        onDismissRequest = onDismiss,
        onConfirm = onConfirm,
        modifier = modifier,
    )
}
