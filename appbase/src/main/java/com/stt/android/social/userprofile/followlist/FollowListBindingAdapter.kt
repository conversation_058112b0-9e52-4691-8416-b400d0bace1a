package com.stt.android.social.userprofile.followlist

import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.content.ContextCompat
import androidx.databinding.BindingAdapter
import coil3.load
import coil3.request.transformations
import coil3.transform.CircleCropTransformation
import com.stt.android.R
import com.stt.android.coil.placeholderWithFallback
import com.stt.android.follow.FollowStatus
import com.stt.android.core.R as CR

@BindingAdapter("avatar")
fun loadAvatar(imageView: ImageView, avatarUrl: String?) {
    if (avatarUrl != null) {
        loadAvatarWithCoil(imageView, avatarUrl)
    } else {
        imageView.setImageResource(CR.drawable.ic_default_profile_image_light)
    }
}

@BindingAdapter("avatars")
fun loadAvatars(linearLayout: LinearLayout, avatarUrls: List<String?>) {
    (0..linearLayout.childCount).forEach { i ->
        val avatarView = linearLayout.getChildAt(i) as? ImageView ?: return@forEach

        if (i >= avatarUrls.size) {
            avatarView.visibility = View.GONE
        } else {
            val avatarUrl = avatarUrls[i]
            loadAvatarWithCoil(avatarView, avatarUrl)
            avatarView.visibility = View.VISIBLE
        }
    }
}

@BindingAdapter("followStatus")
fun setFollowButton(textView: TextView, followStatus: FollowStatus?) {
    followStatus ?: return

    textView.setText(
        when (followStatus) {
            FollowStatus.FOLLOWING -> R.string.following
            FollowStatus.PENDING -> R.string.requested
            // We don't show REJECTED status for users to be more polite. Instead we show
            // status as PENDING.
            FollowStatus.REJECTED -> R.string.requested
            FollowStatus.UNFOLLOWING -> R.string.follow
            FollowStatus.FAILED -> R.string.requested
            FollowStatus.FRIENDS -> R.string.following
        }
    )

    val context = textView.context
    if (followStatus == FollowStatus.FOLLOWING) {
        textView.setCompoundDrawablesWithIntrinsicBounds(
            AppCompatResources.getDrawable(
                context,
                R.drawable.ic_check_follow
            ),
            null,
            null,
            null
        )
        textView.setTextColor(ContextCompat.getColor(context, R.color.secondary_accent))
    } else {
        textView.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null)
        textView.setTextColor(ContextCompat.getColor(context, R.color.accent))
    }
}

private fun loadAvatarWithCoil(imageView: ImageView, avatarUrl: String?) {
    imageView.load(avatarUrl) {
        placeholderWithFallback(imageView.context, CR.drawable.ic_default_profile_image_light)
        transformations(CircleCropTransformation())
    }
}
