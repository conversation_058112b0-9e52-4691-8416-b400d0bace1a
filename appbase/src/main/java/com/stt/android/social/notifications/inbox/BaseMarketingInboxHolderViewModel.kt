package com.stt.android.social.notifications.inbox

import androidx.annotation.DrawableRes
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.emarsys.Emarsys.messageInbox
import com.emarsys.core.api.result.CompletionListener
import com.stt.android.R
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.EmarsysAnalyticsImpl
import com.stt.android.common.ui.RxViewModel
import com.stt.android.controllers.FeedController
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.eventtracking.EventTracker
import com.stt.android.social.notifications.list.EmarsysAction
import com.stt.android.social.notifications.list.EmarsysInboxItem
import com.stt.android.social.notifications.list.EmarsysInboxSource
import com.stt.android.social.notifications.list.InboxMessageTag
import com.stt.android.ui.utils.SingleLiveEvent
import io.reactivex.Scheduler
import kotlinx.coroutines.rx2.asObservable
import timber.log.Timber

open class BaseMarketingInboxHolderViewModel(
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    feedController: FeedController,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    private val emarsysAnalyticsImpl: EmarsysAnalyticsImpl,
    private val eventTracker: EventTracker,
) : RxViewModel(ioThread, mainThread) {
    private val _inboxDataEvent = MutableLiveData<List<EmarsysInboxItem>>()
    val inboxDataEvent: LiveData<List<EmarsysInboxItem>> = _inboxDataEvent

    private val _inboxClickEvent = SingleLiveEvent<EmarsysAction>()
    val inboxClickEvent: LiveData<EmarsysAction> = _inboxClickEvent

    @DrawableRes
    var titleLogoRes = R.drawable.app_logo_small

    private val _isLoading = MutableLiveData(true)
    val isLoading: LiveData<Boolean> = _isLoading

    init {
        // Get latest marketing inbox event and send the analytics event
        val marketingInboxObservable = feedController.emarsysInboxItems.asObservable()
        disposables.add(
            marketingInboxObservable
                .subscribeOn(ioThread)
                .subscribe {
                    if (it.isNotEmpty()) {
                        val inboxItems = arrayListOf<EmarsysInboxItem>()
                        val needMarkSeenItems = arrayListOf<EmarsysInboxItem>()
                        for (item in it) {
                            // deleted message don't show
                            if (item.deleted) {
                                continue
                            }
                            if (item.unSeen) {
                                needMarkSeenItems.add(item)
                            }
                            inboxItems.add(
                                item.copy(
                                    onInboxItemClick = if (item.actions.isNullOrEmpty()) null else ::onInboxClick,
                                    onInboxItemLongClick = ::onInboxLongClick,
                                    onInboxItemExposure = if (item.actions.isNullOrEmpty()) ::onInboxExposure else null,
                                )
                            )
                        }
                        markMessageSeen(needMarkSeenItems, feedController)
                        _inboxDataEvent.postValue(inboxItems.sortedByDescending { it.receivedAtTime })
                        val properties = AnalyticsProperties().apply {
                            put(AnalyticsEventProperty.UNREAD_MESSAGES, it.size)
                        }
                        amplitudeAnalyticsTracker.trackEvent(
                            AnalyticsEvent.INBOX_MESSAGE_LIST,
                            properties
                        )
                    }
                    _isLoading.postValue(false)
                }
        )
    }

    private fun markMessageSeen(items: List<EmarsysInboxItem>, feedController: FeedController) {
        val totalCount = items.size
        var addEmarsysMessageTagCompletedCount = 0
        val listener = CompletionListener { throwable ->
            if (throwable == null) {
                if (totalCount == ++addEmarsysMessageTagCompletedCount) {
                    feedController.feedEmarsysUpdate()
                }
            } else {
                Timber.w(throwable, "set tag seen error")
            }
        }
        items.forEach {
            markMessageSeen(it, listener)
        }
    }

    fun updateMessageReadState(readPosRange: IntRange) {
        var needUpdated = false
        val updatedInboxItems = _inboxDataEvent.value?.let { items ->
            items.takeIf {
                it.any { item -> item.unOpened }
            }?.mapIndexed { index, emarsysInboxItem ->
                if (index in readPosRange &&
                    emarsysInboxItem.unOpened &&
                    emarsysInboxItem.actions?.isEmpty() == true
                ) {
                    markMessageOpened(emarsysInboxItem)
                    needUpdated = true
                    emarsysInboxItem.addOpenedTagCopy()
                } else {
                    emarsysInboxItem
                }
            }
        }
        if (needUpdated) {
            updatedInboxItems?.let {
                _inboxDataEvent.postValue(it)
            }
        }
    }

    private fun markMessageSeen(
        item: EmarsysInboxItem,
        listener: CompletionListener,
    ) = addTag(item, InboxMessageTag.SEEN.tag, listener)

    private fun markMessageOpened(
        item: EmarsysInboxItem,
    ) = addTag(item, InboxMessageTag.OPENED.tag) {
        it?.let {
            Timber.w(it, "set tag seen error:${item.id}")
        }
    }

    private fun markMessageDeleted(
        item: EmarsysInboxItem,
    ) = addTag(item, InboxMessageTag.DELETED.tag) {
        it?.let {
            Timber.w(it, "set tag deleted error:${item.id}")
        }
    }

    protected open fun addTag(item: EmarsysInboxItem, tag: String, listener: CompletionListener) {
        val messageId = item.id
        when (item.source) {
            EmarsysInboxSource.EMARSYS -> {
                messageInbox.addTag(tag, messageId, listener)
            }

            EmarsysInboxSource.SUUNTO -> Unit
        }
    }

    private fun EmarsysInboxItem.addOpenedTagCopy(): EmarsysInboxItem {
        val tags = (tags ?: listOf()).toMutableList()
        if (!tags.contains(InboxMessageTag.OPENED.tag)) {
            tags.add(InboxMessageTag.OPENED.tag)
        }
        return copy(tags = tags)
    }

    private fun EmarsysInboxItem.addDeletedTagCopy(): EmarsysInboxItem {
        val tags = (tags ?: listOf()).toMutableList()
        if (!tags.contains(InboxMessageTag.DELETED.tag)) {
            tags.add(InboxMessageTag.DELETED.tag)
        }
        return copy(tags = tags)
    }

    private fun onInboxClick(item: EmarsysInboxItem, action: EmarsysAction) {
        _inboxClickEvent.value = action
        markMessageOpened(item)
        val dataList = mutableListOf<EmarsysInboxItem>()
        val messageId = item.id
        _inboxDataEvent.value?.forEach {
            if (it.id == messageId) {
                var addTagItem = it.addOpenedTagCopy()
                if (it.clickDelete) {
                    addTagItem = addTagItem.addDeletedTagCopy()
                    markMessageDeleted(item)
                }
                dataList.add(addTagItem)
            } else {
                dataList.add(it)
            }
        }
        _inboxDataEvent.postValue(dataList)
        if (!action.eventName.isNullOrEmpty()) {
            emarsysAnalyticsImpl.trackEvent(action.eventName)
        }
        eventTracker.trackEvent(
            AnalyticsEvent.INBOX_MESSAGE_CLICK,
            mapOf(AnalyticsEventProperty.INBOX_MESSAGE_TITLE to item.title),
        )
    }

    private fun onInboxLongClick(item: EmarsysInboxItem) {
        markMessageDeleted(item)
        val messageId = item.id
        val dataList = _inboxDataEvent.value?.filter { it.id != messageId } ?: emptyList()
        _inboxDataEvent.postValue(dataList)
    }

    private fun onInboxExposure(item: EmarsysInboxItem) {
        if (item.tags?.contains(InboxMessageTag.OPENED.tag) == true) return

        markMessageOpened(item)
        val dataList = buildList {
            val messageId = item.id
            _inboxDataEvent.value?.forEach {
                val item = if (it.id == messageId) it.addOpenedTagCopy() else it
                add(item)
            }
        }
        _inboxDataEvent.value = dataList
    }
}
