package com.stt.android.usecases.startup

import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsUserProperty
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.user.Sex
import com.stt.android.extensions.capitalize
import com.stt.android.mapping.InfoModelFormatter
import timber.log.Timber
import java.time.Instant
import java.time.ZoneId
import java.util.Locale
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Utility class to track UserSettings changes and notify analytics with new settings such as
 * gender, birthdate, country and first day of the week
 */
@Singleton
class UserSettingsTracker
@Inject constructor(
    private val userSettingsController: UserSettingsController,
    private val currentUserController: CurrentUserController,
    private val emarsysAnalytics: EmarsysAnalytics,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    private val infoModelFormatter: InfoModelFormatter
) {

    private val updateListener: UserSettingsController.UpdateListener =
        UserSettingsController.UpdateListener { didLocalChanges ->
            // Update user properties only when the user is logged in. When the user is logged
            // out, the properties are reset to default values (30 year old male). Updating the
            // default values will skew the use demographics and prevent proper targeting for
            // marketing.
            if (didLocalChanges && currentUserController.isLoggedIn) {
                trackGender()
                trackBirthDate()
                trackCountry()
                trackFirstDayOfTheWeek()
            }
            infoModelFormatter.refreshFormattingOptions()
        }

    /**
     * Meant to be called once when applications starts, to listen to user settings changes and update
     * specific analytics properties.
     * Calling it more than once has no effect.
     */
    fun listenToUserSettingsChanges() {
        userSettingsController.addUpdateListener(updateListener)
    }

    @JvmOverloads
    fun trackGender(gender: Sex? = userSettingsController.settings.gender) {
        if (gender != null) {
            amplitudeAnalyticsTracker.trackUserProperty(
                AnalyticsUserProperty.GENDER,
                if (gender == Sex.MALE) "Male" else "Female"
            )
            Timber.d("Updated user gender")
        }
    }

    @JvmOverloads
    fun trackBirthDate(birthDate: Long? = userSettingsController.settings.birthDate) {
        if (birthDate != null) {
            val year = Instant.ofEpochMilli(birthDate)
                .atZone(ZoneId.systemDefault())
                .toLocalDate()
                .year
            trackBirthYear(year)
            Timber.d("Updated user birth year")
        }
    }

    fun trackBirthYear(year: Int) {
        amplitudeAnalyticsTracker.trackUserProperty(AnalyticsUserProperty.BIRTH_YEAR, year)
        emarsysAnalytics.setBirthYear(year)
    }

    fun trackCountry() {
        val country = userSettingsController.settings.country
        amplitudeAnalyticsTracker.trackUserProperty(AnalyticsUserProperty.ASKO_PROFILE_COUNTRY, country)
        emarsysAnalytics.trackStringUserProperty(AnalyticsUserProperty.ASKO_PROFILE_COUNTRY, country)
        emarsysAnalytics.trackStringUserProperty(AnalyticsUserProperty.COUNTRY_CODE, country)
    }

    fun trackFirstDayOfTheWeek() {
        val firstDayOfTheWeek = userSettingsController.settings.firstDayOfTheWeek.name
            .lowercase(Locale.US).capitalize(Locale.US)
        amplitudeAnalyticsTracker.trackUserProperty(AnalyticsUserProperty.FIRST_DAY_OF_THE_WEEK, firstDayOfTheWeek)
        emarsysAnalytics.trackStringUserProperty(AnalyticsUserProperty.FIRST_DAY_OF_THE_WEEK, firstDayOfTheWeek)
    }
}
