package com.stt.android.usecases.startup

import android.app.Application
import android.content.SharedPreferences
import android.hardware.Sensor
import android.hardware.SensorManager
import android.os.Build
import androidx.collection.ArrayMap
import androidx.core.content.edit
import coil3.ImageLoader
import coil3.SingletonImageLoader
import coil3.network.okhttp.OkHttpNetworkFetcherFactory
import coil3.request.crossfade
import coil3.video.VideoFrameDecoder
import com.google.firebase.analytics.FirebaseAnalytics
import com.stt.android.AppConfig
import com.stt.android.BuildConfig
import com.stt.android.analytics.ANALYTICS_VARIANT_CHINA
import com.stt.android.analytics.ANALYTICS_VARIANT_GLOBAL
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsUserProperty
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.analytics.FirebaseAnalyticsTracker
import com.stt.android.analytics.tencent.TencentAnalytics
import com.stt.android.analytics.toYesNo
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.data.TimeUtils
import com.stt.android.data.source.local.startup.ConfigFileStorage
import com.stt.android.domain.movescount.MovescountAppInfoUseCase
import com.stt.android.domain.workouts.extensions.SummaryExtensionUpdateWithZappsUseCase
import com.stt.android.remote.SharedOkHttpClient
import com.stt.android.utils.STTConstants
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import timber.log.Timber
import java.time.LocalDate
import java.util.UUID
import javax.inject.Inject

/**
 * TODO move to domain layer when we get rid of controllers
 * This class replaces previous async-task which handled some background tasks
 * lazily executed at main process start.
 */
class LowPriorityStartupUseCase @Inject constructor(
    private val movescountAppInfoUseCase: MovescountAppInfoUseCase,
    private val configFileStorage: ConfigFileStorage,
    private val appStatRepository: AppStatRepository,
    private val currentUserController: CurrentUserController,
    private val userSettingsController: UserSettingsController,
    private val emarsysAnalytics: EmarsysAnalytics,
    private val sensorManager: SensorManager,
    private val tencentAnalytics: TencentAnalytics,
    private val sharedPreferences: SharedPreferences,
    private val summaryExtensionUpdateWithZappsUseCase: SummaryExtensionUpdateWithZappsUseCase,
    private val firebaseAnalyticsTracker: FirebaseAnalyticsTracker,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    private val coroutinesDispatchers: CoroutinesDispatchers,
    @SharedOkHttpClient private val okhttpClient: OkHttpClient,
) {
    suspend fun getStartupTask(app: Application) = withContext(coroutinesDispatchers.io) {
        setupCoil()
        updateAppStats(app)
        configFileStorage.storeConfigFiles()
        checkUpdateOfSummaryExtensionWithZapps()
    }

    private fun setupCoil() {
        SingletonImageLoader.setSafe { context ->
            ImageLoader.Builder(context)
                .components {
                    add(
                        OkHttpNetworkFetcherFactory(
                            callFactory = { okhttpClient },
                        )
                    )
                    add(VideoFrameDecoder.Factory())
                }
                .crossfade(true)
                .build()
        }
    }

    private fun checkUpdateOfSummaryExtensionWithZapps() {
        val shouldScheduleSummaryUpdates = sharedPreferences.getBoolean(
            STTConstants.DefaultPreferences.KEY_SCHEDULE_SUMMARY_EXTENSION_UPDATE_WITH_ZAPPS,
            false
        )
        Timber.w("Should schedule summaries update: $shouldScheduleSummaryUpdates")
        if (shouldScheduleSummaryUpdates) {
            summaryExtensionUpdateWithZappsUseCase.schedule()
            sharedPreferences.edit {
                putBoolean(
                    STTConstants.DefaultPreferences.KEY_SCHEDULE_SUMMARY_EXTENSION_UPDATE_WITH_ZAPPS,
                    false
                )
            }
        }
    }

    private fun updateAppStats(app: Application) {
        if (!appStatRepository.hasInstallUUID) {
            // new install, initialize the unique ID for this installation
            appStatRepository.installUUID = UUID.randomUUID().toString()
            amplitudeAnalyticsTracker.trackEvent(
                AnalyticsEvent.FIRST_OPEN,
                "Date",
                LocalDate.now().toString()
            )
            emarsysAnalytics.trackEvent(AnalyticsEvent.FIRST_OPEN)
            firebaseAnalyticsTracker.trackEvent(AnalyticsEvent.FIRST_OPEN)
        }
        // recording app start
        appStatRepository.appStarts++

        val userProperties = ArrayMap<String, Any>()
        userProperties[AnalyticsUserProperty.APPLICATION_ID] = AppConfig.APPLICATION_ID
        userProperties[AnalyticsUserProperty.APPLICATION_VARIANT] =
            when (BuildConfig.FLAVOR_store) {
                STORE_NAME_GLOBAL -> ANALYTICS_VARIANT_GLOBAL
                STORE_NAME_CHINA -> ANALYTICS_VARIANT_CHINA
                else -> ""
            }
        userProperties[AnalyticsUserProperty.OS_VERSION_NUMBER] = Build.VERSION.SDK_INT

        val isUserLoggedIn = currentUserController.isLoggedIn

        // tracking this only in amplitude
        amplitudeAnalyticsTracker.trackUserProperty(
            AnalyticsUserProperty.ANONYMOUS_USER,
            (!isUserLoggedIn).toYesNo()
        )

        val userSession = currentUserController.session
        userProperties[AnalyticsUserProperty.FACEBOOK_CONNECTED] =
            (userSession?.isConnectedToFacebook == true).toYesNo()

        val hasPhoneBarometer = (sensorManager.getDefaultSensor(Sensor.TYPE_PRESSURE) != null).toYesNo()
        userProperties[AnalyticsUserProperty.HAS_PHONE_BAROMETER] = hasPhoneBarometer

        if (isUserLoggedIn) {
            userSettingsController.settings.birthDate?.let {
                val birthYear = TimeUtils.epochToZonedDateTime(it).year
                userProperties[AnalyticsUserProperty.BIRTH_YEAR] = birthYear
                emarsysAnalytics.setBirthYear(birthYear)
            }
        }

        if (isUserLoggedIn) {
            val country = userSettingsController.settings.country
            userProperties[AnalyticsUserProperty.ASKO_PROFILE_COUNTRY] = country
            emarsysAnalytics.trackStringUserProperty(
                AnalyticsUserProperty.ASKO_PROFILE_COUNTRY,
                country
            )
            emarsysAnalytics.trackStringUserProperty(
                AnalyticsUserProperty.COUNTRY_CODE,
                country
            )
        }

        // tracking this only in amplitude
        amplitudeAnalyticsTracker.trackUserProperty(
            AnalyticsUserProperty.DAYS_SINCE_FIRST_SESSION,
            appStatRepository.daysSinceInstall
        )
        firebaseAnalyticsTracker.trackEvent(
            AnalyticsEvent.DAYS_SINCE_FIRST_SESSION,
            FirebaseAnalytics.Param.VALUE,
            appStatRepository.daysSinceInstall
        )

        userProperties[AnalyticsUserProperty.SUUNTO_MOVESCOUNT_APP_INSTALLED_ON_SAME_PHONE] =
            (movescountAppInfoUseCase.isMovescountAppInstalledOnPhone()).toYesNo()

        tencentAnalytics.getToken(app)?.let {
            userProperties[AnalyticsUserProperty.TENCENT_SDK_TOKEN] = it
        }

        // tracking this only in amplitude
        amplitudeAnalyticsTracker.trackUserProperty(
            AnalyticsUserProperty.PROFILE_PICTURE_SET,
            (currentUserController.currentUser.profileImageUrl?.isNotBlank() ?: false).toYesNo()
        )

        amplitudeAnalyticsTracker.trackUserProperties(userProperties)
        emarsysAnalytics.trackUserProperties(userProperties)
    }

    companion object {
        const val STORE_NAME_GLOBAL = "playstore"
        const val STORE_NAME_CHINA = "china"
    }
}
