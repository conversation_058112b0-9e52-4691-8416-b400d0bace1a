package com.stt.android.usecases.startup

import android.content.SharedPreferences
import androidx.core.content.edit
import com.stt.android.di.AnalyticsSharedPreferences
import com.stt.android.domain.android.DaysSinceInstallationUseCase
import com.stt.android.utils.STTConstants.AnalyticsPreferences.KEY_INITIAL_USER_DETAILS_SENT_TO_ANALYTICS
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_APP_STARTS
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_INSTALLATION_UNIQUE_ID
import javax.inject.Inject

/**
 * TODO Move to repository module when all app shared preferences are handled there
 */
class AppStatRepository
@Inject constructor(
    private val preferences: SharedPreferences,
    @AnalyticsSharedPreferences private val analyticsPreferences: SharedPreferences,
    private val daysSinceInstallationUseCase: DaysSinceInstallationUseCase
) {
    val hasInstallUUID: Boolean
        get() = preferences.contains(KEY_INSTALLATION_UNIQUE_ID) && installUUID != null

    var installUUID: String?
        get() = preferences.getString(KEY_INSTALLATION_UNIQUE_ID, null)
        set(value) {
            preferences.edit { putString(KEY_INSTALLATION_UNIQUE_ID, value) }
        }

    var appStarts: Int
        get() = preferences.getInt(KEY_APP_STARTS, 0)
        set(value) {
            preferences.edit { putInt(KEY_APP_STARTS, value) }
        }

    var initialUserDetailsSentToAnalytics: Boolean
        get() = analyticsPreferences.getBoolean(KEY_INITIAL_USER_DETAILS_SENT_TO_ANALYTICS, false)
        set(value) = analyticsPreferences.edit {
            putBoolean(KEY_INITIAL_USER_DETAILS_SENT_TO_ANALYTICS, value)
        }

    val daysSinceInstall: Long
        get() = daysSinceInstallationUseCase.getDaysSinceInstallation()
}
