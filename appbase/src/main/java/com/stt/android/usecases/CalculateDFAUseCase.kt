package com.stt.android.usecases

import com.stt.android.domain.sml.Sml
import com.suunto.algorithms.ddfa.DynamicDFAUtils

interface CalculateDFAUseCase {
    suspend operator fun invoke(
        rrs: List<Int>,
        maxHeartRateSetByTheUserAtTheTimeOfRecordingTheWorkout: Double,
        previousBaseline: Double?,
        ignoreFirstTenMinutes: Boolean,
    ): DynamicDFAUtils.Result

    suspend operator fun invoke(
        workoutId: Int,
        sml: Sml?,
    ): List<DynamicDFAUtils.Result>
}
