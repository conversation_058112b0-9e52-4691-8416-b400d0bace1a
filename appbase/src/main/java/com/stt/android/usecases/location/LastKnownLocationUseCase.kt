package com.stt.android.usecases.location

import android.content.Context
import android.location.Geocoder
import com.google.android.gms.maps.model.LatLng
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.location.LastLocationRequest
import com.stt.android.location.LocationModel
import kotlinx.coroutines.withContext
import java.util.Locale
import javax.inject.Inject

class LastKnownLocationUseCase
@Inject constructor(
    private val locationModel: LocationModel,
    private val dispatchers: CoroutinesDispatchers,
    private val context: Context
) {
    suspend fun getLastKnownLocation(
        skipPassiveProvider: Boolean,
        timeInMilliSecondsSinceEpoch: Long
    ): LatLng? {
        val lastKnownLocation = withContext(dispatchers.io) {
            locationModel.getLastLocation(
                LastLocationRequest.builder()
                    .skipPassiveProvider(skipPassiveProvider)
                    .timeInMilliSecondsSinceEpoch(timeInMilliSecondsSinceEpoch)
                    .build()
            )
        }
        val latLng = if (lastKnownLocation != null) {
            LatLng(lastKnownLocation.latitude, lastKnownLocation.longitude)
        } else {
            null
        }
        return latLng
    }

    @Suppress("DEPRECATION")
    suspend fun getLastKnownLocationAsCountryCode(
        skipPassiveProvider: Boolean,
        timeInMilliSecondsSinceEpoch: Long
    ): String? = getLastKnownLocation(
        skipPassiveProvider,
        timeInMilliSecondsSinceEpoch
    )?.let { latLng ->
        Geocoder(context, Locale.ROOT).getFromLocation(latLng.latitude, latLng.longitude, 1)
            ?.firstOrNull()
            ?.countryCode
    }
}
