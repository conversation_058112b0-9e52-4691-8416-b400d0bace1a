package com.stt.android.usecases.startup

import android.content.Context
import android.content.SharedPreferences
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.analytics.isIgnoringBatteryOptimisations
import com.stt.android.analytics.isPowerSaveMode
import com.stt.android.domain.BaseUseCase
import com.stt.android.location.LocationModel.GPS_ACTIVE
import io.reactivex.Completable
import io.reactivex.Scheduler
import io.reactivex.Single
import androidx.core.content.edit

/**
 * Abstract base class for the AppStabilityReportingUseCase class. Reports app stability and GPS tracking stability
 * to Amplitude and Braze. Derived class may extend the reported properties.
 */
abstract class AppStabilityReportingUseCaseBase(
    private val emarsysAnalytics: EmarsysAnalytics,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    private val ioThread: Scheduler,
    mainThread: Scheduler,
    private var sharedPreferences: SharedPreferences,
    private val context: Context
) : BaseUseCase(ioThread, mainThread) {

    /**
     * Completable that will report app stability to Amplitude and Braze when subscribed and completed.
     */
    fun getStabilityReportingCompletable(): Completable =
        stabilityProperties()
            .flatMapCompletable { properties ->
                amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.APP_START_STABILITY_REPORT, properties)
                emarsysAnalytics.trackEventWithProperties(
                    AnalyticsEvent
                        .APP_START_STABILITY_REPORT,
                    properties
                )
                Completable.complete()
            }
            .subscribeOn(ioThread)

    /**
     * Method returning stability properties. Derived class may override this and extend the properties.
     */
    open fun stabilityProperties(): Single<MutableMap<String, Any>> {
        return Single.fromCallable {
            val properties: MutableMap<String, Any> = HashMap()
            val gpsStopped = sharedPreferences.getBoolean(GPS_ACTIVE, false)
            properties.put(AnalyticsEventProperty.GPS_TRACKING_INTERRUPTED, gpsStopped)
            properties.put(AnalyticsEventProperty.POWER_SAVE_MODE_ON, isPowerSaveMode(context))
            properties.put(AnalyticsEventProperty.IGNORING_DOZE_MODE, isIgnoringBatteryOptimisations(context))

            // Clear GPS_ACTIVE flag.
            sharedPreferences.edit { putBoolean(GPS_ACTIVE, false) }
            properties
        }
    }
}
