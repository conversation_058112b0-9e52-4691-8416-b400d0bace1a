package com.stt.android.location

import android.content.Context
import android.os.Handler
import android.os.Looper
import com.stt.android.maps.location.LocationNotAvailableException
import com.stt.android.maps.location.SuuntoLocationCallback
import com.stt.android.maps.location.SuuntoLocationListener
import com.stt.android.maps.location.SuuntoLocationRequest
import com.stt.android.maps.location.SuuntoLocationSource
import com.stt.android.workouts.RecordWorkoutServiceConnection
import java.util.Collections
import javax.inject.Inject

class RecordWorkoutServiceLocationSource @Inject constructor(
    private val context: Context
) : SuuntoLocationSource, RecordWorkoutServiceConnection.Listener {

    private val recordWorkoutServiceConnection = RecordWorkoutServiceConnection(this)
    private val listenerMap = Collections.synchronizedMap(mutableMapOf<SuuntoLocationListener, Handler>())

    private val updateRunnable = object : Runnable {
        override fun run() {
            recordWorkoutServiceConnection.recordWorkoutService?.lastLocation?.let { location ->
                synchronized(listenerMap) {
                    listenerMap.forEach {
                        it.value.post {
                            it.key.onLocationChanged(location, this@RecordWorkoutServiceLocationSource)
                        }
                    }
                }
            }
            handler.postDelayed(this, UPDATE_INTERVAL_MS)
        }
    }

    private val handler: Handler = Handler(Looper.getMainLooper())

    fun getRecordWorkoutService() = recordWorkoutServiceConnection.recordWorkoutService

    override fun getLastKnownLocation(callback: SuuntoLocationCallback) {
        recordWorkoutServiceConnection.recordWorkoutService?.lastLocation?.let {
            callback.onSuccess(it)
        } ?: callback.onFailure(LocationNotAvailableException())
    }

    override fun requestLocationUpdates(request: SuuntoLocationRequest, listener: SuuntoLocationListener, looper: Looper) {
        listenerMap[listener] = Handler(looper)

        if (recordWorkoutServiceConnection.recordWorkoutService == null) {
            recordWorkoutServiceConnection.bindIfStarted(context)
        } else {
            startUpdates()
        }
    }

    override fun removeLocationUpdates(listener: SuuntoLocationListener) {
        listenerMap.remove(listener)

        if (listenerMap.isEmpty()) {
            stopUpdates()
            recordWorkoutServiceConnection.unbind(context)
        }
    }

    override fun onRecordWorkoutServiceBound() {
        if (!listenerMap.isEmpty()) {
            startUpdates()
        }
    }

    override fun onRecordWorkoutServiceUnbound() {
        stopUpdates()
    }

    private fun startUpdates() {
        stopUpdates()
        handler.postDelayed(updateRunnable, INITIAL_UPDATE_DELAYS_MS)
    }

    private fun stopUpdates() {
        handler.removeCallbacks(updateRunnable)
    }

    companion object {
        const val INITIAL_UPDATE_DELAYS_MS = 200L
        const val UPDATE_INTERVAL_MS = 1000L
    }
}
