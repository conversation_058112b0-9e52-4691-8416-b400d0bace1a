package com.stt.android.location

import android.location.Location
import android.os.Looper
import com.stt.android.maps.location.LocationNotAvailableException
import com.stt.android.maps.location.SuuntoLocationCallback
import com.stt.android.maps.location.SuuntoLocationListener
import com.stt.android.maps.location.SuuntoLocationRequest
import com.stt.android.maps.location.SuuntoLocationSource
import java.util.Collections

class FilteringLocationSource(
    private val source: SuuntoLocationSource,
    private val filter: (Location) -> Location?
) : SuuntoLocationSource {

    private val listenerMap = Collections.synchronizedMap(mutableMapOf<SuuntoLocationListener, SuuntoLocationListener>())

    override fun getLastKnownLocation(callback: SuuntoLocationCallback) {
        source.getLastKnownLocation(object : SuuntoLocationCallback {
            override fun onSuccess(location: Location) {
                filter(location)?.let {
                    callback.onSuccess(it)
                } ?: callback.onFailure(LocationNotAvailableException())
            }

            override fun onFailure(exception: Exception) {
                callback.onFailure(exception)
            }
        })
    }

    override fun requestLocationUpdates(request: SuuntoLocationRequest, listener: SuuntoLocationListener, looper: Looper) {
        removeLocationUpdates(listener)

        val filteringListener = object : SuuntoLocationListener {
            override fun onLocationChanged(location: Location, source: SuuntoLocationSource) {
                filter(location)?.let {
                    listener.onLocationChanged(it, source)
                }
            }

            override fun onLocationAvailability(locationAvailable: Boolean, source: SuuntoLocationSource) {
                listener.onLocationAvailability(locationAvailable, source)
            }
        }
        source.requestLocationUpdates(request, filteringListener, looper)
        listenerMap[listener] = filteringListener
    }

    override fun removeLocationUpdates(listener: SuuntoLocationListener) {
        listenerMap.remove(listener)?.let {
            source.removeLocationUpdates(it)
        }
    }
}
