package com.stt.android.workouts.sharepreview

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import com.stt.android.R

@SuppressLint("ViewConstructor")
class WorkoutShareAddPhotoView @JvmOverloads constructor(
    sharePreviewClickListener: SportieOverlayViewClickListener,
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    init {
        val view = inflate(context, R.layout.share_image_add_photo, this)
        view.findViewById<View>(R.id.container)
            .setOnClickListener { sharePreviewClickListener.onAddPhotoClicked() }
    }
}
