package com.stt.android.workouts.details.values

import android.content.res.Resources
import com.amersports.formatter.Failure
import com.amersports.formatter.Result
import com.amersports.formatter.Success
import com.amersports.formatter.Unit
import com.amersports.formatter.UnitType
import com.amersports.formatter.unit.jscience.JScienceUnitConverter
import com.soy.algorithms.climbanalysis.ClimbAnalysis
import com.soy.algorithms.climbanalysis.entities.ClimbGuidanceSettings
import com.soy.algorithms.climbanalysis.entities.ClimbSegment
import com.soy.algorithms.climbanalysis.entities.ClimbSegmentType
import com.soy.algorithms.climbanalysis.entities.Point
import com.soy.algorithms.climbanalysis.entities.StravaClimbCategory
import com.stt.android.R
import com.stt.android.data.workout.tss.getNameStringResId
import com.stt.android.domain.routes.RouteVerticalDeltaCalc
import com.stt.android.domain.sml.CatchEvent
import com.stt.android.domain.sml.CatchMarkType
import com.stt.android.domain.sml.ShotEvent
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.sml.SmlExtensionStreamPoint
import com.stt.android.domain.sml.SwimmingEvent
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.user.workoutextension.FitnessExtension
import com.stt.android.domain.user.workoutextension.SlopeSkiSummary
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workout.WorkoutData
import com.stt.android.domain.workouts.DomainWindow
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.AltitudeSetting
import com.stt.android.domain.workouts.extensions.DiveExtension
import com.stt.android.domain.workouts.extensions.DiveMode
import com.stt.android.domain.workouts.extensions.JumpRopeExtension
import com.stt.android.domain.workouts.extensions.SummaryExtension
import com.stt.android.domain.workouts.extensions.SwimmingExtension
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.domain.workouts.extensions.getAltitudeSettingAsEnum
import com.stt.android.extensions.diveModeLocalized
import com.stt.android.extensions.finalPauseOrStopEvent
import com.stt.android.extensions.traverseEvents
import com.stt.android.infomodel.SummaryItem
import com.stt.android.logbook.NgDiveData
import com.stt.android.logbook.SuuntoLogbookZapp
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.utils.averageOfFloat
import com.stt.android.utils.firstOfType
import com.suunto.algorithms.data.Energy.Companion.kcal
import com.suunto.algorithms.data.HeartRate
import com.suunto.algorithms.data.HeartRate.Companion.bpm
import timber.log.Timber
import kotlin.math.roundToInt
import kotlin.time.Duration
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds
import com.stt.android.core.R as CR

fun applyScubaDiveFilteringRules(algorithm : String?, items: List<SummaryItem>): List<SummaryItem> {
    return if (algorithm == DiveExtension.ALGORITHM_BUHLMANN) {
        items.filterNot { it == SummaryItem.PERSONAL }
    } else {
        items.filterNot { it == SummaryItem.GRADIENTFACTORS }
    }
}

/**
 * We prefer workout header values due to user edits being persisted only on the header
 * The activity window is immutable and comes from the watch/device sml intact
 * For example if the user edits the distance from 1km to 2km we persist that value to the header
 * and we want to use that value.
 */
class WorkoutValueFactory(
    workoutExtensions: List<WorkoutExtension?>,
    // Workout header values ARE preferred
    private val workoutHeader: WorkoutHeader?,
    private val infoModelFormatter: InfoModelFormatter,
    // Activity window values are NOT preferred
    private val activityWindow: DomainWindow? = null,
    private val sml: Sml? = null,
    private val workoutData: WorkoutData? = null,
    private val ngDiveData: NgDiveData? = null,
    private val vo2MaxDuration: Duration? = null,
    private val anaerobicDuration: Duration? = null,
    private val aerobicDuration: Duration? = null,
    private val aerobicHrThreshold: HeartRate? = null,
    private val anaerobicHrThreshold: HeartRate? = null,
    private val zoneSenseBaseline: Double? = null,
    private val zoneSenseCumulativeBaseline: Double? = null,
) {
    private val appResources: Resources = infoModelFormatter.context.resources
    private val unit: MeasurementUnit = infoModelFormatter.unit
    private val slopeSkiSummary: SlopeSkiSummary? = workoutExtensions.firstOfType()
    private val summaryExtension: SummaryExtension? = workoutExtensions.firstOfType()

    // TODO once we start displaying intensity zones on UI start using this object
    private val fitnessExtension: FitnessExtension? = workoutExtensions.firstOfType()
    val diveExtension: DiveExtension? = workoutExtensions.firstOfType()
    private val swimmingExtension: SwimmingExtension? = workoutExtensions.firstOfType()
    private val jumpRopeExtension: JumpRopeExtension? = workoutExtensions.firstOfType()
    private val unitConverter = JScienceUnitConverter()

    private fun MutableList<WorkoutValue>.addIfValid(
        workoutValue: WorkoutValue?
    ) {
        workoutValue?.let {
            if (it.hasValueOrDrawable()) {
                this.add(it)
            }
        }
    }

    private inline fun <T : Any> formatValueIf(
        summaryItem: SummaryItem,
        value: T?,
        predicate: (T) -> Boolean
    ): WorkoutValue? {
        return if (value != null && predicate(value)) {
            formatValue(summaryItem, value)
        } else {
            null
        }
    }

    private fun <T : Any> formatValue(
        summaryItem: SummaryItem,
        value: T?,
        forceUnitUnit: UnitType? = null
    ): WorkoutValue? {
        return if (value != null) {
            infoModelFormatter.formatValue(summaryItem, value, forceUnitUnit)
        } else {
            null
        }
    }

    private data class ZappSummaryOutput(
        val zappName: String,
        val summaryOutput: SuuntoLogbookZapp.SummaryOutput
    )

    fun getSuuntoPlusValues(): List<WorkoutValue> =
        zappSummaryOutputs()
            ?.map { it.toWorkoutValue() }
            ?: listOf()

    fun getSuuntoPlusValuesGrouped(): Map<String, List<WorkoutValue>> =
        zappSummaryOutputs()
            ?.groupBy { it.zappName }
            ?.mapValues { (_, list) -> list.map { it.toWorkoutValue() } } ?: emptyMap()

    private fun zappSummaryOutputs() = summaryExtension
        ?.zapps
        ?.mapNotNull { zapp ->
            zapp.summaryOutputs
                // We want to show only one TSS value, so filter out the SuuntoPlus
                // one if the workoutHeader has TSS that is going to be shown
                ?.filterNot {
                    workoutHeader?.tss != null &&
                        FILTERED_SUUNTO_PLUS_TSS_ZAPPS.contains(it.id)
                }
                ?.map { ZappSummaryOutput(zapp.name, it) }
        }
        ?.flatten()

    private fun ZappSummaryOutput.toWorkoutValue(): WorkoutValue {
        val formatResult: Result = if (summaryOutput.summaryValue == null) {
            Failure("Summary output has null value: $this")
        } else if (summaryOutput.format?.isNotBlank() == true) {
            // format from zapps comes in the form Count_Fourdigits, while we need CountFourdigits
            val formatAndStyle = summaryOutput.format!!.split("_")
            when (formatAndStyle.size) {
                1 -> infoModelFormatter.formatValue(
                    formatName = formatAndStyle[0],
                    value = summaryOutput.summaryValue!!,
                    withStyle = false
                )
                2 -> infoModelFormatter.formatValue(
                    formatName = "${formatAndStyle[0]}${formatAndStyle[1]}",
                    value = summaryOutput.summaryValue!!,
                    withStyle = true
                )
                else -> Failure("Invalid format value for this zapp value: $this")
            }
        } else {
            Failure("Cannot format this zapp value: $this")
        }
        if (formatResult is Failure) {
            Timber.w("Error formatting zapp because: ${formatResult.reason}")
        }
        val value = if (summaryOutput.summaryValue == null) {
            "-" // Watch shows null values as - so let's be consistent
        } else if (formatResult is Success) {
            formatResult.value
        } else {
            // if formatter doesn't work and we don't know how many decimals to show, let's not show any
            summaryOutput.summaryValue?.roundToInt()?.toString().orEmpty()
        }
        val unit = (formatResult as? Success)?.unit?.let { InfoModelFormatter.getUnitResId(it) }
        return WorkoutValue(
            item = null,
            value = value,
            label = "$zappName ${summaryOutput.name}",
            unit = unit,
            // if postfix exists, it overrides the unit resulting from formatter
            unitString = summaryOutput.postfix?.takeIf { it.isNotBlank() }
        )
    }

    fun getValueForItems(summaryItems: List<SummaryItem>): List<WorkoutValue> {
        return summaryItems.flatMap { item ->
            getValueForItem(item)
        }
    }

    fun getValueForItem(item: SummaryItem): List<WorkoutValue> {
        val workoutValues = ArrayList<WorkoutValue>()
        when (item) {
            SummaryItem.DURATION -> {
                workoutValues.addIfValid(formatValue(item, getDurationInSeconds()))
            }
            SummaryItem.TOTALTIME -> {
                workoutValues.addIfValid(
                    formatValueIf(
                        summaryItem = item,
                        value = getTotalTimeInSeconds(),
                    ) { totalTimeInSeconds ->
                        getDurationInSeconds()
                            ?.let { totalTimeInSeconds - it > 30.0 }
                            ?: false
                    }
                )
            }
            SummaryItem.PAUSETIME -> {
                workoutValues.addIfValid(
                    formatValueIf(item, getPauseTimeInSeconds()) { it > 30.0 }
                )
            }
            SummaryItem.MOVINGTIME -> {
                workoutValues.addIfValid(
                    formatValueIf(
                        summaryItem = item,
                        value = getMovingTimeInSeconds(),
                    ) { movingTimeInSeconds ->
                        movingTimeInSeconds > 30.0 &&
                            getDurationInSeconds()?.let { it - movingTimeInSeconds > 30.0 } ?: false
                    }
                )
            }
            SummaryItem.RESTTIME -> {
                workoutValues.addIfValid(formatValueIf(item, getRestTimeInSeconds()) { it > 30.0 })
            }
            SummaryItem.DISTANCE,
            SummaryItem.DIVEDISTANCE,
            SummaryItem.NAUTICALDISTANCE,
            SummaryItem.SWIMDISTANCE -> {
                workoutValues.addIfValid(formatValueIf(item, getTotalDistance()) { it > 0.0 })
            }
            SummaryItem.AVGPACE -> {
                workoutValues.addIfValid(formatValueIf(item, getAvgSpeed()) { it > 0.0 })
            }
            SummaryItem.MOVINGPACE -> {
                workoutValues.addIfValid(formatValueIf(item, getMovingSpeed()) { it > 0.0 })
            }
            SummaryItem.MAXPACE -> {
                workoutValues.addIfValid(formatValueIf(item, getMaxSpeed()) { it > 0.0 })
            }
            SummaryItem.PEAKPACE30S -> {
                workoutValues.addIfValid(
                    formatValueIf(item, getPeakSpeedInXMillis(windowInMillis = 30.seconds.inWholeMilliseconds)) { it > 0.0 }
                )
            }
            SummaryItem.PEAKPACE1M -> {
                workoutValues.addIfValid(
                    formatValueIf(item, getPeakSpeedInXMillis(windowInMillis = 1.minutes.inWholeMilliseconds)) { it > 0.0 }
                )
            }
            SummaryItem.PEAKPACE3M -> {
                workoutValues.addIfValid(
                    formatValueIf(item, getPeakSpeedInXMillis(windowInMillis = 3.minutes.inWholeMilliseconds)) { it > 0.0 }
                )
            }
            SummaryItem.PEAKPACE5M -> {
                workoutValues.addIfValid(
                    formatValueIf(item, getPeakSpeedInXMillis(windowInMillis = 5.minutes.inWholeMilliseconds)) { it > 0.0 }
                )
            }
            SummaryItem.AVGSWIMPACE -> {
                if (workoutHeader?.activityType?.isIndoor == false) {
                    // openwater swimming needs GPS-based speed
                    val workoutValue = formatValueIf(item, getAvgSpeed()) { it > 0.0 }
                        ?.copy(textSizeResId = R.dimen.workout_value_medium_dp)
                    workoutValue?.value?.let { value ->
                        if (value.isNotEmpty()) {
                            workoutValues.addIfValid(workoutValue)
                        }
                    }
                } else {
                    val summarySpeed = summaryExtension?.avgSpeed ?: 0f
                    val speed =
                        if (summarySpeed > 0f) summarySpeed.toDouble() else getAvgSpeed()
                    workoutValues.addIfValid(
                        formatValueIf(item, speed) { it > 0.0 }
                            ?.copy(textSizeResId = R.dimen.workout_value_medium_dp)
                    )
                }
            }
            SummaryItem.SWIMSTROKECOUNT -> {
                workoutValues.addIfValid(formatValueIf(item, getSwimStrokeCount()) { it > 0 })
            }
            SummaryItem.SWIMSTROKEDISTANCE -> {
                val totalDistance = getTotalDistance() ?: 0.0
                val strokeCount = getSwimStrokeCount() ?: 0
                val strokeDistance = if (strokeCount > 0) {
                    totalDistance / strokeCount
                } else {
                    0.0
                }
                workoutValues.addIfValid(formatValueIf(item, strokeDistance) { it > 0.0 })
            }
            SummaryItem.AVGHEARTRATE -> {
                val hz = workoutHeader?.heartRateAverage
                    ?.bpm
                    ?.inHz
                    ?: activityWindow?.hr?.avg
                workoutValues.addIfValid(formatValueIf(item, hz) { it > 0.0 })
            }
            SummaryItem.MAXHEARTRATE -> {
                val hz = workoutHeader?.heartRateMax
                    ?.bpm
                    ?.inHz
                    ?: activityWindow?.hr?.max
                workoutValues.addIfValid(formatValueIf(item, hz) { it > 0.0 })
            }
            SummaryItem.MINHEARTRATE -> {
                // No header value
                val hz = activityWindow?.hr?.min
                workoutValues.addIfValid(formatValueIf(item, hz) { it > 0.0 })
            }
            SummaryItem.ENERGY -> {
                val joules = workoutHeader?.energyConsumption
                    ?.kcal
                    ?.inJoules
                    ?: activityWindow?.energy
                workoutValues.addIfValid(formatValueIf(item, joules) { it > 0.0 })
            }
            SummaryItem.RECOVERYTIME -> {
                // Recovery time is not editable and comes from SML
                val recoveryTime = activityWindow?.recoveryTime
                    ?: summaryExtension?.recoveryTime
                    ?: workoutHeader?.recoveryTime
                recoveryTime?.takeIf { it > 3600L }
                    ?.let { workoutValues.addIfValid(formatValue(item, recoveryTime)) }
            }
            SummaryItem.PTE -> {
                workoutValues.addIfValid(formatValueIf(item, summaryExtension?.pte) { it != 0f })
            }
            SummaryItem.PERFORMANCELEVEL -> {
                workoutValues.addIfValid(
                    formatValueIf(item, summaryExtension?.performanceLevel) {
                        it.roundToInt() > 0
                    }
                )
            }
            SummaryItem.AVGSPEED -> {
                workoutValues.addIfValid(formatValueIf(item, getAvgSpeed()) { it > 0.0 })
            }
            SummaryItem.MOVINGSPEED -> {
                workoutValues.addIfValid(formatValueIf(item, getMovingSpeed()) { it > 0.0 })
            }
            SummaryItem.MAXCADENCE -> {
                val maxCadence = activityWindow?.cadence?.max ?: summaryExtension?.maxCadence?.toDouble()
                if (maxCadence != null) {
                    workoutValues.addIfValid(
                        formatValueIf(
                            item,
                            if (isCadenceProcessingRequiredForSuuntoRun()) maxCadence / 2 else maxCadence
                        ) { it > 0.0 }?.copy(label = appResources.getString(R.string.max_cadence))
                    )
                }
            }
            SummaryItem.AVGCADENCE -> {
                // No need to prefer the header here as cadence is not editable
                val avgCadence =
                    activityWindow?.cadence?.avg ?: summaryExtension?.avgCadence?.toDouble()
                if (avgCadence != null) {
                    // step cadence = cadence * 2 https://suunto.tpondemand.com/entity/183309-optimize-step-frequency-and-stride-data
                    workoutValues.addIfValid(
                        formatValueIf(
                            item,
                            if (isCadenceProcessingRequiredForSuuntoRun()) avgCadence / 2 else avgCadence
                        ) { it > 0.0 }?.copy(label = appResources.getString(R.string.workout_values_headline_avg_cadence))
                    )
                } else {
                    workoutHeader?.let { workoutHeader ->
                        val durationInSeconds = getDurationInSeconds() ?: 0.0
                        if (workoutHeader.activityType.isByFoot &&
                            workoutHeader.stepCount > 0 &&
                            durationInSeconds > 0.0) {
                            val stepCadenceInHz = (workoutHeader.stepCount / durationInSeconds / 2)
                            workoutValues.addIfValid(
                                formatValue(item, stepCadenceInHz)?.copy(unit = CR.string.per_minute)
                            )
                        }
                    }
                }
            }
            SummaryItem.STEPS -> {
                workoutValues.addIfValid(formatValueIf(item, workoutHeader?.stepCount) { it > 0 })
            }
            SummaryItem.AVGSTEPCADENCE -> {
                workoutValues.addIfValid(
                    formatValueIf(item, getStepCadence(), { it > 0.0 })
                        ?.copy(unit = R.string.step_per_minute)
                )
            }
            SummaryItem.MAXSTEPCADENCE -> {
                workoutValues.addIfValid(
                    formatValueIf(item, getMaxStepCadence(), { it > 0.0 })
                        ?.copy(unit = R.string.step_per_minute)
                )
            }
            SummaryItem.AVGSTRIDELENGTH -> {
                workoutValues.addIfValid(formatValueIf(item, getStrideLength()) { it > 0.0 })
            }
            SummaryItem.AVGSTEPLENGTH -> {
                workoutValues.addIfValid(formatValueIf(item, getStepLength()) { it > 0.0 })
            }
            SummaryItem.ASCENTALTITUDE -> {
                if (!hideAllAscentValues()) {
                    val totalAscent = workoutHeader?.totalAscent ?: activityWindow?.ascent
                    workoutValues.addIfValid(formatValue(item, totalAscent))
                }
            }
            SummaryItem.ASCENTTIME -> {
                if (!hideAllAscentValues()) {
                    val ascentTime = activityWindow?.ascentTime ?: summaryExtension?.ascentTime
                    workoutValues.addIfValid(formatValue(item, ascentTime))
                }
            }
            SummaryItem.DESCENTALTITUDE -> {
                if (!hideAllAscentValues()) {
                    val totalDescent = workoutHeader?.totalDescent ?: activityWindow?.descent
                    workoutValues.addIfValid(formatValue(item, totalDescent))
                }
            }
            SummaryItem.DESCENTTIME -> {
                if (!hideAllAscentValues()) {
                    val descentTime = activityWindow?.descentTime ?: summaryExtension?.descentTime
                    workoutValues.addIfValid(formatValue(item, descentTime))
                }
            }
            SummaryItem.HIGHALTITUDE -> {
                if (!hideAllAscentValues()) {
                    // Make sure to get the value from sml when workoutHeader.maxAltitude is 0 for some unknown reason.
                    val maxAltitude = workoutHeader?.maxAltitude?.takeIf { it != 0.0 }?.run {
                            unit.fromDecimetersToMeters(this)
                        }
                        ?: activityWindow?.altitudeRange?.max
                        ?: sml?.streamData?.altitude?.maxOfOrNull { it.value }
                    if (maxAltitude != null) {
                        workoutValues.addIfValid(formatValue(item, maxAltitude))
                    }
                }
            }
            SummaryItem.LOWALTITUDE -> {
                if (!hideAllAscentValues()) {
                    val minAltitude = workoutHeader?.minAltitude?.takeIf { it != 0.0 }?.run {
                            unit.fromDecimetersToMeters(this)
                        }
                        ?: activityWindow?.altitudeRange?.min
                        ?: sml?.streamData?.altitude?.minOfOrNull { it.value }
                    if (minAltitude != null) {
                        workoutValues.addIfValid(formatValueIf(item, minAltitude) { it != 0.0 })
                    }
                }
            }
            SummaryItem.PEAKVERTICALSPEED30S -> {
                workoutValues.addIfValid(
                    formatValueIf(item, getPeakVerticalSpeedInXMillis(windowInMillis = 30.seconds.inWholeMilliseconds)) { it > 0.8333 }
                )
            }
            SummaryItem.PEAKVERTICALSPEED1M -> {
                workoutValues.addIfValid(
                    formatValueIf(item, getPeakVerticalSpeedInXMillis(windowInMillis = 1.minutes.inWholeMilliseconds)) { it > 0.8333 }
                )
            }
            SummaryItem.PEAKVERTICALSPEED3M -> {
                workoutValues.addIfValid(
                    formatValueIf(item, getPeakVerticalSpeedInXMillis(windowInMillis = 3.minutes.inWholeMilliseconds)) { it > 0.8333 }
                )
            }
            SummaryItem.PEAKVERTICALSPEED5M -> {
                workoutValues.addIfValid(
                    formatValueIf(item, getPeakVerticalSpeedInXMillis(windowInMillis = 5.minutes.inWholeMilliseconds)) { it > 0.8333 }
                )
            }
            SummaryItem.AVGTEMPERATURE -> {
                // Temperature value is Kelvin and 0 default value is not real
                val avgTemperature =
                    activityWindow?.temperature?.avg ?: summaryExtension?.avgTemperature?.toDouble()
                workoutValues.addIfValid(
                    formatValueIf(
                        item,
                        avgTemperature
                    ) { it > 0.0 }
                )
            }
            SummaryItem.PEAKEPOC -> {
                val workoutValue =
                    formatValueIf(item, summaryExtension?.peakEpoc) { it.roundToInt() != 0 }
                if (workoutValue != null && !workoutValue.value.isNullOrEmpty()) {
                    workoutValues.add(workoutValue)
                }
            }
            SummaryItem.FEELING -> {
                workoutValues.addIfValid(
                    formatValueIf(item, summaryExtension?.feeling) {
                        it != WorkoutFeeling.UNDEFINED.value
                    }
                )
            }
            SummaryItem.MOVETYPE -> {
            }
            SummaryItem.CATCHFISH -> {
                val catchList = sml?.traverseEvents
                    ?.filterIsInstance<CatchEvent>()
                    ?.filter { it.description == CatchMarkType.FISH }
                val fishCount = catchList?.sumOf { it.count }
                workoutValues.addIfValid(
                    formatValueIf(
                        item,
                        fishCount
                    ) { it > 0 }
                )
            }
            SummaryItem.CATCHBIGGAME -> {
            }
            SummaryItem.CATCHSMALLGAME -> {
            }
            SummaryItem.CATCHBIRD -> {
            }
            SummaryItem.CATCHSHOTCOUNT -> {
                val shotCount = sml?.traverseEvents
                    ?.count { it is ShotEvent }
                workoutValues.addIfValid(formatValueIf(item, shotCount) { it > 0 })
            }
            SummaryItem.AVGPOWER -> {
                val avgPower = activityWindow?.power?.avg ?: summaryExtension?.avgPower?.toDouble()
                workoutValues.addIfValid(
                    formatValueIf(
                        item,
                        avgPower
                    ) { it.roundToInt() > 0 }
                )
            }
            SummaryItem.AVGPOWERWITHZERO -> {
                workoutValues.addIfValid(
                    formatValueIf(item, sml?.streamData?.power?.averageOfFloat(SmlExtensionStreamPoint::value)) { avgPowerWithZeros ->
                        if (!avgPowerWithZeros.isFinite()) {
                            false
                        } else if (avgPowerWithZeros <= 0.0) {
                            false
                        } else {
                            val avgPower = activityWindow?.power?.avg?.roundToInt()
                                ?: summaryExtension?.avgPower?.roundToInt()
                                ?: 0
                            avgPowerWithZeros.roundToInt() != avgPower
                        }
                    }
                )
            }
            SummaryItem.PEAKPOWER30S -> {
                workoutValues.addIfValid(
                    formatValueIf(item, getPeakPowerInXMillis(windowInMillis = 30.seconds.inWholeMilliseconds)) { it > 0.0 }
                )
            }
            SummaryItem.PEAKPOWER1M -> {
                workoutValues.addIfValid(
                    formatValueIf(item, getPeakPowerInXMillis(windowInMillis = 1.minutes.inWholeMilliseconds)) { it > 0.0 }
                )
            }
            SummaryItem.PEAKPOWER3M -> {
                workoutValues.addIfValid(
                    formatValueIf(item, getPeakPowerInXMillis(windowInMillis = 3.minutes.inWholeMilliseconds)) { it > 0.0 }
                )
            }
            SummaryItem.PEAKPOWER5M -> {
                workoutValues.addIfValid(
                    formatValueIf(item, getPeakPowerInXMillis(windowInMillis = 5.minutes.inWholeMilliseconds)) { it > 0.0 }
                )
            }
            SummaryItem.AVGSWOLF -> {
                val avgSwolf = activityWindow?.swolf?.avg?.toInt() ?: swimmingExtension?.avgSwolf
                workoutValues.addIfValid(
                    formatValueIf(
                        item,
                        avgSwolf
                    ) { it > 0 }
                )
            }
            SummaryItem.AVGSWIMSTROKERATE -> {
                val avgStrokeRate = activityWindow?.strokeRate?.avg ?: swimmingExtension?.avgStrokeRate?.toDouble()
                workoutValues.addIfValid(
                    formatValueIf(
                        item,
                        avgStrokeRate
                    ) { it > 0 }
                )
            }
            SummaryItem.AVGNAUTICALSPEED -> {
                workoutValues.addIfValid(formatValueIf(item, getAvgSpeed()) { it > 0.0 })
            }
            SummaryItem.MAXNAUTICALSPEED -> {
                workoutValues.addIfValid(
                    formatValueIf(item, getMaxSpeed()) {
                        it > 0.0
                    }
                )
            }
            SummaryItem.MAXSPEED -> {
                workoutValues.addIfValid(formatValueIf(item, getMaxSpeed()) { it > 0.0 })
            }
            SummaryItem.PEAKSPEED30S -> {
                workoutValues.addIfValid(
                    formatValueIf(item, getPeakSpeedInXMillis(windowInMillis = 30.seconds.inWholeMilliseconds)) { it > 0.0 }
                )
            }
            SummaryItem.PEAKSPEED1M -> {
                workoutValues.addIfValid(
                    formatValueIf(item, getPeakSpeedInXMillis(windowInMillis = 1.minutes.inWholeMilliseconds)) { it > 0.0 }
                )
            }
            SummaryItem.PEAKSPEED3M -> {
                workoutValues.addIfValid(
                    formatValueIf(item, getPeakSpeedInXMillis(windowInMillis = 3.minutes.inWholeMilliseconds)) { it > 0.0 }
                )
            }
            SummaryItem.PEAKSPEED5M -> {
                workoutValues.addIfValid(
                    formatValueIf(item, getPeakSpeedInXMillis(windowInMillis = 5.minutes.inWholeMilliseconds)) { it > 0.0 }
                )
            }
            SummaryItem.AVGDEPTH -> {
                val avgDepth = diveExtension?.avgDepth ?: ngDiveData?.avgDepth
                workoutValues.addIfValid(formatValueIf(item, avgDepth) { it > 0.0 })
            }
            SummaryItem.MAXDEPTH -> {
                val maxDepth = diveExtension?.maxDepth ?: ngDiveData?.maxDepth
                workoutValues.addIfValid(formatValueIf(item, maxDepth) { it > 0.0 })
            }
            SummaryItem.MINDEPTH -> {
                workoutValues.addIfValid(formatValueIf(item, activityWindow?.depth?.min) { it > 0.0 })
            }
            SummaryItem.DIVETIME -> {
                var diveTime = activityWindow?.diveTime ?: diveExtension?.diveTime?.toDouble()
                val totalTime = workoutHeader?.totalTime
                // Exception for legacy scuba and free dives that total time is dive time
                if (diveTime == null && totalTime != null && workoutHeader?.activityType?.isDiving == true) {
                    diveTime = diveExtension?.let {
                        totalTime - (diveExtension.pauseDuration?.toLong() ?: 0L)
                    } ?: totalTime
                }
                val workoutValue = formatValue(item, diveTime)
                if (workoutHeader?.activityType in listOf(
                        ActivityType.SNORKELING,
                        ActivityType.MERMAIDING
                    )
                ) {
                    workoutValues.addIfValid(
                        workoutValue?.copy(
                            label = appResources.getString(R.string.workout_values_headline_total_time_underwater),
                            descriptionTextResId = R.string.item_description_total_time_underwater
                        )
                    )
                } else {
                    workoutValues.addIfValid(workoutValue)
                }
            }
            SummaryItem.DIVETIMEMAX -> {
                workoutValues.addIfValid(formatValue(item, activityWindow?.diveTimeMax))
            }
            SummaryItem.DIVERECOVERYTIME -> {
                workoutValues.addIfValid(formatValue(item, activityWindow?.diveRecoveryTime))
            }
            SummaryItem.DIVEINWORKOUT -> {
                workoutValues.addIfValid(formatValue(item, activityWindow?.diveInWorkout))
            }
            SummaryItem.DIVEMODE -> {
                diveExtension?.diveMode?.let { diveMode ->
                    val mode = try {
                        DiveMode.valueOf(diveMode.filterNot { it.isWhitespace() })
                    } catch (e: Exception) {
                        null
                    }

                    val value = mode?.diveModeLocalized(appResources) ?: diveMode
                    // Text size needs to be adjusted for longer values.
                    val textSizeResId = when {
                        value.length >= 10 -> R.dimen.workout_value_medium_dp
                        else -> R.dimen.workout_value_large_dp
                    }
                    workoutValues.addIfValid(
                        formatValue(item, value)
                            ?.copy(textSizeResId = textSizeResId)
                    )
                }
            }
            SummaryItem.DIVENUMBERINSERIES -> {
                // Only for legacy dive
                diveExtension?.diveNumberInSeries?.let {
                    val workoutValue = formatValueIf(item, it) { ngDiveData == null }
                    workoutValue?.value?.let { value ->
                        workoutValues += workoutValue
                            .copy(
                                value = appResources.getString(
                                    R.string.workout_values_value_dive_num_in_series,
                                    value.toInt()
                                )
                            )
                    }
                }
            }
            SummaryItem.DIVESURFACETIME -> {
                val surfaceTime = diveExtension?.surfaceTime ?: ngDiveData?.surfaceTime
                workoutValues.addIfValid(
                    formatValueIf(
                        item,
                        surfaceTime
                    ) { it > 0.0 }
                )
            }
            SummaryItem.DIVEVISIBILITY -> {
            }
            SummaryItem.DIVEMAXDEPTHTEMPERATURE -> {
                // Only for legacy dive
                // Temperature value is Kelvin and 0 default value is not real
                workoutValues.addIfValid(
                    formatValueIf(
                        item,
                        diveExtension?.maxDepthTemperature
                    ) { ngDiveData == null && it > 0f }
                )
            }
            SummaryItem.DIVEGASES -> {
                val useNgDiveData = ngDiveData?.gasesUsed?.isNotEmpty() == true
                if (useNgDiveData) {
                    val ngDiveGasWorkoutValues = buildNgDiveGasWorkoutValues(item)
                    workoutValues.addAll(ngDiveGasWorkoutValues)
                } else {
                    val legacyDiveGasWorkoutValues = buildLegacyDiveGasWorkoutValues(item)
                    workoutValues.addAll(legacyDiveGasWorkoutValues)
                }
            }
            SummaryItem.PERSONAL -> {
                workoutValues.addIfValid(formatValue(item, diveExtension?.personalSetting))
            }
            SummaryItem.ALTITUDESETTING -> {
                diveExtension?.getAltitudeSettingAsEnum()?.let { altitudeSetting ->
                    // Text size needs to be adjusted for longer values.
                    val textSizeResId = if (altitudeSetting == AltitudeSetting.LOW) {
                        R.dimen.workout_value_medium_dp
                    } else {
                        R.dimen.workout_value_small_dp
                    }
                    workoutValues.addIfValid(
                        formatValue(item, altitudeSetting)
                            ?.copy(textSizeResId = textSizeResId)
                    )
                }
            }
            SummaryItem.GASCONSUMPTION -> {
                workoutValues.addIfValid(formatValue(item, diveExtension?.gasConsumption))
            }
            SummaryItem.DIVECNS -> {
                val cns = diveExtension?.cns ?: ngDiveData?.cns
                workoutValues.addIfValid(
                    formatValueIf(item, cns) {
                        shouldDisplayOxygenToxicityInfo(diveExtension, workoutHeader) && it >= 0f
                    }
                )
            }
            SummaryItem.DIVEOTU -> {
                val otu = diveExtension?.otu ?: ngDiveData?.otu
                workoutValues.addIfValid(
                    formatValueIf(item, otu) {
                        shouldDisplayOxygenToxicityInfo(diveExtension, workoutHeader) && it >= 0f
                    }
                )
            }
            SummaryItem.ALGORITHMLOCK -> {
                workoutValues.addIfValid(formatValueIf(item, diveExtension?.algorithmLock) { it })
            }
            SummaryItem.ALGORITHM -> {
                val algorithm = diveExtension?.algorithm ?: ngDiveData?.algorithm
                workoutValues.addIfValid(
                    formatValue(item, algorithm)?.let {
                        val textSizeResId = if ((it.value?.length ?: 0) < 12) {
                            R.dimen.workout_value_medium_dp
                        } else {
                            R.dimen.workout_value_small_dp
                        }
                        it.copy(textSizeResId = textSizeResId)
                    }
                )
            }
            SummaryItem.SKIRUNCOUNT -> {
                workoutValues.addIfValid(
                    formatValue(
                        item,
                        sml?.summary?.header?.downhillCount ?: slopeSkiSummary?.totalRuns
                    )
                )
            }
            SummaryItem.SKITIME -> {
                val durationInSeconds = slopeSkiSummary?.descentDurationInMilliseconds
                    ?.div(1000.0)
                    ?: sml?.summary?.header?.downhillDuration?.toDouble()
                workoutValues.addIfValid(formatValue(item, durationInSeconds))
            }
            SummaryItem.AVGSKISPEED -> {
                val speedInMetersPerSecond = slopeSkiSummary?.takeIf { slopeSkiSummary ->
                    slopeSkiSummary.descentDurationInMilliseconds > 0L
                }?.let { slopeSkiSummary ->
                    slopeSkiSummary.descentDistanceInMeters / (slopeSkiSummary.descentDurationInMilliseconds / 1000.0)
                } ?: sml?.summary?.header?.downhillSpeed?.avg?.toDouble()
                workoutValues.addIfValid(formatValue(item, speedInMetersPerSecond))
            }
            SummaryItem.MAXSKISPEED -> {
                workoutValues.addIfValid(
                    formatValueIf(
                        item,
                        sml?.summary?.header?.downhillSpeed?.max
                            ?: slopeSkiSummary?.maxSpeedMetersPerSecond?.toFloat()
                    ) { it > 0.0 }
                )
            }
            SummaryItem.SKIDISTANCE -> {
                workoutValues.addIfValid(
                    formatValue(
                        item,
                        sml?.summary?.header?.downhillDistance ?: slopeSkiSummary?.descentDistanceInMeters
                    )
                )
            }
            SummaryItem.ESTVO2PEAK -> {
                workoutValues.addIfValid(formatValueIf(item, fitnessExtension?.vo2Max) { it > 0 })
            }
            SummaryItem.GRADIENTFACTORS -> {
                val minGF = diveExtension?.minGF ?: ngDiveData?.minGF
                val maxGF = diveExtension?.maxGF ?: ngDiveData?.maxGF
                workoutValues.addIfValid(formatValueIf(item, minGF to maxGF) {
                    minGF != null && maxGF != null
                })
            }
            SummaryItem.TRAININGSTRESSSCORE -> {
                workoutHeader?.tss?.let { tss ->
                    workoutValues.addIfValid(
                        formatValueIf(item, tss.trainingStressScore) { it > 0 }?.copy(
                            label = appResources.getString(tss.calculationMethod.getNameStringResId(callManualTSS = true))
                        )
                    )
                }
            }
            SummaryItem.NORMALIZEDPOWER -> {
                val avgPower = activityWindow?.power?.avg ?: summaryExtension?.avgPower?.toDouble()
                val havePower = avgPower != null && avgPower > 0.0
                if (havePower) {
                    getNp()?.let {
                        workoutValues.addIfValid(formatValue(SummaryItem.NORMALIZEDPOWER, it))
                    }
                }
            }
            SummaryItem.NORMALIZEDGRADEDPACE -> {
                val workoutValue = formatValueIf(item, getNgp()) { it > 0.0 }
                if (workoutValue != null && !workoutValue.value.isNullOrEmpty()) {
                    workoutValues.add(workoutValue)
                }
            }
            SummaryItem.MAXDOWNHILLGRADE -> {
                val downhillGradeMax = activityWindow?.downhillGrade?.max
                    ?: sml?.summary?.header?.downhillGrade?.max?.toDouble()
                if (downhillGradeMax != null) {
                    workoutValues.addIfValid(formatValue(item, downhillGradeMax))
                }
            }
            SummaryItem.AVGDOWNHILLGRADE -> {
                val downhillGradeAvg = activityWindow?.downhillGrade?.avg
                    ?: sml?.summary?.header?.downhillGrade?.avg?.toDouble()
                if (downhillGradeAvg != null) {
                    workoutValues.addIfValid(formatValue(item, downhillGradeAvg))
                }
            }
            SummaryItem.AVGDOWNHILLSPEED -> {
                workoutValues.addIfValid(formatValue(item, sml?.summary?.header?.downhillSpeed?.avg))
            }
            SummaryItem.MAXDOWNHILLSPEED -> {
                workoutValues.addIfValid(formatValue(item, sml?.summary?.header?.downhillSpeed?.max))
            }
            SummaryItem.DOWNHILLDISTANCE -> {
                workoutValues.addIfValid(formatValue(item, sml?.summary?.header?.downhillDistance))
            }
            SummaryItem.DOWNHILLDESCENT -> {
                workoutValues.addIfValid(formatValue(item, sml?.summary?.header?.downhillDescent))
            }
            SummaryItem.DOWNHILLDURATION -> {
                workoutValues.addIfValid(formatValue(item, sml?.summary?.header?.downhillDuration))
            }
            SummaryItem.DOWNHILLCOUNT -> {
                workoutValues.addIfValid(formatValue(item, sml?.summary?.header?.downhillCount))
            }
            SummaryItem.DOWNHILLMAXDESCENT -> {
                workoutValues.addIfValid(formatValue(item, sml?.summary?.header?.downhillMaxDescent))
            }
            SummaryItem.DOWNHILLMAXLENGTH -> {
                workoutValues.addIfValid(formatValue(item, sml?.summary?.header?.downhillMaxLength))
            }
            SummaryItem.AVGVERTICALSPEED -> {
            }
            SummaryItem.MAXTEMPERATURE -> {
            }
            SummaryItem.MAXPOWER -> {
            }
            SummaryItem.AVGSEALEVELPRESSURE -> {
            }
            SummaryItem.CUMULATEDDISTANCE -> {
            }
            SummaryItem.CUMULATEDDURATION -> {
            }
            SummaryItem.CUMULATEDSWIMDISTANCE -> {
            }
            SummaryItem.SWIMSTYLE -> {
            }
            SummaryItem.TYPE -> {
            }
            SummaryItem.NONE -> {
            }
            SummaryItem.DIVEGASPRESSURE -> {
                // handled in SummaryItem.DIVEGASES
            }
            SummaryItem.DIVEGASENDPRESSURE -> {
                // handled in SummaryItem.DIVEGASES
            }
            SummaryItem.DIVEGASUSEDPRESSURE -> {
                // handled in SummaryItem.DIVEGASES
            }
            SummaryItem.CO2EMISSIONSREDUCED -> {
                if (workoutHeader != null && workoutHeader.isCommute) {
                    formatValue(
                        SummaryItem.CO2EMISSIONSREDUCED,
                        workoutHeader.co2EmissionsReduced,
                        UnitType.METRIC // co2 emissions reduced is always in kg
                    )?.let {
                        workoutValues.addIfValid(it)
                    }
                }
            }
            SummaryItem.REVOLUTIONCOUNT,
            SummaryItem.ROWINGSTROKECOUNT,
            SummaryItem.SKIPCOUNT -> {
                (activityWindow?.repetitionCount ?: summaryExtension?.repetitionCount)
                    ?.let { repetitionCount ->
                        workoutValues.addIfValid(formatValueIf(item, repetitionCount){
                            it > 0
                        })
                    }
            }
            SummaryItem.HRAEROBICTHRESHOLD -> {
                aerobicHrThreshold?.let { formatValue(SummaryItem.HRAEROBICTHRESHOLD, it.inHz) }
                    ?.let { workoutValues.addIfValid(it) }
            }

            SummaryItem.HRANAEROBICTHRESHOLD -> {
                anaerobicHrThreshold?.let { formatValue(SummaryItem.HRANAEROBICTHRESHOLD, it.inHz) }
                    ?.let { workoutValues.addIfValid(it) }
            }

            SummaryItem.ZONESENSEBASELINE -> zoneSenseBaseline
                ?.let { formatValue(SummaryItem.ZONESENSEBASELINE, it) }
                ?.let { workoutValues.addIfValid(it) }

            SummaryItem.ZONESENSECUMULATIVEBASELINE -> zoneSenseCumulativeBaseline
                ?.let { formatValue(SummaryItem.ZONESENSECUMULATIVEBASELINE, it) }
                ?.let { workoutValues.addIfValid(it) }

            SummaryItem.AEROBICPACETHRESHOLD,
            SummaryItem.ANAEROBICPACETHRESHOLD,
            SummaryItem.AEROBICPOWERTHRESHOLD,
            SummaryItem.ANAEROBICPOWERTHRESHOLD -> Unit

            SummaryItem.AEROBICDURATION -> {
                // We don't show the item if the duration is zero
                aerobicDuration?.takeIf { it > Duration.ZERO }
                    ?.let { formatValue(SummaryItem.AEROBICDURATION, it.inWholeSeconds) }
                    ?.let { workoutValues.addIfValid(it) }
            }

            SummaryItem.ANAEROBICDURATION -> {
                anaerobicDuration?.takeIf { it > Duration.ZERO }
                    ?.let { formatValue(SummaryItem.ANAEROBICDURATION, it.inWholeSeconds) }
                    ?.let { workoutValues.addIfValid(it) }
            }

            SummaryItem.VO2MAXDURATION -> {
                vo2MaxDuration?.takeIf { it > Duration.ZERO }
                    ?.let { formatValue(SummaryItem.VO2MAXDURATION, it.inWholeSeconds) }
                    ?.let { workoutValues.addIfValid(it) }
            }

            // properties for swimming
            SummaryItem.BREATHINGRATE -> {
                swimmingExtension?.breathingRate?.takeIf { it > 0 }?.let {
                    formatValue(item, it)
                }?.let {
                    workoutValues.addIfValid(it)
                }
            }

            SummaryItem.BREASTSTROKEDURATION -> {
                swimmingExtension?.breaststrokeDuration?.takeIf { it > 0 }?.let {
                    formatValue(item, it)
                }?.let {
                    workoutValues.addIfValid(it)
                }
            }

            SummaryItem.BREASTSTROKEPERCENT -> {
                swimmingExtension?.breaststrokePercentage?.takeIf { it > 0 }?.let {
                    formatValue(item, it)
                }?.let {
                    workoutValues.addIfValid(it)
                }
            }

            SummaryItem.BREASTSTROKEGLIDETIME -> {
                swimmingExtension?.breaststrokeGlideTime?.takeIf { it > 0 }?.let {
                    formatValue(item, it)
                }?.let {
                    workoutValues.addIfValid(it)
                }
            }
            SummaryItem.MAXBREASTSTROKEBREATHANGLE -> {
                swimmingExtension?.breaststrokeMaxBreathAngle?.takeIf { it > 0 }?.let {
                    formatValue(item, it)
                }?.let {
                    workoutValues.addIfValid(it)
                }
            }
            SummaryItem.AVGBREASTSTROKEBREATHANGLE -> {
                swimmingExtension?.breaststrokeAvgBreathAngle?.takeIf { it > 0 }?.let {
                    formatValue(item, it)
                }?.let {
                    workoutValues.addIfValid(it)
                }
            }
            SummaryItem.FREESTYLEDURATION -> {
                swimmingExtension?.freestyleDuration?.takeIf { it > 0 }?.let {
                    formatValue(item, it)
                }?.let {
                    workoutValues.addIfValid(it)
                }
            }
            SummaryItem.FREESTYLEPERCENT -> {
                swimmingExtension?.freestylePercentage?.takeIf { it > 0 }?.let {
                    formatValue(item, it)
                }?.let {
                    workoutValues.addIfValid(it)
                }
            }
            SummaryItem.AVGFREESTYLEBREATHANGLE -> {
                swimmingExtension?.freestyleAvgBreathAngle?.takeIf { it > 0 }?.let {
                    formatValue(item, it)
                }?.let {
                    workoutValues.addIfValid(it)
                }
            }
            SummaryItem.MAXFREESTYLEBREATHANGLE -> {
                swimmingExtension?.freestyleMaxBreathAngle?.takeIf { it > 0 }?.let {
                    formatValue(item, it)
                }?.let {
                    workoutValues.addIfValid(it)
                }
            }
            SummaryItem.FREESTYLEPITCHANGLE -> {
                swimmingExtension?.freestylePitchAngle?.takeIf { it > 0 }?.let {
                    formatValue(item, it)
                }?.let {
                    workoutValues.addIfValid(it)
                }
            }
            SummaryItem.BREASTSTROKEHEADANGLE -> {
                swimmingExtension?.breaststrokeHeadAngle?.takeIf { it > 0 }?.let {
                    formatValue(item, it)
                }?.let {
                    workoutValues.addIfValid(it)
                }
            }
            SummaryItem.FATCONSUMPTION -> {
                workoutValues.addIfValid(
                    formatValue(
                        item,
                        activityWindow?.fatConsumption ?: summaryExtension?.fatConsumption
                    )
                )
            }

            SummaryItem.CARBOHYDRATECONSUMPTION -> {
                workoutValues.addIfValid(
                    formatValue(
                        item,
                        activityWindow?.carbohydrateConsumption ?: summaryExtension?.carbohydrateConsumption
                    )
                )
            }

            SummaryItem.AVGGROUNDCONTACTTIME -> {
                workoutValues.addIfValid(
                    formatValue(
                        item,
                        activityWindow?.groundContactTime?.avg ?: summaryExtension?.avgGroundContactTime
                    )
                )
            }

            SummaryItem.AVGVERTICALOSCILLATION -> {
                workoutValues.addIfValid(
                    formatValue(
                        item,
                        activityWindow?.verticalOscillation?.avg ?: summaryExtension?.avgVerticalOscillation
                    )
                )
            }

            SummaryItem.AVGGROUNDCONTACTBALANCE -> {
                val avgLeftGroundContactBalance = activityWindow?.leftGroundContactBalance?.avg ?: summaryExtension?.avgLeftGroundContactBalance
                val avgRightGroundContactBalance = activityWindow?.rightGroundContactBalance?.avg ?: summaryExtension?.avgRightGroundContactBalance
                workoutValues.addIfValid(
                    formatValueIf(
                        item,
                        avgLeftGroundContactBalance to avgRightGroundContactBalance
                    ) {
                        avgLeftGroundContactBalance != null && avgRightGroundContactBalance != null
                    }
                )
            }

            SummaryItem.CLIMBS -> workoutValues.addValuesForClimb()

            SummaryItem.CLIMBSCATEGORY1,
            SummaryItem.CLIMBSCATEGORY2,
            SummaryItem.CLIMBSCATEGORY3,
            SummaryItem.CLIMBSCATEGORY4,
            SummaryItem.CLIMBSCATEGORYHC,
            SummaryItem.CLIMBASCENTCATEGORY1,
            SummaryItem.CLIMBASCENTCATEGORY2,
            SummaryItem.CLIMBASCENTCATEGORY3,
            SummaryItem.CLIMBASCENTCATEGORY4,
            SummaryItem.CLIMBASCENTCATEGORYHC,
            SummaryItem.CLIMBDISTANCECATEGORY1,
            SummaryItem.CLIMBDISTANCECATEGORY2,
            SummaryItem.CLIMBDISTANCECATEGORY3,
            SummaryItem.CLIMBDISTANCECATEGORY4,
            SummaryItem.CLIMBDISTANCECATEGORYHC,
            SummaryItem.CLIMBDURATIONCATEGORY1,
            SummaryItem.CLIMBDURATIONCATEGORY2,
            SummaryItem.CLIMBDURATIONCATEGORY3,
            SummaryItem.CLIMBDURATIONCATEGORY4,
            SummaryItem.CLIMBDURATIONCATEGORYHC -> {
                // Do nothing here. All the climb related items are handled by SummaryItem.CLIMBS,
                // because it could be slow to run the climb analysis algorithm.
            }

            SummaryItem.AVGASCENTSPEED -> {
                if (!hideAllAscentValues()) {
                    (activityWindow?.ascentSpeed?.avg ?: summaryExtension?.avgAscentSpeed?.toDouble())?.let { ascentSpeed ->
                        workoutValues.addIfValid(formatValueIf(item, ascentSpeed) { it > 0.0 })
                    } ?: run {
                        val ascent = workoutHeader?.totalAscent ?: activityWindow?.ascent ?: 0.0
                        val ascentTime = activityWindow?.ascentTime ?: summaryExtension?.ascentTime?.toDouble() ?: 0.0
                        if (ascentTime > 0.0) {
                            workoutValues.addIfValid(formatValueIf(item, ascent / ascentTime) { it > 0.0 })
                        }
                    }
                }
            }

            SummaryItem.AVGDESCENTSPEED -> {
                if (!hideAllAscentValues()) {
                    (activityWindow?.descentSpeed?.avg ?: summaryExtension?.avgDescentSpeed?.toDouble())?.let { descentSpeed ->
                        workoutValues.addIfValid(formatValueIf(item, descentSpeed) { it > 0.0 })
                    } ?: run {
                        val descent = workoutHeader?.totalDescent ?: activityWindow?.descent ?: 0.0
                        val descentTime = activityWindow?.descentTime ?: summaryExtension?.descentTime?.toDouble() ?: 0.0
                        if (descentTime > 0.0) {
                            workoutValues.addIfValid(formatValueIf(item, descent / descentTime) { it > 0.0 })
                        }
                    }
                }
            }

            SummaryItem.MAXASCENTSPEED -> {
                workoutValues.addIfValid(
                    formatValueIf(
                        item,
                        activityWindow?.ascentSpeed?.max ?: summaryExtension?.maxAscentSpeed?.toDouble()
                    ) { it > 0.0 })
            }

            SummaryItem.MAXDESCENTSPEED -> {
                workoutValues.addIfValid(
                    formatValueIf(
                        item,
                        activityWindow?.descentSpeed?.max ?: summaryExtension?.maxDescentSpeed?.toDouble()
                    ) { it > 0.0 })
            }

            SummaryItem.AVGDISTANCEPERSTROKE -> {
                workoutValues.addIfValid(
                    formatValueIf(
                        item,
                        activityWindow?.distancePerStroke?.avg ?: summaryExtension?.avgDistancePerStroke?.toDouble()
                    ) { it > 0.0 })
            }
            SummaryItem.AVGSKIPSRATE -> {
                // No need to prefer the header here as cadence is not editable
                val avgCadence =
                    activityWindow?.cadence?.avg ?: summaryExtension?.avgCadence?.toDouble()
                if (avgCadence != null) {
                    workoutValues.addIfValid(
                        formatValueIf(
                            item,
                            avgCadence
                        ) { it > 0.0 }
                    )
                } else {
                    workoutHeader?.let { workoutHeader ->
                        val durationInSeconds = getDurationInSeconds() ?: 0.0
                        if (workoutHeader.activityType.isByFoot &&
                            workoutHeader.stepCount > 0 &&
                            durationInSeconds > 0.0) {
                            val stepCadenceInHz = (workoutHeader.stepCount / durationInSeconds)
                            workoutValues.addIfValid(
                                formatValue(item, stepCadenceInHz)?.copy(unit = CR.string.per_minute)
                            )
                        }
                    }
                }
            }
            SummaryItem.MAXAVGSKIPSRATE -> {
                val maxCadence = activityWindow?.cadence?.max ?: summaryExtension?.maxCadence?.toDouble()
                workoutValues.addIfValid(
                    formatValueIf(
                        item,
                        maxCadence
                    ) { it > 0.0 }
                )
            }
            SummaryItem.ROUNDS -> {
                workoutValues.addIfValid(
                    formatValueIf(
                        item,
                        jumpRopeExtension?.rounds
                    ) { it > 0 })
            }
            SummaryItem.AVGSKIPSPERROUND -> {
                workoutValues.addIfValid(
                    formatValueIf(
                        item,
                        jumpRopeExtension?.avgSkipsPerRound
                    ) { it > 0.0 })
            }
            SummaryItem.MAXCONSECUTIVESKIPS -> {
                workoutValues.addIfValid(
                    formatValueIf(
                        item,
                        jumpRopeExtension?.maxConsecutiveSkips
                    ) { it > 0 })
            }
            SummaryItem.AVGROWINGPACE -> {
                workoutValues.addIfValid(formatValueIf(item, getAvgSpeed()) { it > 0.0 })
            }
        }

        // adding summary item to workout value
        return workoutValues.map {
            if (it.item == null) {
                it.copy(item = item)
            } else {
                it
            }
        }
    }

    private class ClimbData(
        var count: Int = 0,
        var ascent: Double = 0.0,
        var distance: Double = 0.0,
        var duration: Int = 0,
    )

    private fun MutableList<WorkoutValue>.addValuesForClimb() {
        val climbSegments = getClimbSegments() ?: return

        var totalClimbs = 0
        val climbDataByCategory = mapOf(
            StravaClimbCategory.CATEGORY_1 to ClimbData(),
            StravaClimbCategory.CATEGORY_2 to ClimbData(),
            StravaClimbCategory.CATEGORY_3 to ClimbData(),
            StravaClimbCategory.CATEGORY_4 to ClimbData(),
            StravaClimbCategory.HORS_CATEGORIE to ClimbData(),
        )

        climbSegments.forEach { climbSegment ->
            if (climbSegment.isClimbOrUphill) {
                totalClimbs++
            }

            val climbData = climbSegment.stravaClimbCategory
                ?.let(climbDataByCategory::get)
                ?: return@forEach
            climbData.count++
            climbData.ascent += (climbSegment.ascentAtEnd - climbSegment.ascentAtStart)
            climbData.distance += (climbSegment.distanceAtEnd - climbSegment.distanceAtStart)
            climbData.duration += climbSegment.getDurationInSeconds()
        }

        addIfValid(formatValueIf(SummaryItem.CLIMBS, totalClimbs) { it > 0})
        climbDataByCategory.forEach { (category, climbData) ->
            addIfValid(formatValueIf(category.toSummaryItem(), climbData.count) { it > 0 })
            addIfValid(formatValueIf(category.toDistanceSummaryItem(), climbData.distance) { it > 0.0 })
            addIfValid(formatValueIf(category.toDurationSummaryItem(), climbData.duration) { it > 0 })
            addIfValid(formatValueIf(category.toAscentSummaryItem(), climbData.ascent) { it > 0.0 })
        }
    }

    private fun getClimbSegments(): List<ClimbSegment>? = workoutData?.routePoints
        ?.takeUnless(List<*>::isEmpty)
        ?.map { geoPoint ->
            object : Point {
                override fun altitude(): Double? = geoPoint.altitude.takeIf { geoPoint.hasAltitude() }
                override fun latitude(): Double = geoPoint.latitude
                override fun longitude(): Double = geoPoint.longitude
            }
        }
        ?.let { points ->
            ClimbAnalysis().getClimbSegments(points, ClimbGuidanceSettings())
                .let { climbSegments ->
                    RouteVerticalDeltaCalc.recalculateVerticalData(
                        segments = climbSegments,
                        points = workoutData.routePoints
                            .map { routePoint ->
                                com.stt.android.domain.Point(
                                    longitude = routePoint.longitude,
                                    latitude = routePoint.latitude,
                                    altitude = routePoint.altitude.takeIf { routePoint.hasAltitude() },
                                )
                            },
                    )
                }
        }

    private fun ClimbSegment.getDurationInSeconds(): Int = workoutData?.routePoints
        ?.takeUnless(List<*>::isEmpty)
        ?.let { routePoints ->
            val stopTimeInMillis = routePoints.getOrNull(indexAtEnd)?.millisecondsInWorkout ?: return@let 0
            val startTimeInMillis = routePoints.getOrNull(indexAtStart)?.millisecondsInWorkout ?: return@let 0
            (stopTimeInMillis - startTimeInMillis) / 1000
        }
        ?: 0

    private fun getSwimStrokeCount(): Int? = sml?.streamData
        ?.events
        ?.count { event ->
            (event as? SwimmingEvent)?.type == "Stroke"
        }

    private fun isCadenceProcessingRequiredForSuuntoRun() =
        isCadenceProcessingRequiredForSuuntoRun(workoutHeader, summaryExtension)

    private fun getStepCadence(): Double? {
        // For multi sport, we prefer the step cadence from activity window. Otherwise, we calculate
        // from step count and duration.
        val isMultiSport = workoutHeader?.activityType == ActivityType.MULTISPORT
        if (isMultiSport) {
            activityWindow?.cadence
                ?.avg?.times(if (isCadenceProcessingRequiredForSuuntoRun()) 1 else 2)
                ?.takeIf { it > 0.0 }
                ?.let { return it }
        }

        val avgCadence =
            activityWindow?.cadence?.avg ?: summaryExtension?.avgCadence?.toDouble()
        if (avgCadence != null) {
            return if (isCadenceProcessingRequiredForSuuntoRun()) avgCadence else avgCadence * 2
        }

        val stepCountInHz = workoutHeader?.stepCount
            ?.takeIf { it > 0 }
            ?.let { unitConverter.convert(it.toDouble(), Unit.SPM, Unit.HZ) }
            ?: return null
        val durationInMinutes = getDurationInSeconds()
            ?.takeIf { it > 0.0 }
            ?.let { it / 60.0 }
            ?: return null
        return stepCountInHz / durationInMinutes
    }

    private fun getMaxStepCadence(): Double? {
        val maxStepCadence = activityWindow?.cadence?.max
            ?: summaryExtension?.maxCadence?.toDouble()
            ?: return null
        val avgStepCadence = getStepCadence() ?: return null
        // There have been cases where max step cadence is reported in rpm --> hz, and cases where
        // it is reported in spm --> hz. So we need to do the sanity check here.
        return if (maxStepCadence > avgStepCadence) {
            maxStepCadence
        } else {
            maxStepCadence * 2.0
        }
    }

    // Always try to calculate the value based on the cadence received from the watch side, even though the user can edit both distance and step count.
    private fun getStrideLength(): Double? {
        val cadence = activityWindow?.cadence?.avg ?: summaryExtension?.avgCadence?.toDouble()
        val duration = getDurationInSeconds()?.takeIf { it > 0.0 }
        val distance = getTotalDistance()

        if (cadence != null && cadence > 0 && duration != null) {
            return calculateDistancePerStep(
                cadence = cadence,
                duration = duration,
                distance = distance,
                scaleFactor = if (isCadenceProcessingRequiredForSuuntoRun()) 2.0 else 1.0
            )
        }

        return workoutHeader?.stepCount
            ?.takeIf { it > 0 }
            ?.let { stepCount ->
                (getTotalDistance() ?: 0.0) / (stepCount / 2)
            }
    }

    private fun getStepLength(): Double? {
        val cadence = activityWindow?.cadence?.avg ?: summaryExtension?.avgCadence?.toDouble()
        val duration = getDurationInSeconds()?.takeIf { it > 0.0 }
        val distance = getTotalDistance()

        if (cadence != null && cadence > 0 && duration != null) {
            return calculateDistancePerStep(
                cadence = cadence,
                duration = duration,
                distance = distance,
                scaleFactor = if (isCadenceProcessingRequiredForSuuntoRun()) 1.0 else 0.5
            )
        }

        return workoutHeader?.stepCount
            ?.takeIf { it > 0 }
            ?.let { stepCount ->
                (getTotalDistance() ?: 0.0) / stepCount
            }
    }

    private fun calculateDistancePerStep(
        cadence: Double,
        duration: Double,
        distance: Double?,
        scaleFactor: Double
    ): Double {
        val denominator = cadence * duration
        if (denominator <= 0) return 0.0
        return (distance ?: 0.0) / denominator * scaleFactor
    }

    private fun getTotalDistance(): Double? = workoutHeader?.totalDistance
        ?: activityWindow?.distance

    // The "total time" in workout header is actually the "duration".
    private fun getDurationInSeconds(): Double? = workoutHeader?.totalTime
        ?: activityWindow?.duration
        ?: sml?.finalPauseOrStopEvent
            ?.duration
            ?.toDouble()
            ?.let { it / 1000.0 }

    private fun getTotalTimeInSeconds(): Double? = sml?.finalPauseOrStopEvent
        ?.elapsed
        ?.toDouble()
        ?.let { it / 1000.0 }
        ?: workoutHeader?.totalTime

    private fun getPauseTimeInSeconds(): Double? = sml?.finalPauseOrStopEvent
        ?.let { event ->
            ((event.elapsed ?: 0L) - (event.duration ?: 0L)) / 1000.0
        }

    private fun getMovingTimeInSeconds(): Double? {
        if (workoutHeader?.activityType == ActivityType.SWIMMING) {
            val duration = sml?.calculateMovingTimeInSecondsForSwimming()
            if (duration != null) {
                return duration
            }
        }
        return sml?.calculateMovingTimeInSeconds()
    }

    private fun getRestTimeInSeconds(): Double? {
        val durationInSeconds = getDurationInSeconds() ?: return null
        val movingTimeInSeconds = getMovingTimeInSeconds() ?: return null
        // duration will equal reset time when moving time is 0, so do this judgment
        if (movingTimeInSeconds == 0.0) return null
        return durationInSeconds - movingTimeInSeconds
    }

    private fun getAvgSpeed(): Double? {
        val totalDistance = getTotalDistance()
        val durationInSeconds = getDurationInSeconds()
        return if (totalDistance != null && totalDistance > 0.0 &&
            durationInSeconds != null && durationInSeconds > 0.0) {
            totalDistance / durationInSeconds
        } else {
            workoutHeader?.avgSpeed ?: activityWindow?.speed?.avg
        }
    }

    private fun getMaxSpeed(): Double? = workoutHeader?.maxSpeed ?: activityWindow?.speed?.max

    private fun getMovingSpeed(): Double? {
        val totalDistance = getTotalDistance() ?: return null
        val durationInSeconds = getDurationInSeconds() ?: return null
        val movingTimeInSeconds = sml?.calculateMovingTimeInSeconds() ?: return null
        if (durationInSeconds - movingTimeInSeconds < 30.0) {
            return null
        }
        return totalDistance / movingTimeInSeconds
    }

    private fun getPeakSpeedInXMillis(windowInMillis: Long): Double? =
        sml?.streamData?.speed?.calculatePeakValueInXMillis(windowInMillis)

    private fun getPeakVerticalSpeedInXMillis(windowInMillis: Long): Double? =
        sml?.calculatePeakVerticalSpeedInXMillis(windowInMillis)

    private fun getPeakPowerInXMillis(windowInMillis: Long): Double? =
        sml?.streamData?.power?.calculatePeakValueInXMillis(windowInMillis)

    private fun getNp() = workoutHeader?.tss?.normalizedPower
        ?: workoutHeader?.tssList?.firstNotNullOfOrNull { it.normalizedPower }

    private fun getNgp() = workoutHeader?.tss?.averageGradeAdjustedPace
        ?: workoutHeader?.tssList?.firstNotNullOfOrNull { it.averageGradeAdjustedPace }

    private fun hideAllAscentValues(): Boolean {
        val totalAscent = activityWindow?.ascent ?: workoutHeader?.totalAscent
        val totalDescent = activityWindow?.descent ?: workoutHeader?.totalDescent
        val ascentTime = activityWindow?.ascentTime ?: summaryExtension?.ascentTime?.toDouble() ?: 0.0
        val descentTime = activityWindow?.descentTime ?: summaryExtension?.descentTime?.toDouble() ?: 0.0
        val highAltitude = activityWindow?.altitudeRange?.max ?: workoutHeader?.maxAltitude
        val lowAltitude = activityWindow?.altitudeRange?.min ?: workoutHeader?.minAltitude
        val ascentSpeed = summaryExtension?.avgAscentSpeed
        val descentSpeed = summaryExtension?.avgDescentSpeed
        val maxAscentSpeed = summaryExtension?.maxAscentSpeed
        val maxDescentSpeed = summaryExtension?.maxDescentSpeed
        return listOf(totalAscent, totalDescent, ascentTime, descentTime, highAltitude, lowAltitude, ascentSpeed, descentSpeed, maxAscentSpeed, maxDescentSpeed)
            .all { it == 0.0 }
    }

    private fun shouldDisplayOxygenToxicityInfo(
        diveExtension: DiveExtension?,
        workoutHeader: WorkoutHeader?
    ): Boolean {
        return if (diveExtension?.diveMode != null) {
            diveExtension.hasOxygenToxicityValues()
        } else {
            // There is no diveModes in Seal, show always for Scubadiving
            workoutHeader?.activityType == ActivityType.SCUBADIVING
        }
    }

    private fun buildNgDiveGasWorkoutValues(item: SummaryItem): List<WorkoutValue> {
        val gasesUsed = ngDiveData?.gasesUsed ?: return emptyList()

        return buildList {
            for ((index, gasName) in gasesUsed.withIndex()) {
                // Example: Gas type - Nx32
                addIfValid(formatValue(item, gasName))
                val key = index.toString()
                ngDiveData.gasQuantities?.get(key)?.let { gasData ->
                    // Example: Start pressure, Nx32 - 255 bar
                    gasData.startPressure?.let { startPressure ->
                        val startPressureWorkoutValue = formatValueIf(
                            SummaryItem.DIVEGASPRESSURE,
                            startPressure
                        ) { it > 0 }
                        addIfValid(
                            startPressureWorkoutValue?.copy(label = "${startPressureWorkoutValue.label}, $gasName")
                        )
                    }
                    // Example: End pressure, Nx32 - 38 bar
                    gasData.endPressure?.let { endPressure ->
                        val endPressureWorkoutValue = formatValueIf(
                            SummaryItem.DIVEGASENDPRESSURE,
                            endPressure
                        ) { it > 0 }
                        addIfValid(
                            endPressureWorkoutValue?.copy(label = "${endPressureWorkoutValue.label}, $gasName")
                        )
                    }
                    // Example: Used pressure, Nx32 - 217 bar
                    gasData.usedPressure?.let { usedPressure ->
                        val usedPressureWorkoutValue = formatValueIf(
                            SummaryItem.DIVEGASUSEDPRESSURE,
                            usedPressure
                        ) { it > 0 }
                        addIfValid(
                            usedPressureWorkoutValue?.copy(label = "${usedPressureWorkoutValue.label}, $gasName")
                        )
                    }
                    // Example: Gas consumption, Nx32 - 14.2 l/min
                    gasData.avgVentilation?.let { avgVentilation ->
                        val avgVentilationWorkoutValue = formatValueIf(
                            SummaryItem.GASCONSUMPTION,
                            avgVentilation
                        ) { it > 0 }
                        addIfValid(
                            avgVentilationWorkoutValue?.copy(
                                label = "${appResources.getString(CR.string.all_gas_consumption)}, $gasName"
                            )
                        )
                    }
                }
            }
        }
    }

    private fun buildLegacyDiveGasWorkoutValues(item: SummaryItem) : List<WorkoutValue> {
        val gasesUsed = diveExtension?.gasesUsed ?: return emptyList()

        return buildList {
            for ((index, gasName) in gasesUsed.withIndex()) {
                addIfValid(formatValue(item, gasName))
                val key = (index + 1).toString()
                diveExtension.gasQuantities?.get(key)?.let { startingPressure ->
                    addIfValid(
                        formatValueIf(
                            SummaryItem.DIVEGASPRESSURE,
                            startingPressure
                        ) { it > 0 }
                    )
                }
            }
        }
    }

    private companion object {
        val FILTERED_SUUNTO_PLUS_TSS_ZAPPS = setOf(
            "zztrpp01.tss",
            "zztrph01.hrtss",
            "zztrpr01.rtss",
            "zztrpr01.ngp"
        )

        val ClimbSegment.isClimbOrUphill: Boolean get() =
            climbSegmentType == ClimbSegmentType.CLIMB || climbSegmentType == ClimbSegmentType.UPHILL

        fun StravaClimbCategory.toSummaryItem(): SummaryItem = when (this) {
            StravaClimbCategory.CATEGORY_1 -> SummaryItem.CLIMBSCATEGORY1
            StravaClimbCategory.CATEGORY_2 -> SummaryItem.CLIMBSCATEGORY2
            StravaClimbCategory.CATEGORY_3 -> SummaryItem.CLIMBSCATEGORY3
            StravaClimbCategory.CATEGORY_4 -> SummaryItem.CLIMBSCATEGORY4
            StravaClimbCategory.HORS_CATEGORIE -> SummaryItem.CLIMBSCATEGORYHC
        }

        private fun StravaClimbCategory.toDistanceSummaryItem() = when (this) {
            StravaClimbCategory.CATEGORY_1 -> SummaryItem.CLIMBDISTANCECATEGORY1
            StravaClimbCategory.CATEGORY_2 -> SummaryItem.CLIMBDISTANCECATEGORY2
            StravaClimbCategory.CATEGORY_3 -> SummaryItem.CLIMBDISTANCECATEGORY3
            StravaClimbCategory.CATEGORY_4 -> SummaryItem.CLIMBDISTANCECATEGORY4
            StravaClimbCategory.HORS_CATEGORIE -> SummaryItem.CLIMBDISTANCECATEGORYHC
        }

        private fun StravaClimbCategory.toDurationSummaryItem() = when (this) {
            StravaClimbCategory.CATEGORY_1 -> SummaryItem.CLIMBDURATIONCATEGORY1
            StravaClimbCategory.CATEGORY_2 -> SummaryItem.CLIMBDURATIONCATEGORY2
            StravaClimbCategory.CATEGORY_3 -> SummaryItem.CLIMBDURATIONCATEGORY3
            StravaClimbCategory.CATEGORY_4 -> SummaryItem.CLIMBDURATIONCATEGORY4
            StravaClimbCategory.HORS_CATEGORIE -> SummaryItem.CLIMBDURATIONCATEGORYHC
        }

        private fun StravaClimbCategory.toAscentSummaryItem() = when (this) {
            StravaClimbCategory.CATEGORY_1 -> SummaryItem.CLIMBASCENTCATEGORY1
            StravaClimbCategory.CATEGORY_2 -> SummaryItem.CLIMBASCENTCATEGORY2
            StravaClimbCategory.CATEGORY_3 -> SummaryItem.CLIMBASCENTCATEGORY3
            StravaClimbCategory.CATEGORY_4 -> SummaryItem.CLIMBASCENTCATEGORY4
            StravaClimbCategory.HORS_CATEGORIE -> SummaryItem.CLIMBASCENTCATEGORYHC
        }
    }
}
