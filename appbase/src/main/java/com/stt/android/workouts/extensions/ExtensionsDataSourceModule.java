package com.stt.android.workouts.extensions;

import com.stt.android.controllers.DiveExtensionDataModel;
import com.stt.android.controllers.ExtensionDataModel;
import com.stt.android.controllers.FitnessExtensionDataModel;
import com.stt.android.controllers.IntensityExtensionDataModel;
import com.stt.android.controllers.JumpRopeExtensionDataModel;
import com.stt.android.controllers.SlopeSkiDataModel;
import com.stt.android.controllers.SummaryExtensionDataModel;
import com.stt.android.controllers.SwimmingExtensionDataModel;
import com.stt.android.controllers.WeatherExtensionDataModel;
import com.stt.android.data.ExtensionKey;
import com.stt.android.domain.user.workoutextension.FitnessExtension;
import com.stt.android.domain.user.workoutextension.IntensityExtension;
import com.stt.android.domain.user.workoutextension.SlopeSkiSummary;
import com.stt.android.domain.workouts.extensions.JumpRopeExtension;
import com.stt.android.domain.workouts.extensions.SummaryExtension;
import com.stt.android.domain.workouts.extensions.DiveExtension;
import com.stt.android.domain.workouts.extensions.ExtensionsDataSource;
import com.stt.android.domain.workouts.extensions.SwimmingExtension;
import com.stt.android.domain.workouts.extensions.WeatherExtension;
import com.stt.android.domain.workouts.extensions.WorkoutExtension;
import dagger.Binds;
import dagger.Module;
import dagger.multibindings.IntoMap;

@Module
public abstract class ExtensionsDataSourceModule {
    @Binds
    abstract ExtensionsDataSource bindExtensionsDataSource(ExtensionsRepository builder);

    @Binds
    @IntoMap
    @ExtensionKey(SummaryExtension.class)
    abstract ExtensionDataModel<? extends WorkoutExtension> bindSummaryExtensionDataModel(
        SummaryExtensionDataModel summaryExtensionDataModel);

    @Binds
    @IntoMap
    @ExtensionKey(FitnessExtension.class)
    abstract ExtensionDataModel<? extends WorkoutExtension> bindFitnessExtensionDataModel(
        FitnessExtensionDataModel fitnessExtensionDataModel);

    @Binds
    @IntoMap
    @ExtensionKey(SlopeSkiSummary.class)
    abstract ExtensionDataModel<? extends WorkoutExtension> bindSlopeSkiDataModel(
        SlopeSkiDataModel slopeSkiDataModel);

    @Binds
    @IntoMap
    @ExtensionKey(SwimmingExtension.class)
    abstract ExtensionDataModel<? extends WorkoutExtension> bindSwimmingExtensionDataModel(
        SwimmingExtensionDataModel swimmingExtensionDataModel);

    @Binds
    @IntoMap
    @ExtensionKey(DiveExtension.class)
    abstract ExtensionDataModel<? extends WorkoutExtension> bindDiveExtensionDataModel(
        DiveExtensionDataModel diveExtensionDataModel);

    @Binds
    @IntoMap
    @ExtensionKey(IntensityExtension.class)
    abstract ExtensionDataModel<? extends WorkoutExtension> bindIntensityExtensionDataModel(
        IntensityExtensionDataModel intensityExtensionDataModel);

    @Binds
    @IntoMap
    @ExtensionKey(WeatherExtension.class)
    abstract ExtensionDataModel<? extends WorkoutExtension> bindWeatherExtensionDataModel(
        WeatherExtensionDataModel weatherExtensionDataModel);

    @Binds
    @IntoMap
    @ExtensionKey(JumpRopeExtension.class)
    abstract ExtensionDataModel<? extends WorkoutExtension> bindJumpRopeExtensionDataModel(
        JumpRopeExtensionDataModel jumpRopeExtensionDataModel);
}
