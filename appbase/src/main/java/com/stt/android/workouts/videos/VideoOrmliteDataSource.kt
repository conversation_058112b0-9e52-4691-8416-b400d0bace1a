package com.stt.android.workouts.videos

import android.content.Intent
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.stt.android.common.coroutines.ORMLITE
import com.stt.android.controllers.VideoModel
import com.stt.android.domain.user.VideoInformation
import com.stt.android.domain.workouts.videos.Video
import com.stt.android.domain.workouts.videos.VideoDataSource
import com.stt.android.utils.STTConstants
import kotlinx.coroutines.withContext
import javax.inject.Inject

class VideoOrmliteDataSource
@Inject constructor(
    private val videoModel: VideoModel,
    private val localBroadcastManager: LocalBroadcastManager,
) : VideoDataSource {
    override suspend fun findByWorkoutId(workoutId: Int): List<Video> = withContext(ORMLITE) {
        videoModel.findByWorkoutId(workoutId)
            .map { it.toVideo() }
    }

    override suspend fun findUnsyncedVideos(username: String): List<Video> = withContext(ORMLITE) {
        videoModel.findUnsyncedVideos(username).map { it.toVideo() }
    }

    override suspend fun saveVideo(video: Video) = withContext(ORMLITE) {
        videoModel.storeMetaData(VideoInformation.fromVideo(video)).also {
            Intent(STTConstants.BroadcastActions.PICTURE_OR_VIDEO_STORED).apply {
                putExtra(STTConstants.ExtraKeys.WORKOUT_ID, video.workoutId)
                localBroadcastManager.sendBroadcast(this)
            }
        }
    }

    override suspend fun deleteVideo(video: Video) = withContext(ORMLITE) {
        videoModel.delete(VideoInformation.fromVideo(video))
    }
}
