package com.stt.android.workouts.hardware.steps;

import java.util.concurrent.TimeUnit;

public class StepRateCalculator {
    private static final int BUFFER_SIZE = 60;
    private static final long STEP_RATE_TIME_LIMIT = TimeUnit.SECONDS.toMillis(30L);
    private final StepCount[] stepCounts = new StepCount[BUFFER_SIZE];
    private int oldest = -1;
    private int latest = -1;
    public StepRateCalculator() {
        for (int i = 0; i < stepCounts.length; ++i) {
            stepCounts[i] = new StepCount();
        }
    }

    public void addStepCount(int steps, long timestamp) {
        if (++latest >= stepCounts.length) {
            latest = 0;
        }
        if (latest == oldest) {
            if (++oldest >= stepCounts.length) {
                oldest = 0;
            }
        } else if (oldest == -1) {
            oldest = 0;
        }

        StepCount stepCount = stepCounts[latest];
        stepCount.steps = steps;
        stepCount.timestamp = timestamp;
    }

    /**
     * @return step rate in minutes, for the given timestamp
     */
    public int calculateStepRate(long timestamp) {
        // assume we have four step counted, marked as (t1, s1), (t2, s2), (t3, s3), (t4, s4), and
        // (t1, s1) is too old (we just don't care), then we have:
        // the duration is: timestamp - t2
        // the total steps is: s3 + s4
        int accumulatedSteps = 0;
        long duration = 0L;
        int lastIndex = -1;
        int end = latest < oldest ? oldest - stepCounts.length : oldest;
        for (int i = latest; i >= end; --i) {
            StepCount stepCount = stepCounts[(i + stepCounts.length) % stepCounts.length];
            if (timestamp - stepCount.timestamp > STEP_RATE_TIME_LIMIT) {
                // steps too old, we don't care about them
                break;
            }
            accumulatedSteps += stepCount.steps;
            duration = timestamp - stepCount.timestamp;
            lastIndex = i;
        }
        if (lastIndex >= 0) {
            // get rid of the last step (i.e. s2 in the above sample)
            accumulatedSteps -= stepCounts[lastIndex].steps;
        }
        return accumulatedSteps == 0 || duration == 0L ? 0
            : Math.round(accumulatedSteps * 60 * 1000 / duration);
    }

    private static class StepCount {
        private int steps;
        private long timestamp;
    }
}
