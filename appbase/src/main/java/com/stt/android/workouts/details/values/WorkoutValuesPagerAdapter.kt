package com.stt.android.workouts.details.values

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.annotation.ColorInt
import androidx.collection.SparseArrayCompat
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager.widget.PagerAdapter
import java.util.Collections

class WorkoutValuesPagerAdapter(
    @ColorInt private val separatorColor: Int,
    @ColorInt private val textColor: Int,
    private val itemsPerRow: Int,
    private val itemViewClickable: Boolean = true,
    private val onValueSelectedListener: (value: WorkoutValue) -> Unit
) : PagerAdapter() {

    private val maxNrOfValuesForSinglePage: Int
        get() = 4 * itemsPerRow

    private val maxNrOfValuesForMultiplePages: Int
        get() = 3 * itemsPerRow

    private var workoutValues: MutableList<WorkoutValue>? = null
    private val views = SparseArrayCompat<RecyclerView>()

    override fun getCount(): Int {
        /*
         * If number of values is more than {@link #maxNrOfValuesForSinglePage}
         * we show multiple pages, in each there are {@link #maxNrOfValuesForMultiplePages}
         * values (including empty values).
         */
        val count = workoutValues?.size ?: 0
        return if (count <= maxNrOfValuesForSinglePage) {
            1
        } else {
            Math.ceil((count.toDouble() / maxNrOfValuesForMultiplePages)).toInt()
        }
    }

    override fun getItemPosition(`object`: Any): Int {
        // Return POSITION_NONE to force view pager to recreate views
        // when notifyDataSetChanged is called.
        return POSITION_NONE
    }

    override fun instantiateItem(container: ViewGroup, position: Int): Any {
        val recyclerView = createRecyclerView(container.context)
        val adapter = recyclerView.adapter
        if (workoutValues != null && adapter is WorkoutValueAdapter) {
            updateAdapterValues(position, adapter)
        }

        views.put(position, recyclerView)

        val params = ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        container.addView(recyclerView, params)

        return recyclerView
    }

    override fun isViewFromObject(view: View, `object`: Any): Boolean {
        return view === `object`
    }

    override fun destroyItem(container: ViewGroup, position: Int, `object`: Any) {
        views.remove(position)
        container.removeView(`object` as View)
    }

    /**
     * Sets workout values. If number of values is more than
     * [WorkoutValuesPagerAdapter.maxNrOfValuesForSinglePage]
     * we show multiple pages and add empty values to fill the grid so that each page has
     * [WorkoutValuesPagerAdapter.maxNrOfValuesForMultiplePages] values (including
     * empty values). If there is only one page, then we apply padding to rows by adding empty
     * values. However, in one page case we don't show empty rows.
     *
     * @param workoutValues the workout values
     */
    fun setWorkoutValuesAndPadIfNeeded(workoutValues: MutableList<WorkoutValue>) {
        // Calculate how many values are needed to fill the grid
        val countValuesToAdd = when {
            workoutValues.size <= maxNrOfValuesForSinglePage -> {
                // There is only one page
                val count = maxNrOfValuesForSinglePage - workoutValues.size
                // We don't show empty rows if there is only one page
                count % itemsPerRow
            }
            workoutValues.size % maxNrOfValuesForMultiplePages != 0 -> {
                // We show empty rows if there are multiple pages page
                maxNrOfValuesForMultiplePages - workoutValues.size % maxNrOfValuesForMultiplePages
            }
            else -> 0
        }

        workoutValues.addAll(Collections.nCopies(countValuesToAdd, WorkoutValue.createEmpty()))
        this.workoutValues = workoutValues
        notifyDataSetChanged()
    }

    private fun updateAdapterValues(position: Int, adapter: WorkoutValueAdapter) {
        workoutValues?.let { values ->
            val pageValues = if (values.size <= maxNrOfValuesForSinglePage) {
                values
            } else {
                values.subList(
                    position * maxNrOfValuesForMultiplePages,
                    (position + 1) * maxNrOfValuesForMultiplePages
                )
            }
            adapter.resetValues(pageValues)
        }
    }

    private fun createRecyclerView(context: Context): RecyclerView {
        return RecyclerView(context).apply {
            adapter = WorkoutValueAdapter(
                maxNrOfValuesForMultiplePages,
                itemsPerRow,
                separatorColor,
                textColor,
                onValueSelectedListener,
                itemViewClickable
            )
            // TODO: 17.11.2021 Consider using FlexboxLayoutManager to lay out value items based on
            // their own width instead of specifying spanSize externally
            layoutManager = GridLayoutManager(context, itemsPerRow, RecyclerView.VERTICAL, false)
            isNestedScrollingEnabled = false
            overScrollMode = View.OVER_SCROLL_NEVER
            disableChangeAnimations(this)
        }
    }

    private fun disableChangeAnimations(recyclerView: RecyclerView) {
        // Disable change animations.
        val itemAnimator = recyclerView.itemAnimator
        if (itemAnimator is DefaultItemAnimator) {
            itemAnimator.supportsChangeAnimations = false
        }
    }
}
