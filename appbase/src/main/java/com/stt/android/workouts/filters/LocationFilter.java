package com.stt.android.workouts.filters;

import android.location.Location;
import android.location.LocationManager;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class LocationFilter {
    private static final float MAXIMUM_ACCURACY_IN_METERS = 200.0F;
    private static final int MINIMUM_OBSERVATIONS_TO_DROP = 3;

    private int droppedObservations;

    @Inject
    public LocationFilter() {
        reset();
    }

    public void reset() {
        droppedObservations = 0;
    }

    public boolean filter(Location location) {
        if (!LocationManager.GPS_PROVIDER.equals(location.getProvider())) {
            // we only allow GPS
            return true;
        }

        if (location.getAccuracy() > MAXIMUM_ACCURACY_IN_METERS) {
            // too inaccurate
            return true;
        }

        if (location.getLatitude() == 0.0 && location.getLongitude() == 0.0) {
            // nobody is supposed to go there
            return true;
        }

        if (droppedObservations < MINIMUM_OBSERVATIONS_TO_DROP) {
            // too few observations
            ++droppedObservations;
            return true;
        }

        return false;
    }
}
