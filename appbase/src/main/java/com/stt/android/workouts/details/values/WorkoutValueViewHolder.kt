package com.stt.android.workouts.details.values

import android.text.SpannableString
import android.text.Spanned
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.text.style.TextAppearanceSpan
import android.util.TypedValue
import android.view.View
import android.view.View.GONE
import android.view.View.VISIBLE
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.res.ResourcesCompat
import androidx.recyclerview.widget.RecyclerView
import com.stt.android.FontRefs
import com.stt.android.R
import com.stt.android.databinding.ItemWorkoutValueBinding
import com.stt.android.utils.CustomFontStyleSpan
import kotlinx.android.extensions.LayoutContainer
import com.stt.android.core.R as CR

class WorkoutValueViewHolder constructor(
    private val adapter: RecyclerView.Adapter<WorkoutValueViewHolder>,
    private val onValueSelectedListener: (value: WorkoutValue) -> Unit = {},
    private val itemsPerRow: Int,
    private val binding: ItemWorkoutValueBinding,
    override val containerView: View = binding.root,
    // Controls whether workoutValue is clickable, when is longScreenshot, value is false
    private val itemViewClickable: Boolean = true
) : RecyclerView.ViewHolder(containerView), LayoutContainer {

    private val unitTextSpans = listOfNotNull<Any>(
        TextAppearanceSpan(containerView.context, R.style.WorkoutDetailsGridUnit),
        ResourcesCompat.getFont(containerView.context, FontRefs.WORKOUT_UNIT_FONT_REF)?.let {
            CustomFontStyleSpan(it)
        }
    )

    fun bind(workoutValue: WorkoutValue) = with(binding) {
        val resources = containerView.context.resources
        image.visibility = GONE
        setupDividers()
        if (workoutValue.value != null) {
            val textSize = resources.getDimensionPixelSize(workoutValue.textSizeResId)
            val valueTextSpans = listOfNotNull(
                TextAppearanceSpan(containerView.context, R.style.Body_Medium_Black),
                ResourcesCompat.getFont(containerView.context, FontRefs.WORKOUT_VALUE_FONT_REF)?.let {
                    CustomFontStyleSpan(it)
                },
                AbsoluteSizeSpan(textSize, false)
            )

            val unitLabel = workoutValue.getUnitLabel(containerView.context)
            val text = String.format("%s %s", workoutValue.value, unitLabel).trim()

            val spannable = SpannableString(text)
            valueTextSpans.forEach {
                spannable.setSpan(it, 0, workoutValue.value.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }

            if (unitLabel.isNotBlank()) {
                unitTextSpans.forEach {
                    spannable.setSpan(it, workoutValue.value.length, text.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                }
            }

            spannable.setSpan(
                ForegroundColorSpan(valueAndUnit.textColors.defaultColor),
                0,
                spannable.length,
                Spanned.SPAN_INCLUSIVE_INCLUSIVE
            )

            valueAndUnit.text = spannable
        } else {
            valueAndUnit.text = ""
            val drawableResId = workoutValue.drawableResId
            if (drawableResId != null) {
                // There is image instead of numerical string
                image.setImageResource(drawableResId)
                image.visibility = VISIBLE
                image.contentDescription = drawableResId.toString()
            }
        }
        label.text = workoutValue.label
        if (workoutValue.hasDescription) {
            // longScreenshot can't click and don't show rightCorner
            itemView.isClickable = itemViewClickable
            triangleTopRightCorner.visibility = if (itemViewClickable) VISIBLE else GONE
            if (itemViewClickable) {
                itemView.setOnClickListener {
                    onValueSelectedListener(workoutValue)
                }
            }
            with(TypedValue()) {
                itemView.context.theme.resolveAttribute(android.R.attr.selectableItemBackground, this, true)
                itemView.setBackgroundResource(resourceId)
            }
        } else {
            itemView.isClickable = false
            itemView.setOnClickListener(null)
            itemView.background = null
            triangleTopRightCorner.visibility = GONE
        }
    }

    private fun setupDividers() = with(binding) {
        vVerticalSeparator.visibility = if ((adapterPosition + 1) % itemsPerRow == 0) GONE else VISIBLE
        val lastRowIndex = (adapter.itemCount - 1) / itemsPerRow
        vHorizontalSeparator.visibility = if (adapterPosition / itemsPerRow == lastRowIndex) GONE else VISIBLE
        val padding = itemView.context.resources.getDimensionPixelSize(CR.dimen.padding)
        when (adapterPosition % itemsPerRow) {
            0 -> {
                with(ConstraintSet()) {
                    clone(constraintLayout)
                    connect(R.id.vHorizontalSeparator, ConstraintSet.START, R.id.label, ConstraintSet.START)
                    applyTo(constraintLayout)
                }
                with(constraintLayout.layoutParams as ViewGroup.MarginLayoutParams) {
                    marginEnd = 0
                    constraintLayout.layoutParams = this
                }
            }
            (itemsPerRow - 1) -> {
                with(ConstraintSet()) {
                    clone(constraintLayout)
                    connect(
                        R.id.vHorizontalSeparator,
                        ConstraintSet.START,
                        ConstraintSet.PARENT_ID,
                        ConstraintSet.START
                    )
                    applyTo(constraintLayout)
                }
                with(constraintLayout.layoutParams as ViewGroup.MarginLayoutParams) {
                    marginEnd = padding
                    constraintLayout.layoutParams = this
                }
            }
            else -> {
                with(ConstraintSet()) {
                    clone(constraintLayout)
                    connect(
                        R.id.vHorizontalSeparator,
                        ConstraintSet.START,
                        ConstraintSet.PARENT_ID,
                        ConstraintSet.START
                    )
                    applyTo(constraintLayout)
                }
                with(constraintLayout.layoutParams as ViewGroup.MarginLayoutParams) {
                    marginEnd = 0
                    constraintLayout.layoutParams = this
                }
            }
        }
    }
}
