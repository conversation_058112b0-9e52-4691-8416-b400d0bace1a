package com.stt.android.workouts.sharepreview

import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.stt.android.databinding.ItemWorkoutGraphPickBinding

class WorkoutGraphPickingViewHolder constructor(
    private val binding: ItemWorkoutGraphPickBinding,
    private val listener: (index: Int) -> Unit
) : RecyclerView.ViewHolder(binding.root) {

    fun bind(position: Int, isSelected: <PERSON><PERSON><PERSON>, graphOption: WorkoutShareGraphOption) {
        binding.workoutGraphText.text = graphOption.name
        itemView.setOnClickListener {
            listener.invoke(position)
        }
        binding.selectedIcon.visibility = if (isSelected) View.VISIBLE else View.GONE
    }
}
