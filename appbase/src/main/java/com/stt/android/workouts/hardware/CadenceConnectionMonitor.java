package com.stt.android.workouts.hardware;

import android.content.Context;
import androidx.annotation.Nullable;
import com.stt.android.bluetooth.BleHelper;
import com.stt.android.bluetooth.CadenceEventListener;
import com.stt.android.cadence.CadenceHelper;
import java.io.Closeable;
import timber.log.Timber;

public abstract class CadenceConnectionMonitor implements Closeable {
    @Nullable
    public static CadenceConnectionMonitor newInstance(Context context,
        CadenceEventListener cadenceEventListener) {
        if (!BleHelper.supportsBle(context)) {
            return null;
        }

        if (!CadenceHelper.hasKnownCadence(context)) {
            return null;
        }

        try {
            return BleCadenceConnectionMonitor.requestUpdates(context.getApplicationContext(),
                cadenceEventListener);
        } catch (IllegalStateException e) {
            Timber.w(e, "Can't connect to cadence/speed sensor");
        }
        return null;
    }

    public abstract boolean isConnected();

    @Override
    public abstract void close();
}
