package com.stt.android.workouts.details;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.MotionEvent;
import com.github.chrisbanes.photoview.PhotoView;

/**
 * A {@link PhotoView} that changes its scale type from {@link ScaleType#CENTER_CROP} to
 * {@link ScaleType#FIT_CENTER} once the height of this view is larger than the image drawable used.
 *
 * <em>Note:</em> for whatever reason the behaviour on PhotoView 2.0 changed and we need to
 * hijack the click listener in order to propagate click events when zoom is disabled. (See
 * {@link #setOnClickListener(OnClickListener)} and {@link #onTouchEvent(MotionEvent)})
 */
public class CenterCropToFitPhotoView extends PhotoView {
    private int imageHeight;
    private OnClickListener clickListener;
    private boolean isPlaceholderImage = false;

    public CenterCropToFitPhotoView(Context context) {
        super(context);
    }

    public CenterCropToFitPhotoView(Context context, AttributeSet attr) {
        super(context, attr);
    }

    public CenterCropToFitPhotoView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    @Override
    public void setImageDrawable(Drawable drawable) {
        super.setImageDrawable(drawable);
        if (drawable != null) {
            imageHeight = drawable.getIntrinsicHeight();
        }
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        int usableHeight = getMeasuredHeight() - getPaddingBottom() - getPaddingTop();
        if (usableHeight < imageHeight && !isPlaceholderImage) {
            setScaleType(ScaleType.CENTER_INSIDE);
        } else {
            setScaleType(ScaleType.FIT_CENTER);
        }
    }

    @Override
    public void setOnClickListener(OnClickListener l) {
        this.clickListener = l;
        super.setOnClickListener(l);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        // If we're in zoom mode we don't handle the event
        if (isZoomable()) {
            return super.onTouchEvent(event);
        }

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                if (clickListener != null) {
                    clickListener.onClick(this);
                }
                return true;
            case MotionEvent.ACTION_UP:
                // If you want to receive ACTION_UP you must return true in ACTION_DOWN
                // http://stackoverflow.com/a/16495363

                // Must be called as per ClickableViewAccessibility lint check
                performClick();
                return true;
        }
        return super.onTouchEvent(event);
    }

    // Must be overridden to suppress ClickableViewAccessibility lint check
    @Override
    public boolean performClick() {
        return super.performClick();
    }

    public void isPlaceholderImage(boolean isPlaceholderImage) {
        this.isPlaceholderImage = isPlaceholderImage;
    }
}
