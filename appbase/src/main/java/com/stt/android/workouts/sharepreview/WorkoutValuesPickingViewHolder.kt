package com.stt.android.workouts.sharepreview

import android.os.Build
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.TextAppearanceSpan
import android.view.View
import androidx.core.content.res.ResourcesCompat
import androidx.recyclerview.widget.RecyclerView
import com.stt.android.FontRefs
import com.stt.android.R
import com.stt.android.databinding.ItemWorkoutValuePickBinding
import com.stt.android.utils.CustomFontStyleSpan
import com.stt.android.workouts.details.values.WorkoutValue

class WorkoutValuesPickingViewHolder(
    private val binding: ItemWorkoutValuePickBinding,
    private val listener: (index: Int) -> Unit
) : RecyclerView.ViewHolder(binding.root) {

    fun bind(position: Int, isSelected: Boolean, workoutValue: WorkoutValue) = with(binding) {
        val context = binding.workoutValueText.context
        val workoutValueStr = with(workoutValue) {
            value.takeUnless { it == null || it == "" }?.let { value ->
                val unitLabel = getUnitLabel(context)
                String.format("%s\n%s %s", label, value, unitLabel)
            } ?: label
        }
        binding.workoutValueText.text = SpannableStringBuilder(workoutValueStr).apply {
            // Set label style
            setSpan(
                TextAppearanceSpan(context, R.style.Body_Large_Bold),
                0,
                workoutValue.label.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )

            // On API level 26 and below, setting TextAppearanceSpan does not affect font family
            if (Build.VERSION.SDK_INT < 27) {
                val userTypeface = ResourcesCompat.getFont(context, FontRefs.WORKOUT_VALUE_FONT_REF)
                if (userTypeface != null) {
                    setSpan(
                        CustomFontStyleSpan(userTypeface),
                        0,
                        workoutValue.label.length,
                        Spannable.SPAN_INCLUSIVE_EXCLUSIVE
                    )
                }
            }

            // Set value and unit style
            setSpan(
                TextAppearanceSpan(context, R.style.Body_Small),
                workoutValue.label.length,
                workoutValueStr.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )

            if (Build.VERSION.SDK_INT < 27) {
                val userTypeface = ResourcesCompat.getFont(context, FontRefs.WORKOUT_UNIT_FONT_REF)
                if (userTypeface != null) {
                    setSpan(
                        CustomFontStyleSpan(userTypeface),
                        workoutValue.label.length,
                        workoutValueStr.length,
                        Spannable.SPAN_INCLUSIVE_EXCLUSIVE
                    )
                }
            }
        }
        binding.root.setOnClickListener {
            listener.invoke(position)
        }
        binding.selectedIcon.visibility = if (isSelected) View.VISIBLE else View.GONE
    }
}
