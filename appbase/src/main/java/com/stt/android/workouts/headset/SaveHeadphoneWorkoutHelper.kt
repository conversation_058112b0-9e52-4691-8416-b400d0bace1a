package com.stt.android.workouts.headset

import android.content.Context
import android.content.Intent
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.work.WorkManager
import com.amersports.formatter.Unit
import com.amersports.formatter.unit.jscience.JScienceUnitConverter
import com.squareup.moshi.Moshi
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutExtensionDataModels
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.coroutines.LoggingExceptionHandler
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.TimeUtils
import com.stt.android.data.workout.binary.BinaryFileRepository
import com.stt.android.domain.advancedlaps.WindowType
import com.stt.android.domain.smlzip.SaveSMLZipReferenceUseCase
import com.stt.android.domain.sync.SyncRequest
import com.stt.android.domain.sync.SyncRequestHandlerWorker
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.JumpRopeExtension
import com.stt.android.domain.workouts.extensions.SwimmingExtension
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.infomodel.ActivityMapping
import com.stt.android.utils.STTConstants
import com.stt.android.utils.WorkoutShareUtils
import com.stt.android.utils.toEpochMilli
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeFormatterBuilder
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream
import javax.inject.Inject
import kotlin.math.roundToInt
import com.stt.android.domain.advancedlaps.SwimStyle as LapSwimmingStyle

/**
 * save headphone workout to local
 */
private fun createParentScope() =
    CoroutineScope(Dispatchers.Main + SupervisorJob() + LoggingExceptionHandler)

class SaveHeadphoneWorkoutHelper @Inject constructor(
    private val workoutHeaderController: WorkoutHeaderController,
    private val workoutExtensionDataModels: WorkoutExtensionDataModels,
    private val currentUserController: CurrentUserController,
    private val saveSMLZipReferenceUseCase: SaveSMLZipReferenceUseCase,
    private val dummyBinaryFile: BinaryFileRepository,
    private val workoutShareUtils: WorkoutShareUtils,
    private val workManager: dagger.Lazy<WorkManager>,
    private val localBroadcastManager: LocalBroadcastManager,
    private val context: Context,
    private val moshi: Moshi,
    coroutinesDispatchers: CoroutinesDispatchers
) : CoroutinesDispatchers by coroutinesDispatchers, CoroutineScope by createParentScope() {

    private val unitConverter = JScienceUnitConverter()

    fun saveSwimmingWorkout(swimmingData: List<SwimmingData>) {
        launch(io) {
            runSuspendCatching {
                var swimmingEndTime = LocalDateTime.now().toEpochMilli()
                swimmingData.reversed().forEach { swimmingItem ->
                    // s
                    val totalDuration =
                        swimmingItem.freestyleDuration + swimmingItem.breaststrokeDuration + swimmingItem.otherDuration
                    // ms
                    val swimmingDuration = totalDuration * 1000
                    val swimmingStartTime = swimmingEndTime - swimmingDuration
                    val poolWindowTimeOffset =
                        swimmingStartTime - (swimmingItem.intervalLaps.firstOrNull()?.startTimeInMillis
                            ?: 0L) + OFFSET_TIME
                    val activityWindows = if (swimmingItem.swimType == SwimmingType.POOL) {
                        listOf(
                            SummarySampleItem(
                                attributes = SummaryAttributes(
                                    suuntoSml = SummarySuuntoSml(
                                        windows = listOf(
                                            getSwimmingWindow(
                                                "Activity",
                                                totalDuration,
                                                swimmingItem
                                            ),
                                            getSwimmingWindow("Move", totalDuration, swimmingItem)
                                        )
                                    ),
                                ),
                                source = "source-${swimmingItem.deviceMac}",
                                timeISO8601 = swimmingStartTime.toTimeISO8601()
                            )
                        )
                    } else emptyList()
                    storeWorkout(
                        workoutHeader = getWorkoutHeader(
                            workoutId = swimmingItem.id.hashCode(),
                            activityType = if (swimmingItem.swimType == SwimmingType.OPEN_WATER) ActivityType.OPENWATER_SWIMMING else ActivityType.SWIMMING,
                            totalDuration = totalDuration,
                            startTime = swimmingStartTime,
                            endTime = swimmingEndTime
                        ).copy(
                            totalDistance = swimmingItem.swimDistanceInMeter?.toDouble() ?: 0.0,
                            energyConsumption = unitConverter.convert(
                                swimmingItem.calorie?.toDouble() ?: 0.0,
                                com.amersports.formatter.Unit.KCAL,
                                Unit.J
                            )
                        ),
                        extension = getSwimmingExtension(
                            workoutId = swimmingItem.id.hashCode(),
                            swimmingData = swimmingItem
                        ),
                        headsetHeader = getSwimmingHeader(
                            swimmingData = swimmingItem,
                            startTime = swimmingStartTime,
                            duration = totalDuration,
                            activityTypeMcId = if (swimmingItem.swimType == SwimmingType.OPEN_WATER) ActivityMapping.OPENWATERSWIMMING.mcId else ActivityMapping.SWIMMING.mcId
                        ),
                        sampleItems = getSwimmingSamples(swimmingItem, poolWindowTimeOffset),
                        windows = activityWindows + swimmingItem.intervalLaps.map {
                            SummarySampleItem(
                                attributes = SummaryAttributes(
                                    suuntoSml = SummarySuuntoSml(
                                        windows = listOf(
                                            HeadsetWindow(
                                                activityTypeId = if (swimmingItem.swimType == SwimmingType.OPEN_WATER) ActivityMapping.OPENWATERSWIMMING.mcId else ActivityMapping.SWIMMING.mcId,
                                                type = if (it.interval == IntervalType.LAP) WindowType.POOLLENGTH.type else WindowType.INTERVAL.type,
                                                swimStyle = when (it.swimStyle) {
                                                    SwimStyle.FREESTYLE -> LapSwimmingStyle.FREE.value
                                                    SwimStyle.BREASTSTROKE -> LapSwimmingStyle.BREAST.value
                                                    else -> LapSwimmingStyle.OTHER.value
                                                },
                                                duration = it.duration.toFloat(),
                                                distance = it.swimDistanceInMeter,
                                                breaststrokeGlideTime = it.breaststrokeGlideTime,
                                                freestyleAvgBreathAngle = it.freestyleAvgBreathAngle,
                                                breaststrokeAvgBreathAngle = it.breaststrokeAvgBreathAngle,
                                                breaststrokeHeadAngle = it.breaststrokeHeadAngle,
                                                freestyleHeadAngle = it.freestyleHeadAngle,
                                                breathingRate = it.breathingRate,
                                                speed = listOf(
                                                    MinMaxValue(it.speed, it.speed, it.speed)
                                                )
                                            )
                                        )
                                    ),
                                ),
                                source = "source-${swimmingItem.deviceMac}",
                                timeISO8601 = (it.startTimeInMillis + poolWindowTimeOffset).toTimeISO8601()
                            )
                        }
                    )
                    swimmingEndTime = swimmingStartTime
                }
            }.fold({
                Timber.d("save headphone workout successfully")
            }, {
                Timber.w(
                    it,
                    "save swimming workout:(${swimmingData}) that is from headset failed: ${it.message}"
                )
            })
        }
    }

    private fun getSwimmingSamples(
        swimmingItem: SwimmingData,
        timeOffset: Long
    ): List<SamplesItem> {
        val laps = swimmingItem.intervalLaps.filter { it.interval == IntervalType.LAP }
        // if only have a lap, don't add it
        return if (laps.size > 1) laps.map {
            listOf(
                SamplesItem(
                    attributes = SampleAttributes(
                        suuntoSml = SampleSuuntoSml(
                            sample = HeadsetSample(
                                distance = it.cumulativeDistanceInMeter.roundToInt(),
                                speed = it.speed,
                                duration = it.duration.toFloat(),
                                breaststrokeGlideTime = it.breaststrokeGlideTime,
                                freestyleAvgBreathAngle = it.freestyleAvgBreathAngle,
                                breaststrokeAvgBreathAngle = it.breaststrokeAvgBreathAngle,
                                breathingRate = it.breathingRate,
                                breaststrokeHeadAngle = it.breaststrokeHeadAngle,
                                freestyleHeadAngle = it.freestyleHeadAngle
                            )
                        )
                    ),
                    source = "source-${swimmingItem.deviceMac}",
                    timeISO8601 = (it.startTimeInMillis + timeOffset).toTimeISO8601()
                ),
                SamplesItem(
                    attributes = SampleAttributes(
                        suuntoSml = SampleSuuntoSml(
                            sample = HeadsetSample(
                                distance = it.cumulativeDistanceInMeter.roundToInt(),
                                speed = it.speed,
                                duration = it.duration.toFloat(),
                                breaststrokeGlideTime = it.breaststrokeGlideTime,
                                freestyleAvgBreathAngle = it.freestyleAvgBreathAngle,
                                breaststrokeAvgBreathAngle = it.breaststrokeAvgBreathAngle,
                                breathingRate = it.breathingRate,
                                breaststrokeHeadAngle = it.breaststrokeHeadAngle,
                                freestyleHeadAngle = it.freestyleHeadAngle
                            )
                        )
                    ),
                    source = "source-${swimmingItem.deviceMac}",
                    timeISO8601 = (it.endTimeInMillis + timeOffset).toTimeISO8601()
                )
            )

        }.flatten() else emptyList()
    }

    private fun getSwimmingWindow(
        type: String,
        totalDuration: Int,
        swimmingItem: SwimmingData
    ): HeadsetWindow {
        return HeadsetWindow(
            activityTypeId = ActivityMapping.SWIMMING.mcId,
            type = type,
            duration = totalDuration.toFloat(),
            distance = swimmingItem.swimDistanceInMeter,
            energy = unitConverter.convert(
                swimmingItem.calorie?.toDouble() ?: 0.0,
                com.amersports.formatter.Unit.KCAL,
                Unit.J
            ),
            speed = listOf(
                MinMaxValue(
                    avg = swimmingItem.speed ?: 0f,
                    max = swimmingItem.speed ?: 0f,
                    min = swimmingItem.speed ?: 0f,
                )
            )
        )
    }

    fun saveJumpRopeWorkout(jumpRopeData: List<JumpRopeData>) {
        launch(io) {
            runSuspendCatching {
                var jumpRopeEndTime = LocalDateTime.now().toEpochMilli()
                jumpRopeData.reversed().forEach { jumpRopeItem ->
                    val jumpRopeStartTime = jumpRopeEndTime - jumpRopeItem.durationInSecond * 1000
                    storeWorkout(
                        workoutHeader = getWorkoutHeader(
                            workoutId = jumpRopeItem.id.hashCode(),
                            activityType = ActivityType.JUMP_ROPE,
                            totalDuration = jumpRopeItem.durationInSecond,
                            startTime = jumpRopeStartTime,
                            endTime = jumpRopeEndTime
                        ).copy(
                            totalDistance = 0.0,
                            energyConsumption = unitConverter.convert(
                                jumpRopeItem.calorie.toDouble(),
                                com.amersports.formatter.Unit.KCAL,
                                Unit.J
                            )
                        ),
                        extension = getJumpRopeExtension(
                            workoutId = jumpRopeItem.id.hashCode(),
                            jumpRopeData = jumpRopeItem
                        ),
                        headsetHeader = getJumpRopeHeader(
                            jumpRopeData = jumpRopeItem,
                            startTime = jumpRopeStartTime,
                            duration = jumpRopeItem.durationInSecond
                        ),
                        sampleItems = getJumpRopeSamples(jumpRopeItem, jumpRopeStartTime),
                        windows = listOf(
                            SummarySampleItem(
                                attributes = SummaryAttributes(
                                    suuntoSml = SummarySuuntoSml(
                                        windows = listOf(
                                            getJumpRopeWindow("Activity", jumpRopeItem),
                                            getJumpRopeWindow("Move", jumpRopeItem)
                                        )
                                    ),
                                ),
                                source = "source-${jumpRopeItem.deviceMac}",
                                timeISO8601 = jumpRopeStartTime.toTimeISO8601()
                            )
                        )
                    )
                    jumpRopeEndTime = jumpRopeStartTime
                }
            }.fold({
                Timber.d("save headphone workout successfully")
            }, {
                Timber.w(
                    it,
                    "save jump rope workout:(${jumpRopeData}) that is from headset failed: ${it.message}"
                )
            })
        }
    }

    private fun getJumpRopeSamples(
        jumpRopeItem: JumpRopeData,
        jumpRopeStartTime: Long
    ): List<SamplesItem> {
        return if (jumpRopeItem.jumpRoundsData.size > 1) {
            val timeOffset =
                jumpRopeStartTime - jumpRopeItem.jumpRoundsData.first().startTime + OFFSET_TIME
            jumpRopeItem.jumpRoundsData.map {
                val items = mutableListOf(
                    SamplesItem(
                        attributes = SampleAttributes(
                            suuntoSml = SampleSuuntoSml(
                                sample = HeadsetSample(
                                    cadence = unitConverter.convert(
                                        it.frequencyPerRound.toDouble(),
                                        Unit.RPM,
                                        Unit.HZ
                                    ).toFloat(),
                                    skipsPerRound = it.skipsPerRound
                                )
                            )
                        ),
                        source = "source-${jumpRopeItem.deviceMac}",
                        timeISO8601 = (timeOffset + (it.endTime?.let { _ -> it.startTime * 1000 }
                            ?: it.durationInMilliseconds)).toTimeISO8601()
                    )
                )
                it.endTime?.let { _ ->
                    items.add(
                        SamplesItem(
                            attributes = SampleAttributes(
                                suuntoSml = SampleSuuntoSml(
                                    sample = HeadsetSample(
                                        cadence = unitConverter.convert(
                                            it.frequencyPerRound.toDouble(),
                                            Unit.RPM,
                                            Unit.HZ
                                        ).toFloat(),
                                        skipsPerRound = it.skipsPerRound
                                    )
                                )
                            ),
                            source = "source-${jumpRopeItem.deviceMac}",
                            timeISO8601 = (timeOffset + it.endTime * 1000).toTimeISO8601()
                        )
                    )
                }
                items
            }.flatten()
        } else emptyList()
    }

    private fun getJumpRopeWindow(type: String, jumpRopeItem: JumpRopeData): HeadsetWindow {
        return HeadsetWindow(
            activityTypeId = ActivityMapping.JUMPROPE.mcId,
            type = type,
            duration = jumpRopeItem.durationInSecond.toFloat(),
            energy = unitConverter.convert(
                jumpRopeItem.calorie.toDouble(),
                com.amersports.formatter.Unit.KCAL,
                Unit.J
            ),
            cadence = listOf(
                MinMaxValue(
                    avg = unitConverter.convert(
                        jumpRopeItem.skipsPerMin.toDouble(),
                        Unit.RPM,
                        Unit.HZ
                    ).toFloat(),
                    max = unitConverter.convert(
                        jumpRopeItem.maxSkipsPerMin.toDouble(),
                        Unit.RPM,
                        Unit.HZ
                    ).toFloat(),
                    min = unitConverter.convert(
                        jumpRopeItem.skipsPerMin.toDouble(),
                        Unit.RPM,
                        Unit.HZ
                    ).toFloat()
                )
            ),
            rounds = jumpRopeItem.rounds,
            avgSkipsPerRound = jumpRopeItem.avgSkipsPerRound,
            maxConsecutiveSkips = jumpRopeItem.maxConsecutiveSkips,
            skipsCount = jumpRopeItem.skipsCount
        )
    }

    private suspend fun storeWorkout(
        workoutHeader: WorkoutHeader,
        extension: WorkoutExtension,
        headsetHeader: HeadsetHeader,
        sampleItems: List<SamplesItem>,
        windows: List<SummarySampleItem>
    ) {
        val existWorkout = workoutHeaderController.find(listOf(workoutHeader.id))
        if (existWorkout.isEmpty()) {
            workoutHeaderController.store(workoutHeader)
            dummyBinaryFile.create(workoutHeader)
            workoutExtensionDataModels.storeExtensions(listOf(extension))
            val smlZip = createSmlZip(
                workoutHeader.id.toString(),
                getSampleJson(
                    startTime = workoutHeader.startTime,
                    endTime = workoutHeader.stopTime,
                    mac = headsetHeader.device.serialNumber,
                    items = sampleItems
                ),
                getSummaryJson(
                    header = headsetHeader, windows = windows
                )
            )
            saveSMLZipReferenceUseCase(
                workoutId = workoutHeader.id,
                logbookEntryId = workoutHeader.id.toLong(),
                zipPath = smlZip.path,
            )
            notifyWorkoutSaved(workoutId = workoutHeader.id.toString())
        }
    }

    private fun notifyWorkoutSaved(workoutId: String) {
        localBroadcastManager.sendBroadcast(
            Intent(STTConstants.BroadcastActions.MANUAL_WORKOUT_SAVED)
                .putExtra(STTConstants.ExtraKeys.WORKOUT_ID, workoutId)
        )
        SyncRequestHandlerWorker.enqueue(workManager.get(), SyncRequest.push())
    }

    private fun getWorkoutHeader(
        workoutId: Int,
        activityType: ActivityType,
        totalDuration: Int,
        startTime: Long,
        endTime: Long
    ): WorkoutHeader {
        return createWorkoutHeader(
            workoutId,
            activityType,
            totalDuration,
            startTime,
            endTime
        )
    }

    private fun getSwimmingExtension(
        workoutId: Int,
        swimmingData: SwimmingData
    ): SwimmingExtension {
        return SwimmingExtension(
            workoutId = workoutId,
            avgSwolf = 0,
            avgStrokeRate = 0.0f,
            breathingRate = swimmingData.breathingRate,
            breaststrokeDuration = swimmingData.breaststrokeDuration,
            breaststrokePercentage = swimmingData.breaststrokePercentage,
            breaststrokeGlideTime = swimmingData.breaststrokeGlideTime,
            breaststrokeMaxBreathAngle = swimmingData.breaststrokeMaxBreathAngle,
            breaststrokeAvgBreathAngle = swimmingData.breaststrokeAvgBreathAngle,
            freestyleDuration = swimmingData.freestyleDuration,
            freestylePercentage = swimmingData.freestylePercentage,
            freestyleMaxBreathAngle = swimmingData.freestyleMaxBreathAngle,
            freestyleAvgBreathAngle = swimmingData.freestyleAvgBreathAngle,
            freestylePitchAngle = swimmingData.freestylePitchAngle,
            breaststrokeHeadAngle = swimmingData.breaststrokeGlideAngle,
        )
    }

    private fun getJumpRopeExtension(
        workoutId: Int,
        jumpRopeData: JumpRopeData
    ): JumpRopeExtension {
        return JumpRopeExtension(
            workoutId = workoutId,
            rounds = jumpRopeData.rounds,
            avgSkipsPerRound = jumpRopeData.avgSkipsPerRound,
            maxConsecutiveSkips = jumpRopeData.maxConsecutiveSkips
        )
    }

    private fun getSampleJson(
        startTime: Long,
        endTime: Long,
        mac: String,
        items: List<SamplesItem>
    ): String {
        val sampleItems = mutableListOf<SamplesItem>().apply {
            add(
                SamplesItem(
                    attributes = SampleAttributes(
                        suuntoSml = SampleSuuntoSml(
                            sample = HeadsetSample(
                                headsetEvents = listOf(
                                    HeadsetEvent(
                                        arrayBegin = 0,
                                        lap = HeadsetLap("Start")
                                    )
                                )
                            )
                        )
                    ),
                    source = "source-$mac",
                    timeISO8601 = startTime.toTimeISO8601()
                )
            )
            addAll(items)
            add(
                SamplesItem(
                    attributes = SampleAttributes(
                        suuntoSml = SampleSuuntoSml(
                            sample = HeadsetSample(
                                headsetEvents = listOf(
                                    HeadsetEvent(
                                        arrayBegin = 0,
                                        lap = HeadsetLap("Stop")
                                    )
                                )
                            )
                        )
                    ),
                    source = "source-$mac",
                    timeISO8601 = endTime.toTimeISO8601()
                )
            )
        }
        val headsetSamples = HeadsetSamples(sampleItems)
        return moshi.adapter(HeadsetSamples::class.java).toJson(headsetSamples)
    }

    private fun Long.toTimeISO8601(): String =
        TimeUtils.epochToLocalZonedDateTime(this).format(ISO_OFFSET_DATE_TIME_FORMAT)

    private fun getSummaryJson(header: HeadsetHeader, windows: List<SummarySampleItem>): String {
        val summaries = mutableListOf(
            SummarySampleItem(
                attributes = SummaryAttributes(
                    suuntoSml = SummarySuuntoSml(
                        header = header
                    )
                ),
                source = "source-${header.device.serialNumber}",
                timeISO8601 = header.dateTime
            )
        ).apply {
            addAll(windows)
        }
        val headsetSummary = HeadsetSummary(summaries)
        return moshi.adapter(HeadsetSummary::class.java).toJson(headsetSummary)
    }

    private fun getSwimmingHeader(
        swimmingData: SwimmingData,
        startTime: Long,
        duration: Int,
        activityTypeMcId: Int,
    ): HeadsetHeader {
        val dateTime = startTime.toTimeISO8601()
        return HeadsetHeader(
            activity = "",
            activityType = activityTypeMcId,
            dateTime = dateTime,
            device = HeadsetDevice(
                info = HeadsetDeviceInfo(
                    batteryDesignCapacity = 0,
                    batteryFullCapacity = 0.0,
                    hW = "",
                    sW = ""
                ),
                name = swimmingData.deviceName,
                serialNumber = swimmingData.deviceMac
            ),
            duration = duration,
            swimmingMetrics = SwimmingMetrics(
                breaststrokeGlideTime = swimmingData.breaststrokeGlideTime,
                ventilationFrequency = swimmingData.breathingRate,
                avgFreestyleBreathAngle = swimmingData.freestyleAvgBreathAngle,
                maxFreestyleBreathAngle = swimmingData.freestyleMaxBreathAngle,
                freestyleGlideAngle = swimmingData.freestylePitchAngle,
                avgBreaststrokeBreathAngle = swimmingData.breaststrokeAvgBreathAngle,
                maxBreaststrokeBreathAngle = swimmingData.breaststrokeMaxBreathAngle,
                freestyleDuration = swimmingData.freestyleDuration,
                breaststrokeDuration = swimmingData.breaststrokeDuration,
                otherStylesDuration = swimmingData.otherDuration,
                freestyleSwimPercentage = swimmingData.freestylePercentage,
                breaststrokePercentage = swimmingData.breaststrokePercentage,
                otherStylesPercentage = swimmingData.otherPercentage,
                breaststrokeHeadAngle = swimmingData.breaststrokeGlideAngle
            ),
            distance = swimmingData.swimDistanceInMeter,
            energy = unitConverter.convert(
                swimmingData.calorie?.toDouble() ?: 0.0,
                com.amersports.formatter.Unit.KCAL,
                Unit.J
            )
        )
    }

    private fun getJumpRopeHeader(
        jumpRopeData: JumpRopeData,
        startTime: Long,
        duration: Int
    ): HeadsetHeader {
        val dateTime = startTime.toTimeISO8601()
        return HeadsetHeader(
            activity = "",
            activityType = ActivityMapping.JUMPROPE.mcId,
            dateTime = dateTime,
            device = HeadsetDevice(
                info = HeadsetDeviceInfo(
                    batteryDesignCapacity = 0,
                    batteryFullCapacity = 0.0,
                    hW = "",
                    sW = ""
                ),
                name = jumpRopeData.deviceName,
                serialNumber = jumpRopeData.deviceMac
            ),
            duration = duration,
            skipsCount = jumpRopeData.skipsCount,
            energy = unitConverter.convert(
                jumpRopeData.calorie.toDouble(),
                com.amersports.formatter.Unit.KCAL,
                Unit.J
            )
        )
    }

    private fun createSmlZip(workoutId: String, sampleJson: String, summaryJson: String): File {
        Timber.d("save headphone workout($workoutId) data-> sample:$sampleJson")
        Timber.d("save headphone workout($workoutId) data-> summary:$summaryJson")
        val destFolder = File(context.getExternalFilesDir(null), "smlzip")
        if (!destFolder.exists()) {
            destFolder.mkdirs()
        }
        val destZip = File(destFolder, "entry_${workoutId}.zip")
        ZipOutputStream(destZip.outputStream().buffered()).use { zos ->
            val sampleZip = ZipEntry(SAMPLE_FILE_NAME)
            zos.putNextEntry(sampleZip)
            zos.write(sampleJson.toByteArray())
            val summaryZip = ZipEntry(SUMMARY_FILE_NAME)
            zos.putNextEntry(summaryZip)
            zos.write(summaryJson.toByteArray())
        }
        return destZip
    }

    private fun createWorkoutHeader(
        id: Int,
        activityType: ActivityType,
        totalTime: Int,
        startTime: Long,
        endTime: Long
    ): WorkoutHeader {
        val sharingFlags = workoutShareUtils.getDefaultWorkoutBackendShareFlag()
        val username = currentUserController.username
        return WorkoutHeader.builder().id(id).key(null)
            .activityId(activityType.id)
            .startTime(startTime, false).stopTime(endTime).totalTime(totalTime.toDouble(), false)
            .userName(username).sharingFlags(sharingFlags).build()
    }

    companion object {
        private const val SAMPLE_FILE_NAME = "samples.json"
        private const val SUMMARY_FILE_NAME = "summary.json"

        // timestamp offset 100ms to adapt to rule from watch
        const val OFFSET_TIME = 100L

        @JvmStatic
        private val ISO_OFFSET_DATE_TIME_FORMAT: DateTimeFormatter = DateTimeFormatterBuilder()
            .parseCaseSensitive()
            .appendPattern("yyyy-MM-dd'T'HH:mm:ss.SSS")
            .appendOffset("+HH:MM", "+00:00")
            .toFormatter()
    }
}
