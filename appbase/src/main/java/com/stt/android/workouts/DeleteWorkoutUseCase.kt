package com.stt.android.workouts

import android.text.TextUtils
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.PicturesController
import com.stt.android.controllers.VideoModel
import com.stt.android.controllers.WorkoutBinaryController
import com.stt.android.controllers.WorkoutCommentController
import com.stt.android.controllers.WorkoutExtensionDataModels
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.domain.sml.DeleteSmlDataUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.exceptions.InternalDataException
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

/**
 * Deletes a workout along with its associated data
 *
 * TODO: This use case should be moved to domain once all the used controllers are part of
 * data source module
 */
class DeleteWorkoutUseCase
@Inject constructor(
    private val currentUserController: Current<PERSON><PERSON><PERSON><PERSON>roller,
    private val workoutHeaderController: WorkoutHeaderController,
    private val workoutCommentController: WorkoutCommentController,
    private val workoutDataController: WorkoutBinaryController,
    private val picturesController: PicturesController,
    private val videoModel: VideoModel,
    private val workoutExtensionDataModels: WorkoutExtensionDataModels,
    private val deleteSmlDataUseCase: DeleteSmlDataUseCase,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) {
    /**
     * Deletes a workout if it has never been synced to server otherwise marks as deleted
     * @param workoutHeader the workout header
     * @return true if the workout was deleted or false if it is marked for deletion
     * @throws InternalDataException
     */
    @Throws(InternalDataException::class)
    suspend operator fun invoke(workoutHeader: WorkoutHeader): Boolean = withContext(coroutinesDispatchers.io) {
        try {
            val isDeleted: Boolean
            val deletedWorkout: WorkoutHeader
            if (currentUserController.isLoggedIn && workoutHeader.isSynced) {
                // if the workout has been synced, we should only mark it as "deleted" so that it
                // can be synced to the backend properly
                deletedWorkout = workoutHeaderController.markAsDeleted(workoutHeader)
                isDeleted = false
            } else {
                // the workout is never synced, we just delete it
                workoutHeaderController.remove(workoutHeader)
                deletedWorkout = workoutHeader.toBuilder().deleted().locallyChanged(true).build()
                isDeleted = true
            }

            // deletes the cached comments
            try {
                val workoutKey = workoutHeader.key
                if (!TextUtils.isEmpty(workoutKey)) {
                    workoutCommentController.removeByWorkoutKey(workoutKey)
                }
            } catch (e: InternalDataException) {
                // it fails if it fails
                Timber.w(e, "Unable to delete workout comments")
            }

            // deletes the binary anyway
            try {
                workoutDataController.removeNonCachedBinary(deletedWorkout)
            } catch (e: InternalDataException) {
                // there are users suffering from failed deletions for some reason, but it's not
                // a fatal issue anyway, so swallow the exception here
                Timber.w(e, "Unable to delete the binary")
            }

            // deletes the images
            try {
                picturesController.deleteByWorkout(deletedWorkout)
            } catch (e: InternalDataException) {
                // it fails if it fails
                Timber.w(e, "Unable to delete workout images")
            }

            // deletes the videos
            try {
                videoModel.delete(deletedWorkout)
            } catch (e: InternalDataException) {
                // it fails if it fails
                Timber.w(e, "Unable to delete workout videos")
            }

            // deletes the extensions
            try {
                workoutExtensionDataModels.removeExtensionsByWorkoutIds(
                    listOf(deletedWorkout.id),
                    forceDeleteNotPrefetched = true,
                )
            } catch (e: InternalDataException) {
                // it fails if it fails
                Timber.w(e, "Unable to delete workout extensions")
            }

            // deletes sml data
            deleteSmlDataUseCase(deletedWorkout.id)

            isDeleted
        } catch (e: InternalDataException) {
            Timber.w(e, "Unable to delete workout from the local database")
            throw e
        }
    }
}
