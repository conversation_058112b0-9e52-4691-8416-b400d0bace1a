package com.stt.android.workouts.details.values

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.annotation.ColorInt
import androidx.recyclerview.widget.RecyclerView
import com.stt.android.databinding.ItemWorkoutValueBinding

class WorkoutValueAdapter(
    initialEmptyAmount: Int,
    private val itemsPerRow: Int,
    @ColorInt private val separatorColor: Int,
    @ColorInt private val textColor: Int,
    private val onValueSelectedListener: (value: WorkoutValue) -> Unit = {},
    private val itemViewClickable: Boolean = true,
    // add data by the constructor method
    initialValues: List<WorkoutValue> = emptyList()
) : RecyclerView.Adapter<WorkoutValueViewHolder>() {

    private val values = ArrayList<WorkoutValue>()

    init {
        if (initialEmptyAmount < 0 || itemsPerRow < 0) {
            throw IllegalArgumentException(
                "Arguments cannot be negative. initialAmount: $initialEmptyAmount, itemsPerRow: $itemsPerRow"
            )
        }

        // We initialize the adapter with initialAmount elements so the UI has some placeholders
        for (i in 0 until initialEmptyAmount) {
            values.add(WorkoutValue.createEmpty())
        }
        values.addAll(initialValues)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): WorkoutValueViewHolder =
        WorkoutValueViewHolder(
            adapter = this,
            onValueSelectedListener = onValueSelectedListener,
            itemsPerRow = itemsPerRow,
            binding = ItemWorkoutValueBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            ).apply {
                vHorizontalSeparator.setBackgroundColor(separatorColor)
                vVerticalSeparator.setBackgroundColor(separatorColor)

                label.setTextColor(textColor)
                valueAndUnit.setTextColor(textColor)
            },
            itemViewClickable = itemViewClickable
        )

    override fun onBindViewHolder(holder: WorkoutValueViewHolder, position: Int) {
        holder.bind(values[position])
    }

    fun resetValues(newValues: MutableList<WorkoutValue>) {
        // Fill the last row which has at least one value
        val amountLastRowWithValues = newValues.size % itemsPerRow
        if (amountLastRowWithValues > 0) {
            for (i in 0 until itemsPerRow - amountLastRowWithValues) {
                newValues.add(WorkoutValue.createEmpty())
            }
        }

        val previousValuesAmount = values.size
        values.clear()
        values.addAll(newValues)
        val newValuesAmount = newValues.size

        val diffValuesAmount = newValuesAmount - previousValuesAmount
        if (diffValuesAmount < 0) {
            // If we have less new values than previously
            // First notify that some have changed
            notifyItemRangeChanged(0, newValuesAmount)
            // Then notify the remaining ones are gone
            notifyItemRangeRemoved(newValuesAmount, -diffValuesAmount)
        } else {
            // If we have more new values than previously
            // First notify all previous one have changed
            notifyItemRangeChanged(0, previousValuesAmount)
            // Then notify how many new ones we have
            notifyItemRangeInserted(previousValuesAmount, diffValuesAmount)
        }
    }

    override fun getItemCount(): Int {
        return values.size
    }
}
