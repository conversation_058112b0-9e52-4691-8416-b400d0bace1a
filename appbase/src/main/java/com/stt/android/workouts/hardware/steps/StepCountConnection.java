package com.stt.android.workouts.hardware.steps;

import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import androidx.annotation.Nullable;
import com.stt.android.STTApplication;
import java.io.Closeable;
import javax.inject.Inject;
import pub.devrel.easypermissions.EasyPermissions;

public class StepCountConnection implements SensorEventListener, Closeable {
    public interface StepCountListener {
        void stepCountChanged(int steps);
    }

    @Inject
    SensorManager sensorManager;

    private int lastStepCount = -1;
    private StepCountListener listener;

    public StepCountConnection() {
        STTApplication.getComponent().inject(this);
        registerStepCounter();
    }

    private void registerStepCounter() {
        Sensor sensor = getStepCountSensor();
        if (sensor != null) {
            sensorManager.registerListener(this, sensor, SensorManager.SENSOR_DELAY_FASTEST);
        }
    }

    @Nullable
    private Sensor getStepCountSensor() {
        return sensorManager.getDefaultSensor(Sensor.TYPE_STEP_COUNTER);
    }

    public void setListener(StepCountListener listener) {
        this.listener = listener;
    }

    @Override
    public void close() {
        sensorManager.unregisterListener(this);
    }

    public boolean hasStepCountSensor() {
        return getStepCountSensor() != null;
    }

    @Override
    public void onSensorChanged(SensorEvent event) {
        int stepCount = (int) event.values[0];
        if (lastStepCount == -1) {
            lastStepCount = stepCount;
        }
        int steps = stepCount - lastStepCount;
        lastStepCount = stepCount;
        if (listener != null) {
            listener.stepCountChanged(steps);
        }
    }

    @Override
    public void onAccuracyChanged(Sensor sensor, int accuracy) {
        // do nothing
    }
}
