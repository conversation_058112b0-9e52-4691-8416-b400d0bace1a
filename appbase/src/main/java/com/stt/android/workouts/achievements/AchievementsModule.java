package com.stt.android.workouts.achievements;

import com.stt.android.data.achievements.AchievementRepository;
import com.stt.android.data.source.local.DaoFactory;
import com.stt.android.data.source.local.achievements.AchievementDao;
import com.stt.android.domain.achievements.AchievementAnalytics;
import com.stt.android.domain.achievements.AchievementDataSource;
import dagger.Binds;
import dagger.Module;
import dagger.Provides;

@Module
public abstract class AchievementsModule {

    @Binds
    abstract AchievementDataSource bindAchievementsDataSource(
        AchievementRepository achievementRepository);

    @Binds
    abstract AchievementAnalytics bindAchievementAnalytics(
        AchievementAnalyticsImpl achievementAnalytics);

    @Provides
    static AchievementDao prodiveAchievementDao(DaoFactory daoFactory) {
        return daoFactory.getAchievementDao();
    }
}

