package com.stt.android.workouts.details.values

import android.content.Context
import android.content.res.Resources
import android.os.Parcelable
import androidx.annotation.DimenRes
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.stt.android.R
import com.stt.android.infomodel.SummaryItem
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize

/**
 * @property extraInfoTextResId Used to show additional information below the image in WorkoutValueDescriptionPopupFragment with smaller/grayed font
 */
@Parcelize
data class WorkoutValue(
    val item: SummaryItem? = null,
    val value: String? = null,
    val label: String = "",
    @StringRes val unit: Int? = null,
    val unitString: String? = null,
    @DrawableRes val drawableResId: Int? = null,
    @DimenRes var textSizeResId: Int = R.dimen.workout_value_large_dp,
    @StringRes val descriptionTitleResId: Int? = null,
    @StringRes val descriptionSubtitleResId: Int? = null,
    @StringRes val descriptionTextResId: Int? = null,
    @DrawableRes val descriptionImageResId: Int? = null,
    @StringRes val extraInfoTextResId: Int? = null,
    val descriptionUrl: String? = null,
    val urlText: Int? = null,
    val showActivityType: Boolean = true,
    val showActivityStartTime: Boolean = true
) : Parcelable {

    /**
     * We display description pop-up for this WorkoutValue if there is at least
     * a text description [descriptionTextResId]. [descriptionImageResId] and
     * [descriptionUrl] can be null for now.
     */
    @IgnoredOnParcel
    val hasDescription: Boolean = descriptionTextResId != null

    @IgnoredOnParcel
    val hasSubtitle: Boolean = descriptionSubtitleResId != null

    /**
     * Returns empty string if there is no label
     */
    fun getUnitLabel(context: Context): String = getUnitLabel(context.resources)

    fun getUnitLabel(resources: Resources): String = unitString
        ?: unit?.let { resources.getString(it) }
        ?: ""

    fun getDescriptionTitle(context: Context): String =
        descriptionTitleResId?.let { context.getString(it) } ?: label

    fun getDescriptionSubtitle(context: Context): String? =
        descriptionSubtitleResId?.let { context.getString(it) }

    /**
     * Excluding all the items with null/blank values (like Steps) and with no drawableResId (like Feeling)
     */
    fun hasValueOrDrawable(): Boolean {
        return (value != null && value.isNotBlank()) || (drawableResId != null)
    }

    companion object {
        @JvmStatic
        fun createEmpty(): WorkoutValue {
            return WorkoutValue(value = "")
        }
    }
}
