package com.stt.android.workouts.logbookentries

import com.stt.android.common.coroutines.ORMLITE
import com.stt.android.controllers.LogbookEntryModel
import com.stt.android.domain.user.workout.SuuntoLogbookEntry
import com.stt.android.domain.workouts.logbookentry.LogbookEntry
import com.stt.android.domain.workouts.logbookentry.LogbookEntryDataSource
import kotlinx.coroutines.withContext
import javax.inject.Inject

class LogbookEntryOrmliteDataSource
@Inject constructor(
    private val logbookEntryModel: LogbookEntryModel
) : LogbookEntryDataSource {
    override suspend fun insert(entry: LogbookEntry) = withContext(ORMLITE) {
        logbookEntryModel.store(SuuntoLogbookEntry.fromLogbookEntry(entry))
    }

    override suspend fun update(entry: LogbookEntry) = withContext(ORMLITE) {
        logbookEntryModel.store(SuuntoLogbookEntry.fromLogbookEntry(entry))
    }

    override suspend fun findByWorkoutId(workoutId: Int): LogbookEntry? = withContext(ORMLITE) {
        logbookEntryModel.findByWorkoutId(workoutId)?.toLogbookEntry()
    }
}
