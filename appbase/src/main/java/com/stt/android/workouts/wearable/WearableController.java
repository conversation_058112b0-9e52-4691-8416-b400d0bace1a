package com.stt.android.workouts.wearable;

import android.content.Context;
import android.net.Uri;
import android.os.SystemClock;
import androidx.annotation.NonNull;
import com.google.android.gms.wearable.CapabilityClient;
import com.google.android.gms.wearable.CapabilityInfo;
import com.google.android.gms.wearable.DataMap;
import com.google.android.gms.wearable.Node;
import com.google.android.gms.wearable.PutDataMapRequest;
import com.google.android.gms.wearable.Wearable;
import com.stt.android.core.bridge.Encoder;
import com.stt.android.core.bridge.WearConstants;
import com.stt.android.core.bridge.WearHelper;
import com.stt.android.domain.user.MeasurementUnit;
import com.stt.android.domain.workout.ActivityType;
import com.stt.android.domain.workout.GhostDistanceTimeState;
import com.stt.android.domain.workout.SpeedPaceState;
import java.util.concurrent.atomic.AtomicInteger;

public class WearableController implements CapabilityClient.OnCapabilityChangedListener {
    private static final long NOTIFY_WEAR_AMBIENT_MODE_OFF_INTERVAL_IN_MILLISECONDS = 1000L;
    private static final long NOTIFY_WEAR_AMBIENT_MODE_ON_INTERVAL_IN_MILLISECONDS = 10000L;

    private final byte[] bytes = Encoder.createByteBufferForSnapshot();
    private final Context applicationContext;

    private AtomicInteger connectedNodesCount = new AtomicInteger(0);
    private boolean wearableAmbientModeOn = false;
    private long lastWearableUpdatedTime;

    public WearableController(Context context) {
        applicationContext = context.getApplicationContext();
    }

    public void start() {
        Wearable.getNodeClient(applicationContext).getConnectedNodes()
            .addOnSuccessListener(nodes -> connectedNodesCount.set(getNonCloudNodeCount(nodes)));

        Wearable.getCapabilityClient(applicationContext)
            .addListener(this, Uri.parse("wear://"), CapabilityClient.FILTER_REACHABLE);
    }

    public boolean hasConnectedDevices() {
        return connectedNodesCount.get() > 0;
    }

    public void shutdown() {
        // doing this in a separate service to make sure everything is properly cleaned-up before it's killed
        WearableCleanupService.enqueueWork(applicationContext);

        Wearable.getCapabilityClient(applicationContext).removeListener(this);
    }

    public void setAmbientMode(boolean ambientModeOn) {
        if (wearableAmbientModeOn != ambientModeOn) {
            wearableAmbientModeOn = ambientModeOn;
            lastWearableUpdatedTime = 0L;
        }
    }

    public boolean shouldUpdate() {
        if (connectedNodesCount.get() == 0) {
            return false;
        }

        long elapsedTimeSinceLastUpdate = SystemClock.elapsedRealtime() - lastWearableUpdatedTime;
        return wearableAmbientModeOn ? elapsedTimeSinceLastUpdate >= NOTIFY_WEAR_AMBIENT_MODE_ON_INTERVAL_IN_MILLISECONDS
                : elapsedTimeSinceLastUpdate >= NOTIFY_WEAR_AMBIENT_MODE_OFF_INTERVAL_IN_MILLISECONDS;
    }

    public void notifyRecordingWarmedUp() {
        notifyRecordingState(WearConstants.WARMED_UP);
    }

    private void notifyRecordingState(byte state) {
        PutDataMapRequest putDataMapRequest = PutDataMapRequest
                .create(WearConstants.ONGOING_WORKOUT_RECORDING_STATE);
        DataMap dataMap = putDataMapRequest.getDataMap();
        dataMap.putByte(WearConstants.RECORDING_STATE, state);
        WearHelper.sendDataItem(applicationContext, putDataMapRequest);
    }

    public void notifyRecordingWarmUpStopped() {
        notifyRecordingState(WearConstants.WARM_UP_STOPPED);
    }

    public void notifyRecordingStartedOrResumed() {
        notifyRecordingState(WearConstants.STARTED_OR_RESUMED);
    }

    public void notifyRecordingPaused() {
        notifyRecordingState(WearConstants.PAUSED);
    }

    public void notifyRecordingAutoPaused() {
        notifyRecordingState(WearConstants.AUTO_PAUSED);
    }

    public void notifyRecordingStopped(MeasurementUnit measurementUnit, double duration, double distance,
                                       double energy, double speed, double maxSpeed,
                                       int heartRate, int maxHeartRate) {
        notifyRecordingState(WearConstants.STOPPED);

        PutDataMapRequest putDataMapRequest = PutDataMapRequest.create(WearConstants.WORKOUT_SUMMARY);
        DataMap dataMap = putDataMapRequest.getDataMap();

        byte mu = measurementUnit == MeasurementUnit.IMPERIAL
                ? WearConstants.MEASUREMENT_UNIT_IMPERIAL : WearConstants.MEASUREMENT_UNIT_METRIC;
        speed = measurementUnit.toSpeedUnit(speed);
        maxSpeed = measurementUnit.toSpeedUnit(maxSpeed);
        float pace = (float) measurementUnit.toPaceUnit(speed);
        float maxPace = (float) measurementUnit.toPaceUnit(maxSpeed);
        dataMap.putByteArray(WearConstants.SUMMARY, Encoder.encodeSummary(mu, (int) duration,
                (float) measurementUnit.toDistanceUnit(distance), (float) energy, (float) speed,
                (float) maxSpeed, pace, maxPace, (short) heartRate, (short) maxHeartRate));
        WearHelper.sendDataItem(applicationContext, putDataMapRequest);
    }

    public void notifyNewLapAdded(MeasurementUnit measurementUnit, boolean autoLap, double lapDistance,
                                  int lapDurationInSeconds, SpeedPaceState speedPaceState, double lapSpeed) {
        PutDataMapRequest putDataMapRequest = PutDataMapRequest.create(WearConstants.ONGOING_WORKOUT_EVENT);
        DataMap dataMap = putDataMapRequest.getDataMap();

        dataMap.putByte(WearConstants.WORKOUT_EVENT, WearConstants.LAP_ADDED);

        byte mu = measurementUnit == MeasurementUnit.IMPERIAL
                ? WearConstants.MEASUREMENT_UNIT_IMPERIAL : WearConstants.MEASUREMENT_UNIT_METRIC;
        byte sps;
        float speedPace;
        if (speedPaceState == SpeedPaceState.SPEED) {
            sps = WearConstants.SPEED_PACE_STATE_SPEED;
            speedPace = (float) measurementUnit.toSpeedUnit(lapSpeed);
        } else {
            sps = WearConstants.SPEED_PACE_STATE_PACE;
            speedPace = (float) measurementUnit.toPaceUnit(lapSpeed);
        }
        dataMap.putByteArray(WearConstants.LAP_INFO, Encoder.encodeLapInfo(mu,
                autoLap ? WearConstants.LAP_TYPE_AUTO : WearConstants.LAP_TYPE_MANUAL,
                (float) measurementUnit.toDistanceUnit(lapDistance), lapDurationInSeconds, sps, speedPace));

        WearHelper.sendDataItem(applicationContext, putDataMapRequest);
    }

    public void notifyActivityType(ActivityType activityType) {
        PutDataMapRequest putDataMapRequest = PutDataMapRequest
                .create(WearConstants.ONGOING_WORKOUT_ACTIVITY_TYPE);
        DataMap dataMap = putDataMapRequest.getDataMap();
        dataMap.putByte(WearConstants.ACTIVITY_TYPE, (byte) activityType.getId());
        WearHelper.sendDataItem(applicationContext, putDataMapRequest);
    }

    public void update(MeasurementUnit measurementUnit, boolean autoPaused, float gpsAccuracy,
                       int duration, double distance, SpeedPaceState speedPaceState, double speed,
                       double avgSpeed, double energy, int heartRate, int avgHeartRate, boolean hasGhostTarget,
                       GhostDistanceTimeState ghostDistanceTimeState, double ghostDifference,
                       double lapDistance, int lapDuration) {
        PutDataMapRequest putDataMapRequest = PutDataMapRequest.create(WearConstants.ONGOING_WORKOUT_SNAPSHOT);
        DataMap dataMap = putDataMapRequest.getDataMap();
        byte mu = measurementUnit == MeasurementUnit.IMPERIAL
                ? WearConstants.MEASUREMENT_UNIT_IMPERIAL : WearConstants.MEASUREMENT_UNIT_METRIC;

        byte sps;
        float speedPace;
        float avgSpeedPace;
        if (speedPaceState == SpeedPaceState.SPEED) {
            sps = WearConstants.SPEED_PACE_STATE_SPEED;
            speedPace = (float) measurementUnit.toSpeedUnit(speed);
            avgSpeedPace = (float) measurementUnit.toSpeedUnit(avgSpeed);
        } else {
            sps = WearConstants.SPEED_PACE_STATE_PACE;
            speedPace = (float) measurementUnit.toPaceUnit(speed);
            avgSpeedPace = (float) measurementUnit.toPaceUnit(avgSpeed);
        }

        byte aheadBehindState;
        if (hasGhostTarget) {
            if (ghostDistanceTimeState == null) {
                aheadBehindState = WearConstants.AHEAD_BEHIND_STATE_NO_MATCH;
            } else {
                if (ghostDistanceTimeState == GhostDistanceTimeState.TIME) {
                    aheadBehindState = WearConstants.AHEAD_BEHIND_STATE_TIME;
                } else {
                    aheadBehindState = WearConstants.AHEAD_BEHIND_STATE_DISTANCE;
                    ghostDifference = measurementUnit.toDistanceUnit(ghostDifference);
                }
            }
        } else {
            aheadBehindState = WearConstants.AHEAD_BEHIND_STATE_NONE;
        }
        dataMap.putByteArray(WearConstants.SNAPSHOT, Encoder.encodeSnapshot(bytes, mu, autoPaused, gpsAccuracy,
                duration, (float) measurementUnit.toDistanceUnit(distance), sps, speedPace, avgSpeedPace,
                (float) energy, aheadBehindState, (float) ghostDifference, (short) heartRate, (short) avgHeartRate,
                (float) measurementUnit.toDistanceUnit(lapDistance), lapDuration));
        WearHelper.sendDataItem(applicationContext, putDataMapRequest);

        lastWearableUpdatedTime = SystemClock.elapsedRealtime();
    }

    @Override
    public void onCapabilityChanged(@NonNull CapabilityInfo capabilityInfo) {
        int connectedCount = getNonCloudNodeCount(capabilityInfo.getNodes());
        if (connectedNodesCount.getAndSet(connectedCount) == 0 && connectedCount > 0) {
            lastWearableUpdatedTime = 0L;
        }
    }

    private static boolean isCloudNode(Node node) {
        return "cloud".equals(node.getDisplayName()) && "cloud".equals(node.getId());
    }

    private static int getNonCloudNodeCount(Iterable<Node> nodes) {
        int count = 0;

        if (nodes != null) {
            for (Node node : nodes) {
                if (!isCloudNode(node)) {
                    count++;
                }
            }
        }

        return count;
    }
}
