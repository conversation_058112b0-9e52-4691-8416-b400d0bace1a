package com.stt.android.workouts;

import android.os.Parcel;
import android.os.Parcelable;
import com.stt.android.domain.Point;

/**
 * Basic picture information used to transfer them from
 * {@link com.stt.android.ui.activities.WorkoutActivity}
 * to {@link com.stt.android.workouts.RecordWorkoutService}
 */
public class PendingPictureInfo implements Parcelable {
    private final String destinationFileName;
    private final String md5Hash;
    private final Point location;
    private final int width;
    private final int height;

    public PendingPictureInfo(String destinationFileName, String md5Hash, Point location, int width,
        int height) {
        this.destinationFileName = destinationFileName;
        this.md5Hash = md5Hash;
        this.location = location;
        this.width = width;
        this.height = height;
    }

    public String getDestinationFileName() {
        return destinationFileName;
    }

    public String getMd5Hash() {
        return md5Hash;
    }

    public Point getLocation() {
        return location;
    }

    public int getWidth() {
        return width;
    }

    public int getHeight() {
        return height;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(destinationFileName);
        dest.writeString(md5Hash);
        dest.writeParcelable(location, flags);
        dest.writeInt(width);
        dest.writeInt(height);
    }

    public static final Creator<PendingPictureInfo> CREATOR = new Creator<PendingPictureInfo>() {
        @Override
        public PendingPictureInfo createFromParcel(Parcel source) {
            String destinationFileName = source.readString();
            String md5Hash = source.readString();
            Point location = source.readParcelable(Point.class.getClassLoader());
            int width = source.readInt();
            int height = source.readInt();
            return new PendingPictureInfo(destinationFileName, md5Hash, location, width, height);
        }

        @Override
        public PendingPictureInfo[] newArray(int size) {
            return new PendingPictureInfo[size];
        }
    };
}
