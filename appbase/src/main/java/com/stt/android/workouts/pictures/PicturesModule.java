package com.stt.android.workouts.pictures;

import com.squareup.moshi.Moshi;
import com.stt.android.data.workout.pictures.PictureFileRepository;
import com.stt.android.domain.workouts.pictures.PicturesDataSource;
import com.stt.android.remote.AuthProvider;
import com.stt.android.remote.BaseUrl;
import com.stt.android.remote.SharedOkHttpClient;
import com.stt.android.remote.UserAgent;
import com.stt.android.remote.di.BrandOkHttpConfigFactory;
import com.stt.android.remote.di.RestApiFactory;
import com.stt.android.remote.workout.picture.PictureRestApi;
import dagger.Binds;
import dagger.Module;
import dagger.Provides;
import okhttp3.OkHttpClient;

@Module
public abstract class PicturesModule {
    @Binds
    abstract PictureFileRepository bindPictureFileRepository(FsPictureFileRepository repository);

    @Binds
    abstract PicturesDataSource bindPicturesDataSource(PicturesOrmLiteDataSource dataSource);

    @Provides
    static PictureRestApi providePictureRestApi(
        @SharedOkHttpClient OkHttpClient sharedClient,
        @BaseUrl String baseUrl,
        @UserAgent String userAgent,
        AuthProvider authProvider,
        Moshi moshi
    ) {
        return RestApiFactory.buildRestApi(
            sharedClient,
            baseUrl,
            PictureRestApi.class,
            BrandOkHttpConfigFactory.getStOkHttpConfig(authProvider, userAgent),
            moshi);
    }
}
