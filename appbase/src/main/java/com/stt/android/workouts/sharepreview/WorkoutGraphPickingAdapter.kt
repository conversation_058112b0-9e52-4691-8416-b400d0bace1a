package com.stt.android.workouts.sharepreview

import android.view.LayoutInflater
import android.view.ViewGroup
import com.stt.android.databinding.ItemWorkoutGraphPickBinding

class WorkoutGraphPickingAdapter(
    activityWorkoutGraphs: List<WorkoutShareGraphOption>,
    initialPosition: Int,
    listener: (index: Int) -> Unit
) : WorkoutElementPickingAdapter<WorkoutShareGraphOption, WorkoutGraphPickingViewHolder>(
    activityWorkoutGraphs,
    initialPosition,
    listener
) {
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = WorkoutGraphPickingViewHolder(
        ItemWorkoutGraphPickBinding.inflate(LayoutInflater.from(parent.context), parent, false),
        listener
    )

    override fun onBindViewHolder(viewHolder: WorkoutGraphPickingViewHolder, position: Int) {
        viewHolder.bind(position, position == initialPosition, options[position])
    }
}
