package com.stt.android.workouts.edit

import android.content.Context
import android.content.Intent
import androidx.core.app.JobIntentService
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.work.WorkManager
import com.stt.android.controllers.SessionController
import com.stt.android.domain.workout.Workout
import com.stt.android.exceptions.InternalDataException
import com.stt.android.services.JOB_ID_SAVE_WORKOUT_SERVICE
import com.stt.android.utils.STTConstants
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import java.util.concurrent.ConcurrentLinkedQueue
import javax.inject.Inject

/**
 * Saves the specified workout to local database
 */
@AndroidEntryPoint
class SaveWorkoutService : JobIntentService() {

    @Inject
    internal lateinit var sessionController: SessionController

    @Inject
    internal lateinit var localBroadcastManager: LocalBroadcastManager

    @Inject
    internal lateinit var workManager: WorkManager

    override fun onHandleWork(intent: Intent) {
        var workout = workoutsToSave.poll()
        while (workout != null) {
            try {
                sessionController.storeWorkout(workout)
                localBroadcastManager.sendBroadcast(
                    Intent(STTConstants.BroadcastActions.WORKOUT_SAVED)
                        .putExtra(STTConstants.ExtraKeys.WORKOUT_ID, workout.header.id)
                )
            } catch (e: InternalDataException) {
                Timber.w(e, "Internal data exception while saving workout")
                throw RuntimeException(e)
            }

            workout = workoutsToSave.poll()
        }
    }

    companion object {
        private val workoutsToSave = ConcurrentLinkedQueue<Workout>()

        @JvmStatic
        fun enqueueWork(context: Context, workout: Workout) {
            workoutsToSave.offer(workout)
            val intent = Intent(context, SaveWorkoutService::class.java)
            enqueueWork(
                context,
                SaveWorkoutService::class.java,
                JOB_ID_SAVE_WORKOUT_SERVICE,
                intent
            )
        }
    }
}
