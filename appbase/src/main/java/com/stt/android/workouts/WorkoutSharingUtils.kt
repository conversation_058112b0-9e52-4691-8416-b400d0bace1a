package com.stt.android.workouts

import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.domain.sync.SyncRequest
import com.stt.android.domain.sync.SyncRequestHandler
import com.stt.android.domain.workout.SharingOption
import com.stt.android.domain.workouts.WorkoutHeader
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Get a single to check if workout is private and if so, change sharing setting to link shared
 * @param workoutHeader Header of the workout, for which privacy is to be checked and changed
 * @param workoutHeaderController Controller used to update workouts
 * @param syncSingleWorkoutUseCase instance of [SyncSingleWorkoutUseCase]
 * @return Single to do update
 */
suspend fun setSharingLinkIfPrivate(
    workoutHeader: WorkoutHeader,
    workoutHeaderController: WorkoutHeaderController,
    syncSingleWorkoutUseCase: SyncSingleWorkoutUseCase,
): WorkoutHeader = withContext(Dispatchers.IO) {
    val currentSharingFlags = workoutHeader.sharingFlags
    if (currentSharingFlags == SharingOption.NOT_SHARED.backendId || currentSharingFlags == SharingOption.FOLLOWERS.backendId) {
        val newSharingFlags = if (currentSharingFlags == SharingOption.NOT_SHARED.backendId) {
            SharingOption.flagOf(listOf(SharingOption.LINK))
        } else {
            SharingOption.flagOf(listOf(SharingOption.LINK, SharingOption.FOLLOWERS))
        }

        val updatedWorkoutHeader = workoutHeader
            .toBuilder()
            .sharingFlags(newSharingFlags)
            .locallyChanged(true)
            .build()
        workoutHeaderController.store(updatedWorkoutHeader).also {
            // Backend sync must be performed at this point, otherwise workout link does not work
            // immediately after workout is changed from private to public before sharing
            val syncSuccessfully = syncSingleWorkoutUseCase.sync(it)
            if (!syncSuccessfully) {
                // if sync is failed, reset the sharingFlags to not shared
                workoutHeaderController.store(updatedWorkoutHeader.copy(sharingFlags = SharingOption.NOT_SHARED.backendId, locallyChanged = false))
                throw Exception("Push local workout changes to the server failed")
            }
        }
    } else {
        workoutHeader
    }
}
