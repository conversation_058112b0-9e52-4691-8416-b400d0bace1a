package com.stt.android.workouts.autosave;

import android.content.Context;
import android.os.SystemClock;
import androidx.core.util.AtomicFile;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.gson.Gson;
import com.google.gson.JsonParseException;
import com.google.gson.stream.JsonReader;
import com.stt.android.STTApplication;
import com.stt.android.utils.DateUtils;
import com.stt.android.utils.FileUtils;
import com.stt.android.workouts.BaseOngoingWorkout;
import com.stt.android.workouts.OngoingWorkout;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import javax.inject.Inject;
import javax.inject.Named;
import timber.log.Timber;

/**
 * Class responsible to provide convenient methods to store
 * {@link BaseOngoingWorkout} in increments so it can be restored in case of
 * sudden crash from the app.
 */
public class AutoSaveOngoingWorkoutController {
    private static final String UTF8_CHARSET = "UTF-8";
    private static final int MAX_AUTO_RECOVERS = 5;
    /**
     * Filename where the basic (i.e. no lists or complex objects) ongoing information will be
     * written
     */
    private static final String ONGOING_BASIC_FILENAME = "ongoing_basic";
    /**
     * Minimum amount of time needed before we reset the auto recover counter.
     * That is, minimum amount of time we've been auto saving before resetting the counter.
     * Basically we're saying that if within in one minute we've auto-recovered more than
     * {@link #MAX_AUTO_RECOVERS} then there's something really wrong with the system.
     */
    private static final long MIN_TIME_RESET = DateUtils.MINUTE_IN_MILLIS;
    /**
     * Separate thread to perform write tasks to disk.
     * WARNING! KEEP IT SINGLE THREADED unless you know what you're doing!
     */
    private final ExecutorService writeService = Executors.newSingleThreadExecutor();
    @SuppressWarnings("WeakerAccess")
    final OngoingWorkout ongoingWorkout;
    private final GeoPointsController geoPointsController;
    private final HeartRatesController heartRatesController;

    @SuppressWarnings("WeakerAccess")
    @Inject
    Context appContext;

    @SuppressWarnings("WeakerAccess")
    @Inject
    @Named("Float")
    Gson gson;
    /**
     * Last timestamp (see {@link android.os.SystemClock#elapsedRealtime()}) of the reset counter.
     */
    @SuppressWarnings("WeakerAccess")
    long lastAutoRecoverCounterReset;

    /**
     * Constructor used to initialize the basic information using auto saved data.
     * Basically it triggers a recover process.
     *
     * @throws java.io.IOException if we could not save the just auto-recovered data
     */
    private AutoSaveOngoingWorkoutController() throws IOException {
        init();
        Timber.d("About to recover basic workout information");
        ongoingWorkout = recoverBasicOngoingWorkoutInformation();
        Timber.d("Basic workout information recovered");
        geoPointsController = new GeoPointsController(appContext, gson);
        Timber.d("About to recover workout geopoints");
        geoPointsController.recoverSavedList(ongoingWorkout);
        Timber.d("Workout geopoints recovered %d", ongoingWorkout.getRoutePoints().size());
        heartRatesController = new HeartRatesController(appContext, gson);
        Timber.d("About to recover HR data");
        heartRatesController.recoverSavedList(ongoingWorkout);
        Timber.d("Workout hr recovered %d", ongoingWorkout.getHeartRateEvents().size());
    }

    public AutoSaveOngoingWorkoutController(OngoingWorkout ongoingWorkout) {
        init();
        this.ongoingWorkout = ongoingWorkout;
        geoPointsController = new GeoPointsController(appContext, gson);
        heartRatesController = new HeartRatesController(appContext, gson);
    }

    /**
     * Recovers auto saved ongoing workout if possible.
     *
     * @return the new controller which holds the auto recovered workout.
     * @throws TooManyAutoRecoversException if the auto saved data has been recovered more than
     * {@link #MAX_AUTO_RECOVERS}
     */
    public static AutoSaveOngoingWorkoutController recoverAutoSavedWorkout()
        throws TooManyAutoRecoversException, IOException {
        AutoSaveOngoingWorkoutController autoSaveOngoingWorkoutController =
            new AutoSaveOngoingWorkoutController();
        OngoingWorkout unfinishedWorkout = autoSaveOngoingWorkoutController.getOngoingWorkout();
        int autoRecoveredCounter = unfinishedWorkout.getAutoRecovered();
        Timber.d("AutoSave.recoverAutoSavedWorkout() auto recovered counter: %d",
            autoRecoveredCounter);
        if (autoRecoveredCounter > MAX_AUTO_RECOVERS) {
            throw new TooManyAutoRecoversException();
        }
        return autoSaveOngoingWorkoutController;
    }

    /**
     * @return the time when auto save data file was last modified. Returns 0 if the file does not
     * exist.
     */
    public static long autoSaveDataTimestamp(Context appContext) {
        // We only need to check if there are headers any other data is optional
        FileInputStream headers = null;
        try {
            // Try to open all the required files.
            File basicFile = appContext.getFileStreamPath(ONGOING_BASIC_FILENAME);
            AtomicFile atomicFile = new AtomicFile(basicFile);
            headers = atomicFile.openRead();
            Timber.d("AutoSaveOngoingWorkoutController Found auto saved data headers");
            return basicFile.lastModified();
        } catch (FileNotFoundException e) {
            Timber.d("AutoSaveOngoingWorkoutController No auto saved data found");
            return 0;
        } finally {
            if (headers != null) {
                try {
                    headers.close();
                } catch (IOException e) {
                    // ignored
                }
            }
        }
    }

    /**
     * Deletes any temporary saved data
     */
    public static void cleanSavedData(Context applicationContext) {
        File basicFile = applicationContext.getFileStreamPath(ONGOING_BASIC_FILENAME);
        AtomicFile atomicFile = new AtomicFile(basicFile);
        atomicFile.delete();
        GeoPointsController.cleanSavedData(applicationContext);
        HeartRatesController.cleanSavedData(applicationContext);
    }

    public static void dumpFiles(Context context, File dst) {
        String filename = ONGOING_BASIC_FILENAME;
        dumpFile(context, dst, filename);
        filename = GeoPointsController.ONGOING_ROUTE_FILENAME;
        dumpFile(context, dst, filename);
        filename = HeartRatesController.ONGOING_HEART_RATES_FILENAME;
        dumpFile(context, dst, filename);
    }

    private static void dumpFile(Context context, File dst, String filename) {
        try {
            File src = context.getFileStreamPath(filename);
            FileUtils.copyFile(src, new File(dst, src.getName()), true);
        } catch (Throwable e) {
            // Capture all the things, we don't want to fail here
            Timber.w(e, "Unable to save data");
        }
    }

    private OngoingWorkout recoverBasicOngoingWorkoutInformation() {
        JsonReader reader = null;
        try {
            File basicFile = appContext.getFileStreamPath(ONGOING_BASIC_FILENAME);
            AtomicFile atomicFile = new AtomicFile(basicFile);

            FileInputStream is = atomicFile.openRead();
            InputStreamReader in = new InputStreamReader(is, UTF8_CHARSET);
            reader = new JsonReader(in);
            OngoingWorkout recoveredOngoingWorkout = gson.fromJson(reader, OngoingWorkout.class);
            // Recover as many geopoints as possible (this might take quite some seconds)
            recoveredOngoingWorkout.incrementAutoRecovered();
            return recoveredOngoingWorkout;
        } catch (JsonParseException | IOException e) {
            throw new IllegalStateException("Autosaved data not available", e);
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    // Not much we can do
                }
            }
        }
    }

    private void init() {
        STTApplication.getComponent().inject(this);
    }

    public void save() {
        writeService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    saveBasicInformation();
                    incrementalSave();
                    long now = SystemClock.elapsedRealtime();
                    if (lastAutoRecoverCounterReset == 0) {
                        // If it's the first time set it to the current timestamp
                        lastAutoRecoverCounterReset = now;
                    } else if ((now - MIN_TIME_RESET) > lastAutoRecoverCounterReset) {
                        lastAutoRecoverCounterReset = now;
                        ongoingWorkout.resetAutoRecovered();
                    }
                } catch (JsonParseException | IOException e) {
                    Timber.e(e, "Error while auto saving ongoing workout");
                }
            }
        });
    }

    /**
     * Saves the {@link #ongoingWorkout} lists in an incremental way.
     *
     * @throws IOException
     * @throws JsonParseException
     */
    @SuppressWarnings("WeakerAccess")
    void incrementalSave() throws JsonParseException, IOException {
        long start = System.currentTimeMillis();
        geoPointsController.incrementalListSave(ongoingWorkout.getRoutePoints());
        heartRatesController.incrementalListSave(ongoingWorkout.getHeartRateEvents());
        // TODO: Save other lists
        long end = System.currentTimeMillis();
        Timber.d("AutoSaveOngoingWorkoutController.incrementalSave() Took %dms", (end - start));
    }

    /**
     * Deletes any autosave file that won't be needed after the ongoing workout has finished
     * successfully.
     */
    public void finish() {
        cleanSavedData(appContext);
        geoPointsController.closeOutput();
        heartRatesController.closeOutput();
    }

    /**
     * Saves elements from {@link BaseOngoingWorkout}. To speed up things we should not be saving
     * any list or complex data structure. The previous contents of will be overwritten.
     *
     * @throws JsonParseException
     */
    @SuppressWarnings("WeakerAccess")
    void saveBasicInformation() {
        long start = System.currentTimeMillis();
        File basicFile = appContext.getFileStreamPath(ONGOING_BASIC_FILENAME);
        //noinspection ResultOfMethodCallIgnored
        basicFile.setWritable(true, true);
        //noinspection ResultOfMethodCallIgnored
        basicFile.setReadable(true, true);
        AtomicFile atomicFile = new AtomicFile(basicFile);
        FileOutputStream os = null;
        try {
            os = atomicFile.startWrite();
            // Ideally we would create an OutputStreamWriter to give to gson but it seems that
            // AtomicFile doesn't get along with multiple layers of streams so let's keep it to
            // the basics. That is, get the bytes of the Json string representation.
            String json = gson.toJson(ongoingWorkout, OngoingWorkout.class);
            os.write(json.getBytes(UTF8_CHARSET));
            atomicFile.finishWrite(os);
        } catch (Throwable e) {
            if (os != null) {
                atomicFile.failWrite(os);
                Timber.d("AutoSaveOngoing saveBasicInformation() atomic file fail write");
            }
            FirebaseCrashlytics.getInstance().recordException(e);
        } finally {
            long end = System.currentTimeMillis();
            Timber.d("AutoSaveOngoingWorkoutController.saveBasicInformation() Took %dms",
                (end - start));
        }
    }

    public OngoingWorkout getOngoingWorkout() {
        return ongoingWorkout;
    }
}
