package com.stt.android.workouts.autosave;

import android.content.Context;
import com.google.gson.Gson;
import com.google.gson.JsonParseException;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.stt.android.network.interfaces.ANetworkProvider;
import com.stt.android.utils.ZipUtils;
import com.stt.android.workouts.OngoingWorkout;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.lang.reflect.Type;
import java.util.List;
import java.util.zip.Deflater;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;
import timber.log.Timber;

abstract class IncrementalSaveListController<T> {
    private final String filename;
    private final Context appContext;
    private final Gson gson;
    /**
     * Index of the next list entry to save
     */
    private int nextIdxToSave = 0;
    /**
     * Zip files are containers that can contain multiple files. We use this concept of multiple
     * files to store units of information.
     */
    private ZipOutputStream zipOutput = null;

    public IncrementalSaveListController(String filename, Context appContext, Gson gson) {
        this.filename = filename;
        this.appContext = appContext;
        this.gson = gson;
    }

    /**
     * Stores all the points between {@link #nextIdxToSave} and
     * the current size of the given list. It assumes that the list won't
     * decrease in size (i.e. entries are not removed)
     * <p/>
     * We use a {@link java.util.zip.ZipEntry} per each entry to save. Each entry will
     * contain all the complete compression information so it can be read properly.
     *
     * @throws java.io.IOException
     */
    public void incrementalListSave(List<T> listToSave) throws IOException {
        if (listToSave.size() == nextIdxToSave) {
            // There's nothing new to save so move along
            return;
        }
        if (zipOutput == null) {
            initOutput();
        }
        JsonWriter writer = new JsonWriter(new OutputStreamWriter(zipOutput, ANetworkProvider.UTF_8_CHARSET));
        while (nextIdxToSave < listToSave.size()) {
            Timber.d("Autosaving route point %d", nextIdxToSave);
            ZipEntry entry = new ZipEntry("RoutePoint" + nextIdxToSave);
            //FIXME: There's a limit of 65535 (64*1024-1) entries per zip file!!!
            zipOutput.putNextEntry(entry);
            T entryToSave = listToSave.get(nextIdxToSave);
            gson.toJson(entryToSave, getListClass(), writer);
            writer.flush();
            zipOutput.closeEntry();
            nextIdxToSave++;
        }
    }

    protected abstract Type getListClass();

    public void recoverSavedList(OngoingWorkout ongoingWorkout) throws IOException {
        FileInputStream fis = null;
        try {
            Timber.d("Opening file %s", filename);
            File savedListFile = new File(appContext.getFilesDir(), filename);
            fis = new FileInputStream(savedListFile);
            Timber.d("Opening zip input stream");
            ZipInputStream zis = new ZipInputStream(fis);
            Timber.d("Opening input stream reader");
            InputStreamReader in = new InputStreamReader(zis, ANetworkProvider.UTF_8_CHARSET);
            Timber.d("Opening json reader");
            JsonReader reader = new JsonReader(in);
            Timber.d("Are more bytes available in zis? %d", zis.available());
            ZipEntry ze;
            while ((ze = zis.getNextEntry()) != null) {
                Timber.d("Reading %s", ze.getName());
                ZipUtils.checkZipPathTraversal(savedListFile, ze);
                T recoveredEntry = gson.fromJson(reader, getListClass());
                Timber.d("Route point read: %s", recoveredEntry);
                addEntryToOngoingWorkout(ongoingWorkout, recoveredEntry);
            }
            // Since the app crashed it's likely that it crashed when writing points so it's not
            // big deal and continue because we want to recover as much as possible
        } catch (JsonParseException | IOException e) {
            Timber.w(e, "Unable to read saved list at %s", filename);
        } finally {
            if (fis != null) {
                try {
                    Timber.d("Closing fis %s", filename);
                    fis.close();
                    Timber.d("Closed fis %s", filename);
                } catch (IOException e) {
                    // Not much we can do
                }
            }
        }
    }

    public abstract void addEntryToOngoingWorkout(OngoingWorkout ongoingWorkout, T recoveredEntry);

    private void initOutput() throws FileNotFoundException {
        FileOutputStream os = appContext.openFileOutput(filename, Context.MODE_PRIVATE);
        zipOutput = new ZipOutputStream(os);
        zipOutput.setLevel(Deflater.DEFAULT_COMPRESSION);
    }

    public void closeOutput() {
        if (zipOutput != null) {
            try {
                zipOutput.close();
            } catch (IOException e) {
                // Not much we can do
            }
        }
    }
}
