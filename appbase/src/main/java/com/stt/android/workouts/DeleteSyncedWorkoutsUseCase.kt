package com.stt.android.workouts

import com.stt.android.controllers.WorkoutExtensionDataModels
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.domain.sml.DeleteSmlDataUseCase
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Deletes all synced workout headers from the local database.
 * This method acts as a bridge to kotlin coroutines from Java.
 */
fun deleteSyncedWorkouts(
    workoutHeaderController: WorkoutHeaderController,
    workoutExtensionDataModels: WorkoutExtensionDataModels,
    deleteSmlDataUseCase: DeleteSmlDataUseCase
) {
    GlobalScope.launch {
        DeleteSyncedWorkoutsUseCase(
            workoutHeaderController,
            workoutExtensionDataModels,
            deleteSmlDataUseCase
        ).invoke()
    }
}

/**
 * Deletes all synced workout headers from the local database
 */
class DeleteSyncedWorkoutsUseCase
@Inject constructor(
    private val workoutHeaderController: WorkoutHeaderController,
    private val workoutExtensionDataModels: WorkoutExtensionDataModels,
    private val deleteSmlDataUseCase: DeleteSmlDataUseCase,
) {
    suspend operator fun invoke() {
        val syncedWorkoutHeaders = workoutHeaderController.findAllSynced()
        workoutHeaderController.remove(syncedWorkoutHeaders)
        val workoutIds = syncedWorkoutHeaders.map { it.id }.toSet()
        workoutExtensionDataModels.removeExtensionsByWorkoutIds(workoutIds, true)
        workoutIds.forEach { deleteSmlDataUseCase(it) }
    }
}
