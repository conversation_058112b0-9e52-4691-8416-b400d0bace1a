package com.stt.android.workouts.sharepreview

import android.app.Activity
import android.content.Context
import android.content.Intent
import com.stt.android.R

class WorkoutGraphPickingActivity :
    WorkoutElementPickingActivity<WorkoutShareGraphOption, WorkoutGraphPickingViewHolder>() {

    override val actionbarTitleId = R.string.select_graph

    private val onItemClick: (Int) -> Unit = {
        val intent = Intent().putExtra(INTENT_EXTRA_WORKOUT_GRAPH_FINAL_INDEX_KEY, it)
        setResult(Activity.RESULT_OK, intent)
        finish()
    }

    override fun createAdapter(options: List<WorkoutShareGraphOption>, initialIndex: Int): WorkoutGraphPickingAdapter =
        WorkoutGraphPickingAdapter(options, initialIndex, onItemClick)

    companion object {
        private const val packageName = "com.stt.android.workouts.sharepreview.WorkoutGraphPickingActivity"
        const val INTENT_EXTRA_WORKOUT_GRAPH_FINAL_INDEX_KEY = "$packageName.WORKOUT_VALUE_FINAL_INDEX"
        const val INTENT_WORKOUT_GRAPH_REQUEST_CODE = 2

        fun newStartIntent(
            context: Context,
            graphOptionsList: List<WorkoutShareGraphOption>,
            initialIndex: Int
        ): Intent {
            val data = createDataForStartIntent(graphOptionsList, initialIndex)
            return Intent(context, WorkoutGraphPickingActivity::class.java).putExtra(INTENT_EXTRA, data)
        }
    }
}
