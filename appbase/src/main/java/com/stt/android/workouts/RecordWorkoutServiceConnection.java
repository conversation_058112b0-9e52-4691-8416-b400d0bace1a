package com.stt.android.workouts;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import androidx.annotation.Nullable;

import timber.log.Timber;

/**
 * Defines callbacks for service binding, passed to bindService().
 * <p/>
 * To bound to the service you have to call {@link #bindIfStarted(Context)}, and to unbind call
 * {@link #unbind(Context)}.
 * <p/>
 * Don't mix this calls with {@link Context#bindService(Intent, ServiceConnection, int)} and
 * {@link Context#unbindService(ServiceConnection)} because it seems that the connection can't be
 * unbound from a different class.
 */
public class RecordWorkoutServiceConnection implements ServiceConnection {
    @Nullable
    private final Listener listener;
    private RecordWorkoutService recordWorkoutService;

    public RecordWorkoutServiceConnection() {
        listener = null;
    }

    public RecordWorkoutServiceConnection(Listener listener) {
        this.listener = listener;
    }

    @Override
    public void onServiceConnected(ComponentName name, IBinder service) {
        Timber.d("RecordWorkoutService connected");
        RecordWorkoutService.ServiceBinder binder = (RecordWorkoutService.ServiceBinder) service;
        recordWorkoutService = binder.getService();
        if (listener != null) {
            listener.onRecordWorkoutServiceBound();
        }
    }

    @Override
    public void onServiceDisconnected(ComponentName name) {
        Timber.d("RecordWorkoutService disconnected");
        if (listener != null) {
            listener.onRecordWorkoutServiceUnbound();
        }
        recordWorkoutService = null;
    }

    @Nullable
    public RecordWorkoutService getRecordWorkoutService() {
        return recordWorkoutService;
    }

    public void bindIfStarted(Context context) {
        context = context.getApplicationContext();
        /* It seems that the flags parameter has some errors and the documentation doesn't help
           much. Using {@link Context#BIND_IMPORTANT} and {@link Context#BIND_ADJUST_WITH_ACTIVITY}
           seems to provide the best results so far. For more details, see:
             https://sportstracker.atlassian.net/browse/STFA-659
             http://stackoverflow.com/a/14293528/1081101
         */
        int flags = Context.BIND_AUTO_CREATE | Context.BIND_IMPORTANT | Context.BIND_ADJUST_WITH_ACTIVITY;
        boolean success = context.bindService(new Intent(context, RecordWorkoutService.class),
                this, flags);
        Timber.d("Bind to RecordWorkoutService: %s", success);
    }

    public void unbind(Context context) {
        try {
            context.getApplicationContext().unbindService(this);
        } catch (IllegalArgumentException e) {
            // service already unbound, do nothing
        }
        recordWorkoutService = null;
    }

    public interface Listener {
        /**
         * This is called when the {@link RecordWorkoutService} is bound.
         */
        public void onRecordWorkoutServiceBound();

        /**
         * This is called when the {@link RecordWorkoutService} is unbound.
         */
        public void onRecordWorkoutServiceUnbound();
    }
}
