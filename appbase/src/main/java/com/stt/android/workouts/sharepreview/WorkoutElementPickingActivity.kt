package com.stt.android.workouts.sharepreview

import android.os.Bundle
import android.os.Parcelable
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.stt.android.databinding.ActivityWorkoutShareElementPickBinding
import com.stt.android.ui.utils.WideScreenPaddingDecoration

abstract class WorkoutElementPickingActivity<ElementType : Parcelable, ElementViewHolder : RecyclerView.ViewHolder> :
    AppCompatActivity() {

    protected open val actionbarTitleId = 0

    private lateinit var binding: ActivityWorkoutShareElementPickBinding

    @Suppress("UNCHECKED_CAST")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityWorkoutShareElementPickBinding.inflate(layoutInflater)
        with(binding) {
            setContentView(root)

            setSupportActionBar(toolbar)
            supportActionBar?.apply {
                setDisplayShowHomeEnabled(false)
                setDisplayHomeAsUpEnabled(true)
                title = getString(actionbarTitleId)
            }
            toolbar.setNavigationOnClickListener { onBackPressed() }

            intent.getBundleExtra(INTENT_EXTRA)?.run {
                val options =
                    getParcelableArray(INTENT_EXTRA_WORKOUT_ELEMENT_LIST_KEY)?.let {
                        (it as? Array<ElementType>)?.toList()
                    } ?: emptyList()

                val initialIndex = getInt(INTENT_EXTRA_WORKOUT_ELEMENT_INITIAL_INDEX_KEY)

                recyclerView.apply {
                    setHasFixedSize(true)
                    layoutManager = LinearLayoutManager(
                        this@WorkoutElementPickingActivity,
                        LinearLayoutManager.VERTICAL,
                        false
                    )
                    addItemDecoration(WideScreenPaddingDecoration(resources, theme))
                    adapter = createAdapter(options, initialIndex)
                }
            }
        }
    }

    protected abstract fun createAdapter(
        options: List<ElementType>,
        initialIndex: Int
    ): WorkoutElementPickingAdapter<ElementType, ElementViewHolder>

    companion object {
        private const val packageName = "com.stt.android.workouts.sharepreview.WorkoutElementPickingActivity"
        const val INTENT_EXTRA_WORKOUT_ELEMENT_LIST_KEY = "$packageName.WORKOUT_ELEMENT_LIST"
        const val INTENT_EXTRA_WORKOUT_ELEMENT_INITIAL_INDEX_KEY = "$packageName.WORKOUT_ELEMENT_INITIAL_INDEX"
        const val INTENT_EXTRA = "$packageName.INTENT_EXTRA"

        // Instead of putExtra, we are passing a bundle. For some reason, putExtra does not work for
        // parcelable array list
        fun createDataForStartIntent(
            workoutElementsList: List<Parcelable>,
            initialIndex: Int
        ): Bundle =
            Bundle().apply {
                putInt(INTENT_EXTRA_WORKOUT_ELEMENT_INITIAL_INDEX_KEY, initialIndex)
                putParcelableArray(INTENT_EXTRA_WORKOUT_ELEMENT_LIST_KEY, workoutElementsList.toTypedArray())
            }
    }
}
