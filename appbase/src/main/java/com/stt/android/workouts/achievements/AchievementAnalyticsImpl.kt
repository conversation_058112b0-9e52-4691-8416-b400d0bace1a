package com.stt.android.workouts.achievements

import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.EmarsysAnalyticsImpl
import com.stt.android.domain.achievements.AchievementAnalytics
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.WorkoutHeader
import java.time.Instant
import java.time.ZoneId
import java.util.concurrent.TimeUnit
import javax.inject.Inject

class AchievementAnalyticsImpl
@Inject constructor(
    private val emarsysAnalytics: EmarsysAnalyticsImpl,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
) : AchievementAnalytics {
    override fun trackAchievementReachedEvent(
        achievementType: String,
        workout: WorkoutHeader
    ) {
        val activityType = ActivityType.valueOf(workout.activityTypeId).simpleName
        val totalTimeInMinutes = TimeUnit.SECONDS.toMinutes(workout.totalTime.toLong())
        val startHour =
            Instant.ofEpochMilli(workout.startTime).atZone(ZoneId.systemDefault())
                .toLocalTime().hour

        val properties = AnalyticsProperties().apply {
            put(AnalyticsEventProperty.ACTIVITY_TYPE, activityType)
            put(AnalyticsEventProperty.ACHIEVEMENT_TYPE, achievementType)
            put(AnalyticsEventProperty.ACHIEVEMENT_WORKOUT_DURATION, totalTimeInMinutes)
            put(AnalyticsEventProperty.ACHIEVEMENT_WORKOUT_DISTANCE, workout.totalDistance)
            put(AnalyticsEventProperty.ACHIEVEMENT_WORKOUT_START_HOUR, startHour)
        }

        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.ACHIEVEMENT_REACHED,
            properties
        )
        emarsysAnalytics.trackEventWithProperties(
            AnalyticsEvent.ACHIEVEMENT_REACHED,
            properties.map
        )
    }
}
