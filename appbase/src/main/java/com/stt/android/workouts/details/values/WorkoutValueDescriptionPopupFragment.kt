package com.stt.android.workouts.details.values

import android.app.Dialog
import android.content.Intent
import android.os.Bundle
import android.util.Patterns
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.net.toUri
import androidx.core.os.BundleCompat
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.stt.android.R
import com.stt.android.aerobiczone.AerobicZoneDialogFragmentCreator
import com.stt.android.aerobiczone.AerobicZonesInfoSheet
import com.stt.android.databinding.WorkoutValueDescriptionPopupFragmentBinding
import com.stt.android.ui.utils.SmartBottomSheetDialogFragment
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class WorkoutValueDescriptionPopupFragment : SmartBottomSheetDialogFragment() {

    @Inject
    lateinit var aerobicZoneDialogFragmentCreator: AerobicZoneDialogFragmentCreator

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        if (dialog is BottomSheetDialog) {
            dialog.behavior.state = BottomSheetBehavior.STATE_EXPANDED
        }
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val binding =
            WorkoutValueDescriptionPopupFragmentBinding.inflate(inflater, container, false)
        val workoutId = requireArguments().getInt(EXTRA_WORKOUT_ID)
        val workoutKey = requireArguments().getString(EXTRA_WORKOUT_KEY) // Workout key is optional
        val workoutValue = requireNotNull(
            BundleCompat.getParcelable(requireArguments(), EXTRA_WORKOUT_VALUE, WorkoutValue::class.java)
        )
        binding.workoutValue = workoutValue
        setupImage(binding, workoutValue)
        setupUrl(binding, workoutValue, workoutId, workoutKey)
        setupSecondaryDescription(binding, workoutValue)
        return binding.root
    }

    private fun setupImage(
        binding: WorkoutValueDescriptionPopupFragmentBinding,
        workoutValue: WorkoutValue
    ) {
        with(binding.descriptionImage) {
            if (workoutValue.descriptionImageResId != null) {
                visibility = View.VISIBLE
                setImageResource(workoutValue.descriptionImageResId)
            } else {
                visibility = View.GONE
            }
        }
    }

    private fun setupUrl(
        binding: WorkoutValueDescriptionPopupFragmentBinding,
        workoutValue: WorkoutValue,
        workoutId: Int,
        workoutKey: String?,
    ) {
        if (workoutValue.descriptionUrl != null) {
            if (workoutValue.urlText != null) {
                binding.textDescriptionUrl.setText(workoutValue.urlText)
            } else {
                binding.textDescriptionUrl.setText(R.string.item_description_link)
            }
            binding.textDescriptionUrlContainer.visibility = View.VISIBLE

            binding.textDescriptionUrl.setOnClickListener {
                when {
                    isUrl(workoutValue.descriptionUrl) -> {
                        val browserIntent =
                            Intent(Intent.ACTION_VIEW, workoutValue.descriptionUrl.toUri())
                        startActivity(browserIntent)
                    }

                    isNavigateTo(workoutValue.descriptionUrl) -> {
                        val screenId = workoutValue.descriptionUrl.removePrefix(PREFIX_NAVIGATION)
                        // Currently we only navigate to aerobic bottom sheets, this can be improved later when we support other destinations
                        when (screenId) {
                            "aerobic_heart_rate_zones" -> {
                                AerobicZonesInfoSheet.HEART_RATE
                            }

                            "aerobic_pace_zones" -> {
                                AerobicZonesInfoSheet.PACE
                            }

                            "aerobic_power_zones" -> {
                                AerobicZonesInfoSheet.POWER
                            }

                            else -> {
                                Timber.d("screenId: [${screenId}] is not handled")
                                null
                            }
                        }?.let {
                            val dialog = aerobicZoneDialogFragmentCreator.create(
                                dest = it,
                                workoutId = workoutId,
                                workoutKey = workoutKey,
                            )
                            dialog.show(parentFragmentManager, screenId)
                        }
                    }

                    else -> {
                        Timber.d("Unknown url scheme: [${workoutValue.descriptionUrl}] ")
                    }
                }
            }
        } else {
            binding.textDescriptionUrlContainer.visibility = View.GONE
        }
    }

    private fun setupSecondaryDescription(
        binding: WorkoutValueDescriptionPopupFragmentBinding,
        workoutValue: WorkoutValue
    ) {
        if (workoutValue.extraInfoTextResId != null) {
            binding.secondaryTextDescription.visibility = View.VISIBLE
            binding.secondaryTextDescription.text = getText(workoutValue.extraInfoTextResId)
        } else {
            binding.secondaryTextDescription.visibility = View.GONE
        }
    }

    private fun isUrl(url: String): Boolean = Patterns.WEB_URL.matcher(url).matches()
    private fun isNavigateTo(url: String): Boolean = url.startsWith(PREFIX_NAVIGATION)

    companion object {
        private const val PREFIX_NAVIGATION = "navigateTo://" // Make sure this matches what is used in `itemDescriptions.json`

        const val TAG = "WorkoutValueDescriptionPopupFragment"
        private const val EXTRA_WORKOUT_VALUE = "EXTRA_WORKOUT_VALUE"
        const val EXTRA_WORKOUT_ID = "EXTRA_WORKOUT_ID"
        const val EXTRA_WORKOUT_KEY = "EXTRA_WORKOUT_KEY"

        @JvmStatic
        fun newInstance(
            workoutValue: WorkoutValue,
            workoutId: Int,
            workoutKey: String?,
        ): WorkoutValueDescriptionPopupFragment {
            return WorkoutValueDescriptionPopupFragment().apply {
                arguments = Bundle().apply {
                    putParcelable(EXTRA_WORKOUT_VALUE, workoutValue)
                    putInt(EXTRA_WORKOUT_ID, workoutId)
                    workoutKey?.let { putString(EXTRA_WORKOUT_KEY, it) }
                }
            }
        }
    }
}
