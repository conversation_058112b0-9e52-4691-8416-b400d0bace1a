package com.stt.android.workouts.comments

import com.stt.android.common.coroutines.ORMLITE
import com.stt.android.controllers.WorkoutCommentController
import com.stt.android.domain.comments.WorkoutCommentDataSource
import com.stt.android.domain.workouts.comment.DomainWorkoutComment
import com.stt.android.workoutdetail.comments.WorkoutComment
import kotlinx.coroutines.withContext
import javax.inject.Inject

class WorkoutCommentOrmLiteDataSource
@Inject constructor(
    private val workoutCommentController: WorkoutCommentController
) : WorkoutCommentDataSource {
    override suspend fun removeByWorkoutKey(workoutKey: String) = withContext<Unit>(ORMLITE) {
        workoutCommentController.removeByWorkoutKey(workoutKey)
    }

    override suspend fun removeComment(commentKey: String) = withContext<Unit>(ORMLITE) {
        workoutCommentController.removeByCommentKey(commentKey)
    }

    override suspend fun saveComment(workoutKey: String?, comments: List<DomainWorkoutComment>) =
        withContext(ORMLITE) {
            workoutCommentController.store(comments.map { it.toWorkoutComment(workoutKey) })
        }
}

fun DomainWorkoutComment.toWorkoutComment(workoutKey: String?): WorkoutComment {
    return WorkoutComment(
        key,
        workoutKey,
        message,
        username,
        realname,
        profilePictureUrl,
        timestamp
    )
}
