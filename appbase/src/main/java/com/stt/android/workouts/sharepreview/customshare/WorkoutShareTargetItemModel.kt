package com.stt.android.workouts.sharepreview.customshare

import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.stt.android.R
import com.stt.android.common.KotlinEpoxyHolder
import timber.log.Timber

@EpoxyModelClass
abstract class WorkoutShareTargetItemModel :
    EpoxyModelWithHolder<WorkoutShareTargetItemViewHolder>() {

    @EpoxyAttribute
    var shareTarget: ShareTarget? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var onSelectTargetListener: View.OnClickListener? = null

    override fun getDefaultLayout() = R.layout.model_workout_share_intent_target

    override fun bind(holder: WorkoutShareTargetItemViewHolder) {
        holder.rootLayout.setOnClickListener(onSelectTargetListener)
        when (val mShareTarget = shareTarget) {
            is ShareTarget.CustomTarget -> {
                holder.icon.setImageResource(mShareTarget.iconRes)
                holder.name.setText(mShareTarget.nameRes)
            }

            is ShareTarget.SaveToMedia -> {
                holder.icon.setImageResource(R.drawable.share_save)
                holder.name.setText(R.string.save)
            }

            is ShareTarget.DelegateToOS -> {
                holder.icon.setImageResource(R.drawable.share_other)
                holder.name.setText(R.string.settings_other)
            }
            else -> {
                Timber.w("Unknown share target: $shareTarget")
            }
        }
    }
}

class WorkoutShareTargetItemViewHolder : KotlinEpoxyHolder() {
    val rootLayout by bind<View>(R.id.container)
    val icon by bind<ImageView>(R.id.icon)
    val name by bind<TextView>(R.id.name)
}
