package com.stt.android.workouts.hardware;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.text.TextUtils;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import com.stt.android.STTApplication;
import com.stt.android.bluetooth.BleCadenceModel;
import com.stt.android.bluetooth.BluetoothDeviceManager;
import com.stt.android.bluetooth.CadenceEventListener;
import com.stt.android.cadence.CadenceHelper;
import javax.inject.Inject;
import timber.log.Timber;

public class BleCadenceConnectionMonitor extends CadenceConnectionMonitor {
    @Inject
    @Nullable
    BleCadenceModel bleCadenceModel;
    @Inject
    LocalBroadcastManager localBroadcastManager;

    private final Context context;
    private final CadenceEventListener listener;

    private final BroadcastReceiver bluetoothStateListener = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, -1) == BluetoothAdapter.STATE_ON) {
                context.unregisterReceiver(this);
                try {
                    requestUpdatesFromPrePairedDevice();
                } catch (Exception e) {
                    Timber.w(e, "Can't connect to speed/cadence sensor");
                }
            }
        }
    };

    /**
     * Starts requesting updates from the cadence/speed sensor previously setup by the user.
     * If bluetooth is disabled then we'll ask updates once it becomes available.
     *
     * @param context application context
     * @param listener where to forward the updates
     * @return a new connection monitor instance to control it
     * @throws IllegalStateException if there isn't a previously set up cadence/speed sensor
     */
    @NonNull
    static BleCadenceConnectionMonitor requestUpdates(Context context,
        CadenceEventListener listener) throws IllegalStateException {
        BleCadenceConnectionMonitor cadenceConnectionMonitor =
            new BleCadenceConnectionMonitor(context, listener);
        cadenceConnectionMonitor.requestUpdates();
        return cadenceConnectionMonitor;
    }

    private BleCadenceConnectionMonitor(Context context, CadenceEventListener listener) {
        super();
        STTApplication.getComponent().inject(this);
        this.context = context;
        this.listener = listener;
    }

    private void requestUpdates() throws IllegalStateException {
        if (BluetoothDeviceManager.getBluetoothAdapter(context).isEnabled()) {
            requestUpdatesFromPrePairedDevice();
        } else {
            ContextCompat.registerReceiver(
                context,
                bluetoothStateListener,
                new IntentFilter(BluetoothAdapter.ACTION_STATE_CHANGED),
                ContextCompat.RECEIVER_EXPORTED
            );
        }
    }

    @SuppressWarnings("WeakerAccess")
    void requestUpdatesFromPrePairedDevice() throws IllegalStateException {
        String pairedDeviceAddress = CadenceHelper.getKnownCadenceAddress(context);
        if (TextUtils.isEmpty(pairedDeviceAddress)) {
            throw new IllegalStateException("Speed/Cadence sensor hasn't been set up yet");
        } else {
            Timber.d("Re-connecting to an already discovered BLE cadence with address %s",
                pairedDeviceAddress);
            BluetoothDevice device = BluetoothDeviceManager.getBluetoothAdapter(context)
                .getRemoteDevice(pairedDeviceAddress);
            try {
                requestUpdatesFrom(device);
            } catch (Exception e) {
                throw new IllegalStateException("Failed to connect to BLE cadence sensor", e);
            }
        }
    }

    private void requestUpdatesFrom(BluetoothDevice device) throws NullPointerException, SecurityException {
        if (bleCadenceModel != null) {
            bleCadenceModel.addListener(listener);
            bleCadenceModel.requestUpdates(device);
        }
    }

    @Override
    public boolean isConnected() {
        return bleCadenceModel != null && bleCadenceModel.isConnected();
    }

    @Override
    public void close() {
        try {
            context.unregisterReceiver(bluetoothStateListener);
        } catch (IllegalArgumentException ignored) {
            // not registered, or already unregistered
        }
        if (bleCadenceModel != null) {
            bleCadenceModel.removeListener(listener);
            bleCadenceModel.stopUpdates();
        }
    }
}
