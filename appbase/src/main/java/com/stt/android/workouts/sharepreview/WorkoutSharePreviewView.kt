package com.stt.android.workouts.sharepreview

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.TextureView
import android.widget.FrameLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.graphics.createBitmap
import androidx.core.view.doOnLayout
import androidx.core.view.isVisible
import androidx.transition.AutoTransition
import androidx.transition.Transition
import androidx.transition.TransitionListenerAdapter
import androidx.transition.TransitionManager
import coil3.load
import coil3.request.Disposable
import com.stt.android.R
import com.stt.android.colorfultrack.HeartRateWorkoutColorfulTrackLoader
import com.stt.android.colorfultrack.PaceWorkoutColorfulTrackLoader
import com.stt.android.colorfultrack.PowerWorkoutColorfulTrackLoader
import com.stt.android.colorfultrack.WorkoutColorfulTrackLoader
import com.stt.android.colorfultrack.WorkoutColorfulTrackMapData
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.core.domain.GraphType
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.databinding.ShareImagePreviewItemBinding
import com.stt.android.divetrack.DiveTrackSettings
import com.stt.android.divetrack.ui.addDiveTrackView
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.infomodel.SummaryGraph
import com.stt.android.infomodel.SummaryItem
import com.stt.android.maps.MapSnapshotSpec
import com.stt.android.maps.bindMapSnapshot
import com.stt.android.multimedia.sportie.SportieAddPhoto
import com.stt.android.multimedia.sportie.SportieAspectRatio
import com.stt.android.multimedia.sportie.SportieDiveTrack
import com.stt.android.multimedia.sportie.SportieHelper
import com.stt.android.multimedia.sportie.SportieImage
import com.stt.android.multimedia.sportie.SportieInfo
import com.stt.android.multimedia.sportie.SportieItem
import com.stt.android.multimedia.sportie.SportieMap
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@SuppressLint("ViewConstructor")
@AndroidEntryPoint
class WorkoutSharePreviewView
@JvmOverloads constructor(
    private val sportieItem: SportieItem,
    private val measurementUnit: MeasurementUnit,
    sharePreviewClickListener: SportieOverlayViewClickListener,
    private var aspectRatio: SportieAspectRatio,
    private var editMode: Boolean,
    initialSportieInfo: SportieInfo,
    private val heartRateWorkoutColorfulTrackLoader: HeartRateWorkoutColorfulTrackLoader,
    private val paceWorkoutColorfulTrackLoader: PaceWorkoutColorfulTrackLoader,
    private val powerWorkoutColorfulTrackLoader: PowerWorkoutColorfulTrackLoader,
    initialSummaryItems: List<SummaryItem>,
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    @Inject
    lateinit var coroutinesDispatchers: CoroutinesDispatchers

    private val scope: CoroutineScope by lazy {
        CoroutineScope(coroutinesDispatchers.main + SupervisorJob())
    }

    private var imageLoadingDisposable: Disposable? = null

    private val colorfulPolylinesMap = hashMapOf<String, MapSnapshotSpec.ColorfulPolylines>()

    private val binding = ShareImagePreviewItemBinding.inflate(
        LayoutInflater.from(context),
        this,
        true,
    )

    init {
        with(binding.sportieOverlay) {
            setClickListener(
                sportieOverlayViewClickListener = sharePreviewClickListener,
                enableEditIconClickAnimation = sportieItem !is SportieDiveTrack,
            )
            setSportieInfoIndexes(initialSportieInfo)
        }

        refreshAspectRatio(false)
        setEditMode(editMode, false)

        doOnLayout {
            scope.launch {
                runSuspendCatching {
                    showWorkoutDetail(initialSportieInfo, initialSummaryItems)
                    loadImageOrMap()
                    startEditIconAnimationDelayed()
                }.onFailure { e ->
                    Timber.w(e, "Failed to prepare sportie image")
                }
            }
        }
    }

    private suspend fun showWorkoutDetail(
        sportieInfo: SportieInfo,
        initialSummaryItems: List<SummaryItem>,
    ) {
        with(binding.sportieOverlay) {
            isVisible = true
            setWorkout(
                sportieItem,
                measurementUnit,
                sportieInfo,
                initialSummaryItems,
            )
        }
    }

    private fun loadImageOrMap() {
        when (sportieItem) {
            is SportieImage -> when (sportieItem.imageInformation) {
                is DefaultImageResInformation -> binding.imageView
                    .setImageResource(sportieItem.imageInformation.defaultImageResId)

                else -> imageLoadingDisposable = binding.imageView
                    .load(sportieItem.imageInformation.getHighResUri(context))
            }

            is SportieMap -> if (colorfulTrackEnabled() && sportieItem.geoPoints.isNotEmpty()) {
                loadColorfulTrackData()
            } else {
                bindMapSnapshotWithoutDashline(sportieItem.mapSnapshotSpec)
            }

            is SportieDiveTrack -> with(binding.diveTrackHolder) {
                removeAllViews()

                addDiveTrackView(
                    diveTrack = sportieItem.diveTrack,
                    settings = DiveTrackSettings(
                        supportGestures = true,
                        shouldDestroyWhenViewDetached = true,
                        cameraSetting = DiveTrackSettings.CameraSetting.FAR,
                        showBottomPlane = false,
                    ),
                    onCameraMoved = null,
                )
            }

            is SportieAddPhoto -> Unit
        }
    }

    private fun bindMapSnapshotWithoutDashline(snapshotSpec: MapSnapshotSpec) {
        // we don't support dash line in preview, so remove it here
        val updatedSnapshotSpec = when (snapshotSpec) {
            is MapSnapshotSpec.Sample,
            is MapSnapshotSpec.Route,
            is MapSnapshotSpec.Polylines,
            is MapSnapshotSpec.WorkoutPolyline,
            is MapSnapshotSpec.Workout,
            is MapSnapshotSpec.DashboardMapWidget,
            is MapSnapshotSpec.PopularRoute, -> snapshotSpec

            is MapSnapshotSpec.ColorfulPolylines -> snapshotSpec.copy(
                colorfulTrackMapData = snapshotSpec.colorfulTrackMapData,
            )
        }
        binding.imageView.bindMapSnapshot(updatedSnapshotSpec)
    }

    private fun colorfulTrackEnabled(): Boolean = when (binding.sportieOverlay.graphType) {
        GraphType.Summary(SummaryGraph.HEARTRATE),
        GraphType.Summary(SummaryGraph.RECOVERYHRINTHREEMINS),
        GraphType.Summary(SummaryGraph.PACE),
        GraphType.Summary(SummaryGraph.POWER) -> true

        null,
        is GraphType.Summary,
        is GraphType.SuuntoPlus -> false
    }

    private fun loadColorfulTrackData() {
        if (sportieItem !is SportieMap) {
            return
        }

        val graphType = binding.sportieOverlay.graphType ?: return
        val polylines = colorfulPolylinesMap[graphType.key]
        if (polylines != null) {
            bindMapSnapshotWithoutDashline(polylines)
        } else {
            scope.launch {
                graphType.getColorfulTrackLoader()
                    ?.loadColorfulTrack(
                        sportieItem.geoPoints,
                        sportieItem.workoutHeader,
                        sportieItem.sml,
                        sportieItem.originalHrEvents,
                        true
                    )
                    ?.let { mapData ->
                        if (mapData.activityRoutesWithColor.isEmpty()) {
                            bindMapSnapshotWithoutDashline(sportieItem.mapSnapshotSpec)
                        } else {
                            loadColorfulTrackMap(graphType, mapData)
                        }
                    }
            }
        }
    }

    private fun GraphType.getColorfulTrackLoader(): WorkoutColorfulTrackLoader? = when (this) {
        GraphType.Summary(SummaryGraph.HEARTRATE), GraphType.Summary(SummaryGraph.RECOVERYHRINTHREEMINS) -> heartRateWorkoutColorfulTrackLoader
        GraphType.Summary(SummaryGraph.PACE) -> paceWorkoutColorfulTrackLoader
        GraphType.Summary(SummaryGraph.POWER) -> powerWorkoutColorfulTrackLoader
        is GraphType.Summary,
        is GraphType.SuuntoPlus -> null
    }

    private fun startEditIconAnimationDelayed() {
        postDelayed(
            { binding.sportieOverlay.startEditIconShowAnimation() },
            EDIT_ICON_ANIMATION_START_DELAY_MS,
        )
    }

    private fun loadColorfulTrackMap(
        graphType: GraphType,
        colorfulTrackMapData: WorkoutColorfulTrackMapData,
    ) {
        if (sportieItem !is SportieMap) {
            return
        }

        (sportieItem.mapSnapshotSpec as? MapSnapshotSpec.Workout)
            ?.let { workout ->
                MapSnapshotSpec.ColorfulPolylines(
                    colorfulTrackMapData = colorfulTrackMapData,
                    graphType = graphType,
                    workoutId = workout.workoutId,
                    width = workout.width,
                    height = workout.height,
                    explicitProviderName = workout.explicitProviderName,
                    explicitMapType = workout.explicitMapType,
                    explicitRoutePadding = workout.explicitRoutePadding,
                )
            }
            ?.let { polylines ->
                colorfulPolylinesMap[graphType.key] = polylines
                bindMapSnapshotWithoutDashline(polylines)
            }
    }

    fun setAspectRatio(newAspectRatio: SportieAspectRatio, animated: Boolean = false) {
        if (aspectRatio != newAspectRatio) {
            aspectRatio = newAspectRatio
            refreshAspectRatio(animated)
        }
    }

    fun setEditMode(enabled: Boolean, showKeyboard: Boolean) {
        editMode = enabled
        binding.sportieOverlay.setEditMode(editMode, showKeyboard)
    }

    fun showEditIcons() {
        binding.sportieOverlay.startEditIconShowAnimation()
    }

    override fun onDetachedFromWindow() {
        scope.cancel()
        imageLoadingDisposable?.dispose()
        super.onDetachedFromWindow()
    }

    private fun refreshAspectRatio(animated: Boolean) {
        val (width, height) = when (sportieItem) {
            is SportieImage -> SportieHelper.getTargetDimensions(sportieItem.imageInformation)
            else -> 1 to 1 // Other items like maps are squares
        }

        /*
        in case of aspectRatio = ORIGINAL:
        we have to scale the view so that it stays inside of the bounds of its parents,
        so we always scale in reference of the smallest dimension
         */
        val refDimension = if (width <= height) "W" else "H"
        val aspectRatioString = when (aspectRatio) {
            SportieAspectRatio.UNKNOWN, SportieAspectRatio.ORIGINAL -> "$refDimension,$width:$height"
            SportieAspectRatio.ONE_TO_ONE -> "W,1:1"
        }
        if (animated) {
            binding.sportieOverlay.findViewById<TextView>(R.id.descriptionText).also {
                it.alpha = 0f
                post {
                    applyAspectRatio(aspectRatioString)
                    TransitionManager.beginDelayedTransition(
                        this,
                        AutoTransition()
                            .excludeTarget(it, true)
                            .addListener(object : TransitionListenerAdapter() {
                                override fun onTransitionEnd(transition: Transition) {
                                    it.animate().alpha(1f).start()
                                }
                            })
                    )
                }
            }
        } else {
            applyAspectRatio(aspectRatioString)
        }
    }

    private fun applyAspectRatio(aspectRatioString: String) = with(binding) {
        ConstraintSet().apply {
            clone(sportieScaledContentWrapper)
            setDimensionRatio(R.id.sportieScaledContent, aspectRatioString)
        }.applyTo(sportieScaledContentWrapper)
    }

    fun onWorkoutValueLabelSelected(selectedTextViewIndex: Int, workoutValueIndex: Int) {
        binding.sportieOverlay.onWorkoutLabelSelected(selectedTextViewIndex, workoutValueIndex)
    }

    fun updateShowActivityAndStartTimeState(
        showNothing: Boolean,
        showActivityType: Boolean,
        showActivityStartTime: Boolean
    ) {
        binding.sportieOverlay.updateShowActivityAndStartTimeState(showNothing, showActivityType, showActivityStartTime)
    }

    fun getCurrentSportieInfo() = binding.sportieOverlay.getCurrentSportieInfo()

    fun getColorfulPolylines(): MapSnapshotSpec.ColorfulPolylines? {
        return if (colorfulTrackEnabled()) {
            val graphType = binding.sportieOverlay.graphType
            colorfulPolylinesMap[graphType?.key ?: ""]
        } else {
            null
        }
    }

    fun getDiveTrack(): Bitmap? = if (sportieItem is SportieDiveTrack) {
        (binding.diveTrackHolder.getChildAt(0) as? TextureView)?.let { textureView ->
            val bitmap = createBitmap(textureView.width, textureView.height)
            textureView.getBitmap(bitmap)
            bitmap
        }
    } else {
        null
    }

    fun onGraphSelected(index: Int) {
        scope.launch {
            runSuspendCatching {
                binding.sportieOverlay
                    .onGraphSelected(sportieItem, index, measurementUnit)

                loadImageOrMap()
            }.onFailure { e ->
                Timber.w(e, "Error on setting graph type")
            }
        }
    }

    private companion object {
        const val EDIT_ICON_ANIMATION_START_DELAY_MS = 500L
    }
}
