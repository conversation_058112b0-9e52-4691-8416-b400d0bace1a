package com.stt.android.workouts.wearable;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import androidx.annotation.NonNull;
import androidx.core.app.JobIntentService;
import android.text.TextUtils;
import com.google.android.gms.common.GoogleApiAvailability;
import com.google.android.gms.common.api.AvailabilityException;
import com.google.android.gms.tasks.Tasks;
import com.google.android.gms.wearable.DataClient;
import com.google.android.gms.wearable.Node;
import com.google.android.gms.wearable.NodeClient;
import com.google.android.gms.wearable.PutDataRequest;
import com.google.android.gms.wearable.Wearable;
import com.stt.android.core.bridge.WearConstants;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicBoolean;
import timber.log.Timber;

import static com.stt.android.services.JobIdsKt.JOB_ID_WEARABLE_CLEANUP_SERVICE;

public class WearableCleanupService extends JobIntentService {

    private static final String KEY_RESCHEDULE_ON_ERROR = "KEY_RESCHEDULE_ON_ERROR";

    /**
     * Enqueuing work and rescheduling only once in case of error.
     */
    public static void enqueueWork(Context context) {
        enqueueWork(context, true);
    }

    public static void enqueueWork(Context context, boolean rescheduleOnError) {
        Intent intent = new Intent(context, WearableCleanupService.class);
        intent.putExtra(KEY_RESCHEDULE_ON_ERROR, rescheduleOnError);
        enqueueWork(context, WearableCleanupService.class, JOB_ID_WEARABLE_CLEANUP_SERVICE, intent);
    }

    private AtomicBoolean stopCurrentJob = new AtomicBoolean(false);

    @Override
    public boolean onStopCurrentWork() {
        stopCurrentJob.set(true);
        return super.onStopCurrentWork();
    }

    @Override
    protected void onHandleWork(@NonNull Intent intent) {
        stopCurrentJob.set(false);
        try {
            NodeClient nodeClient = Wearable.getNodeClient(this);
            Tasks.await(GoogleApiAvailability.getInstance().checkApiAvailability(nodeClient));

            Node localNode = Tasks.await(nodeClient.getLocalNode());

            if (TextUtils.isEmpty(localNode.getId())) {
                // nothing to do
                return;
            }

            DataClient dataClient = Wearable.getDataClient(this);

            if (stopCurrentJob.get()) {
                return;
            }

            Tasks.await(
                dataClient.deleteDataItems(
                    new Uri.Builder()
                        .scheme(PutDataRequest.WEAR_URI_SCHEME).authority(localNode.getId())
                        .path(WearConstants.ONGOING_WORKOUT_RECORDING_STATE).build()),
                30L, TimeUnit.SECONDS);

            if (stopCurrentJob.get()) {
                return;
            }

            Tasks.await(dataClient.deleteDataItems(
                new Uri.Builder()
                    .scheme(PutDataRequest.WEAR_URI_SCHEME).authority(localNode.getId())
                    .path(WearConstants.ONGOING_WORKOUT_SNAPSHOT).build()),
                30L, TimeUnit.SECONDS);
        } catch (ExecutionException | InterruptedException | TimeoutException e) {
            if (e instanceof ExecutionException) {
                ExecutionException executionException = (ExecutionException)e;
                Throwable cause = executionException.getCause();
                if (cause instanceof AvailabilityException) {
                    Timber.d("Error running WearableCleanupService, got AvailabilityException");
                    return;
                }
            }

            boolean rescheduleOnError = intent.getBooleanExtra(KEY_RESCHEDULE_ON_ERROR, false);
            Timber.w(e, "Error running WearableCleanupService, rescheduling: %s", rescheduleOnError);
            if (rescheduleOnError) {
                enqueueWork(this, false);
            }
        }
    }
}
