package com.stt.android.workouts.sharepreview.customshare

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.ResolveInfo
import android.net.Uri
import androidx.core.app.ShareCompat
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.analytics.FirebaseAnalyticsTracker
import com.stt.android.analytics.ShareBroadcastReceiver
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.multimedia.sportie.SportieSelection
import com.stt.android.multimedia.sportie.SportieShareSource
import com.stt.android.multimedia.sportie.SportieShareType
import com.stt.android.network.interfaces.ANetworkProvider
import com.stt.android.sharingplatform.ShareResultHandler
import com.stt.android.ui.extensions.targetWorkoutVisibility

interface WorkoutShareHelper {

    val emarsysAnalytics: EmarsysAnalytics

    val firebaseAnalyticsTracker: FirebaseAnalyticsTracker

    val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker

    /**
     * Returns true if we should handle certain intent sharing in-app, instead of using system api.
     */
    fun hasCustomIntentHandling(): Boolean

    /**
     * Retrieves list of [ShareTarget]s for workout link sharing
     */
    fun getCustomShareLinkTargets(context: Context): List<ShareTarget>

    /**
     * Retrieves list of [ShareTarget]s for workout image sharing
     */
    fun getCustomShareImageTargets(context: Context): List<ShareTarget>

    /**
     * Retrieves list of [ShareTarget]s for workout video sharing
     */
    fun getCustomShareVideoTargets(context: Context): List<ShareTarget>

    /**
     * Share workout link to a [ShareTarget.CustomTarget], implementation left abstract.
     * For an example check [WeChatWorkoutShareHelper]
     */
    fun shareLinkToCustomTarget(
        activity: Activity,
        customTarget: ShareTarget.CustomTarget,
        workoutHeader: WorkoutHeader,
        source: SportieShareSource
    )

    /**
     * Share workout image to a [ShareTarget.CustomTarget], implementation left abstract.
     * For an example check [WeChatWorkoutShareHelper]
     */
    fun shareImageToCustomTarget(
        activity: Activity,
        customTarget: ShareTarget.CustomTarget,
        header: WorkoutHeader,
        imageUri: Uri,
        sportieSelection: SportieSelection
    )

    fun shareVideoToCustomTarget(
        activity: Activity,
        customTarget: ShareTarget.CustomTarget,
        videoUri: Uri,
        shareType: SportieShareType,
    )

    /**
     * Utility to be called if the share is not done through implicit intent
     */
    fun sendLinkShareAnalytics(
        source: SportieShareSource,
        analyticsTarget: String
    ) {
        val analytics = buildLinkShareAnalytics(source)
        analytics.put(AnalyticsEventProperty.TARGET, analyticsTarget)
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.SHARE_WORKOUT, analytics)
        emarsysAnalytics.trackEventWithProperties(AnalyticsEvent.SHARE_WORKOUT, analytics.map)
        firebaseAnalyticsTracker.trackEvent(AnalyticsEvent.SHARE_WORKOUT, analytics)
    }

    /**
     * Utility to be called if the share is not done through implicit intent
     */
    fun sendImageShareAnalytics(
        workoutHeader: WorkoutHeader,
        sportieSelection: SportieSelection,
        analyticsTarget: String
    ) {
        val analytics = buildImageShareAnalytics(workoutHeader, sportieSelection)
        analytics.put(AnalyticsEventProperty.TARGET, analyticsTarget)
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.SHARE_WORKOUT, analytics)
        emarsysAnalytics.trackEventWithProperties(AnalyticsEvent.SHARE_WORKOUT, analytics.map)
        firebaseAnalyticsTracker.trackEvent(AnalyticsEvent.SHARE_WORKOUT, analytics)
    }

    /**
     * Share workout image with standard system implicit intent, and sends analytics
     */
    fun sendImplicitImageShareIntent(
        activity: Activity,
        workoutHeader: WorkoutHeader,
        imageUri: Uri,
        sportieSelection: SportieSelection,
        numPhotosAdded: Int
    ) {
        val analytics = buildImageShareAnalytics(workoutHeader, sportieSelection)
        analytics.put(AnalyticsEventProperty.NUM_PHOTOS_ADDED, numPhotosAdded)
        ShareBroadcastReceiver.shareWorkoutImage(activity, imageUri, workoutHeader, analytics)
    }

    fun sendVideoShareAnalytics(
        shareType: SportieShareType,
        analyticsTarget: String
    ) {
        val analytics = AnalyticsProperties().apply {
            put(AnalyticsEventProperty.TYPE, shareType.type)
            put(AnalyticsEventProperty.TARGET, analyticsTarget)
        }
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.SHARE_WORKOUT, analytics)
        emarsysAnalytics.trackEventWithProperties(AnalyticsEvent.SHARE_WORKOUT, analytics.map)
        firebaseAnalyticsTracker.trackEvent(AnalyticsEvent.SHARE_WORKOUT, analytics)
    }

    fun sendImplicitVideoShareIntent(
        activity: Activity,
        videoUri: Uri,
        shareType: SportieShareType,
    ) {
        val analytics = AnalyticsProperties().apply {
            put(AnalyticsEventProperty.TYPE, shareType.type)
        }
        ShareBroadcastReceiver.shareWorkoutVideo(activity, videoUri, analytics)
    }

    /**
     *  Share workout link with standard system implicit intent, and sends analytics
     */
    fun sendImplicitWorkoutLinkShareIntent(
        activity: Activity,
        workoutHeader: WorkoutHeader,
        source: SportieShareSource,
        numPhotosAdded: Int = 0
    ) {
        val shareIntent = createWorkoutLinkSharingIntent(workoutHeader, activity, source)
        val analytics = buildLinkShareAnalytics(source)
        analytics.put(AnalyticsEventProperty.NUM_PHOTOS_ADDED, numPhotosAdded)
        ShareBroadcastReceiver.shareWorkout(activity, shareIntent, analytics)
    }

    private fun buildLinkShareAnalytics(source: SportieShareSource): AnalyticsProperties =
        AnalyticsProperties().apply {
            val type = if (source == SportieShareSource.WORKOUT_SUMMARY) {
                SportieShareType.LINK.type
            } else {
                SportieShareType.LINK_3D.type
            }
            put(AnalyticsEventProperty.TYPE, type)
            put(AnalyticsEventProperty.SOURCE, source.type)
        }

    private fun buildImageShareAnalytics(
        workoutHeader: WorkoutHeader,
        sportieSelection: SportieSelection
    ): AnalyticsProperties = sportieSelection.toAnalyticsProperties().apply {
        put(
            AnalyticsEventProperty.TARGET_WORKOUT_VISIBILITY,
            workoutHeader.targetWorkoutVisibility()
        )
        put(AnalyticsEventProperty.NUM_PHOTOS, workoutHeader.pictureCount)
        put(AnalyticsEventProperty.HAS_DESCRIPTION, !workoutHeader.description.isNullOrBlank())
        put(AnalyticsEventProperty.ACTIVITY_TYPE, workoutHeader.activityType?.simpleName)
    }

    private fun createWorkoutLinkSharingIntent(
        workoutHeader: WorkoutHeader,
        launchingActivity: Activity,
        source: SportieShareSource,
        target: ResolveInfo? = null
    ): Intent {
        val text = ANetworkProvider.buildSecureWebWorkoutUrl(
            workoutHeader.username,
            workoutHeader.key,
            source,
        )
        val shareIntent = ShareCompat.IntentBuilder(launchingActivity)
            .setType("text/plain")
            .setText(text)
            .intent

        if (target != null && target.activityInfo.targetActivity != null) {
            shareIntent.setClassName(
                target.activityInfo.packageName,
                target.activityInfo.targetActivity
            )
        }

        shareIntent.putExtra(Intent.EXTRA_TEXT, text)

        return shareIntent
    }

    /**
     * true: jump to summary & longScreenshot share(suunto flavor)
     * false: jump to summary share(WorkoutSharePreviewActivity)
     */
    fun showMultipleWorkoutShareWays(): Boolean = false

    /**
     * to summary share and longScreenshot share
     */
    fun toMultipleWorkoutShareWays(
        context: Context,
        workoutHeader: WorkoutHeader,
        imageIndex: Int,
        watchName: String,
        byScreenshot: Boolean = false
    ) {}

    fun handleWeiboShareResult(intent: Intent, shareResultHandler: ShareResultHandler?) {}

    fun setWeChatShareResultHandler(handler: ShareResultHandler?) {
    }

    fun shareImageToOS(
        activity: Activity,
        workoutHeader: WorkoutHeader,
        imageUri: Uri,
        sportieSelection: SportieSelection,
        numPhotosAdded: Int
    ) {}

    fun shareVideoToOS(activity: Activity, videoUri: Uri, shareType: SportieShareType) {}

    fun saveToPhotoAlbum(context: Context, imageUri: Uri) {}

    /**
     * return the app whether is installed
     */
    fun hasSharePlatformApp(activity: Activity, appId: String): Boolean = false

    fun observeScreenshot(activity: Activity) {}

    fun support3DVideo(): Boolean = false

    fun supportLongScreenshot(): Boolean = false
}


