package com.stt.android.workouts;

import com.stt.android.utils.STTConstants;
import java.util.List;

public class AltitudeChangeCalculator {
    private static final double MINIMUM_ALTITUDE_DIFF =
        STTConstants.AltitudeCalculations.MINIMUM_ALTITUDE_DIFF;
    private static final int ALTITUDE_AVG_AMOUNT =
        STTConstants.AltitudeCalculations.ALTITUDE_AVG_AMOUNT;

    private double previousAltitude = 0.0;
    private boolean previousAltitudeInitialized = false;
    private double[] altitudeValuesToAverage =
        new double[STTConstants.AltitudeCalculations.ALTITUDE_AVG_AMOUNT];
    private int oldestAverageIdx = 0;

    private double totalAscent;
    private double totalDescent;

    private double minAltitude = Double.MAX_VALUE;
    private double maxAltitude = -Double.MAX_VALUE;

    public void addAltitude(double altitude) {
        minAltitude = Math.min(minAltitude, altitude);
        maxAltitude = Math.max(maxAltitude, altitude);

        altitudeValuesToAverage[oldestAverageIdx] = altitude;
        oldestAverageIdx = (oldestAverageIdx + 1) % ALTITUDE_AVG_AMOUNT;
        boolean averageReady = true;
        double averagedAltitude = 0.0;
        for (double altitudeValue : altitudeValuesToAverage) {
            if (altitudeValue == 0.0) {
                averageReady = false;
                break;
            }
            averagedAltitude += altitudeValue;
        }
        averagedAltitude /= ALTITUDE_AVG_AMOUNT;
        if (averageReady) {
            if (previousAltitudeInitialized) {
                double altitudeDiff = averagedAltitude - previousAltitude;
                if (altitudeDiff > MINIMUM_ALTITUDE_DIFF) {
                    totalAscent += altitudeDiff;
                    previousAltitude = averagedAltitude;
                } else if (altitudeDiff < -MINIMUM_ALTITUDE_DIFF) {
                    totalDescent += -altitudeDiff;
                    previousAltitude = averagedAltitude;
                }
            } else {
                previousAltitude = averagedAltitude;
                previousAltitudeInitialized = true;
            }
        }
    }

    public void addAltitudes(List<Double> altitudes) {
        for (Double altitude : altitudes) {
            addAltitude(altitude);
        }
    }

    public double getTotalAscent() {
        return totalAscent;
    }

    public double getTotalDescent() {
        return totalDescent;
    }

    public double getMinAltitude() {
        return minAltitude;
    }

    public double getMaxAltitude() {
        return maxAltitude;
    }
}
