package com.stt.android.workouts.pictures

import android.content.Intent
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.stt.android.common.coroutines.ORMLITE
import com.stt.android.controllers.PicturesController
import com.stt.android.domain.user.ImageInformation
import com.stt.android.domain.workouts.pictures.Picture
import com.stt.android.domain.workouts.pictures.PicturesDataSource
import com.stt.android.utils.STTConstants.BroadcastActions.PICTURE_OR_VIDEO_STORED
import com.stt.android.utils.STTConstants.ExtraKeys.WORKOUT_ID
import kotlinx.coroutines.withContext
import javax.inject.Inject

class PicturesOrmLiteDataSource
@Inject constructor(
    private val picturesController: PicturesController,
    private val localBroadcastManager: LocalBroadcastManager
) : PicturesDataSource {
    override suspend fun findByWorkoutId(workoutId: Int): List<Picture> = withContext(ORMLITE) {
        picturesController.findByWorkoutId(workoutId)
            .map { it.toPicture() }
    }

    override suspend fun findUnsyncedPictures(username: String): List<Picture> = withContext(ORMLITE) {
        picturesController.findUnsyncedPictures(username)
            .map { it.toPicture() }
    }

    override suspend fun savePicture(picture: Picture) = withContext<Unit>(ORMLITE) {
        picturesController.store(ImageInformation.fromPicture(picture))

        Intent(PICTURE_OR_VIDEO_STORED).apply {
            putExtra(WORKOUT_ID, picture.workoutId)
            localBroadcastManager.sendBroadcast(this)
        }
    }

    override suspend fun deletePicture(picture: Picture) = withContext(ORMLITE) {
        val imageInformation = ImageInformation.fromPicture(picture)
        picturesController.deletePicture(imageInformation)
        // remove picture metadata
        picturesController.remove(imageInformation)
    }
}
