package com.stt.android.workouts.sharepreview.customshare

import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.ViewStateEpoxyController
import javax.inject.Inject

class WorkoutShareTargetListController
@Inject constructor() : ViewStateEpoxyController<WorkoutShareLinkTargets>() {
    override fun buildModels(viewState: ViewState<WorkoutShareLinkTargets?>) {
        if (viewState.data != null) {
            viewState.data.list.forEach {
                workoutShareTargetItem {
                    id(it.id)
                    shareTarget(it.target)
                    onSelectTargetListener { _, _, _, _ ->
                        it.onSelect(it.target)
                    }
                }
            }
        }
    }
}
