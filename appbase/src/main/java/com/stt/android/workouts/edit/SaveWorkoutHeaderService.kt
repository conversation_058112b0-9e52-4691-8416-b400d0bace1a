package com.stt.android.workouts.edit

import android.content.Context
import android.content.Intent
import androidx.core.app.JobIntentService
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.work.WorkManager
import com.stt.android.controllers.PicturesController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.controllers.VideoModel
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.domain.sync.SyncRequest
import com.stt.android.domain.sync.SyncRequestHandlerWorker
import com.stt.android.domain.tags.UserTagsRepository
import com.stt.android.domain.user.ImageInformation
import com.stt.android.domain.user.VideoInformation
import com.stt.android.domain.user.autoCommuteTaggingEnabled
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.domain.workouts.tag.SuuntoTagUseCase
import com.stt.android.domain.workouts.toBasic
import com.stt.android.exceptions.InternalDataException
import com.stt.android.services.JOB_ID_SAVE_WORKOUT_HEADER_SERVICE
import com.stt.android.ui.activities.SaveWorkoutAnalyticsJob
import com.stt.android.utils.STTConstants
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.runBlocking
import timber.log.Timber
import javax.inject.Inject

/**
 * Saves the workout header to database, broadcasts the change, and sync to server if required.
 */
@AndroidEntryPoint
class SaveWorkoutHeaderService : JobIntentService() {

    @Inject
    internal lateinit var workManager: WorkManager

    @Inject
    internal lateinit var picturesController: PicturesController

    @Inject
    internal lateinit var videoModel: VideoModel

    @Inject
    internal lateinit var workoutHeaderController: WorkoutHeaderController

    @Inject
    internal lateinit var localBroadcastManager: LocalBroadcastManager

    @Inject
    internal lateinit var tagsRepository: UserTagsRepository

    @Inject
    internal lateinit var suuntoTagUseCase: SuuntoTagUseCase

    @Inject
    internal lateinit var userSettingsController: UserSettingsController

    override fun onHandleWork(intent: Intent) {
        try {
            val workoutHeader = intent.getParcelableExtra<WorkoutHeader>(KEY_WORKOUT_HEADER)
                ?: throw Exception("Workout header did not exist")

            // for whatever reason, Dao.update() doesn't always return the correct result
            // therefore, first checks if the workout is already there in DB
            val isNewWorkout = !workoutHeaderController.hasWorkout(workoutHeader.id)

            workoutHeaderController.store(
                workoutHeader.copy(suuntoTags = handleSuuntoTags(workoutHeader, isNewWorkout)),
            )

            // handle tags
            runBlocking {
                val originalUserTags =
                    tagsRepository.getAllUserTagsForWorkoutId(workoutId = workoutHeader.id)
                val tagsToAttach = workoutHeader.userTags.minus(originalUserTags.toSet())
                val tagsToDetach = originalUserTags.minus(workoutHeader.userTags.toSet())

                for (userTag in tagsToAttach) {
                    val userTagId = tagsRepository.upsertUserTag(userTag)
                    tagsRepository.addUserTagToWorkoutHeader(
                        workoutId = workoutHeader.id,
                        userTagId = userTagId,
                        isSynced = false
                    )
                }
                for (userTag in tagsToDetach) {
                    val userTagId = userTag.id
                    // List of tags to detach is based on the original saved list of tags, which means the id for those item is not null
                    // Add this block of check just in case anything wrong happened
                    if (userTagId != null) {
                        val userTagKey = userTag.key
                        if (userTagKey.isNullOrBlank()) {
                            // User tag is not synced to backend, which means it never sent as a workout attribute, we can just delete it from local the many to many table
                            tagsRepository.deleteUserTagsFromAWorkout(
                                workoutId = workoutHeader.id,
                                userTagId = userTagId
                            )
                        } else {
                            tagsRepository.markUserTagInWorkoutHeaderAsRemoved(
                                workoutId = workoutHeader.id,
                                userTagId = userTagId
                            )
                        }
                    }
                }
            }

            val imageInfo = intent.getParcelableExtra<ImageInformation>(KEY_WORKOUT_PICTURE)
            if (imageInfo != null) {
                picturesController.store(imageInfo)
            }

            val videoInfo = intent.getParcelableExtra<VideoInformation>(KEY_WORKOUT_VIDEO)
            if (videoInfo != null) {
                videoModel.storeMetaData(videoInfo)
            }

            if (intent.getBooleanExtra(KEY_SYNC_TO_SERVER, true)) {
                SyncRequestHandlerWorker.enqueue(workManager, SyncRequest.push())
            }

            if (intent.getBooleanExtra(KEY_SEND_ANALYTICS, false)) {
                SaveWorkoutAnalyticsJob.enqueue(
                    workManager,
                    workoutHeader,
                    intent.getBooleanExtra(KEY_HAS_ANDROID_WEAR, false)
                )
            }
            val isManualWorkout = intent.getBooleanExtra(KEY_MANUAL_WORKOUT, false)
            if (isNewWorkout) {
                if (isManualWorkout) {
                    localBroadcastManager.sendBroadcast(
                        Intent(STTConstants.BroadcastActions.MANUAL_WORKOUT_SAVED)
                            .putExtra(STTConstants.ExtraKeys.WORKOUT_ID, workoutHeader.id)
                    )
                } else {
                    localBroadcastManager.sendBroadcast(
                        Intent(STTConstants.BroadcastActions.WORKOUT_SAVED)
                            .putExtra(STTConstants.ExtraKeys.WORKOUT_ID, workoutHeader.id)
                    )
                }
            } else {
                localBroadcastManager.sendBroadcast(
                    Intent(STTConstants.BroadcastActions.WORKOUT_UPDATED)
                        .putExtra(STTConstants.ExtraKeys.WORKOUT_ID, workoutHeader.id)
                        .putExtra(STTConstants.ExtraKeys.WORKOUT_HEADER, workoutHeader)
                )
            }
        } catch (e: InternalDataException) {
            Timber.w(e, "Failed to update workout header")
        }
    }

    // TODO refactor to support suspend functions
    private fun handleSuuntoTags(
        workoutHeader: WorkoutHeader,
        isNewWorkout: Boolean,
    ): List<SuuntoTag> = runBlocking {
        if (isNewWorkout) {
            suuntoTagUseCase.calculateSuuntoTags(
                workoutHeader = workoutHeader.toBasic(),
                autoTagCommute = userSettingsController.settings.autoCommuteTaggingEnabled,
                marathonTagSupported = true,
                startPoint = workoutHeader.startPosition,
                endPoint = workoutHeader.stopPosition,
            )
        } else {
            // For existing workouts, keep all the editable tags from the header.
            val editableTags = workoutHeader.suuntoTags.filter(SuuntoTag::editable)

            // And recalculate all non-editable tags.
            val nonEditableTags = suuntoTagUseCase.calculateSuuntoTags(
                workoutHeader = workoutHeader.toBasic(),
                autoTagCommute = false,
                marathonTagSupported = false,
                startPoint = workoutHeader.startPosition,
                endPoint = workoutHeader.stopPosition,
            ).filterNot(SuuntoTag::editable)

            editableTags + nonEditableTags
        }
    }

    companion object {
        private const val KEY_WORKOUT_HEADER = "com.stt.android.KEY_WORKOUT_HEADER"
        private const val KEY_WORKOUT_PICTURE = "com.stt.android.KEY_WORKOUT_PICTURE"
        private const val KEY_WORKOUT_VIDEO = "com.stt.android.KEY_WORKOUT_VIDEO"
        private const val KEY_SYNC_TO_SERVER = "com.stt.android.KEY_SYNC_TO_SERVER"
        private const val KEY_MANUAL_WORKOUT = "com.stt.android.KEY_MANUAL_WORKOUT"
        private const val KEY_HAS_ANDROID_WEAR = "com.stt.android.KEY_HAS_ANDROID_WEAR"
        private const val KEY_SEND_ANALYTICS = "com.stt.android.KEY_SEND_ANALYTICS"

        @JvmStatic
        fun enqueueWork(context: Context, workoutHeader: WorkoutHeader, shouldSync: Boolean) {
            enqueueWork(
                context,
                SaveWorkoutHeaderService::class.java,
                JOB_ID_SAVE_WORKOUT_HEADER_SERVICE,
                createIntent(
                    context,
                    workoutHeader,
                    null,
                    null,
                    shouldSync
                )
            )
        }

        @JvmStatic
        fun enqueueSaveWorkoutAndSendAnalytics(
            context: Context,
            workoutHeader: WorkoutHeader,
            hasAndroidWear: Boolean
        ) {
            enqueueWork(
                context,
                SaveWorkoutHeaderService::class.java,
                JOB_ID_SAVE_WORKOUT_HEADER_SERVICE,
                createIntent(
                    context,
                    workoutHeader,
                    null,
                    null,
                    shouldSync = true,
                    isManualWorkout = false,
                    shouldSendAnalytics = true,
                    hasAndroidWear = hasAndroidWear
                )
            )
        }

        @JvmStatic
        fun enqueueManualWorkout(
            context: Context,
            workoutHeader: WorkoutHeader,
            shouldSync: Boolean
        ) {
            enqueueWork(
                context,
                SaveWorkoutHeaderService::class.java,
                JOB_ID_SAVE_WORKOUT_HEADER_SERVICE,
                createIntent(
                    context,
                    workoutHeader,
                    null,
                    null,
                    shouldSync,
                    true
                )
            )
        }

        @JvmStatic
        fun enqueueWork(
            context: Context,
            workoutHeader: WorkoutHeader,
            imageInfo: ImageInformation,
            shouldSync: Boolean
        ) {
            enqueueWork(
                context,
                SaveWorkoutHeaderService::class.java,
                JOB_ID_SAVE_WORKOUT_HEADER_SERVICE,
                createIntent(
                    context,
                    workoutHeader,
                    imageInfo,
                    null,
                    shouldSync
                )
            )
        }

        @JvmStatic
        fun enqueueWork(
            context: Context,
            workoutHeader: WorkoutHeader,
            videoInfo: VideoInformation,
            shouldSync: Boolean
        ) {
            enqueueWork(
                context,
                SaveWorkoutHeaderService::class.java,
                JOB_ID_SAVE_WORKOUT_HEADER_SERVICE,
                createIntent(
                    context,
                    workoutHeader,
                    null,
                    videoInfo,
                    shouldSync
                )
            )
        }

        private fun createIntent(
            context: Context,
            workoutHeader: WorkoutHeader,
            imageInfo: ImageInformation?,
            videoInfo: VideoInformation?,
            shouldSync: Boolean,
            isManualWorkout: Boolean = false,
            shouldSendAnalytics: Boolean = false,
            hasAndroidWear: Boolean? = null
        ): Intent {
            val intent = Intent(context, SaveWorkoutHeaderService::class.java).putExtra(
                KEY_WORKOUT_HEADER,
                workoutHeader
            ).putExtra(KEY_SYNC_TO_SERVER, shouldSync)
            if (imageInfo != null) {
                intent.putExtra(KEY_WORKOUT_PICTURE, imageInfo)
            }
            if (videoInfo != null) {
                intent.putExtra(KEY_WORKOUT_VIDEO, videoInfo)
            }
            intent.putExtra(KEY_MANUAL_WORKOUT, isManualWorkout)
            intent.putExtra(KEY_SEND_ANALYTICS, shouldSendAnalytics)
            if (hasAndroidWear != null) {
                intent.putExtra(KEY_HAS_ANDROID_WEAR, hasAndroidWear)
            }
            return intent
        }
    }
}
