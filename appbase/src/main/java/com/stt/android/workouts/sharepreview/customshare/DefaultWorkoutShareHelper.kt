package com.stt.android.workouts.sharepreview.customshare

import android.app.Activity
import android.content.Context
import android.net.Uri
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.analytics.FirebaseAnalyticsTracker
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.multimedia.sportie.SportieSelection
import com.stt.android.multimedia.sportie.SportieShareSource
import com.stt.android.multimedia.sportie.SportieShareType
import javax.inject.Inject

class DefaultWorkoutShareHelper
@Inject constructor(
    override val emarsysAnalytics: EmarsysAnalytics,
    override val firebaseAnalyticsTracker: FirebaseAnalyticsTracker,
    override val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker
) : WorkoutShareHelper {

    override fun hasCustomIntentHandling(): Boolean = false

    override fun getCustomShareLinkTargets(context: Context): List<ShareTarget> = emptyList()

    override fun getCustomShareImageTargets(context: Context): List<ShareTarget> = emptyList()

    override fun getCustomShareVideoTargets(context: Context): List<ShareTarget> = emptyList()

    override fun shareLinkToCustomTarget(
        activity: Activity,
        customTarget: ShareTarget.CustomTarget,
        workoutHeader: WorkoutHeader,
        source: SportieShareSource
    ) {
        // do nothing, not supported
    }

    override fun shareImageToCustomTarget(
        activity: Activity,
        customTarget: ShareTarget.CustomTarget,
        header: WorkoutHeader,
        imageUri: Uri,
        sportieSelection: SportieSelection
    ) {
        // do nothing, not supported
    }

    override fun shareVideoToCustomTarget(
        activity: Activity,
        customTarget: ShareTarget.CustomTarget,
        videoUri: Uri,
        shareType: SportieShareType,
    ) {
        // do nothing, not supported
    }
}
