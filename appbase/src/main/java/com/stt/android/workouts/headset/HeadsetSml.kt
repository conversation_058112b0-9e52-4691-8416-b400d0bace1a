package com.stt.android.workouts.headset

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class HeadsetSamples(
    @Json(name = "Samples") val samples: List<SamplesItem>
)

@JsonClass(generateAdapter = true)
data class HeadsetSummary(
    @Json(name = "Samples") val samples: List<SummarySampleItem>
)

@JsonClass(generateAdapter = true)
data class SamplesItem(
    @Json(name = "Attributes") val attributes: SampleAttributes,
    @<PERSON><PERSON>(name = "Source") val source: String,
    @Json(name = "TimeISO8601") val timeISO8601: String
)

@JsonClass(generateAdapter = true)
data class SampleAttributes(
    @Json(name = "suunto/sml") val suuntoSml: SampleSuuntoSml
)

@JsonClass(generateAdapter = true)
data class SampleSuuntoSml(
    @<PERSON><PERSON>(name = "Sample") val sample: HeadsetSample
)

@JsonClass(generateAdapter = true)
open class HeadsetSample(
    @Json(name = "Events") val headsetEvents: List<HeadsetEvent>? = null,
    @Json(name = "Distance") val distance: Int? = null,
    @Json(name = "Speed") val speed: Float? = null,
    @Json(name = "Time") val duration: Float? = null,
    @Json(name = "BreaststrokeGlideTime") val breaststrokeGlideTime: Int? = null,
    @Json(name = "AvgFreestyleBreathAngle") val freestyleAvgBreathAngle: Int? = null,
    @Json(name = "AvgBreaststrokeBreathAngle") val breaststrokeAvgBreathAngle: Int? = null,
    @Json(name = "VentilationFrequency") val breathingRate: Int? = null,
    @Json(name = "BreaststrokeHeadAngle") val breaststrokeHeadAngle: Int? = null,
    @Json(name = "FreestyleHeadAngle") val freestyleHeadAngle: Int? = null,
    @Json(name = "Cadence") val cadence: Float? = null,
    @Json(name = "SkipsPerRound") val skipsPerRound: Int? = null,
)

@JsonClass(generateAdapter = true)
data class HeadsetEvent(
    @Json(name = "ArrayBegin") val arrayBegin: Int,
    @Json(name = "Lap") val lap: HeadsetLap
)

@JsonClass(generateAdapter = true)
data class HeadsetLap(
    @Json(name = "Type") val type: String
)

@JsonClass(generateAdapter = true)
data class SummarySampleItem(
    @Json(name = "Attributes") val attributes: SummaryAttributes,
    @Json(name = "Source") val source: String,
    @Json(name = "TimeISO8601") val timeISO8601: String,
)

@JsonClass(generateAdapter = true)
data class SummaryAttributes(
    @Json(name = "suunto/sml") val suuntoSml: SummarySuuntoSml
)

@JsonClass(generateAdapter = true)
data class SummarySuuntoSml(
    @Json(name = "Header") val header: HeadsetHeader? = null,
    @Json(name = "Windows") val windows: List<HeadsetWindow> = emptyList(),
)

@JsonClass(generateAdapter = true)
data class HeadsetHeader(
    @Json(name = "Activity") val activity: String,
    @Json(name = "ActivityType") val activityType: Int,
    @Json(name = "DateTime") val dateTime: String,
    @Json(name = "Device") val device: HeadsetDevice,
    @Json(name = "Duration") val duration: Int,
    @Json(name = "SwimmingMetrics") val swimmingMetrics: SwimmingMetrics? = null,
    @Json(name = "Energy") val energy: Double? = null,
    @Json(name = "Distance") val distance: Int? = null,
    @Json(name = "RepetitionCount") val skipsCount: Int? = null,
)

@JsonClass(generateAdapter = true)
data class HeadsetWindow(
    @Json(name = "ActivityId") val activityTypeId: Int,
    @Json(name = "Type") val type: String,
    @Json(name = "Duration") val duration: Float,
    @Json(name = "Energy") val energy: Double? = null,
    @Json(name = "SwimStyle") val swimStyle: String? = null,
    @Json(name = "Distance") val distance: Int? = null,
    @Json(name = "BreaststrokeGlideTime") val breaststrokeGlideTime: Int? = null,
    @Json(name = "AvgFreestyleBreathAngle") val freestyleAvgBreathAngle: Int? = null,
    @Json(name = "AvgBreaststrokeBreathAngle") val breaststrokeAvgBreathAngle: Int? = null,
    @Json(name = "VentilationFrequency") val breathingRate: Int? = null,
    @Json(name = "BreaststrokeHeadAngle") val breaststrokeHeadAngle: Int? = null,
    @Json(name = "FreestyleHeadAngle") val freestyleHeadAngle: Int? = null,
    @Json(name = "Speed") val speed: List<MinMaxValue>? = null,
    @Json(name = "Cadence") val cadence: List<MinMaxValue>? = null,
    @Json(name = "Rounds") val rounds: Int? = null,
    @Json(name = "AvgSkipsPerRound") val avgSkipsPerRound: Int? = null,
    @Json(name = "MaxConsecutiveSkips") val maxConsecutiveSkips: Int? = null,
    @Json(name = "RepetitionCount") val skipsCount: Int? = null
)

@JsonClass(generateAdapter = true)
data class MinMaxValue(
    @Json(name = "Avg") val avg: Float,
    @Json(name = "Max") val max: Float,
    @Json(name = "Min") val min: Float,
)

@JsonClass(generateAdapter = true)
data class HeadsetDevice(
    @Json(name = "Info") val info: HeadsetDeviceInfo,
    @Json(name = "Name") val name: String,
    @Json(name = "SerialNumber") val serialNumber: String,
)

@JsonClass(generateAdapter = true)
data class SwimmingMetrics(
    @Json(name = "AvgBreaststrokeBreathAngle") val avgBreaststrokeBreathAngle: Int,
    @Json(name = "AvgFreestyleBreathAngle") val avgFreestyleBreathAngle: Int,
    @Json(name = "BreaststrokeDuration") val breaststrokeDuration: Int,
    @Json(name = "BreaststrokeGlideTime") val breaststrokeGlideTime: Int,
    @Json(name = "BreaststrokePercent") val breaststrokePercentage: Int,
    @Json(name = "FreestyleDuration") val freestyleDuration: Int,
    @Json(name = "FreestyleGlideAngle") val freestyleGlideAngle: Int,
    @Json(name = "FreestylePercent") val freestyleSwimPercentage: Int,
    @Json(name = "MaxBreaststrokeBreathAngle") val maxBreaststrokeBreathAngle: Int,
    @Json(name = "MaxFreestyleBreathAngle") val maxFreestyleBreathAngle: Int,
    @Json(name = "OtherStylesDuration") val otherStylesDuration: Int,
    @Json(name = "OtherStylesPercent") val otherStylesPercentage: Int,
    @Json(name = "VentilationFrequency") val ventilationFrequency: Int,
    @Json(name = "BreaststrokeHeadAngle") val breaststrokeHeadAngle: Int,
)

@JsonClass(generateAdapter = true)
data class HeadsetDeviceInfo(
    @Json(name = "BatteryDesignCapacity") val batteryDesignCapacity: Int,
    @Json(name = "BatteryFullCapacity") val batteryFullCapacity: Double,
    @Json(name = "HW") val hW: String,
    @Json(name = "SW") val sW: String,
)
