@file:Suppress("BlockingMethodInNonBlockingContext")

package com.stt.android.workouts.binary

import android.content.Context
import android.content.pm.PackageManager
import android.telephony.TelephonyManager
import androidx.annotation.WorkerThread
import com.stt.android.common.coroutines.ORMLITE
import com.stt.android.controllers.PicturesController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.controllers.WorkoutBinaryController
import com.stt.android.controllers.WorkoutExtensionDataModels
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.data.workout.binary.BinaryFileRepository
import com.stt.android.domain.advancedlaps.Statistics
import com.stt.android.domain.user.ImageInformation
import com.stt.android.domain.workout.Workout
import com.stt.android.domain.workout.WorkoutData
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.domain.workout.WorkoutHrEvent
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.SummaryExtension
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.exceptions.InternalDataException
import com.stt.android.laps.CompleteLap
import com.stt.android.tracker.event.Event
import com.stt.android.utils.DeviceUtils
import com.stt.android.utils.FileUtils
import com.stt.android.utils.STTConstants
import com.stt.android.utils.firstOfType
import com.stt.android.workouts.WeChatWorkoutRepository
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import java.io.FileNotFoundException
import javax.inject.Inject

class FsBinaryFileRepository
@Inject constructor(
    private val userSettingsController: UserSettingsController,
    private val telephonyManager: TelephonyManager?,
    private val picturesController: PicturesController,
    private val workoutExtensionDataModels: WorkoutExtensionDataModels,
    private val fileUtils: FileUtils,
    private val workoutHeaderController: WorkoutHeaderController,
    private val applicationContext: Context,
    private val workoutDataController: WorkoutBinaryController,
    private val weChatWorkoutRepository: WeChatWorkoutRepository
) : BinaryFileRepository {
    override suspend fun get(workoutHeader: WorkoutHeader): File {
        val file = fileUtils.getInternalFilePath(
            STTConstants.DIRECTORY_WORKOUTS,
            workoutHeader.filename
        )
        if (!file.exists()) {
            throw FileNotFoundException("Binary workout file '${file.name}' not found")
        }

        return file
    }

    override suspend fun create(workoutHeader: WorkoutHeader) = withContext(ORMLITE) {
        try {
            val routePoints = emptyList<WorkoutGeoPoint>()
            val heartRateEvents = emptyList<WorkoutHrEvent>()
            val manualLaps = emptyList<CompleteLap>()
            val measurementUnit = userSettingsController.settings.measurementUnit
            val events = emptyList<Event>()
            val longitudeStatistics = Statistics()
            val latitudeStatistics = Statistics()
            val altitudeStatistics = Statistics()
            val speedStatistics = Statistics()
            val heartRateStatistics = Statistics()
            val cadenceStatistics = Statistics()
            val userSettings = userSettingsController.settings
            val altitudeOffset = userSettings.altitudeOffset
            val heartRateMaximum = userSettings.hrMaximum
            val mccAndMnc = DeviceUtils.getMccAndMnc(telephonyManager)
            val mobileNetworkCode = mccAndMnc.second
            val mobileCountryCode = mccAndMnc.first
            val data = WorkoutData(
                routePoints, heartRateEvents, manualLaps, measurementUnit, events,
                longitudeStatistics, latitudeStatistics, altitudeStatistics, speedStatistics,
                heartRateStatistics, cadenceStatistics, altitudeOffset, heartRateMaximum,
                mobileNetworkCode, mobileCountryCode
            )
            val pictures = emptyList<ImageInformation>()
            val extensions = emptyList<WorkoutExtension>()
            val workout = Workout(workoutHeader, data, pictures, extensions)
            storeWorkout(workout)
        } catch (e: InternalDataException) {
            Timber.w(e, "Unable to store binary file for manually added workout %d", workoutHeader.id)
            throw e
        }
    }

    @WorkerThread
    fun storeWorkout(workout: Workout) {
        val workoutHeader = workout.header
        workoutHeaderController.store(workoutHeader)
        workoutExtensionDataModels.storeExtensions(workout.extensions)
        saveWorkoutBinary(workout, workoutHeader.filename)
        picturesController.store(workout.pictures)
        val workoutExtension =
            workout.extensions.firstOfType<SummaryExtension>()
        workoutExtension?.let {
            weChatWorkoutRepository.store(workoutHeader, it)
        }
    }

    private fun saveWorkoutBinary(workout: Workout, filename: String) {
        val weightInKg = userSettingsController.settings.weightInKg ?: 0
        @Suppress(
            "DEPRECATION",
            "BlockingMethodInNonBlockingContext",
            "BlockingMethodInNonBlockingContext"
        )
        workoutDataController.saveWorkoutBinary(
            workout,
            filename,
            weightInKg,
            0, // machineId not used anymore
            getApplicationId()
        )
    }

    /**
     * @return the version code of the app plus a mask (0xF00000)
     */
    @Deprecated("Old appId system")
    private fun getApplicationId(): Int {
        return try {
            val packageManager = applicationContext.packageManager
            val packageName = applicationContext.packageName
            @Suppress("DEPRECATION")
            0xF00000 + packageManager.getPackageInfo(packageName, 0).versionCode
        } catch (e: PackageManager.NameNotFoundException) {
            Timber.w(e, "Couldn't retrieve application version code")
            0
        }
    }

    override suspend fun update(workoutHeader: WorkoutHeader) = withContext(ORMLITE) {
        try {
            val data = withContext(IO) { readWorkoutDataFromDisk(workoutHeader) }
            val pictures = picturesController.findByWorkoutId(workoutHeader.id)
            val extensions = workoutExtensionDataModels.buildExtensions(workoutHeader)
            val updatedWorkout = Workout(workoutHeader, data, pictures, extensions)
            saveWorkoutBinary(updatedWorkout, workoutHeader.filename)
        } catch (e: Exception) {
            Timber.w(e, "Unable to update binary for workout %d", workoutHeader.id)
            throw e
        }
    }

    @WorkerThread
    @Throws(InternalDataException::class, FileNotFoundException::class)
    fun readWorkoutDataFromDisk(workoutHeader: WorkoutHeader): WorkoutData {
        try {
            val fileName = workoutHeader.filename
            var file = fileUtils.getInternalFilePath(STTConstants.DIRECTORY_WORKOUTS, fileName)
            if (!file.exists()) {
                file = fileUtils.getCachedFilePath(STTConstants.DIRECTORY_WORKOUTS, fileName)
            }
            if (!file.exists()) {
                throw FileNotFoundException("Binary workout file not found: $fileName")
            }
            return WorkoutBinaryController.readLegacyWorkoutBinary(file)
        } catch (e: IllegalStateException) {
            throw InternalDataException("Workout data could not be read", e)
        }
    }

    fun cachedFileExists(workoutHeader: WorkoutHeader): Boolean {
        val fileName = workoutHeader.filename
        var file = fileUtils.getCachedFilePath(STTConstants.DIRECTORY_WORKOUTS, fileName)
        return file.exists()
    }
}
