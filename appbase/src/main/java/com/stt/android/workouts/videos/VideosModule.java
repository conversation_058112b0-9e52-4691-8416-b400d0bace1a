package com.stt.android.workouts.videos;

import com.squareup.moshi.Moshi;
import com.stt.android.data.workout.videos.VideoFileRepository;
import com.stt.android.domain.workouts.videos.VideoDataSource;
import com.stt.android.remote.AuthProvider;
import com.stt.android.remote.BaseUrl;
import com.stt.android.remote.SharedOkHttpClient;
import com.stt.android.remote.UserAgent;
import com.stt.android.remote.di.BrandOkHttpConfigFactory;
import com.stt.android.remote.di.RestApiFactory;
import com.stt.android.remote.workout.video.VideoRestApi;
import dagger.Binds;
import dagger.Module;
import dagger.Provides;
import okhttp3.OkHttpClient;

@Module
public abstract class VideosModule {
    @Binds
    abstract VideoFileRepository bindVideoFileRepository(FsVideoFileRepository repository);

    @Binds
    abstract VideoDataSource bindVideosDataSource(VideoOrmliteDataSource videoOrmliteDataSource);

    @Provides
    static VideoRestApi provideVideoRestApi(
        @SharedOkHttpClient OkHttpClient sharedClient,
        @BaseUrl String baseUrl,
        @UserAgent String userAgent,
        AuthProvider authProvider,
        Moshi moshi
    ) {
        return RestApiFactory.buildRestApi(
            sharedClient,
            baseUrl,
            VideoRestApi.class,
            BrandOkHttpConfigFactory.getStOkHttpConfig(authProvider, userAgent),
            moshi);
    }
}
