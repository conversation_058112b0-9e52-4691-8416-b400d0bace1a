package com.stt.android.workouts.remove

import android.content.Context
import android.content.Intent
import androidx.core.app.JobIntentService
import com.stt.android.data.Local
import com.stt.android.domain.sync.SyncRequest
import com.stt.android.domain.sync.SyncRequestHandler
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.WorkoutHeaderDataSource
import com.stt.android.services.JOB_ID_REMOVE_WORKOUT_SERVICE
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.runBlocking
import timber.log.Timber
import javax.inject.Inject

/**
 * Removes the specified workout, broadcast the change, and syncs to backend.
 */
@AndroidEntryPoint
class RemoveWorkoutService : JobIntentService() {

    @Inject
    internal lateinit var syncRequestHandler: SyncRequestHandler

    @Local
    @Inject
    lateinit var workoutHeaderDataSource: WorkoutHeaderDataSource

    override fun onHandleWork(intent: Intent) = runBlocking {
        val id = intent.getIntExtra(WORKOUT_HEADER_ID, 0)
        if (id != 0) {
            try {
                // Only sync with server when workout has already been synced before
                if (!workoutHeaderDataSource.markDeletedOrPermanentlyDelete(id)) {
                    syncRequestHandler.runRequestInQueue(SyncRequest.push())
                }
            } catch (e: Exception) {
                Timber.w(e, "Error while deleting workout")
            }
        }
    }

    companion object {
        private const val WORKOUT_HEADER_ID = "com.stt.android.WORKOUT_HEADER_ID"

        @JvmStatic
        fun enqueueWork(context: Context, workoutHeader: WorkoutHeader) {
            val intent = Intent(context, RemoveWorkoutService::class.java)
                .putExtra(WORKOUT_HEADER_ID, workoutHeader.id)
            enqueueWork(
                context,
                RemoveWorkoutService::class.java,
                JOB_ID_REMOVE_WORKOUT_SERVICE,
                intent
            )
        }
    }
}
