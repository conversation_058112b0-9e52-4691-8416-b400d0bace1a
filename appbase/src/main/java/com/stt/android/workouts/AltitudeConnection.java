package com.stt.android.workouts;

import android.content.SharedPreferences;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import com.stt.android.STTApplication;
import com.stt.android.utils.DateUtils;
import com.stt.android.utils.STTConstants;
import com.stt.android.utils.UpdatePressureTask;
import java.io.Closeable;
import javax.inject.Inject;
import timber.log.Timber;

public class AltitudeConnection
    implements SensorEventListener, UpdatePressureTask.Callbacks, Closeable {
    @Inject
    SensorManager sensorManager;

    @Inject
    SharedPreferences sharedPreferences;

    private boolean started = false;

    private UpdatePressureTask updatePressureTask;

    private final float[] altitudes = new float[15];
    private int index;
    private boolean rotated;

    private float referencePressureInMillibar;
    private boolean altitudeReady;
    private float averagedAltitude;

    public AltitudeConnection() {
        STTApplication.getComponent().inject(this);
        if (updatePressureTask == null) {
            updatePressureTask = new UpdatePressureTask(this);
            updatePressureTask.execute();
        }
    }

    public void startAltitudeUpdates() {
        if (started) {
            return;
        }
        started = true;

        Timber.d("Starting altitude updates.");

        if (sharedPreferences.getLong(
            STTConstants.DefaultPreferences.KEY_REFERENCE_PRESSURE_TIMESTAMP, 0L)
            < System.currentTimeMillis() - 12L * DateUtils.HOUR_IN_MILLIS) {
            // the reference value is too old, use the standard one
            referencePressureInMillibar = SensorManager.PRESSURE_STANDARD_ATMOSPHERE;
        } else {
            referencePressureInMillibar =
                sharedPreferences.getFloat(STTConstants.DefaultPreferences.KEY_REFERENCE_PRESSURE,
                    SensorManager.PRESSURE_STANDARD_ATMOSPHERE);
        }

        index = 0;
        rotated = false;
        altitudeReady = false;

        Sensor pressureSensor = sensorManager.getDefaultSensor(Sensor.TYPE_PRESSURE);
        if (pressureSensor == null) {
            return;
        }
        sensorManager.registerListener(this, pressureSensor, SensorManager.SENSOR_DELAY_NORMAL);
    }

    @Override
    public void close() {
        Timber.d("Stopping altitude updates.");

        if (updatePressureTask != null) {
            updatePressureTask.cancel(true);
            updatePressureTask = null;
        }

        sensorManager.unregisterListener(this);
    }

    public float getAltitude() throws IllegalStateException {
        if (!altitudeReady) {
            throw new IllegalStateException("Altitude value not yet available");
        }
        return averagedAltitude;
    }

    @Override
    public void onSensorChanged(SensorEvent event) {
        float pressureInMillibar = event.values[0];
        float altitudeFromReference =
            SensorManager.getAltitude(referencePressureInMillibar, pressureInMillibar);

        altitudes[index++] = altitudeFromReference;
        if (index == altitudes.length) {
            // reached last element, start over
            index = 0;
            rotated = true;
        }

        float totalAltitude = 0.0F;
        // If the array has been filled at least once then use all the elements in the array.
        // Otherwise, use all until the oldest one.
        int end = rotated ? altitudes.length : index;
        for (int i = 0; i < end; ++i) {
            totalAltitude += altitudes[i];
        }
        averagedAltitude = totalAltitude / end;

        altitudeReady = true;
    }

    @Override
    public void onAccuracyChanged(Sensor sensor, int accuracy) {
        // do nothing
    }

    @Override
    public void onSuccess() {
        updatePressureTask = null;
        startAltitudeUpdates();
    }

    @Override
    public void onFailure(Exception e) {
        updatePressureTask = null;
        startAltitudeUpdates();
    }
}
