package com.stt.android.workouts

import com.stt.android.workouts.details.values.WorkoutValue

data class WorkoutSummaryData(
    val left: WorkoutValue?,
    val middle: WorkoutValue?,
    val right: WorkoutValue?
) {

    fun leftAligned(): WorkoutSummaryData {
        val values = listOfNotNull(left, middle, right)
            .filter { !it.value.isNullOrBlank() }

        return WorkoutSummaryData(
            values.firstOrNull(),
            if (values.size > 1) values[1] else null,
            if (values.size > 2) values[2] else null
        )
    }
}
