package com.stt.android.workouts.hardware;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothSocket;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import androidx.core.content.ContextCompat;
import androidx.core.util.Pair;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import com.stt.android.STTApplication;
import com.stt.android.hr.BluetoothHeartRateDeviceManager;
import com.stt.android.hr.HeartRateDeviceConnectionManager;
import com.stt.android.hr.HeartRateDeviceManager;
import com.stt.android.hr.HeartRateMonitorType;
import com.stt.android.hr.HeartRateUpdateProvider;
import com.stt.android.utils.STTConstants;
import javax.inject.Inject;
import timber.log.Timber;

public class BluetoothHeartRateConnectionMonitor extends HeartRateConnectionMonitor
        implements HeartRateDeviceConnectionManager.Callbacks {
    private final Context context;
    private final BroadcastReceiver receiver;
    @Inject
    BluetoothHeartRateDeviceManager hrDeviceManager;
    @Inject
    HeartRateDeviceConnectionManager hrDeviceConnectionManager;
    private final BroadcastReceiver bluetoothOnReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            int state = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, -1);
            if (state == BluetoothAdapter.STATE_ON) {
                context.unregisterReceiver(this);
                try {
                    requestUpdatesFromPrePairedDevice();
                } catch (IllegalStateException e) {
                    Timber.w(e, "Can't connect HR");
                }
            }
        }
    };
    @Inject
    HeartRateUpdateProvider hrUpdateProvider;

    @Inject
    LocalBroadcastManager localBroadcastManager;
    private volatile boolean connected = false;

    private BluetoothHeartRateConnectionMonitor(Context context, BroadcastReceiver receiver) {
        STTApplication.getComponent().inject(this);

        this.context = context;
        this.receiver = receiver;
    }

    public static BluetoothHeartRateConnectionMonitor requestUpdates(Context context,
                                                                     BroadcastReceiver receiver)
            throws IllegalStateException {
        BluetoothHeartRateConnectionMonitor heartRateConnectionMonitor = new
                BluetoothHeartRateConnectionMonitor(context, receiver);
        heartRateConnectionMonitor.requestUpdates();
        return heartRateConnectionMonitor;
    }

    private void requestUpdates() throws IllegalStateException {
        hrDeviceConnectionManager.addListener(this);

        if (BluetoothAdapter.getDefaultAdapter().isEnabled()) {
            requestUpdatesFromPrePairedDevice();
        } else {
            ContextCompat.registerReceiver(
                context,
                bluetoothOnReceiver,
                new IntentFilter(BluetoothAdapter.ACTION_STATE_CHANGED),
                ContextCompat.RECEIVER_EXPORTED
            );
        }
    }

    private void requestUpdatesFromPrePairedDevice() throws IllegalStateException {
        Pair<HeartRateMonitorType, BluetoothDevice> previouslyConnectedDevice
                = HeartRateDeviceManager.getPairedDevice(context);
        if (previouslyConnectedDevice != null) {
            hrDeviceConnectionManager.connect(context,
                    previouslyConnectedDevice.second, previouslyConnectedDevice.first);
        } else {
            throw new IllegalStateException("Classic HR Monitor hasn't been set up yet");
        }
    }

    @Override
    public boolean isConnected() {
        return connected;
    }

    @Override
    public void close() {
        stopRecordingHeartRates();
    }

    private void stopRecordingHeartRates() {
        try {
            context.unregisterReceiver(bluetoothOnReceiver);
        } catch (IllegalArgumentException ignored) {
            // not registered, or already unregistered
        }
        try {
            localBroadcastManager.unregisterReceiver(receiver);
        } catch (IllegalArgumentException ignored) {
            // not registered, or already unregistered
        }

        hrUpdateProvider.stopRequestingHeartRateUpdates();
        hrDeviceConnectionManager.removeListener(this);
        hrDeviceConnectionManager.disconnect(context);
        connected = false;
    }

    @Override
    public void onConnected(BluetoothSocket socket) {
        Timber.d("Bluetooth heart rate monitor connected");

        @SuppressLint("MissingPermission")
        String deviceName = socket.getRemoteDevice().getName();

        localBroadcastManager.registerReceiver(receiver,
                new IntentFilter(STTConstants.BroadcastActions.HEART_RATE_UPDATE));

        HeartRateMonitorType type = HeartRateMonitorType.fromName(deviceName);
        hrUpdateProvider.requestHeartRateUpdates(socket, type);

        connected = true;
    }

    @Override
    public void onConnectionError(Throwable e) {
        Timber.e(e, "Bluetooth heart rate monitor connection error");

        connected = false;
        stopRecordingHeartRates();
    }

    @Override
    public void onDisconnected() {
        Timber.w("Bluetooth heart rate monitor disconnected");

        connected = false;
        stopRecordingHeartRates();
    }

    @Override
    public void onUnpaired() {
        Timber.w("Bluetooth heart rate monitor unpaired");

        connected = false;
        stopRecordingHeartRates();
    }

    @Override
    public void onNoConnection() {
        Timber.w("No connection for Bluetooth heart rate monitor");

        connected = false;
        stopRecordingHeartRates();
        ContextCompat.registerReceiver(
            context,
            bluetoothOnReceiver,
            new IntentFilter(BluetoothAdapter.ACTION_STATE_CHANGED),
            ContextCompat.RECEIVER_EXPORTED
        );
    }
}
