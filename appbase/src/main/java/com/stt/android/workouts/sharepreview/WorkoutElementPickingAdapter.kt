package com.stt.android.workouts.sharepreview

import androidx.recyclerview.widget.RecyclerView

abstract class WorkoutElementPickingAdapter<Element, ElementViewHolder : RecyclerView.ViewHolder>(
    protected val options: List<Element>,
    protected val initialPosition: Int,
    protected val listener: (index: Int) -> Unit
) : RecyclerView.Adapter<ElementViewHolder>() {

    override fun getItemCount() = options.size
}
