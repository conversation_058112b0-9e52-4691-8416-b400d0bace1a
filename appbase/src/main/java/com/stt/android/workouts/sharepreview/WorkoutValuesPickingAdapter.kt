package com.stt.android.workouts.sharepreview

import android.view.LayoutInflater
import android.view.ViewGroup
import com.stt.android.databinding.ItemWorkoutValuePickBinding
import com.stt.android.workouts.details.values.WorkoutValue

class WorkoutValuesPickingAdapter(
    activityWorkoutValues: List<WorkoutValue>,
    initialPosition: Int,
    listener: (index: Int) -> Unit
) : WorkoutElementPickingAdapter<WorkoutValue, WorkoutValuesPickingViewHolder>(
    activityWorkoutValues,
    initialPosition,
    listener
) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
        WorkoutValuesPickingViewHolder(
            ItemWorkoutValuePickBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            ),
            listener
        )

    override fun onBindViewHolder(viewHolder: WorkoutValuesPickingViewHolder, position: Int) {
        viewHolder.bind(position, position == initialPosition, options[position])
    }
}
