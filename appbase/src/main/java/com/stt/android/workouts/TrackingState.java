package com.stt.android.workouts;

public enum TrackingState {
    /**
     * State when there's no workout being recorded at all.
     */
    NOT_STARTED,
    /**
     * State when there's one workout being actively recorded.
     */
    RECORDING,
    /**
     * State when the workout being recorded is in pause.
     */
    PAUSED,
    /**
     * State when the workout is paused automatically
     */
    AUTO_PAUSED,
    /**
     * State when the workout being recorded has ended but is not yet saved.
     */
    NOT_SAVED,
    /**
     * State when the workout has ended and saved.
     */
    SAVED;
}
