package com.stt.android.workouts.hardware;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.text.TextUtils;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import com.stt.android.STTApplication;
import com.stt.android.bluetooth.BleHrModel;
import com.stt.android.bluetooth.BluetoothDeviceManager;
import com.stt.android.bluetooth.HrEventListener;
import com.stt.android.hr.HeartRateDeviceManager;
import javax.inject.Inject;
import timber.log.Timber;

public class BleHeartRateConnectionMonitor extends HeartRateConnectionMonitor {
    @Inject
    @Nullable
    BleHrModel bleHrModel;

    private final Context context;
    private final HrEventListener listener;

    private final BroadcastReceiver bluetoothStateListener = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, -1) == BluetoothAdapter.STATE_ON) {
                context.unregisterReceiver(this);
                try {
                    requestUpdatesFromPrePairedDevice();
                } catch (IllegalStateException e) {
                    Timber.w(e, "Can't connect to BLE HR");
                }
            }
        }
    };

    private BleHeartRateConnectionMonitor(Context context, HrEventListener listener) {
        STTApplication.getComponent().inject(this);

        this.context = context;
        this.listener = listener;
    }

    /**
     * Starts requesting updates from the HR Belt previously setup by the user.
     * If bluetooth is disabled then we'll ask updates once it becomes available.
     *
     * @param context application context
     * @param listener where to forward the updates
     * @return a new connection monitor instance to control it
     * @throws IllegalStateException if there isn't a previously set up HR sensor
     */
    static BleHeartRateConnectionMonitor requestUpdates(Context context, HrEventListener listener)
        throws IllegalStateException {
        BleHeartRateConnectionMonitor bleHeartRateConnectionMonitor =
            new BleHeartRateConnectionMonitor(context, listener);
        bleHeartRateConnectionMonitor.requestUpdates();
        return bleHeartRateConnectionMonitor;
    }

    private void requestUpdates() throws IllegalStateException {
        if (BluetoothAdapter.getDefaultAdapter().isEnabled()) {
            requestUpdatesFromPrePairedDevice();
        } else {
            ContextCompat.registerReceiver(
                context,
                bluetoothStateListener,
                new IntentFilter(BluetoothAdapter.ACTION_STATE_CHANGED),
                ContextCompat.RECEIVER_EXPORTED
            );
        }
    }

    @SuppressWarnings("WeakerAccess")
    void requestUpdatesFromPrePairedDevice() throws IllegalStateException {
        String pairedDeviceAddress = HeartRateDeviceManager.getPairedDeviceAddress(context);
        if (TextUtils.isEmpty(pairedDeviceAddress)) {
            throw new IllegalStateException("BLE HR Monitor hasn't been set up yet");
        } else {
            Timber.d("Re-connecting to an already discovered BLE HR with address %s",
                pairedDeviceAddress);
            BluetoothDevice device = BluetoothDeviceManager.getBluetoothAdapter(context)
                .getRemoteDevice(pairedDeviceAddress);
            try {
                requestUpdatesFrom(device);
            } catch (Exception e) {
                throw new IllegalStateException("Failed to connect to BLE heart rate monitor", e);
            }
        }
    }

    private void requestUpdatesFrom(BluetoothDevice device) throws NullPointerException, SecurityException {
        if (bleHrModel != null) {
            bleHrModel.addListener(listener);
            bleHrModel.requestUpdates(device);
        }
    }

    @Override
    public boolean isConnected() {
        return bleHrModel != null && bleHrModel.isConnected();
    }

    @Override
    public void close() {
        try {
            context.unregisterReceiver(bluetoothStateListener);
        } catch (IllegalArgumentException ignored) {
            // not registered, or already unregistered
        }
        if (bleHrModel != null) {
            bleHrModel.removeListener(listener);
            bleHrModel.stopUpdates();
        }
    }
}
