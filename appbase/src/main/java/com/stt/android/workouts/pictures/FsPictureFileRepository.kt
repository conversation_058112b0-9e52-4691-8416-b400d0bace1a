package com.stt.android.workouts.pictures

import com.stt.android.data.workout.pictures.PictureFileRepository
import com.stt.android.utils.FileUtils
import com.stt.android.utils.STTConstants
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import javax.inject.Inject

/**
 * An implementation of [PictureFileRepository] that uses the filesystem to store picture files
 */
@Suppress("BlockingMethodInNonBlockingContext")
class FsPictureFileRepository
@Inject constructor(
    private val fileUtils: FileUtils,
) : PictureFileRepository {
    override suspend fun getPictureFile(filename: String): File = withContext(Dispatchers.IO) {
        fileUtils.getInternalFilePath(STTConstants.DIRECTORY_PICTURES, filename)
    }
}
