package com.stt.android.workouts.sharepreview.customshare

import android.app.Activity
import android.content.Context
import android.net.Uri
import androidx.lifecycle.LiveData
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.viewstate.LoadingStateViewModel
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.multimedia.sportie.SportieSelection
import com.stt.android.multimedia.sportie.SportieShareSource
import com.stt.android.multimedia.sportie.SportieShareType
import com.stt.android.ui.utils.SingleLiveEvent
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import kotlinx.coroutines.launch
import java.util.UUID
import javax.inject.Inject

@HiltViewModel
class WorkoutShareTargetListViewModel
@Inject constructor(
    private val workoutShareHelper: WorkoutShareHelper,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    coroutinesDispatchers: CoroutinesDispatchers
) : LoadingStateViewModel<WorkoutShareLinkTargets>(
    ioThread,
    mainThread,
    coroutinesDispatchers
) {
    private val _targetSelectLiveEvent = SingleLiveEvent<ShareTarget>()

    val targetSelectLiveEvent: LiveData<ShareTarget>
        get() = _targetSelectLiveEvent

    override fun retryLoading() {
        // do nothing
    }

    fun setupTargets(targetList: List<ShareTarget>) {
        val data = targetList.map {
            TargetEpoxyContainer(UUID.randomUUID().toString(), it, ::onTargetSelected)
        }
        notifyDataLoaded(WorkoutShareLinkTargets(data))
    }

    private fun onTargetSelected(target: ShareTarget) {
        _targetSelectLiveEvent.postValue(target)
    }

    fun shareLinkToCustomTarget(
        activity: Activity,
        header: WorkoutHeader,
        source: SportieShareSource,
        shareTarget: ShareTarget.CustomTarget
    ) {
        workoutShareHelper.shareLinkToCustomTarget(activity, shareTarget, header, source)
    }

    fun shareImageToCustomTarget(
        activity: Activity,
        header: WorkoutHeader,
        imageUri: Uri,
        sportieSelection: SportieSelection,
        shareTarget: ShareTarget.CustomTarget
    ) {
        workoutShareHelper.shareImageToCustomTarget(activity, shareTarget, header, imageUri, sportieSelection)
    }

    fun shareVideoToCustomTarget(
        activity: Activity,
        videoUri: Uri,
        shareTarget: ShareTarget.CustomTarget,
        shareType: SportieShareType,
    ) {
        workoutShareHelper.shareVideoToCustomTarget(activity, shareTarget, videoUri, shareType)
    }

    fun shareToOS(
        activity: Activity,
        workoutHeader: WorkoutHeader,
        imageUri: Uri,
        sportieSelection: SportieSelection,
        numPhotosAdded: Int
    ) {
        workoutShareHelper.shareImageToOS(activity, workoutHeader, imageUri, sportieSelection, numPhotosAdded)
    }

    fun shareVideoToOS(activity: Activity, videoUri: Uri, shareType: SportieShareType) {
        workoutShareHelper.shareVideoToOS(activity, videoUri, shareType)
    }

    fun saveToPicture(context: Context, imageUri: Uri) {
        workoutShareHelper.saveToPhotoAlbum(context, imageUri)
    }

    fun deleteCacheFile(context: Context, uri: Uri) {
        launch(io) {
            context.contentResolver.delete(uri, "", null)
        }
    }
}
