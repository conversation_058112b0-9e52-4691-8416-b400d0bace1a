package com.stt.android.workouts.autosave;

import android.content.Context;
import com.google.gson.Gson;
import com.stt.android.domain.workout.WorkoutGeoPoint;
import com.stt.android.workouts.OngoingWorkout;
import java.lang.reflect.Type;

class GeoPointsController extends IncrementalSaveListController<WorkoutGeoPoint> {
    static final String ONGOING_ROUTE_FILENAME = "ongoing_route";

    public GeoPointsController(Context appContext, Gson gson) {
        super(ONGOING_ROUTE_FILENAME, appContext, gson);
    }

    public static void cleanSavedData(Context context) {
        context.deleteFile(ONGOING_ROUTE_FILENAME);
    }

    @Override
    protected Type getListClass() {
        return WorkoutGeoPoint.class;
    }

    @Override
    public void addEntryToOngoingWorkout(OngoingWorkout ongoingWorkout,
        WorkoutGeoPoint recoveredEntry) {
        ongoingWorkout.addWorkoutGeoPoint(recoveredEntry);
    }
}
