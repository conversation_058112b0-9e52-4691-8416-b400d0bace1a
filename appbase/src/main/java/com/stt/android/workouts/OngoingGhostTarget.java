package com.stt.android.workouts;

import android.location.Location;
import androidx.annotation.NonNull;
import com.stt.android.domain.workout.WorkoutData;
import com.stt.android.domain.workout.WorkoutGeoPoint;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.exceptions.GhostMatchNotFoundException;
import com.stt.android.exceptions.InitialGhostMatchNotFoundException;
import com.stt.android.utils.AndroidCoordinateUtils;
import com.stt.android.utils.CoordinateUtils;
import com.stt.android.workoutcomparison.GhostTarget;
import com.suunto.algorithms.geo.LatLng;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import timber.log.Timber;

/**
 * Class responsible to keep track of the ghost status.
 * The main method is {@link #processNewLocation(double, double)} which is called by
 * {@link com.stt.android.workouts.RecordWorkoutService} every time there's a new location.
 * The algorithm responsible to find a match between the given location and the target workout is described at:
 * <a href="https://sportstracker.atlassian.net/wiki/display/STFA/Active+subscription+features#Activesubscriptionfeatures-Targetworkoutmatchingalgorithmdescription(English)">
 * Target workout matching algorithm </a>
 */
public class OngoingGhostTarget implements GhostTarget {
    /**
     * Maximum distance
     */
    private static final float MAX_MATCH_DISTANCE = 250.0F;
    private final WorkoutHeader ghostWorkoutHeader;
    /**
     * List that holds all the route points for the target workout
     */
    private final List<WorkoutGeoPoint> targetRoutePoints;
    private volatile MatchCandidate currentMatch;
    private int previousDurationIdx = 0;
    /**
     * Indicates if we've found a starting match point.
     */
    private boolean initialMatchFound = false;
    private int lastKnownMatchPosition = 0;

    public OngoingGhostTarget(WorkoutHeader ghostWorkoutHeader, WorkoutData ghostWorkoutData) {
        this.ghostWorkoutHeader = ghostWorkoutHeader;
        this.targetRoutePoints = ghostWorkoutData.getRoutePoints();
    }

    public WorkoutHeader getWorkoutHeader() {
        return ghostWorkoutHeader;
    }

    @Override
    public WorkoutGeoPoint getLastMatchedPosition() {
        if (currentMatch != null) {
            return currentMatch.candidate;
        }
        return null;
    }

    @Override
    public void processNewLocation(@NonNull WorkoutGeoPoint currentWorkoutGeoPoint)
        throws InitialGhostMatchNotFoundException {
        processNewLocation(currentWorkoutGeoPoint.getLatitude(), currentWorkoutGeoPoint.getLongitude());
    }

    /**
     * In charge of updating the ghost status as per the new location given.
     * <p><em><b>IMPORTANT!</b></em> This method does not support multi-threading</p>
     */
    public void processNewLocation(double latitude, double longitude) throws InitialGhostMatchNotFoundException {
        MatchCandidate currentCandidate;
        if (!initialMatchFound) {
            try {
                // Step 0. Find current candidate (P1)
                currentCandidate = searchInitialCandidate(latitude, longitude);
                if (currentCandidate == null) {
                    throw new InitialGhostMatchNotFoundException("Initial ghost match could not be found");
                }
                initialMatchFound = true;
            } catch (IllegalStateException e) {
                Timber.w(e, "Error finding initial candidate");
                return;
            }
        } else {
            // Calculate the distance between current point and last match (if no there's no last match then
            // MAX_MATCH_DISTANCE is used)
            float[] results = new float[]{MAX_MATCH_DISTANCE};
            if (currentMatch != null) {
                Location.distanceBetween(latitude, longitude, currentMatch.candidate.getLatitude(),
                        currentMatch.candidate.getLongitude(), results);
            }
            if (results[0] < MAX_MATCH_DISTANCE) {
                // Step 8
                currentCandidate = currentMatch;
            } else {
                Timber.w("New location %.2f meters far from last match. Trying to find a closer one", results[0]);
                // Step 9
                try {
                    currentCandidate = findNextWithinMaxRange(latitude, longitude);
                } catch (GhostMatchNotFoundException ghostMatchNotFoundException) {
                    Timber.w(ghostMatchNotFoundException, "No close enough location has been found.");
                    if (currentMatch != null) {
                        // Before resetting current match store the last known match that will be used later to find the
                        // next within the max range
                        lastKnownMatchPosition = currentMatch.candidatePosition;
                    }
                    currentMatch = null;
                    return;
                }
            }
        }
        // Step 1: find next smallest candidate starting from current candidate (P2).
        currentCandidate = findNextSmallest(latitude, longitude, currentCandidate);

        // Step 2:
        // Calculate d2
        float[] results = new float[1];
        Location.distanceBetween(latitude, longitude, currentCandidate.candidate.getLatitude(),
                currentCandidate.candidate.getLongitude(), results);
        float distanceCurrentCandidate = results[0];

        // Calculate d(segment1)
        LatLng pointInPreviousSegment = calculateDistanceToPreviousSegment(
                latitude, longitude, currentCandidate);
        Location.distanceBetween(latitude, longitude, pointInPreviousSegment.getLatitude(),
                pointInPreviousSegment.getLongitude(), results);
        float distancePreviousSegment = results[0];

        // Calculate d(segment2)
        LatLng pointInNextSegment = calculateDistanceToNextSegment(
                latitude, longitude, currentCandidate);
        Location.distanceBetween(latitude, longitude, pointInNextSegment.getLatitude(),
                pointInNextSegment.getLongitude(), results);
        float distanceNextSegment = results[0];

        // Step 3: Find out dTarget. The smallest distance among the three distances: d(segment1), d(segment2), d2
        float minimumDistance = Math.min(Math.min(distanceCurrentCandidate, distancePreviousSegment),
                distanceNextSegment);
        MatchCandidate nextSmallestCandidate = findNextSmallestIn(latitude, longitude, minimumDistance,
                currentCandidate.candidatePosition);
        if (nextSmallestCandidate != null) {
            // Set the found candidate as the best one for the time being and start over again (set p1 = p3 and go to
            // step 1)
            currentMatch = nextSmallestCandidate;
            processNewLocation(latitude, longitude);
            return;
        }

        // Step 5
        if (minimumDistance == distancePreviousSegment) {
            currentCandidate = interpolateCandidateOnPreviousSegment(pointInPreviousSegment, currentCandidate);
        } else if (minimumDistance == distanceNextSegment) {
            currentCandidate = interpolateCandidateOnNextSegment(pointInNextSegment, currentCandidate);
        } else { // must be from current candidate (d2)
            // nothing to do since it's already currentCandidate
        }
        // Finally set the current match to the best candidate found so far
        currentMatch = currentCandidate;
    }

    /**
     * Iterates through all the points in the target workout (starting at the latest known match) to find one location
     * which is closer than {@link #MAX_MATCH_DISTANCE}.
     *
     * @param latitude
     * @param longitude
     * @return
     * @throws com.stt.android.exceptions.GhostMatchNotFoundException if no point is found
     */
    private MatchCandidate findNextWithinMaxRange(double latitude, double longitude) throws GhostMatchNotFoundException {
        float[] distanceResults = new float[1];
        int startIdx = currentMatch != null ? currentMatch.candidatePosition : lastKnownMatchPosition;
        for (int idx = startIdx; idx < targetRoutePoints.size(); idx++) {
            WorkoutGeoPoint targetRoutePoint = targetRoutePoints.get(idx);
            Location.distanceBetween(latitude, longitude, targetRoutePoint.getLatitude(),
                    targetRoutePoint.getLongitude(), distanceResults);
            if (distanceResults[0] < MAX_MATCH_DISTANCE) {
                return new MatchCandidate(targetRoutePoint, idx);
            }
        }
        throw new GhostMatchNotFoundException("No matching point found for the new location");
    }

    @NonNull
    private MatchCandidate interpolateCandidateOnPreviousSegment(@NonNull LatLng pointInPreviousSegment,
        @NonNull MatchCandidate currentCandidate) {
        int previousCandidatePosition = currentCandidate.candidatePosition - 1;
        if (previousCandidatePosition < 0) {
            return currentCandidate;
        }
        MatchCandidate previousCandidate = new MatchCandidate(targetRoutePoints.get(previousCandidatePosition),
                previousCandidatePosition);
        return interpolateCandidateOnSegment(pointInPreviousSegment, previousCandidate, currentCandidate);
    }

    @NonNull
    private MatchCandidate interpolateCandidateOnNextSegment(@NonNull LatLng pointInNextSegment,
        @NonNull MatchCandidate currentCandidate) {
        int nextCandidatePosition = currentCandidate.candidatePosition + 1;
        if (nextCandidatePosition >= targetRoutePoints.size()) {
            return currentCandidate;
        }
        MatchCandidate nextCandidate = new MatchCandidate(targetRoutePoints.get(nextCandidatePosition),
                nextCandidatePosition);
        return interpolateCandidateOnSegment(pointInNextSegment, currentCandidate, nextCandidate);
    }

    @NonNull
    private MatchCandidate interpolateCandidateOnSegment(@NonNull LatLng pointInSegment,
        @NonNull MatchCandidate startCandidate, @NonNull MatchCandidate endCandidate) {
        WorkoutGeoPoint startCandidatePoint = startCandidate.candidate;
        WorkoutGeoPoint endCandidatePoint = endCandidate.candidate;
        float interpolateFactor = calculateInterpolateFactorBasedOnDistance(pointInSegment, startCandidatePoint,
                endCandidatePoint);

        WorkoutGeoPoint interpolatedCandidate = AndroidCoordinateUtils.calculateInterpolatedPoint
                (startCandidatePoint, endCandidatePoint, interpolateFactor);
        // Always use the end candidate position to avoid going backwards in the target workout
        return new MatchCandidate(interpolatedCandidate, endCandidate.candidatePosition);
    }

    private float calculateInterpolateFactorBasedOnDistance(@NonNull LatLng inBetweenPoint,
        @NonNull WorkoutGeoPoint startPoint, @NonNull WorkoutGeoPoint endPoint) {
        float[] results = new float[1];
        Location.distanceBetween(startPoint.getLatitude(), startPoint.getLongitude(), inBetweenPoint.getLatitude(),
                inBetweenPoint.getLongitude(), results);
        float distanceToProjection = results[0];
        Location.distanceBetween(startPoint.getLatitude(), startPoint.getLongitude(), endPoint.getLatitude(),
                endPoint.getLongitude(), results);
        float segmentDistance = results[0];
        return segmentDistance <= 0.0F ? 0 : distanceToProjection / segmentDistance;
    }

    private float calculateInterpolateFactorBasedOnDuration(int millisecondsInWorkout, WorkoutGeoPoint startPoint,
                                                            WorkoutGeoPoint endPoint) {
        float timeBetweenPoints = endPoint.getMillisecondsInWorkout() - startPoint.getMillisecondsInWorkout();
        float timeBetweenStartAndTarget = millisecondsInWorkout - startPoint.getMillisecondsInWorkout();
        return timeBetweenStartAndTarget / timeBetweenPoints;
    }

    /**
     * @param latitude,
     * @param longitude
     * @param minimumDistance
     * @param startPosition
     * @return the new smallest found or null if none could be found
     */
    private MatchCandidate findNextSmallestIn(double latitude, double longitude,
                                              float minimumDistance, int startPosition) {
        // We decided to check as many points ahead as half of the minimum distance :-/
        int aheadPointsToCheck = (int) (minimumDistance / 2);

        float newMinimum = minimumDistance;
        float maxDistance = 0;
        float results[] = new float[1];
        int newMinimumPosition = -1;
        for (int currentPos = startPosition; currentPos < targetRoutePoints.size(); currentPos++) {
            // Stop only if we've scanned enough points and we've found a point which had at least double the given
            // minimum distance
            int scannedPoints = currentPos - startPosition;
            if (scannedPoints > aheadPointsToCheck && maxDistance > minimumDistance * 2) {
                break;
            }
            WorkoutGeoPoint targetRoutePoint = targetRoutePoints.get(currentPos);
            Location.distanceBetween(latitude, longitude, targetRoutePoint.getLatitude(),
                    targetRoutePoint.getLongitude(), results);
            if (results[0] < newMinimum) {
                newMinimum = results[0];
                newMinimumPosition = currentPos;
            }
            if (results[0] > maxDistance) {
                maxDistance = results[0];
            }
        }
        if (newMinimumPosition != -1) {
            return new MatchCandidate(targetRoutePoints.get(newMinimumPosition), newMinimumPosition);
        } else {
            return null;
        }
    }

    @NonNull
    private LatLng calculateDistanceToNextSegment(double latitude, double longitude,
        @NonNull MatchCandidate currentCandidate) {
        int oneAfterCandidatePosition = currentCandidate.candidatePosition + 1;
        // Make sure we're not going outside the route list
        if (oneAfterCandidatePosition >= targetRoutePoints.size()) {
            oneAfterCandidatePosition = targetRoutePoints.size() - 1;
        }
        MatchCandidate oneAfterCurrentCandidate = new MatchCandidate(targetRoutePoints.get(oneAfterCandidatePosition)
                , oneAfterCandidatePosition);
        return calculateShortestDistanceToSegment(latitude, longitude, oneAfterCurrentCandidate.candidate,
                currentCandidate.candidate);
    }

    @NonNull
    private LatLng calculateDistanceToPreviousSegment(double latitude, double longitude,
        @NonNull MatchCandidate currentCandidate) {
        int oneBeforeCandidatePosition = currentCandidate.candidatePosition - 1;
        // Make sure we're not going outside the route list
        if (oneBeforeCandidatePosition < 0) {
            oneBeforeCandidatePosition = 0;
        }
        MatchCandidate oneBeforeCurrentCandidate = new MatchCandidate(targetRoutePoints.get
                (oneBeforeCandidatePosition), oneBeforeCandidatePosition);
        return calculateShortestDistanceToSegment(latitude, longitude, oneBeforeCurrentCandidate.candidate,
                currentCandidate.candidate);
    }

    @NonNull
    private LatLng calculateShortestDistanceToSegment(double latitude, double longitude,
                                                                     WorkoutGeoPoint segmentStart,
                                                                     WorkoutGeoPoint segmentEnd) {
        if (segmentStart.isSameLocation(segmentEnd)) {
            // If the start and end are the same we can return the location to any of them
            return new LatLng(segmentStart.getLongitude(), segmentStart.getLatitude());
        }
        return nearestPointOnSegment(latitude, longitude, segmentStart, segmentEnd);
    }

    @NonNull
    private LatLng nearestPointOnSegment(double latitude, double longitude,
        @NonNull WorkoutGeoPoint segmentStart, @NonNull WorkoutGeoPoint segmentEnd) {
        LatLng a = new LatLng(segmentStart.getLongitude(), segmentStart.getLatitude());
        LatLng b = new LatLng(segmentEnd.getLongitude(), segmentEnd.getLatitude());
        LatLng p = new LatLng(longitude, latitude);
        return CoordinateUtils.nearestPointOnLine(a, b, p, true);
    }

    /**
     * Finds the candidate that has the smallest distance starting from the {@code currentCandidate}. By smallest we
     * mean the next smallest point as far as the distance from location decreases.
     *
     * @param latitude
     * @param longitude
     * @param currentCandidate
     * @return the candidate with smallest distance (can be the given {@code currentCandidate})
     */
    private MatchCandidate findNextSmallest(double latitude, double longitude, MatchCandidate currentCandidate) {
        float[] distanceResults = new float[1];
        WorkoutGeoPoint candidate = currentCandidate.candidate;
        Location.distanceBetween(latitude, longitude, candidate.getLatitude(),
                candidate.getLongitude(), distanceResults);
        float smallestDistanceSoFar = distanceResults[0];
        for (int currentPos = currentCandidate.candidatePosition; currentPos < targetRoutePoints.size(); currentPos++) {
            WorkoutGeoPoint targetRoutePoint = targetRoutePoints.get(currentPos);
            Location.distanceBetween(latitude, longitude, targetRoutePoint.getLatitude(),
                    targetRoutePoint.getLongitude(), distanceResults);
            if (distanceResults[0] < smallestDistanceSoFar) {
                // If the distance is smaller then set it as the best candidate so far
                currentCandidate = new MatchCandidate(targetRoutePoint, currentPos);
                smallestDistanceSoFar = distanceResults[0];
            } else if (distanceResults[0] == smallestDistanceSoFar) {
                Timber.d("OngoingGhostTarget.findNextSmallest() exactly same distance found %d", currentPos);
                // If the distance is the same just continue searching
                continue;
            } else {
                // If the distance increases then stop searching
                break;
            }
        }
        // Return the best candidate found (potentially can be the initial given one)
        return currentCandidate;
    }

    private MatchCandidate searchInitialCandidate(double latitude, double longitude) throws IllegalStateException {
        if (targetRoutePoints == null) {
            throw new IllegalStateException("Target workout doesn't have route points");
        }
        float[] results = new float[1];
        // Loop through the points from the beginning until we find a workout that has distance less than
        // MAX_MATCH_DISTANCE from the given location or 80% of the total
        double maxPosition = targetRoutePoints.size() * 0.8;
        for (int currentPos = 0; currentPos < maxPosition; currentPos++) {
            WorkoutGeoPoint targetRoutePoint = targetRoutePoints.get(currentPos);
            Location.distanceBetween(latitude, longitude, targetRoutePoint.getLatitude(),
                    targetRoutePoint.getLongitude(), results);
            if (results[0] < MAX_MATCH_DISTANCE) {
                Timber.d("Found first ever P1 with distance %.2f and index %d: %s", results[0], currentPos,
                        targetRoutePoint);
                return new MatchCandidate(targetRoutePoint, currentPos);
            }
        }
        Timber.w("Can't find any initial candidate (P1)");
        return null;
    }

    /**
     * Request the next position closest to the given millisecondsInWorkout.
     * <p><em><b>IMPORTANT!!!</b></em> in order to avoid scanning the entire ghost route we remember the last
     * matched position index. Therefore the millisecondsInWorkout has to be always bigger than the last one
     * requested</p>
     *
     * @param millisecondsInWorkout
     * @return
     */
    public WorkoutGeoPoint getPositionAtDuration(int millisecondsInWorkout) throws IllegalStateException, GhostMatchNotFoundException {
        if (targetRoutePoints == null) {
            throw new IllegalStateException("Target workout doesn't have route points");
        }
        return findNextByDuration(previousDurationIdx, millisecondsInWorkout);
    }

    /**
     * Performs a sequential search starting at {@code startingIdx} to find the nearest position in the ghost target
     * to {@code millisecondsInWorkout}.
     *
     * @param startingIdx           initial index to scan from
     * @param millisecondsInWorkout
     * @return the closest match (might be interpolated) to the given time inside the ghost target route.
     * @throws com.stt.android.exceptions.GhostMatchNotFoundException
     */
    private WorkoutGeoPoint findNextByDuration(int startingIdx, int millisecondsInWorkout) throws
            GhostMatchNotFoundException {
        for (int idx = startingIdx; idx < targetRoutePoints.size(); idx++) {
            WorkoutGeoPoint targetRoutePoint = targetRoutePoints.get(idx);
            if (targetRoutePoint.getMillisecondsInWorkout() > millisecondsInWorkout) {
                if (idx == 0) {
                    return targetRoutePoint;
                }
                // So the match is in between idx and idx-1. Let's interpolate!! :-P
                WorkoutGeoPoint startPoint = targetRoutePoints.get(idx - 1);
                WorkoutGeoPoint endPoint = targetRoutePoints.get(idx);
                float interpolateFactor = calculateInterpolateFactorBasedOnDuration(millisecondsInWorkout,
                        startPoint, endPoint);
                // Remember tha last matched duration index in the route
                previousDurationIdx = idx - 1;
                return AndroidCoordinateUtils.calculateInterpolatedPoint(startPoint, endPoint, interpolateFactor);
            }
        }
        throw new GhostMatchNotFoundException("We could not find a match for the given duration " + millisecondsInWorkout);
    }

    public int calculateCurrentMatchTimeDifference(int referenceTime) throws GhostMatchNotFoundException {
        if (currentMatch == null || currentMatch.candidate == null) {
            throw new GhostMatchNotFoundException("There's no match available");
        }
        int ghostTime = currentMatch.candidate.getMillisecondsInWorkout() / 1000;
        return referenceTime - ghostTime;
    }

    @Override
    public long calculateCurrentMatchTimeDifferenceInMilliseconds(long referenceTimeInMilliseconds)
            throws GhostMatchNotFoundException {
        if (currentMatch == null || currentMatch.candidate == null) {
            throw new GhostMatchNotFoundException("There's no match available");
        }
        long ghostTime = currentMatch.candidate.getMillisecondsInWorkout();
        return referenceTimeInMilliseconds - ghostTime;
    }

    public double calculateCurrentMatchDistanceDifference(int timeReferenceInMilliseconds) throws GhostMatchNotFoundException {
        if (currentMatch == null || currentMatch.candidate == null) {
            throw new GhostMatchNotFoundException("There's no match available");
        }
        WorkoutGeoPoint ghostPositionAtCurrentTime = findByDuration(timeReferenceInMilliseconds);
        if (ghostPositionAtCurrentTime == null) {
            throw new GhostMatchNotFoundException("There's no match available");
        }
        double ghostMatchDistance = currentMatch.candidate.getTotalDistance();
        return ghostPositionAtCurrentTime.getTotalDistance() - ghostMatchDistance;
    }

    /**
     * Performs a binary search through all the ghost workout geo points and returns the matched
     * one or an interpolated value to the closes match.
     * <p/>
     * <em>NOTE: if the {@code millisecondsInWorkout} is
     * earlier than the first point then the first point is returned. If the
     * {@code millisecondsInWorkout} is after the last point then the last point is returned.</em>
     *
     * @param millisecondsInWorkout
     * @return the found workout geo point.
     */
    @Override
    public WorkoutGeoPoint findByDuration(int millisecondsInWorkout) {
        WorkoutGeoPoint dummyReferencePoint = new WorkoutGeoPoint(0, 0, 0, false, 0, 0,
                millisecondsInWorkout, 0, 0, 0);
        int foundPosition = Collections.binarySearch(targetRoutePoints, dummyReferencePoint,
                new WorkoutGeoPointTimeComparator());
        if (foundPosition < 0) {
            // We didn't find an exact match but got the position where it should be
            int expectedPosition = -foundPosition - 1;
            if (expectedPosition == targetRoutePoints.size()) {
                // If the point should be after the last point return the last point
                return targetRoutePoints.get(targetRoutePoints.size() - 1);
            }
            WorkoutGeoPoint endPoint = targetRoutePoints.get(expectedPosition);
            if (expectedPosition == 0) {
                // If the time is before the first point in the target workout return the first one.
                return endPoint;
            }
            WorkoutGeoPoint startPoint = targetRoutePoints.get(expectedPosition - 1);
            float interpolateFactor = calculateInterpolateFactorBasedOnDuration
                    (millisecondsInWorkout, startPoint, endPoint);
            return AndroidCoordinateUtils.calculateInterpolatedPoint(startPoint, endPoint,
                    interpolateFactor);
        }
        return targetRoutePoints.get(foundPosition);
    }

    /**
     * Dummy data structure to hold a candidate and its position inside {@link #targetRoutePoints}
     */
    private static class MatchCandidate {
        private final WorkoutGeoPoint candidate;
        private final int candidatePosition;

        private MatchCandidate(WorkoutGeoPoint candidate, int candidatePosition) {
            this.candidate = candidate;
            this.candidatePosition = candidatePosition;
        }

        @Override
        public String toString() {
            return "MatchCandidate{candidate="
                + candidate
                + ", candidatePosition="
                + candidatePosition
                + '}';
        }
    }

    private static class WorkoutGeoPointTimeComparator implements Comparator<WorkoutGeoPoint> {
        @Override
        public int compare(WorkoutGeoPoint lhsWorkoutGeoPoint, WorkoutGeoPoint rhsWorkoutGeoPoint) {
            int lhs = lhsWorkoutGeoPoint.getMillisecondsInWorkout();
            int rhs = rhsWorkoutGeoPoint.getMillisecondsInWorkout();
            return lhs < rhs ? -1 : (lhs == rhs ? 0 : 1);
        }
    }
}
