package com.stt.android.workouts

import com.google.gson.annotations.SerializedName
import com.stt.android.domain.weather.WeatherConditions

/**
 * GSON-serializable class for storing WeatherConditions on disk during on ongoing workout.
 */
class OngoingWorkoutWeatherConditions(
    @SerializedName("airPressure") val airPressure: Float?, // kPa
    @SerializedName("cloudiness") val cloudiness: Int?,
    @SerializedName("groundLevelAirPressure") val groundLevelAirPressure: Float?, // kPa
    @SerializedName("humidity") val humidity: Int?,
    @SerializedName("rainVolume1h") val rainVolume1h: Float?,
    @SerializedName("rainVolume3h") val rainVolume3h: Float?,
    @SerializedName("seaLevelAirPressure") val seaLevelAirPressure: Float?, // kPa
    @SerializedName("snowVolume1h") val snowVolume1h: Float?,
    @SerializedName("snowVolume3h") val snowVolume3h: Float?,
    @SerializedName("temperature") val temperature: Float?, // Kelvin
    @SerializedName("weatherIcon") val weatherIcon: String?,
    @SerializedName("windDirection") val windDirection: Float?, // Degrees
    @SerializedName("windSpeed") val windSpeed: Float? // m/s
) {
    fun toDomainWeatherConditions() =
        WeatherConditions(
            airPressure = airPressure,
            cloudiness = cloudiness,
            groundLevelAirPressure = groundLevelAirPressure,
            humidity = humidity,
            rainVolume1h = rainVolume1h,
            rainVolume3h = rainVolume3h,
            seaLevelAirPressure = seaLevelAirPressure,
            snowVolume1h = snowVolume1h,
            snowVolume3h = snowVolume3h,
            temperature = temperature,
            weatherIcon = weatherIcon,
            windDirection = windDirection,
            windSpeed = windSpeed
        )

    constructor(domainWeatherConditions: WeatherConditions) : this(
        airPressure = domainWeatherConditions.airPressure,
        cloudiness = domainWeatherConditions.cloudiness,
        groundLevelAirPressure = domainWeatherConditions.groundLevelAirPressure,
        humidity = domainWeatherConditions.humidity,
        rainVolume1h = domainWeatherConditions.rainVolume1h,
        rainVolume3h = domainWeatherConditions.rainVolume3h,
        seaLevelAirPressure = domainWeatherConditions.seaLevelAirPressure,
        snowVolume1h = domainWeatherConditions.snowVolume1h,
        snowVolume3h = domainWeatherConditions.snowVolume3h,
        temperature = domainWeatherConditions.temperature,
        weatherIcon = domainWeatherConditions.weatherIcon,
        windDirection = domainWeatherConditions.windDirection,
        windSpeed = domainWeatherConditions.windSpeed
    )
}
