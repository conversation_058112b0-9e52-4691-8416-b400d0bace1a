package com.stt.android.workouts.wearable;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import androidx.core.content.ContextCompat;
import com.google.android.gms.common.ConnectionResult;
import com.google.android.gms.common.api.GoogleApiClient;
import com.google.android.gms.common.data.FreezableUtils;
import com.google.android.gms.wearable.DataEvent;
import com.google.android.gms.wearable.DataEventBuffer;
import com.google.android.gms.wearable.DataItem;
import com.google.android.gms.wearable.DataMap;
import com.google.android.gms.wearable.DataMapItem;
import com.google.android.gms.wearable.Wearable;
import com.google.android.gms.wearable.WearableListenerService;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.stt.android.core.bridge.ThrowableSerializer;
import com.stt.android.core.bridge.WearConstants;
import com.stt.android.domain.user.ActivityTypeHelper;
import com.stt.android.domain.workout.ActivityType;
import com.stt.android.ui.activities.WorkoutActivity;
import com.stt.android.workouts.RecordWorkoutService;
import com.stt.android.workoutsettings.WorkoutSettingsActivity;
import java.util.ArrayList;
import timber.log.Timber;

/**
 * This service is known to leak the WearableListenerService.zzc IBinder when destroyed
 */
public class WearableListener extends WearableListenerService
    implements GoogleApiClient.ConnectionCallbacks, GoogleApiClient.OnConnectionFailedListener {
    private GoogleApiClient googleApiClient;

    @Override
    public void onCreate() {
        super.onCreate();

        googleApiClient =
            new GoogleApiClient.Builder(this, this, this).addApi(Wearable.API).build();
        googleApiClient.connect();
    }

    @Override
    public void onDestroy() {
        googleApiClient.disconnect();
        super.onDestroy();
    }

    @Override
    public void onDataChanged(DataEventBuffer dataEvents) {
        ArrayList<DataEvent> events = FreezableUtils.freezeIterable(dataEvents);
        dataEvents.release();
        for (DataEvent dataEvent : events) {
            switch (dataEvent.getType()) {
                case DataEvent.TYPE_CHANGED:
                    DataItem dataItem = dataEvent.getDataItem();
                    Uri dataItemUri = dataItem.getUri();
                    String path = dataItemUri.getPath();
                    if (WearConstants.ONGOING_WORKOUT_RECORDING_ACTION.equals(path)) {
                        handleRecordingAction(dataItem, dataItemUri);
                    } else if (WearConstants.WEARABLE_MODE.equals(path)) {
                        DataMap dataMap = DataMapItem.fromDataItem(dataItem).getDataMap();
                        boolean ambientModeOn = dataMap.getBoolean(WearConstants.AMBIENT_MODE_ON);
                        ContextCompat.startForegroundService(
                            this,
                            RecordWorkoutService.newWearAmbientModeUpdatedIntent(this, ambientModeOn)
                        );
                        if (googleApiClient.isConnected()) {
                            Wearable.DataApi.deleteDataItems(googleApiClient, dataItemUri);
                        }
                    } else if (WearConstants.WEARABLE_UI_EVENTS.equals(path)) {
                        if (googleApiClient.isConnected()) {
                            Wearable.DataApi.deleteDataItems(googleApiClient, dataItemUri);
                        }
                    } else if (WearConstants.PATH_EXCEPTION.equals(path)) {
                        handleCrashReport(dataItem, dataItemUri);
                    }
                    break;
                case DataEvent.TYPE_DELETED:
                    break;
            }
        }
    }

    private void handleRecordingAction(DataItem dataItem, Uri dataItemUri) {
        DataMap dataMap = DataMapItem.fromDataItem(dataItem).getDataMap();
        byte action = dataMap.getByte(WearConstants.RECORDING_ACTION, (byte) -1);
        String actionStr = "UNKNOWN";
        switch (action) {
            case WearConstants.START_WARM_UP:
                ContextCompat.startForegroundService(
                    this,
                    RecordWorkoutService.newStartWarmUpFromWearable(this)
                );
                byte activityType = dataMap.getByte(WearConstants.ACTIVITY_TYPE, (byte) -1);
                if (activityType == -1) {
                    startActivity(WorkoutSettingsActivity.newStartIntent(this)
                        .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK));
                } else {
                    ActivityType at = ActivityType.valueOf(activityType);
                    startActivity(WorkoutActivity.newStartIntent(this, at, true, true)
                        .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK));
                }
                actionStr = "START_WARM_UP";
                break;
            case WearConstants.STOP_WARM_UP:
                ContextCompat.startForegroundService(
                    this,
                    RecordWorkoutService.newStopWarmUpFromWearable(this)
                );
                actionStr = "STOP_WARM_UP";
                break;
            case WearConstants.PREPARE:
                ActivityType at =
                    ActivityType.valueOf(dataMap.getByte(WearConstants.ACTIVITY_TYPE, (byte) -1));
                ActivityTypeHelper.saveRecentActivity(this, at);
                ContextCompat.startForegroundService(
                    this,
                    RecordWorkoutService.newPrepareWorkoutIntent(this, at)
                );
                actionStr = "PREPARE";
                break;
            case WearConstants.START:
                ContextCompat.startForegroundService(
                    this,
                    RecordWorkoutService.newStartRecordingIntent(this)
                );
                startService(TTSStarter.newStartIntent(this));
                actionStr = "START";
                break;
            case WearConstants.STOP:
                ContextCompat.startForegroundService(
                    this,
                    RecordWorkoutService.newStopRecordingIntent(this)
                );
                actionStr = "STOP";
                break;
            case WearConstants.RESUME:
                ContextCompat.startForegroundService(
                    this,
                    RecordWorkoutService.newResumeRecordingIntent(this)
                );
                actionStr = "RESUME";
                break;
            case WearConstants.PAUSE:
                ContextCompat.startForegroundService(
                    this,
                    RecordWorkoutService.newPauseRecordingIntent(this)
                );
                actionStr = "PAUSE";
                break;
            case WearConstants.ADD_LAP:
                ContextCompat.startForegroundService(
                    this,
                    RecordWorkoutService.newLapIntent(this)
                );
                actionStr = "ADD_LAP";
                break;
        }
        if (googleApiClient.isConnected()) {
            Wearable.DataApi.deleteDataItems(googleApiClient, dataItemUri);
        }
    }

    private void handleCrashReport(DataItem dataItem, Uri dataItemUri) {
        DataMap dataMap = DataMapItem.fromDataItem(dataItem).getDataMap();
        String deviceModel = dataMap.getString(WearConstants.KEY_DEVICE_MODEL);
        String deviceManufacturer = dataMap.getString(WearConstants.KEY_DEVICE_MANUFACTURER);
        String threadName = dataMap.getString(WearConstants.KEY_THREAD_NAME);
        Throwable throwable = ThrowableSerializer.deserializeThrowable(
            dataMap.getByteArray(WearConstants.KEY_SERIALIZED_THROWABLE));
        FirebaseCrashlytics crashlytics = FirebaseCrashlytics.getInstance();
        crashlytics.log("Crashed on Android Wear");
        crashlytics.log("Device Model: " + deviceModel);
        crashlytics.log("Device Manufacturer: " + deviceManufacturer);
        crashlytics.log("Thread Name: " + threadName);
        Timber.w(throwable);

        if (googleApiClient.isConnected()) {
            Wearable.DataApi.deleteDataItems(googleApiClient, dataItemUri);
        }
    }

    @Override
    public void onConnected(Bundle bundle) {
        // do nothing
    }

    @Override
    public void onConnectionSuspended(int cause) {
        // do nothing
    }

    @Override
    public void onConnectionFailed(ConnectionResult connectionResult) {
        // do nothing
    }
}
