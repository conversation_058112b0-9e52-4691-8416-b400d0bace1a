package com.stt.android.workouts

import androidx.annotation.WorkerThread
import com.stt.android.domain.workouts.WeChatWorkoutDataSource
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.SummaryExtension
import javax.inject.Inject

class WeChatWorkoutRepository @Inject constructor(
    private val weChatWorkoutDataSource: WeChatWorkoutDataSource
) {

    @WorkerThread
    fun store(workoutHeader: WorkoutHeader, extension: SummaryExtension) {
        weChatWorkoutDataSource.store(workoutHeader, extension)
    }

    suspend fun updateStatus(id: Int, sync: <PERSON><PERSON><PERSON>) {
        weChatWorkoutDataSource.updateStatus(id, sync)
    }

    fun deleteAll() {
        weChatWorkoutDataSource.deleteAll()
    }
}
