package com.stt.android.workouts.wearable;

import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.IBinder;
import android.speech.tts.TextToSpeech;
import androidx.annotation.Nullable;

import androidx.core.content.ContextCompat;
import com.stt.android.R;
import com.stt.android.domain.user.ActivityTypeHelper;
import com.stt.android.domain.user.VoiceFeedbackSettingsHelper;
import com.stt.android.workouts.RecordWorkoutService;
import com.stt.android.workouts.tts.TextToSpeechHelper;

import java.util.Locale;

/**
 * This service is used to start Text-to-Speech engine, if the workout is started from a wearable
 * device.
 * <p/>
 * Here, we first check if the specific language is available, and falls back to English if not. If
 * neither language is available, we don't do anything.
 */
public class TTSStarter extends Service implements TextToSpeech.OnInitListener {
    public static Intent newStartIntent(Context context) {
        return new Intent(context, TTSStarter.class);
    }

    private static final String DEFAULT_LANGUAGE = Locale.ENGLISH.getLanguage();

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    private TextToSpeech textToSpeech;

    @Override
    public void onCreate() {
        super.onCreate();

        if (VoiceFeedbackSettingsHelper.getVoiceFeedbackSettings(this,
                ActivityTypeHelper.getLastActivity(this).getId()).enabled) {
            textToSpeech = new TextToSpeech(this, this);
        } else {
            stopSelf();
        }
    }

    @Override
    public void onInit(int status) {
        if (textToSpeech == null) {
            // just in case some weird engine won't stop even if TextToSpeech.shutdown() is called
            return;
        }

        if (status != TextToSpeech.SUCCESS) {
            stopSelf();
            return;
        }

        initializeTTSLanguage(getString(R.string.tts_language));
    }

    private void initializeTTSLanguage(String language) {
        switch (TextToSpeechHelper.isLanguageSupported(textToSpeech, language, false)) {
            case TextToSpeech.LANG_NOT_SUPPORTED:
                if (language.equals(DEFAULT_LANGUAGE)) {
                    // English not supported, do nothing
                    stopSelf();
                } else {
                    // falls back to English
                    initializeTTSLanguage(DEFAULT_LANGUAGE);
                }
                break;
            case TextToSpeech.LANG_AVAILABLE:
                ContextCompat.startForegroundService(
                    this,
                    RecordWorkoutService.newStartVoiceFeedbackIntent(this, language)
                );
                stopSelf();
                break;
            default:
                stopSelf();
        }
    }
}
