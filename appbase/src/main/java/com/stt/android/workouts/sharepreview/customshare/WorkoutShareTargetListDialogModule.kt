package com.stt.android.workouts.sharepreview.customshare

import com.stt.android.common.viewstate.ViewStateEpoxyController
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.android.components.FragmentComponent

@Module
@InstallIn(FragmentComponent::class)
abstract class WorkoutShareTargetListDialogModule {

    @Binds
    abstract fun bindWorkoutShareLinkTargetListController(
        workoutShareTargetListController: WorkoutShareTargetListController
    ): ViewStateEpoxyController<WorkoutShareLinkTargets>
}
