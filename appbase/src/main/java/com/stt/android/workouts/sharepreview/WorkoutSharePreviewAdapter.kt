package com.stt.android.workouts.sharepreview

import android.graphics.Bitmap
import android.view.View
import android.view.ViewGroup
import androidx.collection.SparseArrayCompat
import androidx.viewpager.widget.PagerAdapter
import com.stt.android.colorfultrack.HeartRateWorkoutColorfulTrackLoader
import com.stt.android.colorfultrack.PaceWorkoutColorfulTrackLoader
import com.stt.android.colorfultrack.PowerWorkoutColorfulTrackLoader
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.infomodel.SummaryItem
import com.stt.android.maps.MapSnapshotSpec
import com.stt.android.multimedia.sportie.SportieAspectRatio
import com.stt.android.multimedia.sportie.SportieInfo
import com.stt.android.multimedia.sportie.SportieItem
import com.stt.android.multimedia.sportie.SportieOverlayViewBase
import com.stt.android.multimedia.sportie.SportieOverlayViewBase.Companion.DEFAULT_GRAPH_INDEX
import com.stt.android.multimedia.sportie.SportieShareType
import com.stt.android.utils.firstOrNull
import com.stt.android.utils.map
import com.stt.android.utils.plus

class WorkoutSharePreviewAdapter(
    val items: List<SportieItem>,
    private val measurementUnit: MeasurementUnit,
    private val sharePreviewClickListener: SportieOverlayViewClickListener,
    private var aspectRatio: SportieAspectRatio,
    var selectedPosition: Int,
    private val defaultSportieInfo: SparseArrayCompat<SportieInfo>,
    private val initialSummaryItems: List<SummaryItem>,
    private val heartRateWorkoutColorfulTrackLoader: HeartRateWorkoutColorfulTrackLoader,
    private val paceWorkoutColorfulTrackLoader: PaceWorkoutColorfulTrackLoader,
    private val powerWorkoutColorfulTrackLoader: PowerWorkoutColorfulTrackLoader,
) : PagerAdapter(), OverlayItemChangeListener {

    private val views = SparseArrayCompat<WorkoutSharePreviewView>()
    private var editMode: Boolean = false

    // saving sporties infos for destroyed views
    private var savedSportieInfo: SparseArrayCompat<SportieInfo> = SparseArrayCompat(items.size)

    override fun isViewFromObject(view: View, `object`: Any): Boolean = view == `object`

    override fun getCount(): Int = items.size

    override fun instantiateItem(container: ViewGroup, position: Int): Any {
        val item = items[position]
        return when (item.shareType) {
            SportieShareType.ADD_PHOTO -> {
                val view = WorkoutShareAddPhotoView(
                    sharePreviewClickListener,
                    container.context
                )
                container.addView(view)
                view
            }

            SportieShareType.PHOTO,
            SportieShareType.LINK,
            SportieShareType.LINK_3D,
            SportieShareType.VIDEO,
            SportieShareType.VIDEO_3D,
            SportieShareType.MAP,
            SportieShareType.DIVE_TRACK,
            SportieShareType.LONG_SCREENSHOT,
            SportieShareType.BRIEF,
            SportieShareType.UNKNOWN -> {
                var previewPage = views[position]
                if (previewPage == null) {
                    previewPage = WorkoutSharePreviewView(
                        sportieItem = item,
                        measurementUnit = measurementUnit,
                        sharePreviewClickListener = sharePreviewClickListener,
                        aspectRatio = aspectRatio,
                        editMode = editMode,
                        initialSportieInfo = getSportieInfoForPosition(position),
                        heartRateWorkoutColorfulTrackLoader = heartRateWorkoutColorfulTrackLoader,
                        paceWorkoutColorfulTrackLoader = paceWorkoutColorfulTrackLoader,
                        powerWorkoutColorfulTrackLoader = powerWorkoutColorfulTrackLoader,
                        initialSummaryItems = initialSummaryItems,
                        context = container.context
                    )
                }
                container.addView(previewPage)
                views.put(position, previewPage)
                savedSportieInfo.remove(position)
                previewPage
            }
        }
    }

    override fun destroyItem(container: ViewGroup, position: Int, `object`: Any) {
        val view: WorkoutSharePreviewView? = views[position]
        view?.let {
            savedSportieInfo.put(position, it.getCurrentSportieInfo())
            views.remove(position)
        }
        container.removeView(`object` as View)
    }

    override fun onWorkoutValueChange(selectedTextViewIndex: Int, workoutValueIndex: Int) {
        for (i in 0 until views.size()) {
            views.valueAt(i).onWorkoutValueLabelSelected(selectedTextViewIndex, workoutValueIndex)
        }

        savedSportieInfo = savedSportieInfo.map { _, sportieInfo ->
            when (selectedTextViewIndex) {
                0 -> sportieInfo.copy(firstDataIndex = workoutValueIndex)
                1 -> sportieInfo.copy(secondDataIndex = workoutValueIndex)
                2 -> sportieInfo.copy(thirdDataIndex = workoutValueIndex)
                else -> sportieInfo
            }
        }
    }

    fun onShowActivityTypeAndStartTimeStateChange(
        showNothing: Boolean,
        showActivityType: Boolean,
        showActivityStartTime: Boolean
    ) {
        for (i in 0 until views.size()) {
            views.valueAt(i).updateShowActivityAndStartTimeState(showNothing, showActivityType, showActivityStartTime)
        }
        savedSportieInfo = savedSportieInfo.map { _, sportieInfo ->
            sportieInfo.copy(showActivityType = showActivityType, showStartTime = showActivityStartTime)
        }
    }

    fun getCurrentSportieInfo(): SparseArrayCompat<SportieInfo> {
        return views.map { _, preview -> preview.getCurrentSportieInfo() } + savedSportieInfo
    }

    fun getColorfulPolylines(itemPosition: Int): MapSnapshotSpec.ColorfulPolylines? =
        views[itemPosition]?.getColorfulPolylines()

    fun getDiveTrack(itemPosition: Int): Bitmap? = views[itemPosition]?.getDiveTrack()

    fun getSelectedItem() = items.getOrNull(selectedPosition)

    fun refreshAspectRatio(aspectRatio: SportieAspectRatio) {
        this.aspectRatio = aspectRatio
        for (i in 0 until views.size()) {
            views.valueAt(i).setAspectRatio(aspectRatio, selectedPosition == views.keyAt(i))
        }
    }

    fun setEditMode(editMode: Boolean) {
        this.editMode = editMode
        for (i in 0 until views.size()) {
            views.valueAt(i).setEditMode(editMode, editMode && selectedPosition == views.keyAt(i))
        }
    }

    fun showEditIcons() {
        for (i in 0 until views.size()) {
            if (selectedPosition == views.keyAt(i)) {
                views.valueAt(i).showEditIcons()
            }
        }
    }

    /**
     * Logic here is first one of the following:
     * -> saved sportie info in adapter
     * -> default sportie info value from constructor (possibly saved instance state)
     * -> first sportie value available from currents with empty description
     * -> static default sportie value
     */
    private fun getSportieInfoForPosition(position: Int): SportieInfo {
        return savedSportieInfo[position]
            ?: defaultSportieInfo[position]
            ?: getCurrentSportieInfo().firstOrNull()?.copy(descriptionText = "")
            ?: SportieInfo(
                SportieOverlayViewBase.DEFAULT_FIRST_DATA_INDEX,
                SportieOverlayViewBase.DEFAULT_SECOND_DATA_INDEX,
                SportieOverlayViewBase.DEFAULT_THIRD_DATA_INDEX,
                graphIndex = DEFAULT_GRAPH_INDEX
            )
    }

    override fun onWorkoutGraphChange(index: Int) {
        for (i in 0 until views.size()) {
            views.valueAt(i).onGraphSelected(index)
        }
        savedSportieInfo = savedSportieInfo.map { _, sportieInfo ->
            sportieInfo.copy(graphIndex = index)
        }
    }
}
