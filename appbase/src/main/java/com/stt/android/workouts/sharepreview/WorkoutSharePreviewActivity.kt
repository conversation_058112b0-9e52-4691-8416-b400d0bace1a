package com.stt.android.workouts.sharepreview

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Parcelable
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityOptionsCompat
import androidx.core.content.IntentCompat
import androidx.fragment.app.commit
import com.stt.android.R
import com.stt.android.databinding.ActivityWorkoutSharePreviewBinding
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.infomodel.SummaryItem
import com.stt.android.multimedia.sportie.SportieShareSource
import com.stt.android.utils.STTConstants
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class WorkoutSharePreviewActivity : AppCompatActivity() {
    private val binding: ActivityWorkoutSharePreviewBinding by lazy {
        ActivityWorkoutSharePreviewBinding.inflate(layoutInflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        setSupportActionBar(binding.toolbarSharePreview)
        supportActionBar?.apply {
            setDisplayShowHomeEnabled(false)
            setDisplayHomeAsUpEnabled(true)
            title = getString(R.string.share)
        }
        binding.toolbarSharePreview.setNavigationOnClickListener { finish() }

        val workoutHeader = IntentCompat.getParcelableExtra(
            intent,
            STTConstants.ExtraKeys.WORKOUT_HEADER,
            WorkoutHeader::class.java
        )
        val currentIndex = intent.getIntExtra(CURRENT_ITEM_INDEX, 0)
        val workoutDetail = IntentCompat.getParcelableExtra(
            intent,
            EXTRA_SHARE_SOURCE, SportieShareSource::class.java
        )
        val currentSummaryItems =
            intent.getStringArrayListExtra(CURRENT_SUMMARY_ITEMS) ?: arrayListOf()
        supportFragmentManager.commit {
            add(
                R.id.fragment_container_view,
                WorkoutSharePreviewFragment.newInstance(
                    workoutHeader?.id,
                    currentIndex,
                    workoutDetail,
                    currentSummaryItems,
                ),
                PREVIEW_FRAGMENT_TAG
            )
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        // ImagePicker use  activity.startActivityForResult() in WorkoutSharePreviewFragment
        // so need to call WorkoutSharePreviewFragment's onActivityResult()
        if (requestCode == STTConstants.RequestCodes.PICK_WORKOUT_PICTURE) {
            supportFragmentManager.findFragmentByTag(PREVIEW_FRAGMENT_TAG)?.onActivityResult(
                requestCode,
                resultCode,
                data
            )
        }
    }

    companion object {
        const val CURRENT_ITEM_INDEX = "com.stt.android.CURRENT_ITEM_INDEX"
        const val CURRENT_SUMMARY_ITEMS = "com.stt.android.CURRENT_SUMMARY_ITEMS"
        const val EXTRA_SHARE_SOURCE = "EXTRA_SHARE_SOURCE"
        private const val PREVIEW_FRAGMENT_TAG = "WorkoutSharePreviewFragment"

        fun newStartIntent(
            workoutHeader: WorkoutHeader,
            context: Context,
            itemIndex: Int,
            source: SportieShareSource,
            defaultSummaryItems: List<SummaryItem> = emptyList(),
        ): Pair<Intent, ActivityOptionsCompat> {
            val intent = Intent(context, WorkoutSharePreviewActivity::class.java).apply {
                putExtra(STTConstants.ExtraKeys.WORKOUT_HEADER, workoutHeader)
                putExtra(CURRENT_ITEM_INDEX, itemIndex)
                putExtra(EXTRA_SHARE_SOURCE, source as Parcelable)
                putStringArrayListExtra(
                    CURRENT_SUMMARY_ITEMS,
                    ArrayList(defaultSummaryItems.map { it.name })
                )
            }

            val options = ActivityOptionsCompat.makeCustomAnimation(
                context,
                R.anim.activity_open_enter,
                R.anim.activity_open_exit
            )

            return intent to options
        }
    }
}
