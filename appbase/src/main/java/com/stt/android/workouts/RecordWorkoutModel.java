package com.stt.android.workouts;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.stt.android.domain.routes.Route;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.domain.workout.ActivityType;
import javax.inject.Inject;
import javax.inject.Singleton;
import rx.Observable;
import rx.subjects.BehaviorSubject;
import rx.subjects.Subject;

@Singleton
public class RecordWorkoutModel {
    @Inject
    public RecordWorkoutModel() {
    }

    private final Subject<TrackingState, TrackingState> recordingStateSubject =
        BehaviorSubject.create(TrackingState.NOT_STARTED).toSerialized();
    @Nullable
    private RecordWorkoutService service;

    /* Package-only */
    void setService(@Nullable RecordWorkoutService service) {
        this.service = service;
    }

    public Observable<TrackingState> getRecordingStateObservable() {
        return recordingStateSubject.asObservable();
    }

    public void recordingStateChanged(TrackingState trackingState) {
        recordingStateSubject.onNext(trackingState);
    }

    public ActivityType getActivityType() {
        if (service == null) {
            throw new IllegalStateException("RWS not available");
        }
        return service.getActivityType();
    }

    @Nullable
    public WorkoutHeader getFollowWorkoutHeader() {
        if (service == null) {
            throw new IllegalStateException("RWS not available");
        }
        return service.getFollowWorkoutHeader();
    }

    @Nullable
    public WorkoutHeader getGhostWorkoutHeader() {
        if (service == null) {
            throw new IllegalStateException("RWS not available");
        }
        return service.getGhostTargetWorkoutHeader();
    }

    @Nullable
    public Route getFollowRoute() {
        if (service == null) {
            throw new IllegalStateException("RWS not available");
        }
        return service.getFollowRoute();
    }

    @NonNull
    public TrackingState getRecordingState() {
        return service != null ? service.getCurrentState() : TrackingState.NOT_STARTED;
    }
}
