package com.stt.android.workouts.extensions

import com.stt.android.controllers.ExtensionDataModel
import com.stt.android.controllers.ExtensionRemoteMapper.convertRemoteExtensions
import com.stt.android.controllers.WorkoutExtensionDataModels
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.user.workoutextension.IntensityExtension
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.ExtensionsDataSource
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.remote.extensions.ExtensionsRemoteApi
import com.stt.android.remote.extensions.RemoteWorkoutExtension
import com.stt.android.workouts.binary.FsBinaryFileRepository
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.async
import kotlinx.coroutines.supervisorScope
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import kotlin.reflect.KClass

class ExtensionsRepository
@Inject constructor(
    private val extensionsRemoteApi: ExtensionsRemoteApi,
    private val workoutExtensionDataModels: WorkoutExtensionDataModels,
    private val binaryFileRepository: FsBinaryFileRepository,
    private val extensionDataModelMap: Map<
        Class<out @JvmSuppressWildcards WorkoutExtension>,
        @JvmSuppressWildcards ExtensionDataModel<out @JvmSuppressWildcards WorkoutExtension>
        >
) : ExtensionsDataSource {
    override suspend fun getExtensionsForWorkout(
        workoutHeader: WorkoutHeader
    ): List<WorkoutExtension> = withContext(IO) {
        // SlopeSki activity needs geo points from disk
        var geoPointsForSlopeSki: List<WorkoutGeoPoint>? = null
        if (workoutHeader.activityType.isSlopeSki && !workoutHeader.manuallyAdded) {
            try {
                geoPointsForSlopeSki =
                    binaryFileRepository.readWorkoutDataFromDisk(workoutHeader).routePoints
            } catch (ignored: Exception) {
                // If we can't get the points for slopeSki let's ignore
            }
        }

        workoutExtensionDataModels.buildExtensions(workoutHeader, geoPointsForSlopeSki)
    }

    override suspend fun upsertExtension(workoutId: Int, extension: WorkoutExtension) =
        withContext<Unit>(IO) {
            extensionDataModelMap[extension::class.java]?.upsert(workoutId, extension)
        }

    override suspend fun findExtensionsByType(
        extensionClass: KClass<out WorkoutExtension>
    ): List<WorkoutExtension> = withContext(IO) {
        extensionDataModelMap[extensionClass.java]?.findAll() ?: emptyList()
    }

    override suspend fun loadExtensions(workoutId: Int): List<WorkoutExtension> = supervisorScope {
        withContext(IO) {
            Timber.d("Loading extensions for workout ID: $workoutId from local DB")
            extensionDataModelMap.values.map {
                async { it.findByWorkoutId(workoutId) }
            }.mapNotNull {
                runSuspendCatching {
                    it.await()
                }.getOrElse { e ->
                    Timber.w(e, "Failed to load extension from local DB or extension doesn't exist for workout")
                    null
                }
            }
        }
    }

    override suspend fun loadIntensityExtension(workoutId: Int): IntensityExtension? =
        withContext(IO) {
            extensionDataModelMap[IntensityExtension::class.java]?.findByWorkoutId(workoutId) as? IntensityExtension
        }

    override suspend fun fetchExtensions(
        workoutId: Int,
        workoutKey: String
    ): List<WorkoutExtension> = withContext(IO) {
        Timber.d("Fetching workout extensions from server for workout KEY: $workoutKey")
        convertRemoteExtensions(
            workoutId,
            extensionsRemoteApi.fetchExtensions(
                workoutKey,
                RemoteWorkoutExtension.Type.textValues
            ),
        )
    }
}
