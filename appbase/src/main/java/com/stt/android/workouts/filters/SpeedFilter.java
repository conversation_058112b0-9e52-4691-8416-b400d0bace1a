package com.stt.android.workouts.filters;

import android.location.Location;
import androidx.annotation.Nullable;
import com.stt.android.domain.workout.ActivityType;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class SpeedFilter {
    private static class Movement {
        float distance;
        float time;
        float speed;

        Movement() {
            distance = -1.0F;
            time = -1.0F;
            speed = -1.0F;
        }
    }

    private static final float MAXIMUM_ACCELERATION = 10.0F;
    private static final float MAXIMUM_SPEED_IN_METERS_PER_SECOND = 280.0F;
    private static final float MAXIMUM_OTHER_ACTIVITIES_SPEED_IN_METERS_PER_SECOND = 4.45F;
    private static final int MAXIMUM_ZERO_SPEED_OBSERVATIONS = 6;

    private static final int STATE_STARTING = 0;
    private static final int STATE_STARTED = 1;

    private final Movement[] movements = new Movement[60];
    private int oldestMovement;
    private int latestMovement;

    private int state;

    private Location lastLocation;
    private Location lastAcceptedLocation;

    private int zeroSpeedObservations = 0;
    private boolean firstTimeOtherSpeedOverMax;
    private boolean firstTimeOtherSpeedBelowMax;

    private ActivityType activityType;

    @Inject
    public SpeedFilter() {
        for (int i = 0; i < movements.length; ++i) {
            movements[i] = new Movement();
        }
        reset(null);
    }

    public void reset(@Nullable ActivityType activityType) {
        this.activityType = activityType != null ? activityType : ActivityType.OTHER_1;
        firstTimeOtherSpeedOverMax = true;
        firstTimeOtherSpeedBelowMax = true;
        oldestMovement = -1;
        latestMovement = -1;
    }

    public boolean filter(Location location) {
        boolean shouldFilter = false;

        if (lastAcceptedLocation == null) {
            // first location, mark the speed as 0
            location.setSpeed(0.0F);
        } else if (isTooFast(location)) {
            // human can't move this fast
            shouldFilter = true;
        } else if (filterBySpeed(location)) {
            shouldFilter = true;
        }

        lastLocation = location;

        if (!shouldFilter) {
            lastAcceptedLocation = lastLocation;
        }

        return shouldFilter;
    }

    private boolean isTooFast(Location location) {
        float distanceDelta = lastLocation.distanceTo(location);
        float timeDelta = (location.getTime() - lastLocation.getTime()) / 1000.0F;
        return distanceDelta / timeDelta > MAXIMUM_SPEED_IN_METERS_PER_SECOND;
    }

    private boolean filterBySpeed(Location location) {
        float distanceDelta = lastAcceptedLocation.distanceTo(location);
        float timeDelta = (location.getTime() - lastAcceptedLocation.getTime()) / 1000.0F;
        float speed = distanceDelta / timeDelta;
        switch (state) {
            case STATE_STARTING:
                if (distanceDelta < activityType.getZeroSpeedThreshold() * 1.0) {
                    // the movement is way too small
                    location.setSpeed(0.0F);
                    return true;
                }
                location.setSpeed(speed);
                state = STATE_STARTED;
                break;
            case STATE_STARTED:
                if (speed < activityType.getZeroSpeedThreshold()) {
                    // moving very slowly
                    if (++zeroSpeedObservations > MAXIMUM_ZERO_SPEED_OBSERVATIONS) {
                        // too many slow observations, start over
                        location.setSpeed(0.0F);
                        reset(activityType);
                        zeroSpeedObservations = 0;
                        state = STATE_STARTING;
                    } else {
                        // mark speed the same as last one
                        location.setSpeed(lastAcceptedLocation.getSpeed());
                        addMovement(distanceDelta, timeDelta, speed);
                    }
                } else {
                    zeroSpeedObservations = 0;

                    if (latestMovement >= 0) {
                        float speedDelta = Math.abs(speed - movements[latestMovement].speed);
                        if (speedDelta / timeDelta > MAXIMUM_ACCELERATION) {
                            // human is not supposed to be able to accelerate this fast
                            return true;
                        }
                    }

                    addMovement(distanceDelta, timeDelta, speed);
                    location.setSpeed(calculateSpeed());
                }
                break;
            default:
                break;
        }

        return false;
    }

    private void addMovement(float distance, float time, float speed) {
        if (++latestMovement >= movements.length) {
            latestMovement = 0;
        }
        if (latestMovement == oldestMovement) {
            if (++oldestMovement >= movements.length) {
                oldestMovement = 0;
            }
        } else if (oldestMovement == -1) {
            oldestMovement = 0;
        }

        Movement movement = movements[latestMovement];
        movement.distance = distance;
        movement.time = time;
        movement.speed = speed;
    }

    private float calculateSpeed() {
        if (activityType.equals(ActivityType.CYCLING) || activityType.equals(ActivityType.GRAVEL_CYCLING)) {
            return calculateSpeed(6);
        } else if (activityType.equals(ActivityType.RUNNING) || activityType.equals(
            ActivityType.TRAIL_RUNNING) || activityType.equals(ActivityType.WALKING) || activityType
            .equals(ActivityType.HIKING)) {
            return calculateSpeed(60);
        } else {
            return calculateOtherSpeed();
        }
    }

    private float calculateSpeed(int numberOfMovementsToUse) {
        // calculate the speed as an average speed of recent movements
        float accumulatedDistance = 0.0F;
        float accumulatedTime = 0.0F;
        int count = 0;
        int end =
            latestMovement < oldestMovement ? oldestMovement - movements.length : oldestMovement;
        for (int i = latestMovement; i >= end; --i) {
            Movement movement = movements[(i + movements.length) % movements.length];
            accumulatedDistance += movement.distance;
            accumulatedTime += movement.time;
            if (++count >= numberOfMovementsToUse) {
                break;
            }
        }
        return accumulatedDistance == 0.0F || accumulatedTime == 0.0F ? 0.0F
            : accumulatedDistance / accumulatedTime;
    }

    private float calculateOtherSpeed() {
        if (calculateSpeed(3) > MAXIMUM_OTHER_ACTIVITIES_SPEED_IN_METERS_PER_SECOND) {
            if (firstTimeOtherSpeedOverMax) {
                trimMovementsToSize(2);
                firstTimeOtherSpeedOverMax = false;
                firstTimeOtherSpeedBelowMax = true;
            }
            return calculateSpeed(6);
        } else {
            if (firstTimeOtherSpeedBelowMax) {
                trimMovementsToSize(2);
                firstTimeOtherSpeedBelowMax = true;
                firstTimeOtherSpeedOverMax = false;
            }
            return calculateSpeed(30);
        }
    }

    private void trimMovementsToSize(int size) {
        if ((latestMovement - oldestMovement + movements.length) % movements.length <= size) {
            // we don't have movements more than expected, do nothing
            return;
        }
        oldestMovement = (latestMovement - size + 1 + movements.length) % movements.length;
    }
}
