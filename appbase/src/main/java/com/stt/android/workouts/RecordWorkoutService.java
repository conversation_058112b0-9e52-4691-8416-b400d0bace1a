package com.stt.android.workouts;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.ActivityManager;
import android.app.Notification;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.content.pm.ServiceInfo;
import android.hardware.SensorManager;
import android.location.Location;
import android.location.LocationManager;
import android.os.Binder;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.IBinder;
import android.os.Looper;
import android.os.Message;
import android.os.Parcelable;
import android.os.PowerManager;
import android.os.SystemClock;
import android.telephony.TelephonyManager;
import androidx.annotation.IntDef;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.NotificationManagerCompat;
import androidx.core.app.ServiceCompat;
import androidx.core.content.ContextCompat;
import androidx.core.util.Pair;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.soy.algorithms.met.METCalculator;
import com.soy.algorithms.tss.TSSCalculationMethod;
import com.soy.algorithms.tss.TSSCalculator;
import com.soy.algorithms.tss.TSSInput;
import com.soy.algorithms.tss.TSSSample;
import com.soy.algorithms.utils.Result;
import com.stt.android.FeatureFlags;
import com.stt.android.R;
import com.stt.android.bluetooth.CadenceEventListener;
import com.stt.android.bluetooth.HrEventListener;
import com.stt.android.cadence.CadenceHelper;
import com.stt.android.controllers.CurrentUserController;
import com.stt.android.controllers.UserSettingsController;
import com.stt.android.data.workout.tss.SupportedTSSCalculationMethodRepository;
import com.stt.android.data.workout.tss.TSSUtilsKt;
import com.stt.android.di.IsSuuntoFlavor;
import com.stt.android.domain.Point;
import com.stt.android.domain.routes.GetRouteUseCase;
import com.stt.android.domain.routes.Route;
import com.stt.android.domain.user.ActivityTypeHelper;
import com.stt.android.domain.user.AltitudeSource;
import com.stt.android.domain.user.CadenceDataSource;
import com.stt.android.domain.user.ImageInformation;
import com.stt.android.domain.user.LapSettingHelper;
import com.stt.android.domain.user.MeasurementUnit;
import com.stt.android.domain.user.UserSettings;
import com.stt.android.domain.weather.WeatherConditions;
import com.stt.android.domain.workout.ActivityType;
import com.stt.android.domain.workout.AutoPause;
import com.stt.android.domain.workout.GhostDistanceTimeState;
import com.stt.android.domain.workout.SpeedPaceState;
import com.stt.android.domain.workout.Workout;
import com.stt.android.domain.workout.WorkoutCadenceEvent;
import com.stt.android.domain.workout.WorkoutData;
import com.stt.android.domain.workout.WorkoutGeoPoint;
import com.stt.android.domain.workout.WorkoutHrEvent;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.domain.workouts.extensions.WorkoutExtension;
import com.stt.android.domain.workouts.tss.TSS;
import com.stt.android.exceptions.DozeException;
import com.stt.android.exceptions.GhostMatchNotFoundException;
import com.stt.android.exceptions.InitialGhostMatchNotFoundException;
import com.stt.android.hr.BatteryStatus;
import com.stt.android.hr.BluetoothHeartRateEvent;
import com.stt.android.hr.HeartRateEvent;
import com.stt.android.laps.CompleteLap;
import com.stt.android.laps.Laps;
import com.stt.android.laps.ManualLaps;
import com.stt.android.laps.OngoingLap;
import com.stt.android.laps.ParcelableCompleteLap;
import com.stt.android.location.LastLocationRequest;
import com.stt.android.location.LocationModel;
import com.stt.android.ski.CompleteSkiRun;
import com.stt.android.ski.SlopeSki;
import com.stt.android.ui.controllers.WorkoutDataLoaderController;
import com.stt.android.usecases.startup.AppStabilityReportingUseCase;
import com.stt.android.utils.DateUtils;
import com.stt.android.utils.DeviceUtils;
import com.stt.android.utils.STTConstants;
import com.stt.android.utils.UpdatePressureTask;
import com.stt.android.utils.WorkoutShareUtils;
import com.stt.android.workouts.autosave.AutoSaveOngoingWorkoutController;
import com.stt.android.workouts.autosave.TooManyAutoRecoversException;
import com.stt.android.workouts.edit.SaveWorkoutService;
import com.stt.android.workouts.filters.DistanceFilter;
import com.stt.android.workouts.filters.LocationFilter;
import com.stt.android.workouts.filters.SpeedFilter;
import com.stt.android.workouts.hardware.CadenceConnectionMonitor;
import com.stt.android.workouts.hardware.HeartRateConnectionMonitor;
import com.stt.android.workouts.hardware.steps.StepCountConnection;
import com.stt.android.workouts.tts.Spokeswoman;
import com.stt.android.workouts.wearable.WearableController;
import dagger.hilt.android.AndroidEntryPoint;
import io.reactivex.Observable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;
import java.io.IOException;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.inject.Inject;
import pub.devrel.easypermissions.EasyPermissions;
import timber.log.Timber;

/**
 * This service is both started and bound. That is, the service can be started
 * by calling {@link Context#startService(Intent)}, which allows the service to
 * run indefinitely, and also allow a client to bind to the service by calling
 * {@link Context#bindService(Intent, android.content.ServiceConnection, int)}.
 * Use {@link Context#startService(Intent)} to change the state of the recording
 * but use binding to get data from the workout being recorded.
 */
@AndroidEntryPoint
public class RecordWorkoutService extends Service
    implements WorkoutDataLoaderController.Listener,
    StepCountConnection.StepCountListener,
    WeatherConditionsProvider.Listener
{
    private static final String KEY_ACTION = "com.stt.android.KEY_ACTION";
    private static final String KEY_WARM_UP_ID = "com.stt.android.KEY_WARM_UP_ID";
    private static final String KEY_ACTIVITY_TYPE = "com.stt.android.KEY_ACTIVITY_TYPE";
    private static final String KEY_VOICE_FEEDBACK_LANGUAGE =
        "com.stt.android.KEY_VOICE_FEEDBACK_LANGUAGE";
    private static final String KEY_PICTURE_NAME = "com.stt.android.KEY_PICTURE_FILE_NAME";
    private static final String KEY_PICTURE_MD5 = "com.stt.android.KEY_PICTURE_MD5";
    private static final String KEY_PICTURE_LOCATION = "com.stt.android.KEY_PICTURE_LOCATION";
    private static final String KEY_PICTURE_WIDTH = "com.stt.android.KEY_PICTURE_WIDTH";
    private static final String KEY_PICTURE_HEIGHT = "com.stt.android.KEY_PICTURE_HEIGHT";
    private static final String KEY_WEAR_AMBIENT_MODE_ON =
        "com.stt.android.KEY_WEAR_AMBIENT_MODE_ON";
    /**
     * Gets all components ready, but do not start recording yet.
     */
    private static final int ACTION_START_WARM_UP = 0;
    /**
     * If not currently recording, and the given warm up ID matches the latest warm up ID given
     * by {@link RecordWorkoutService#ACTION_START_WARM_UP}, stops all components and the service.
     */
    private static final int ACTION_STOP_WARM_UP = 1;
    /**
     * Notifies the service that the wearable wants to start a workout. This should come before
     * {@link RecordWorkoutService#ACTION_START_WARM_UP}.
     */
    private static final int ACTION_START_WARM_UP_FROM_WEARABLE = 2;
    /**
     * Notifies the service that the wearable wants to stop warming up. This should come after
     * {@link RecordWorkoutService#ACTION_STOP_WARM_UP}.
     */
    private static final int ACTION_STOP_WARM_UP_FROM_WEARABLE = 3;
    /**
     * Notifies the service the essential information needed for recording.
     */
    private static final int ACTION_PREPARE = 4;
    /**
     * Notifies the service to try to recover an auto-saved workout.
     */
    private static final int ACTION_RECOVER_AUTO_SAVED_WORKOUT = 5;
    /**
     * Notifies the service to start recording.
     */
    private static final int ACTION_START_RECORDING = 6;
    /**
     * Notifies the service to stop recording, but do not end the current workout yet.
     */
    private static final int ACTION_STOP_RECORDING = 7;
    /**
     * Notifies the service to pause recording.
     */
    private static final int ACTION_PAUSE_RECORDING = 8;
    /**
     * Notifies the service to resume recording.
     */
    private static final int ACTION_RESUME_RECORDING = 9;
    /**
     * Notifies the service to add a (manual) lap.
     */
    private static final int ACTION_ADD_LAP = 10;
    /**
     * Add a new picture to the workout.
     */
    private static final int ACTION_ADD_PICTURE = 11;
    /**
     * Notifies the service to start text-to-speech.
     */
    private static final int ACTION_START_TTS = 12;
    /**
     * Notifies the service that the ambient mode on connected wearable device is updated.
     */
    private static final int ACTION_WEAR_AMBIENT_MODE_UPDATED = 13;
    /**
     * Notifies the service the workout target.
     */
    private static final int ACTION_PREPARE_WORKOUT_TARGET = 14;
    private static final long TIME_UPDATE_INTERVAL_IN_MILLISECONDS = 500L;
    /**
     * Maximum amount of time that has passed since the last auto saved data was written to
     * decide if we should continue with the auto-saved workout or store it to disk.
     */
    private static final long MAX_AGE_TO_CONTINUE_AUTO_SAVED_WORKOUT = DateUtils.HOUR_IN_MILLIS;
    /**
     * Maximum amount of time that has passed since the last auto saved data was written to
     * decide if we should continue with the auto-saved workout in resumed or paused state.
     */
    private static final long MAX_AGE_TO_RESUME_AUTO_SAVED_WORKOUT =
        5L * DateUtils.MINUTE_IN_MILLIS;
    private static final int AUTO_PAUSE_THRESHOLD_FOR_CADENCE = 5;
    final Object ongoingWorkoutLock = new Object();
    final Handler autoSaveHandler = new Handler();
    private final Spokeswoman spokeswoman = new Spokeswoman();
    private final List<ImageInformation> pendingPictures = new ArrayList<>();
    private final IBinder binder = new ServiceBinder(this);
    private final HandlerThread workerThread =
        new HandlerThread("com.stt.android.RecordWorkoutService.workerThread");
    /**
     * Amount of minutes outside of Doze mode. That is, being active.
     */
    long minutesActive = 0;
    long activeModeStartTimestamp = 0;
    @Inject
    WorkoutDataLoaderController workoutDataLoaderController;
    @Inject
    CurrentUserController currentUserController;
    @Inject
    UserSettingsController userSettingsController;
    @Inject
    LocalBroadcastManager localBM;
    @Inject
    LocationModel locationModel;
    @Inject
    LocationFilter locationFilter;
    @Inject
    SpeedFilter speedFilter;
    @Inject
    DistanceFilter distanceFilter;
    @Inject
    RecordWorkoutModel recordWorkoutModel;
    @Inject
    SensorManager sensorManager;
    @Inject
    GetRouteUseCase getRouteUseCase;
    @Inject
    FeatureFlags featureFlags;
    @Inject
    WorkoutShareUtils workoutShareUtils;
    @Inject
    WeatherConditionsProvider weatherConditionsProvider;
    @Inject
    PreventDozeServiceHooks preventDozeServiceHooks;
    @Inject
    SupportedTSSCalculationMethodRepository supportedTSSCalculationMethodRepository;

    @Inject
    @Nullable
    TelephonyManager telephonyManager;

    @Inject
    @IsSuuntoFlavor
    Boolean isSuuntoFlavor;

    @Inject
    AppStabilityReportingUseCase appStabilityReportingUseCase;
    // Use volatile on double-checked locking mutable objects
    // (More at http://errorprone.info/bugpattern/DoubleCheckedLocking)
    volatile OngoingWorkout ongoingWorkout;
    WearableController wearableController;
    ActivityType activityType;
    double duration = -1.0;
    float lastSpeed;
    SpeedPaceState currentSpeedPaceState = SpeedPaceState.DEFAULT;
    GhostDistanceTimeState currentGhostTimeDistanceState = GhostDistanceTimeState.DEFAULT;
    boolean isLocked;
    private final BroadcastReceiver uiStateListener = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (STTConstants.BroadcastActions.SPEED_PACE_STATE_CHANGED.equals(action)) {
                currentSpeedPaceState = (SpeedPaceState) intent.getSerializableExtra(
                    STTConstants.ExtraKeys.SPEED_PACE_STATE);
            } else if (STTConstants.BroadcastActions.GHOST_STATE_CHANGED.equals(action)) {
                currentGhostTimeDistanceState =
                    (GhostDistanceTimeState) intent.getSerializableExtra(
                        STTConstants.ExtraKeys.GHOST_TIME_DISTANCE_STATE);
            } else if (STTConstants.BroadcastActions.LOCK_STATE_CHANGED.equals(action)) {
                isLocked = intent.getBooleanExtra(STTConstants.ExtraKeys.LOCK_STATE, false);
            }
        }
    };
    Location unfilteredLocation = null;
    Location lastLocation = null;
    Location locationToProcess;
    HeartRateConnectionMonitor hrmConnection = null;
    BluetoothHeartRateEvent heartRateEventToProcess;

    Handler threadedHandler;
    CadenceConnectionMonitor cadenceConnection = null;
    WorkoutCadenceEvent lastCadenceEvent = null;
    WorkoutCadenceEvent cadenceEventToProcess = null;
    int totalWheelRevolution = -1;
    int pausedCount = 0;
    private boolean needToFetchWeatherConditions = false;
    private final CadenceEventListener cadenceEventListener = new CadenceEventListener() {
        @Override
        public void onCadenceUpdated(long timestamp, int crankRoundsPerMinute,
            int totalWheelRevolution, int wheelRevolution, int wheelTimeDelta,
            double wheelRoundsPerSecond) {
            cadenceEventToProcess =
                new WorkoutCadenceEvent(timestamp, crankRoundsPerMinute, wheelTimeDelta,
                    wheelRoundsPerSecond, wheelRevolution,
                    userSettingsController.getSettings().getWheelCircumference());
            RecordWorkoutService.this.totalWheelRevolution = totalWheelRevolution;
            threadedHandler.post(onCadenceUpdatedTask);
        }

        @Override
        public void onDeviceConnected() {
            synchronized (ongoingWorkoutLock) {
                if (ongoingWorkout != null) {
                    ongoingWorkout.setCadenceDataSource(
                        userSettingsController.getSettings().getCadenceDataSource());
                }
            }
        }

        @Override
        public void onDeviceDisconnected() {
            Timber.w("Error occurred for cadence connection, trying to reconnect");
            stopRecordingCadence();
            cadenceConnection =
                CadenceConnectionMonitor.newInstance(RecordWorkoutService.this, this);
            synchronized (ongoingWorkoutLock) {
                if (ongoingWorkout != null) {
                    ongoingWorkout.setCadenceDataSource(CadenceDataSource.PHONE);
                }
            }
        }
    };

    final Runnable onCadenceUpdatedTask = new Runnable() {
        @Override
        public void run() {
            if (cadenceEventToProcess == null) {
                return;
            }
            lastCadenceEvent = cadenceEventToProcess;
            cadenceEventToProcess = null;

            if (userSettingsController.getSettings().getCadenceDataSource()
                == CadenceDataSource.CADENCE) {
                lastSpeed = lastCadenceEvent.speedInMetersPerSecond;
            }

            synchronized (ongoingWorkoutLock) {
                if (ongoingWorkout != null) {
                    autoPauseOrResume(lastSpeed);
                    ongoingWorkout.updateCadence(lastCadenceEvent);
                }
            }
        }
    };

    EnergyConsumptionCalculator energyConsumptionCalculator;
    AutoSaveOngoingWorkoutController autoSaveOngoingWorkoutController;
    private BroadcastReceiver recordedWorkoutSavedReceiver;
    private final Runnable autoSaveRunnable = new Runnable() {
        @Override
        public void run() {
            if (autoSaveOngoingWorkoutController != null) {
                autoSaveOngoingWorkoutController.save();
                autoSaveHandler.postDelayed(this, 1000);
            }
        }
    };
    private Disposable dozeMonitorLogDisposable;
    private Disposable appStabilityReportingDisposable;
    /**
     * A unique ID (usually a timestamp) is provided whenever a client is trying to start warming
     * up the service. Only the client with the latest ID can stop warming up the service.
     */
    private long latestStartWarmUpId = -1L;
    private PowerManager.WakeLock wakeLock;
    private AutoPause autoPause;
    @Nullable
    WorkoutHeader followWorkout;
    @Nullable
    WorkoutHeader ghostTargetHeader;
    @Nullable
    Route followRoute;
    @Nullable
    OngoingGhostTarget ghostTarget;

    private LocationConnection locationConnection = null;
    private final LocationModel.Listener locationUpdateListener = new LocationModel.Listener() {
        @Override
        public void onGpsEnabled() {
            String msg = "GPS enabled";
            FirebaseCrashlytics.getInstance().log(msg);
            Timber.d(msg);
            stopRecordingLocation();
            startLocationConnection();
        }

        @Override
        public void onGpsDisabled() {
            // TODO: fire up a dialog asking the user to turn on the GPS
            String msg = "GPS disabled";
            FirebaseCrashlytics.getInstance().log(msg);
            Timber.w(msg);
        }

        @Override
        public void onLocationUpdated(@NonNull Location location) {
            String msg = "On location updated with the timestamp " + location.getTime();
            FirebaseCrashlytics.getInstance().log(msg);
            Timber.d(msg);
            locationToProcess = location;
            threadedHandler.post(onLocationUpdatedTask);

            if (needToFetchWeatherConditions) {
                needToFetchWeatherConditions = false;
                fetchWeatherConditions(location);
            }
        }
    };

    private BluetoothHeartRateEvent lastHeartRateEvent = null;
    final HrEventListener hrEventListener = new HrEventListener() {
        @Override
        public void onHrUpdated(long timestamp, int heartRateBpm) {
            heartRateEventToProcess =
                BluetoothHeartRateEvent.newUpdateEvent(new BatteryStatus(false, true, -1),
                    timestamp, heartRateBpm, HeartRateEvent.DUMMY_BINARY_DATA);
            threadedHandler.post(onHeartRateUpdatedTask);
        }

        @Override
        public void onDeviceConnected() {
            // do nothing
        }

        @Override
        public void onDeviceDisconnected() {
            stopRecordingHeartRates();
            hrmConnection = HeartRateConnectionMonitor.newInstance(RecordWorkoutService.this,
                heartRateUpdatedReceiver, this);
        }
    };
    final BroadcastReceiver heartRateUpdatedReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, final Intent intent) {
            BluetoothHeartRateEvent heartRateEvent =
                intent.getParcelableExtra(STTConstants.ExtraKeys.HEART_RATE_EVENT);
            switch (heartRateEvent.getAction()) {
                case BluetoothHeartRateEvent.ACTION_CONNECTED:
                    // do nothing
                    break;
                case BluetoothHeartRateEvent.ACTION_UPDATE:
                    heartRateEventToProcess = heartRateEvent;
                    threadedHandler.post(onHeartRateUpdatedTask);
                    break;
                case BluetoothHeartRateEvent.ACTION_DISCONNECTED:
                case BluetoothHeartRateEvent.ACTION_ERROR:
                    Timber.w("Error occurred for heart rate connection, trying to reconnect");
                    stopRecordingHeartRates();
                    hrmConnection =
                        HeartRateConnectionMonitor.newInstance(RecordWorkoutService.this, this,
                            hrEventListener);
                    break;
                default:
                    break;
            }
        }
    };
    final Runnable onHeartRateUpdatedTask = new Runnable() {
        @Override
        public void run() {
            BluetoothHeartRateEvent heartRateEvent = heartRateEventToProcess;
            if (heartRateEvent == null) {
                return;
            }
            heartRateEventToProcess = null;

            handleHrEvent(heartRateEvent);
        }
    };
    private final Runnable onTimeUpdatedTask = new Runnable() {
        @Override
        public void run() {
            double currentDuration = -1.0;
            synchronized (ongoingWorkoutLock) {
                if (ongoingWorkout != null) {
                    ongoingWorkout.updateEnergyConsumption(
                        energyConsumptionCalculator.update(getCurrentHeartRate(),
                            getCurrentSpeed()));

                    ongoingWorkout.updateDurationsAndDependants();
                    currentDuration = ongoingWorkout.getDurationInSeconds();
                    double currentDistance = ongoingWorkout.getDistance();
                    sayCurrentStatus(null, currentDistance, currentDistance, currentDuration);
                }
            }
            if (currentDuration > 0.0) {
                duration = currentDuration;
            }

            updateWearableIfNeeded();

            threadedHandler.postDelayed(this, TIME_UPDATE_INTERVAL_IN_MILLISECONDS);
        }
    };
    private AltitudeConnection altitudeConnection;
    private StepCountConnection stepCountConnection;
    private BroadcastReceiver dozeModeListener;
    private boolean shouldStopWarmUpWhenRequestedFromWearable;
    private boolean warmUpRequestedFromWearable;
    private int consecutivePauseReadings = 0;
    final Runnable onLocationUpdatedTask = new Runnable() {
        @Override
        public void run() {
            Location location = locationToProcess;
            if (location == null) {
                return;
            }
            unfilteredLocation = locationToProcess;
            locationToProcess = null;

            setAltitudeFromPressureSensorIfAvailable(location);

            // goes through filters
            if (locationFilter.filter(location)) {
                String msg = "Location dropped by location filter";
                FirebaseCrashlytics.getInstance().log(msg);
                Timber.d(msg);
                return;
            }

            processLocationForGhost(location);

            if (speedFilter.filter(location)) {
                String msg = "Location dropped by speed filter";
                FirebaseCrashlytics.getInstance().log(msg);
                Timber.d(msg);
                return;
            }

            if (distanceFilter.filter(location)) {
                String msg = "Location dropped by distance filter";
                FirebaseCrashlytics.getInstance().log(msg);
                Timber.d(msg);
                return;
            }

            lastLocation = location;

            List<CompleteLap> completedLaps = null;
            synchronized (ongoingWorkoutLock) {
                if (ongoingWorkout != null) {
                    if (lastCadenceEvent == null && location.hasSpeed()) {
                        autoPauseOrResume(location.getSpeed());
                    }
                    double previousDistance = ongoingWorkout.getDistance();
                    completedLaps = ongoingWorkout.updateLocation(location);
                    double currentDistance = ongoingWorkout.getDistance();
                    duration = ongoingWorkout.getDurationInSeconds();
                    sayCurrentStatus(completedLaps, previousDistance, currentDistance, duration);
                }
            }

            if (completedLaps != null && completedLaps.size() > 0) {
                Laps.Type currentSelectedLapType =
                    LapSettingHelper.readLapType(RecordWorkoutService.this, activityType.getId());
                MeasurementUnit measurementUnit =
                    userSettingsController.getSettings().getMeasurementUnit();
                for (CompleteLap completedLap : completedLaps) {
                    Laps.Type lapType = completedLap.getLapType();
                    MeasurementUnit lapUnit = completedLap.getLapUnit();
                    if (lapUnit.equals(measurementUnit) && lapType.equals(currentSelectedLapType)) {
                        wearableController.notifyNewLapAdded(measurementUnit, true,
                            completedLap.getWorkoutDistanceOnEnd(),
                            completedLap.getDuration() / 1000, getCurrentSpeedPaceState(),
                            completedLap.getAverageSpeed());
                        break;
                    }
                }
            }

            // checks if we have fake HR data in the location
            if (STTConstants.DEBUG) {
                Bundle extras = location.getExtras();
                if (extras != null && extras.containsKey("HR")) {
                    if (hrmConnection == null || !hrmConnection.isConnected()) {
                        hrmConnection =
                            new HeartRateConnectionMonitor.DummyHeartRateConnectionMonitor();
                    }
                    BluetoothHeartRateEvent hr =
                        BluetoothHeartRateEvent.newUpdateEvent(new BatteryStatus(false, true, 100),
                            location.getTime(), extras.getInt("HR"),
                            HeartRateEvent.DUMMY_BINARY_DATA);
                    handleHrEvent(hr);
                }
            }

            // caches latest speed so that UI can use it even when no workout is being recorded
            if (lastCadenceEvent == null) {
                lastSpeed = location.getSpeed();
            }
        }
    };

    private static Intent newActionIntent(Context context, @Action int action) {
        return new Intent(context, RecordWorkoutService.class).putExtra(KEY_ACTION, action);
    }

    public static Intent newStartWarmUpIntent(Context context, long warmUpId) {
        return newActionIntent(context, ACTION_START_WARM_UP).putExtra(KEY_WARM_UP_ID, warmUpId);
    }

    public static Intent newStopWarmUpIntent(Context context, long warmUpId) {
        return newActionIntent(context, ACTION_STOP_WARM_UP).putExtra(KEY_WARM_UP_ID, warmUpId);
    }

    public static Intent newStartWarmUpFromWearable(Context context) {
        return newActionIntent(context, ACTION_START_WARM_UP_FROM_WEARABLE);
    }

    public static Intent newStopWarmUpFromWearable(Context context) {
        return newActionIntent(context, ACTION_STOP_WARM_UP_FROM_WEARABLE);
    }

    public static Intent newAutoRecoverIntent(Context context) {
        return newActionIntent(context, ACTION_RECOVER_AUTO_SAVED_WORKOUT);
    }

    public static Intent newPrepareWorkoutIntent(Context context, ActivityType activityType) {
        return newActionIntent(context, ACTION_PREPARE).putExtra(KEY_ACTIVITY_TYPE, activityType);
    }

    public static Intent newStartRecordingIntent(Context context) {
        return newActionIntent(context, ACTION_START_RECORDING);
    }

    public static Intent newStopRecordingIntent(Context context) {
        return newActionIntent(context, ACTION_STOP_RECORDING);
    }

    public static Intent newPauseRecordingIntent(Context context) {
        return newActionIntent(context, ACTION_PAUSE_RECORDING);
    }

    public static Intent newResumeRecordingIntent(Context context) {
        return newActionIntent(context, ACTION_RESUME_RECORDING);
    }

    public static Intent newLapIntent(Context context) {
        return newActionIntent(context, ACTION_ADD_LAP);
    }

    public static Intent newAddPictureIntent(Context context, String fileName, String md5,
        Point location, int width, int height) {
        return newActionIntent(context, ACTION_ADD_PICTURE).putExtra(KEY_PICTURE_NAME, fileName)
            .putExtra(KEY_PICTURE_MD5, md5)
            .putExtra(KEY_PICTURE_LOCATION, (Parcelable) location)
            .putExtra(KEY_PICTURE_WIDTH, width)
            .putExtra(KEY_PICTURE_HEIGHT, height);
    }

    public static Intent newStartVoiceFeedbackIntent(Context context, String language) {
        return newActionIntent(context, ACTION_START_TTS).putExtra(KEY_VOICE_FEEDBACK_LANGUAGE,
            language);
    }

    public static Intent newWearAmbientModeUpdatedIntent(Context context, boolean ambientModeOn) {
        return newActionIntent(context, ACTION_WEAR_AMBIENT_MODE_UPDATED).putExtra(
            KEY_WEAR_AMBIENT_MODE_ON, ambientModeOn);
    }

    public static Intent newPrepareWorkoutTargetIntent(Context context, WorkoutHeader followWorkout,
        WorkoutHeader ghostTarget, String followRouteId) {
        Intent intent = newActionIntent(context, ACTION_PREPARE_WORKOUT_TARGET);
        if (followWorkout != null) {
            intent.putExtra(STTConstants.ExtraKeys.FOLLOW_WORKOUT_HEADER, followWorkout);
        } else if (ghostTarget != null) {
            intent.putExtra(STTConstants.ExtraKeys.GHOST_TARGET_WORKOUT_HEADER, ghostTarget);
        } else if (followRouteId != null) {
            intent.putExtra(STTConstants.ExtraKeys.FOLLOW_ROUTE_ID, followRouteId);
        }
        return intent;
    }

    /**
     * Tries to acquire a {@link android.os.PowerManager#PARTIAL_WAKE_LOCK} reusing the given one
     * (or creating a new one if it's null). This method does not fail, it will return null if it
     * can't acquire the wake lock.
     */
    @SuppressLint("WakelockTimeout")
    private static PowerManager.WakeLock acquireWakeLock(Context context,
        PowerManager.WakeLock wakeLock) {
        String msg = "Acquiring wake lock.";
        FirebaseCrashlytics.getInstance().log(msg);
        Timber.d(msg);
        try {
            PowerManager powerManager =
                (PowerManager) context.getSystemService(Context.POWER_SERVICE);
            if (powerManager == null) {
                msg = "Power manager null.";
                FirebaseCrashlytics.getInstance().log(msg);
                Timber.e(msg);
                return wakeLock;
            }
            if (wakeLock == null) {
                wakeLock = powerManager.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK,
                    RecordWorkoutService.class.getSimpleName());
            }
            if (!wakeLock.isHeld()) {
                wakeLock.acquire();
                if (!wakeLock.isHeld()) {
                    msg = "Wake lock created but cannot be acquired.";
                    FirebaseCrashlytics.getInstance().log(msg);
                    Timber.e(msg);
                } else {
                    String debugMessage = "Acquired wake lock.";
                    FirebaseCrashlytics.getInstance().log(msg);
                    Timber.d(debugMessage);
                }
            }
        } catch (RuntimeException e) {
            msg = "Cannot acquire partial wake lock.";
            FirebaseCrashlytics.getInstance().log(msg);
            FirebaseCrashlytics.getInstance().recordException(e);
            Timber.w(msg);
        }
        return wakeLock;
    }

    void processLocationForGhost(Location location) {
        if (ghostTarget != null) {
            try {
                ghostTarget.processNewLocation(location.getLatitude(), location.getLongitude());
            } catch (InitialGhostMatchNotFoundException initialGhostMatchNotFound) {
                // TODO: Inform the user that we couldn't find a match
                Timber.w(initialGhostMatchNotFound, "Match not found");
            }
        }
    }

    void sayCurrentStatus(List<CompleteLap> completedLaps, double previousDistance,
        double currentDistance, double currentDuration) {
        GhostDistanceTimeState ghostDistanceTimeState = null;
        double ghostDifference = 0.0;
        if (hasGhostTarget()) {
            ghostDistanceTimeState = getCurrentGhostTimeDistanceState();
            try {
                switch (ghostDistanceTimeState) {
                    case TIME:
                    default:
                        ghostDifference = getGhostTimeDifference();
                        break;
                    case DISTANCE:
                        ghostDifference = getGhostDistanceDifference();
                }
            } catch (GhostMatchNotFoundException e) {
                ghostDistanceTimeState = null;
            }
        }
        spokeswoman.sayCurrentStatus(userSettingsController.getSettings().getMeasurementUnit(),
            completedLaps, previousDistance, currentDistance, duration, currentDuration,
            ongoingWorkout.getCurrentSpeed(), ongoingWorkout.getAverageSpeed(),
            getCurrentSpeedPaceState(), ongoingWorkout.getEnergyConsumed(), getCurrentHeartRate(),
            getAverageHeartRate(), getCurrentCadence(), getAverageCadence(), ghostDistanceTimeState,
            ghostDifference);
    }

    @Override
    public IBinder onBind(Intent intent) {
        return binder;
    }


    void updateWearableIfNeeded() {
        if (!wearableController.shouldUpdate()) {
            return;
        }

        boolean autoPaused = false;
        int durationInSeconds = 0;
        double distance = 0.0;
        double averageSpeed = 0.0;
        double energy = 0.0;
        int averageHeartRate = -1;
        boolean hasGhostTarget = false;
        GhostDistanceTimeState ghostDistanceTimeState = null;
        double ghostDifference = 0.0;
        double lapDistance = 0.0;
        int lapDurationInSeconds = 0;
        synchronized (ongoingWorkoutLock) {
            if (ongoingWorkout != null) {
                autoPaused = ongoingWorkout.getState() == TrackingState.AUTO_PAUSED;
                durationInSeconds = (int) Math.round(ongoingWorkout.getDurationInSeconds());
                distance = ongoingWorkout.getDistance();
                averageSpeed = ongoingWorkout.getAverageSpeed();
                energy = ongoingWorkout.getEnergyConsumed();
                averageHeartRate = ongoingWorkout.getAverageHeartRate();
                hasGhostTarget = hasGhostTarget();
                if (hasGhostTarget) {
                    ghostDistanceTimeState = getCurrentGhostTimeDistanceState();
                    try {
                        switch (ghostDistanceTimeState) {
                            case TIME:
                            default:
                                ghostDifference = getGhostTimeDifference();
                                break;
                            case DISTANCE:
                                ghostDifference = getGhostDistanceDifference();
                        }
                    } catch (GhostMatchNotFoundException e) {
                        ghostDistanceTimeState = null;
                    }
                }

                Laps.Type currentSelectedLapType =
                    LapSettingHelper.readLapType(RecordWorkoutService.this, activityType.getId());
                MeasurementUnit measurementUnit =
                    userSettingsController.getSettings().getMeasurementUnit();
                lapDistance =
                    getOngoingLapDistanceInMeters(currentSelectedLapType, measurementUnit);
                lapDurationInSeconds =
                    (int) getOngoingLapDurationInSeconds(currentSelectedLapType, measurementUnit);
            }
        }
        wearableController.update(userSettingsController.getSettings().getMeasurementUnit(),
            autoPaused, lastLocation != null ? lastLocation.getAccuracy() : 0.0F, durationInSeconds,
            distance, getCurrentSpeedPaceState(), getCurrentSpeed(), averageSpeed, energy,
            getCurrentHeartRate(), averageHeartRate, hasGhostTarget, ghostDistanceTimeState,
            ghostDifference, lapDistance, lapDurationInSeconds);
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Timber.i("Creating RWS instance: %s", this);

        weatherConditionsProvider.setListener(this);

        recordWorkoutModel.setService(this);

        wearableController = new WearableController(this);
        wearableController.start();

        workerThread.start();
        threadedHandler = new ServiceHandler(workerThread.getLooper(), this);

        IntentFilter intentFilter =
            new IntentFilter(STTConstants.BroadcastActions.SPEED_PACE_STATE_CHANGED);
        intentFilter.addAction(STTConstants.BroadcastActions.GHOST_STATE_CHANGED);
        intentFilter.addAction(STTConstants.BroadcastActions.LOCK_STATE_CHANGED);
        localBM.registerReceiver(uiStateListener, intentFilter);

        registerDozeModeListener();
    }

    private void registerDozeModeListener() {
        dozeModeListener = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (!hasBackgroundLocationPermission()) {
                    // We don't have background location permission, do nothing.
                    return;
                }

                PowerManager powerManager = (PowerManager) getSystemService(POWER_SERVICE);
                if (powerManager == null) {
                    return;
                }
                FirebaseCrashlytics crashlytics = FirebaseCrashlytics.getInstance();
                if (powerManager.isDeviceIdleMode()) {
                    Intent preventDozeIntent = preventDozeServiceHooks.newStartIntent(RecordWorkoutService.this);
                    startForegroundService(preventDozeIntent);

                    String msg = "Device entered doze mode while recording";
                    crashlytics.log(msg);
                    // We assume that if user has whitelisted the app in battery settings for
                    // battery optimization we don't go to doze mode. Let's make sure and
                    // log this every time we go to doze
                    crashlytics.log("IsIgnoringBatteryOptimization:"
                        + powerManager.isIgnoringBatteryOptimizations(getPackageName()));
                    crashlytics.log("IsPowerSaverMode: " + powerManager.isPowerSaveMode());
                    Timber.d("IsIgnoringBatteryOptimization:%s",
                        powerManager.isIgnoringBatteryOptimizations(getPackageName()));
                    Timber.d("IsPowerSaverMode:%s", powerManager.isPowerSaveMode());
                    Timber.w(msg);

                    ActivityType at = getActivityType();
                    String activityType = at != null ? at.getSimpleName() : null;
                    crashlytics.log("Activity type: " + activityType);
                    crashlytics.log("Duration: " + getDurationInSeconds());
                    crashlytics.log("Distance: " + getTotalDistance());
                    Timber.d("Activity type: %s", activityType);

                    Timber.d("Duration: %s", getDurationInSeconds());
                    Timber.d("Distance: %s", getTotalDistance());
                    FirebaseCrashlytics.getInstance().recordException(new DozeException(msg));
                } else {
                    String msg = "Device leaving doze mode while recording";
                    crashlytics.log(msg);
                    Timber.w(msg);
                    startService(preventDozeServiceHooks.newStopIntent(RecordWorkoutService.this));
                }
            }
        };
        ContextCompat.registerReceiver(
            this,
            dozeModeListener,
            new IntentFilter(PowerManager.ACTION_DEVICE_IDLE_MODE_CHANGED),
            ContextCompat.RECEIVER_EXPORTED
        );
    }

    private void unregisterDozeModeListenerIfNeeded() {
        if (dozeModeListener != null) {
            unregisterReceiver(dozeModeListener);
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        // Temporary logging trying to figure out why RWS crashes when background location permission
        // is not granted.
        int action = intent != null ? intent.getIntExtra(KEY_ACTION, -1) : -1;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            Timber.w("onStartCommand: action - %d", action);
        }

        // Call startForeground() in onStartCommand(), because onCreate() is not always called.
        // https://issuetracker.google.com/issues/307329994#comment93
        // https://issuetracker.google.com/issues/307329994#comment100
        showForegroundNotification(action == ACTION_RECOVER_AUTO_SAVED_WORKOUT);

        Message msg = threadedHandler.obtainMessage();
        msg.arg1 = startId;
        msg.obj = intent;
        threadedHandler.sendMessage(msg);

        // This service is always started as foreground service with the assumption that foreground
        // location permission is granted. So only check background location permission.
        // Calling stopSelf() doesn't help, see https://issuetracker.google.com/issues/307329994#comment97
        return hasBackgroundLocationPermission() ? START_STICKY : START_NOT_STICKY;
    }

    private boolean hasBackgroundLocationPermission() {
        return Build.VERSION.SDK_INT < Build.VERSION_CODES.UPSIDE_DOWN_CAKE ||
            ContextCompat.checkSelfPermission(
                this, Manifest.permission.ACCESS_BACKGROUND_LOCATION) == PackageManager.PERMISSION_GRANTED;
    }

    private void showForegroundNotification(boolean isAutoRecovery) {
        TrackingState currentState;
        synchronized (ongoingWorkoutLock) {
            currentState = ongoingWorkout != null ? ongoingWorkout.getState() : TrackingState.NOT_STARTED;
        }
        Notification notification;
        switch (currentState) {
            case RECORDING -> {
                notification = NotificationBuilder.buildStartedNotification(this, getActivityType(),
                    getFollowWorkoutHeader(), getGhostTargetWorkoutHeader(), getFollowRoute());
            }
            case PAUSED -> {
                notification = NotificationBuilder.buildPausedNotification(this, getActivityType(),
                    getFollowWorkoutHeader(), getGhostTargetWorkoutHeader(), getFollowRoute());
            }
            case AUTO_PAUSED -> {
                notification = NotificationBuilder.buildAutoPausedNotification(this, getActivityType(),
                    getFollowWorkoutHeader(), getGhostTargetWorkoutHeader(), getFollowRoute());
            }
            default -> {
                notification = NotificationBuilder.buildRecordingWarmUpNotification(
                    this, ActivityTypeHelper.getLastActivity(this));
            }
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            // In case of auto recovery, very likely it happens when the app is in background, so we
            // need to check if user has background location permission.
            // If after auto-recovery, user opens the app and goes back to a workout recording
            // screen, a non-auto recovery action will be sent and we will show the notification.
            if (!isAutoRecovery || hasBackgroundLocationPermission()) {
                startForeground(
                    STTConstants.NotificationIds.ONGOING_WORKOUT,
                    notification,
                    ServiceInfo.FOREGROUND_SERVICE_TYPE_LOCATION
                );
            }
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            startForeground(
                STTConstants.NotificationIds.ONGOING_WORKOUT,
                notification,
                ServiceInfo.FOREGROUND_SERVICE_TYPE_MANIFEST
            );
        } else {
            startForeground(STTConstants.NotificationIds.ONGOING_WORKOUT, notification);
        }
    }

    @Override
    public void onDestroy() {
        String infoMessage = "Destroying RWS";
        FirebaseCrashlytics.getInstance().log(infoMessage);
        Timber.i(infoMessage);
        // We shouldn't be destroyed while there's a workout ongoing
        if (ongoingWorkout != null) {
            synchronized (ongoingWorkoutLock) {
                if (ongoingWorkout != null) {
                    FirebaseCrashlytics.getInstance().log(
                        "RecordWorkoutService destroyed while recording");
                    FirebaseCrashlytics.getInstance().log("Available memory: "
                        + getAvailableMemory()
                        + ", route points: "
                        + ongoingWorkout.getRoutePoints().size()
                        + ", total duration: "
                        + getTotalTimeInSeconds());
                    FirebaseCrashlytics.getInstance()
                        .recordException(
                            new Exception("RecordWorkoutService destroyed while recording"));
                }
            }
        }

        workerThread.quit();
        weatherConditionsProvider.clear();
        stopRecordingLocation();
        stopRecordingHeartRates();
        stopRecordingCadence();
        stopAltitude();
        stopTextToSpeech();
        stopStepCount();
        if (appStabilityReportingDisposable != null && !appStabilityReportingDisposable.isDisposed()) {
            appStabilityReportingDisposable.dispose();
            appStabilityReportingDisposable = null;
        }
        wearableController.shutdown();
        localBM.unregisterReceiver(uiStateListener);
        if (recordedWorkoutSavedReceiver != null) {
            localBM.unregisterReceiver(recordedWorkoutSavedReceiver);
        }

        // TODO: Do we need to do the same after the workout has been saved?
        if (workoutDataLoaderController != null) {
            // Make sure we're removed as listeners.
            workoutDataLoaderController.removeListener(this);
        }

        // Remove ourselves from the model
        recordWorkoutModel.setService(null);

        unregisterDozeModeListenerIfNeeded();

        // The platform might kill our service so let's make sure we release the wake lock
        releaseWakeLock();

        // Stop foreground
        ServiceCompat.stopForeground(this, ServiceCompat.STOP_FOREGROUND_REMOVE);
    }

    private long getAvailableMemory() {
        ActivityManager.MemoryInfo mi = new ActivityManager.MemoryInfo();
        ActivityManager activityManager = (ActivityManager) getSystemService(ACTIVITY_SERVICE);
        activityManager.getMemoryInfo(mi);
        return mi.availMem / (1024L * 1024L);
    }

    private void stopTextToSpeech() {
        spokeswoman.shutdown();
    }

    /**
     * This method is invoked on the worker thread with a request to process.
     * Only one Intent is processed at a time, but the processing happens on a
     * worker thread that runs independently from other application logic. So,
     * if this code takes a long time, it will hold up other requests to the
     * same IntentService, but it will not hold up anything else.
     */
    void onHandleIntent(Intent intent, int startId) {
        String logMsg = "logMsg(): intent = [" + intent + "], startId = [" + startId + "]";
        FirebaseCrashlytics.getInstance().log(logMsg);
        Timber.d(logMsg);
        if (intent == null) {
            logMsg = "Restarted by system";
            FirebaseCrashlytics.getInstance().log(logMsg);
            Timber.w(logMsg);
        } else {
            int action = intent.getIntExtra(KEY_ACTION, -1);
            if (action == -1) {
                logMsg = "Missing RWS action!";
                FirebaseCrashlytics.getInstance().log(logMsg);
                Timber.w(logMsg);
                return;
            }
            switch (action) {
                case ACTION_STOP_RECORDING:
                    logMsg = "Stopping workout recording";
                    FirebaseCrashlytics.getInstance().log(logMsg);
                    Timber.d(logMsg);
                    handleStopRecordingAction();
                    break;
                case ACTION_PREPARE:
                    logMsg = "Preparing workout recording";
                    FirebaseCrashlytics.getInstance().log(logMsg);
                    Timber.d(logMsg);
                    handlePrepareAction(intent);
                    break;
                case ACTION_START_RECORDING:
                    logMsg = "Starting workout recording";
                    FirebaseCrashlytics.getInstance().log(logMsg);
                    Timber.d(logMsg);
                    handleStartAction(intent);
                    break;
                case ACTION_PAUSE_RECORDING:
                    logMsg = "Pausing workout recording";
                    FirebaseCrashlytics.getInstance().log(logMsg);
                    Timber.d(logMsg);
                    handlePauseAction();
                    break;
                case ACTION_RESUME_RECORDING:
                    logMsg = "Resuming workout recording";
                    FirebaseCrashlytics.getInstance().log(logMsg);
                    Timber.d(logMsg);
                    handleResumeAction(false);
                    break;
                case ACTION_START_WARM_UP:
                    logMsg = "Starting to warm up activity recording";
                    FirebaseCrashlytics.getInstance().log(logMsg);
                    Timber.d(logMsg);
                    handleStartWarmUpAction(intent);
                    break;
                case ACTION_STOP_WARM_UP:
                    logMsg = "Stopping to warm up activity recording";
                    FirebaseCrashlytics.getInstance().log(logMsg);
                    Timber.d(logMsg);
                    handleStopWarmUpAction(intent);
                    break;
                case ACTION_ADD_LAP:
                    logMsg = "Adding lap to a recording activity";
                    FirebaseCrashlytics.getInstance().log(logMsg);
                    Timber.d(logMsg);
                    handleLapAction();
                    break;
                case ACTION_ADD_PICTURE:
                    logMsg = "Adding picture to a recording activity";
                    FirebaseCrashlytics.getInstance().log(logMsg);
                    Timber.d(logMsg);
                    handleAddPictures(intent);
                    break;
                case ACTION_START_TTS:
                    logMsg = "Starting TTS";
                    FirebaseCrashlytics.getInstance().log(logMsg);
                    Timber.d(logMsg);
                    handleStartTTS(intent);
                    break;
                case ACTION_RECOVER_AUTO_SAVED_WORKOUT:
                    logMsg = "Recovering auto saved workout";
                    FirebaseCrashlytics.getInstance().log(logMsg);
                    Timber.d(logMsg);
                    handleRecoverAutoSavedWorkout();
                    break;
                case ACTION_START_WARM_UP_FROM_WEARABLE:
                    logMsg = "Starting warm up from wearable";
                    FirebaseCrashlytics.getInstance().log(logMsg);
                    Timber.d(logMsg);
                    handleStartWarmUpFromWearable();
                    break;
                case ACTION_STOP_WARM_UP_FROM_WEARABLE:
                    logMsg = "Stopping warm up from wearable";
                    FirebaseCrashlytics.getInstance().log(logMsg);
                    Timber.d(logMsg);
                    handleStopWarmUpFromWearable();
                    break;
                case ACTION_WEAR_AMBIENT_MODE_UPDATED:
                    logMsg = "Wear ambient mode updated";
                    FirebaseCrashlytics.getInstance().log(logMsg);
                    Timber.d(logMsg);
                    handleWearAmbientModeUpdated(intent);
                    break;
                case ACTION_PREPARE_WORKOUT_TARGET:
                    logMsg = "Preparing target workout";
                    FirebaseCrashlytics.getInstance().log(logMsg);
                    Timber.d(logMsg);
                    handlePrepareWorkoutTarget(intent);
                    break;
                default:
                    logMsg = "Nothing to be done for action " + action;
                    FirebaseCrashlytics.getInstance().log(logMsg);
                    Timber.w(logMsg);
                    break;
            }
        }
    }

    private void handleWearAmbientModeUpdated(Intent intent) {
        wearableController.setAmbientMode(intent.getBooleanExtra(KEY_WEAR_AMBIENT_MODE_ON, false));
    }

    private void handleStartWarmUpFromWearable() {
        warmUpRequestedFromWearable = true;
        shouldStopWarmUpWhenRequestedFromWearable = false;
    }

    private void handleStopWarmUpFromWearable() {
        // Wearable device will trigger this even if the Service is stopped by phone
        // in this case, the latestStartWarmUpId is -1, we must stop the Service
        if (latestStartWarmUpId == -1L || (warmUpRequestedFromWearable
            && shouldStopWarmUpWhenRequestedFromWearable)) {
            stopWarmUp();
        }
        warmUpRequestedFromWearable = false;
        shouldStopWarmUpWhenRequestedFromWearable = false;
    }

    /**
     * Checks if there's data to recover and how old is that data.
     * <ul>
     * <li>
     * If the data is older than {@link #MAX_AGE_TO_CONTINUE_AUTO_SAVED_WORKOUT} then we just
     * create a new workout in the user's diary with as much data as we can.
     * </li>
     * <li>Otherwise, we recover as much data as possible in memory (See
     * {@link #recoverAndContinueWorkout(boolean)}) and continue recording the workout.</li>
     * </ul>
     */
    private void handleRecoverAutoSavedWorkout() {
        String msg = "Trying to recover auto saved workout";
        FirebaseCrashlytics.getInstance().log(msg);
        Timber.d(msg);
        try {
            // We're gonna (potentially) modify the ongoing workout so we want others to wait
            // that we
            // have fully recovered it.
            // NOTE: This potentially blocks the main thread while we're recovering the data
            synchronized (ongoingWorkoutLock) {
                long autoSavedDataLastModified =
                    AutoSaveOngoingWorkoutController.autoSaveDataTimestamp(this);
                if (autoSavedDataLastModified == 0L) {
                    msg = "We were asked to recover a workout but there's no data to recover";
                    FirebaseCrashlytics.getInstance().log(msg);
                    Timber.e(msg);
                    AutoSaveOngoingWorkoutController.cleanSavedData(this);
                    endService();
                    return;
                }
                long autoSavedDataAge = System.currentTimeMillis() - autoSavedDataLastModified;
                if (autoSavedDataAge < MAX_AGE_TO_CONTINUE_AUTO_SAVED_WORKOUT) {
                    recoverAndContinueWorkout(
                        autoSavedDataAge > MAX_AGE_TO_RESUME_AUTO_SAVED_WORKOUT);
                } else {
                    recoverAndStoreWorkout();
                    endService();
                }
            }
        } catch (Throwable e) {
            FirebaseCrashlytics.getInstance().log("Failed to recover auto saved workout");
            Timber.e(e, "Failed to recover auto saved workout");
            AutoSaveOngoingWorkoutController.cleanSavedData(this);
            endService();
        }
    }

    private void recoverAndContinueWorkout(boolean shouldPause)
        throws TooManyAutoRecoversException, IOException {
        String msg = "Trying to recover and continue auto saved workout";
        Timber.d(msg);
        FirebaseCrashlytics.getInstance().log(msg);

        OngoingWorkout unfinishedWorkout = recoverOngoingWorkoutAndSetAutoSaveController();
        TrackingState trackingState = unfinishedWorkout.getState();
        if (trackingState == TrackingState.SAVED) {
            AutoSaveOngoingWorkoutController.cleanSavedData(this);
            endService();
            return;
        } else if (trackingState == TrackingState.NOT_SAVED) {
            recoverAndStoreWorkout();
            endService();
            return;
        }

        // restart recording
        msg = "Restarting recording: " + unfinishedWorkout.getState();
        FirebaseCrashlytics.getInstance().log(msg);
        Timber.d(msg);
        warmUpServices();
        ActivityType activityType = unfinishedWorkout.getActivityType();
        prepare(activityType);
        preStartWorkout();
        ongoingWorkout = unfinishedWorkout;
        lastSpeed = unfinishedWorkout.getLastSpeed();
        unfinishedWorkout.recover();

        // TODO: Auto save/recover ghost/follow workout details
        postStartWorkout(null);

        if (shouldPause || trackingState == TrackingState.PAUSED) {
            // Make sure that we change to paused state but don't count the time while the
            // service was down in the workout. It has to be called before #pauseWorkout()
            ongoingWorkout.changeToPausedState();
            pauseWorkout();
            notifyRecordingPaused();
            FirebaseCrashlytics.getInstance().log("Recovered workout in paused mode");
            Timber.d("Recovered workout in paused mode");
        } else if (trackingState == TrackingState.AUTO_PAUSED) {
            handleAutoPause();
        } else {
            notifyRecordingStartedOrResumed();
            FirebaseCrashlytics.getInstance().log("Recovered workout in resumed mode");
            Timber.d("Recovered workout in resumed mode");
        }
    }

    private void recoverAndStoreWorkout() throws TooManyAutoRecoversException, IOException {
        FirebaseCrashlytics.getInstance().log("Trying to recover and store auto saved workout");
        Timber.d("Trying to recover and store auto saved workout");
        try {
            OngoingWorkout unfinishedWorkout = recoverOngoingWorkoutAndSetAutoSaveController();
            int sharingFlags = workoutShareUtils.getDefaultWorkoutBackendShareFlag();
            TSS tss = calculateTSS(unfinishedWorkout);
            WorkoutHeader header = unfinishedWorkout.getWorkoutHeader(currentUserController.getUsername(),
                    userSettingsController.getSettings().getHrMaximum(), sharingFlags, tss, null)
                    .toBuilder()
                    .description(getString(R.string.workout_auto_recovered))
                    .locallyChanged(true)
                    .build();
            WorkoutData data = createWorkoutData(unfinishedWorkout);
            List<ImageInformation> pictures = unfinishedWorkout.getPictures();
            List<WorkoutExtension> extensions = unfinishedWorkout.getExtensions();
            Workout workout = new Workout(header, data, pictures, extensions);
            registerRecordedWorkoutSavedBroadcastReceiver(header.getId());
            SaveWorkoutService.enqueueWork(this, workout);
            FirebaseCrashlytics.getInstance().log("Workout recovered and stored");
            Timber.d("Workout recovered and stored");

            // TODO: Show a notification to the user?
        } finally {
            AutoSaveOngoingWorkoutController.cleanSavedData(this);
        }
    }

    @Nullable
    private TSS calculateTSS(OngoingWorkout ongoingWorkout) {
        float durationInSeconds = ongoingWorkout.getDuration() / 1000f;
        if (durationInSeconds <= 0) return null;
        Result<com.soy.algorithms.tss.TSS> tssResult;

        TSSCalculationMethod userTssMethod =
            supportedTSSCalculationMethodRepository
                .getUserSelectedCalculationMethodForType(ongoingWorkout.getActivityType());

        if (ongoingWorkout.getHeartRateEvents().size() > 0 &&
            userTssMethod != TSSCalculationMethod.MET) {
            tssResult = calculateHRTSS(ongoingWorkout, durationInSeconds);
        } else {
            tssResult = calculateMetTSS(ongoingWorkout, durationInSeconds);
        }
        if (tssResult instanceof Result.Failure) {
            Timber.w(((Result.Failure<com.soy.algorithms.tss.TSS>) tssResult).getError(),
                "Error calculating TSS for ST workout");
            return null;
        }
        return TSSUtilsKt.toDomain(((Result.Success<com.soy.algorithms.tss.TSS>) tssResult).getResult());
    }

    @NonNull
    private Result<com.soy.algorithms.tss.TSS> calculateMetTSS(OngoingWorkout ongoingWorkout, float durationInSeconds) {
        Integer weightSetting = userSettingsController.getSettings().getWeight();
        float energyConsumption = (float) ongoingWorkout.getEnergyConsumed();
        if (weightSetting == null ||
            weightSetting <= 0 ||
            energyConsumption <= 0) {
            return new Result.Failure<>(
                new Exception("Missing weight setting or negative energy consumption"));
        }
        float weightKg = weightSetting / 1000f;
        float metScore = METCalculator.INSTANCE.estimateMET(
            energyConsumption,
            weightKg,
            durationInSeconds
        );
        TSSInput tssInput = new TSSInput(
            ongoingWorkout.getActivityType().getId(),
            durationInSeconds,
            null,
            null,
            null,
            null,
            null,
            metScore,
            null,
            Collections.emptyList(),
            null
        );
        return TSSCalculator.INSTANCE.calculateTSS(TSSCalculationMethod.MET, tssInput);
    }

    @NonNull
    private Result<com.soy.algorithms.tss.TSS> calculateHRTSS(OngoingWorkout ongoingWorkout, float durationInSeconds) {
        List<TSSSample> samples = new ArrayList<>();
        for (WorkoutHrEvent hrEvent : ongoingWorkout.getHeartRateEvents()) {
            samples.add(
                new TSSSample(
                    hrEvent.getMillisecondsInWorkout() / 1000f,
                    null,
                    null,
                    null,
                    hrEvent.getHeartRate() / 60f
                )
            );
        }
        // lactate threshold HR is the lower limit of the 5th HR zone, which is 90% of max HR
        float ltHR = 0.9f * userSettingsController.getSettings().getHrMaximum() / 60f;
        TSSInput tssInput = new TSSInput(
            ongoingWorkout.getActivityType().getId(),
            durationInSeconds,
            null,
            null,
            ltHR,
            null,
            null,
            null,
            null,
            samples,
            null
        );
        return TSSCalculator.INSTANCE.calculateTSS(TSSCalculationMethod.HR, tssInput);
    }

    /**
     * Handles the recovering of the autosaved ongoing workout from disk.
     * Note: It might take several seconds (depends on how long the workout is)
     *
     * @return the recovered workout
     * @throws TooManyAutoRecoversException
     * @throws IOException
     */
    private OngoingWorkout recoverOngoingWorkoutAndSetAutoSaveController()
        throws TooManyAutoRecoversException, IOException {
        // Use the controller to recover the auto saved ongoing workout
        autoSaveOngoingWorkoutController =
            AutoSaveOngoingWorkoutController.recoverAutoSavedWorkout();
        return autoSaveOngoingWorkoutController.getOngoingWorkout();
    }

    private WorkoutData createWorkoutData(BaseOngoingWorkout unfinishedWorkout) {
        Pair<String, String> mccAndMnc = DeviceUtils.getMccAndMnc(telephonyManager);
        // FIXME: get manual laps might be null
        UserSettings userSettings = userSettingsController.getSettings();
        return new WorkoutData(unfinishedWorkout.getRoutePoints(),
            unfinishedWorkout.getHeartRateEvents(),
            unfinishedWorkout.getManualLaps().getCompleteLaps(), userSettings.getMeasurementUnit(),
            unfinishedWorkout.getEvents(), unfinishedWorkout.getLongitudeStatistics(),
            unfinishedWorkout.getLatitudeStatistics(), unfinishedWorkout.getAltitudeStatistics(),
            unfinishedWorkout.getSpeedStatistics(), unfinishedWorkout.getHeartRateStatistics(),
            unfinishedWorkout.getCadenceStatistics(), userSettings.getAltitudeOffset(),
            userSettings.getHrMaximum(), mccAndMnc.second, mccAndMnc.first);
    }

    private void handleStartTTS(Intent intent) {
        spokeswoman.start(getApplicationContext(), intent.getStringExtra(KEY_VOICE_FEEDBACK_LANGUAGE));
    }

    private void handleAddPictures(Intent intent) {
        String pictureFile = intent.getStringExtra(KEY_PICTURE_NAME);
        String md5Hash = intent.getStringExtra(KEY_PICTURE_MD5);
        Point location = intent.getParcelableExtra(KEY_PICTURE_LOCATION);
        int width = intent.getIntExtra(KEY_PICTURE_WIDTH, 0);
        int height = intent.getIntExtra(KEY_PICTURE_HEIGHT, 0);
        addWorkoutPicture(pictureFile, md5Hash, location, width, height);
    }

    private void handleLapAction() {
        ParcelableCompleteLap lap;
        synchronized (ongoingWorkoutLock) {
            lap = ongoingWorkout.addManualLap();

            if (lap != null) {
                // set the lap type to manual
                LapSettingHelper.saveLapType(this, activityType.getId(), Laps.Type.MANUAL);

                GhostDistanceTimeState ghostDistanceTimeState = null;
                double ghostDifference = 0.0;
                if (hasGhostTarget()) {
                    ghostDistanceTimeState = getCurrentGhostTimeDistanceState();
                    try {
                        switch (ghostDistanceTimeState) {
                            case TIME:
                            default:
                                ghostDifference = getGhostTimeDifference();
                                break;
                            case DISTANCE:
                                ghostDifference = getGhostDistanceDifference();
                        }
                    } catch (GhostMatchNotFoundException e) {
                        ghostDistanceTimeState = null;
                    }
                }
                MeasurementUnit measurementUnit =
                    userSettingsController.getSettings().getMeasurementUnit();
                spokeswoman.sayLap(measurementUnit, lap, ongoingWorkout.getCurrentSpeed(),
                    ongoingWorkout.getAverageSpeed(), getCurrentSpeedPaceState(),
                    ongoingWorkout.getEnergyConsumed(), getCurrentHeartRate(),
                    getAverageHeartRate(), getCurrentCadence(), getAverageCadence(),
                    ghostDistanceTimeState, ghostDifference);
                wearableController.notifyNewLapAdded(measurementUnit, false, lap.getDistance(),
                    lap.getDuration() / 1000, getCurrentSpeedPaceState(), lap.getAverageSpeed());
            }
        }

        Intent lapIntent = new Intent(STTConstants.BroadcastActions.RECORDING_ADD_LAP);
        lapIntent.putExtra(STTConstants.ExtraKeys.LAP, lap);
        localBM.sendBroadcast(lapIntent);
    }

    private void handleStartWarmUpAction(Intent intent) {
        latestStartWarmUpId = intent.getLongExtra(RecordWorkoutService.KEY_WARM_UP_ID, 0L);
        warmUpServices();

        synchronized (ongoingWorkoutLock) {
            if (ongoingWorkout == null) {
                // the warm up might be called multiple times, but shouldn't inform the wearable
                // more than once
                wearableController.notifyRecordingWarmedUp();
            }
        }
    }

    private void warmUpServices() {
        startLocationConnection();
        startRecordingHeartRates();
        startRecordingCadence();
        startAltitudeConnection();
        startStepCounter();
    }

    private void startAltitudeConnection() {
        // Only update the reference pressure value if the user wants to use barometer or user is
        // doing slope ski
        boolean shouldStartAltitudeConnection = false;
        if (getAltitudeSource() == AltitudeSource.BAROMETER
            || (activityType != null
            && activityType.isSlopeSki()
            && UpdatePressureTask.hasPressureSensor(sensorManager))) {
            shouldStartAltitudeConnection = true;
        }
        if (shouldStartAltitudeConnection && altitudeConnection == null) {
            altitudeConnection = new AltitudeConnection();
        }
    }

    /**
     * Starts listening for locations if the workout is not started or if it's
     * not of type {@link ActivityType#INDOOR}
     */
    void startLocationConnection() {
        if (locationConnection == null && (ongoingWorkout == null
            || !ongoingWorkout.getActivityType().isIndoor())) {
            locationConnection = new LocationConnection(locationUpdateListener, 0L, 0.0F);
        }
    }

    private void handleStopWarmUpAction(Intent intent) {
        // If the ID we got is different than the last known we don't stop it.
        long warmUpId = intent.getLongExtra(RecordWorkoutService.KEY_WARM_UP_ID, 0);
        if (warmUpId != latestStartWarmUpId && latestStartWarmUpId != -1L) {
            String msg = "Trying to stop warm up after we got a more recent start warm up";
            FirebaseCrashlytics.getInstance().log(msg);
            Timber.d(msg);
            return;
        }

        if (warmUpRequestedFromWearable) {
            shouldStopWarmUpWhenRequestedFromWearable = true;
        } else {
            stopWarmUp();
        }
    }

    private void stopWarmUp() {
        String msg = "RWS trying to stop warm-up";
        FirebaseCrashlytics.getInstance().log(msg);
        Timber.d(msg);
        synchronized (ongoingWorkoutLock) {
            TrackingState trackingState =
                ongoingWorkout == null ? TrackingState.NOT_STARTED : ongoingWorkout.getState();
            if (trackingState == TrackingState.NOT_STARTED
                || trackingState == TrackingState.SAVED) {
                wearableController.notifyRecordingWarmUpStopped();
                stopRecordingLocation();
                stopRecordingHeartRates();
                stopRecordingCadence();
                stopAltitude();
                endService();
                msg = "RWS warm-up stopped";
                FirebaseCrashlytics.getInstance().log(msg);
                Timber.d(msg);
            }
        }
    }

    private void handlePrepareAction(Intent intent) {
        prepare((ActivityType) intent.getParcelableExtra(KEY_ACTIVITY_TYPE));
    }

    /**
     * Must be called before starting a workout recording.
     */
    private void prepare(@Nullable ActivityType activityType) {
        if (activityType == null) {
            Timber.w("Activity type not provided using default one!");
            activityType = ActivityType.DEFAULT;
        }
        this.activityType = activityType;
        spokeswoman.setActivityType(activityType);
        autoPause = ActivityTypeHelper.getAutoPause(this, activityType);
        currentSpeedPaceState = ActivityTypeHelper.getUserSpeedPaceState(this, activityType);
        wearableController.notifyActivityType(activityType);
    }

    private void handlePrepareWorkoutTarget(Intent intent) {
        if (intent == null) {
            return;
        }

        WorkoutHeader ghostTargetHeader =
            intent.getParcelableExtra(STTConstants.ExtraKeys.GHOST_TARGET_WORKOUT_HEADER);
        WorkoutHeader followWorkout =
            intent.getParcelableExtra(STTConstants.ExtraKeys.FOLLOW_WORKOUT_HEADER);
        String followRouteId = intent.getStringExtra(STTConstants.ExtraKeys.FOLLOW_ROUTE_ID);
        if (followWorkout != null) {
            this.followWorkout = followWorkout;

            this.ghostTargetHeader = null;
            workoutDataLoaderController.removeListener(this);
            this.ghostTarget = null;
            this.followRoute = null;
        } else if (ghostTargetHeader != null) {
            this.ghostTargetHeader = ghostTargetHeader;
            workoutDataLoaderController.loadWorkout(ghostTargetHeader, this);

            this.followWorkout = null;
            this.followRoute = null;
        } else if (followRouteId != null) {
            try {
                // We're already on a background thread
                this.followRoute = getRouteUseCase.getRouteRx(followRouteId).blockingGet();
                this.followWorkout = null;
                this.ghostTargetHeader = null;
                workoutDataLoaderController.removeListener(this);
                this.ghostTarget = null;
            } catch (Exception ex) {
                Timber.w(ex, "Failed to load route");
            }
        }
    }

    private void handleStartAction(Intent intent) {
        if (activityType == null && warmUpRequestedFromWearable) {
            prepare(ActivityTypeHelper.getLastActivity(this));
        }

        preStartWorkout();
        startWorkout();
        postStartWorkout(intent);
        // Finally notify the user visually and audio (if needed)

        notifyRecordingStartedOrResumed();
        spokeswoman.sayStart();
    }

    private void preStartWorkout() {
        // Start it before the workout in order to get altitude values already in the first
        // locations of the workout.
        startAltitude();
        speedFilter.reset(activityType);
        distanceFilter.reset();
        wakeLock = acquireWakeLock(this, wakeLock);
    }

    /**
     * sends a broadcast (START_WORKOUT_FILTER)
     * to notify all the interested parties with the starting time as
     * WORKOUT_STARTING_TIME extra (long with time in milliseconds since January
     * 1, 1970 00:00:00 UTC)
     */
    private void postStartWorkout(Intent intent) {
        prepareEnergyConsumptionTask();
        linkPendingPicturesToOngoingWorkout();

        handlePrepareWorkoutTarget(intent);

        startAutoSaveOngoingWorkout();
        startContinuousMonitor();
        threadedHandler.post(onTimeUpdatedTask);
        recordWorkoutStateChanged(TrackingState.RECORDING, false);
        Timber.d("RecordWorkoutService.postStartWorkout Auto pause=%s; ongoing workout current speed=%.2f; auto pause threshold=%.2f", autoPause, ongoingWorkout
                .getCurrentSpeed(),
            autoPause.getAmountInMetersPerSecond());
        if (autoPause != AutoPause.OFF && !isSpeedAboveThreshold((float) getCurrentSpeed())) {
            autoPauseWorkout();
        }
    }

    /**
     * This will start a stream of events in background triggered every minute.
     * The purpose is to monitor if the Doze mode affects our background service by stopping it,
     * therefore interrupting this stream.
     */
    private synchronized void startContinuousMonitor() {
        stopContinuousMonitor();

        Observable<Long> interval =
            Observable.interval(1L, TimeUnit.MINUTES, Schedulers.computation())
                // When waking up from Doze we'll get a burst of events so throttle them
                .throttleFirst(40L, TimeUnit.SECONDS);
        activeModeStartTimestamp = SystemClock.elapsedRealtime();
        dozeMonitorLogDisposable = interval.subscribe(count -> {
                // One full minute has passed
                minutesActive++;
                String msg = "Doze monitor (" + minutesActive + " minute)";
                FirebaseCrashlytics.getInstance().log(msg);
                Timber.d(msg);
            });
    }

    private synchronized void stopContinuousMonitor() {
        if (dozeMonitorLogDisposable != null && !dozeMonitorLogDisposable.isDisposed()) {
            dozeMonitorLogDisposable.dispose();

            long now = SystemClock.elapsedRealtime();
            long minutes = TimeUnit.MILLISECONDS.toMinutes(now - activeModeStartTimestamp);
            if (minutes != minutesActive) {
                // The doze mode interrupted our process since we didn't have all the minutes
                long missingMinutes = minutes - minutesActive;
                Timber.e(new DozeException("Workout was missing " + missingMinutes),
                    "Missing %d minutes", missingMinutes);
            }
            dozeMonitorLogDisposable = null;
        }
        minutesActive = 0;
    }

    private void startStepCounter() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q &&
            !EasyPermissions.hasPermissions(this, Manifest.permission.ACTIVITY_RECOGNITION)) {
            Timber.w("Cannot startStepCounter() without Manifest.permission.ACTIVITY_RECOGNITION");
            return;
        }
        if (stepCountConnection == null) {
            stepCountConnection = new StepCountConnection();
        }
        stepCountConnection.setListener(this);
    }

    private void stopStepCount() {
        if (stepCountConnection != null) {
            stepCountConnection.close();
            stepCountConnection = null;
        }
    }

    @Override
    public void stepCountChanged(int steps) {
        synchronized (ongoingWorkoutLock) {
            if (ongoingWorkout != null) {
                ongoingWorkout.updateStepCount(steps);
            }
        }
    }

    void setAltitudeFromPressureSensorIfAvailable(Location location) {
        if (altitudeConnection != null) {
            try {
                location.setAltitude(altitudeConnection.getAltitude());
            } catch (IllegalStateException e) {
                // if barometer fails, we falls back to GPS
                Timber.w(e, "Altitude from pressure sensor not yet available");
            }
        }
    }

    private void startAltitude() {
        // Start listening the barometer if the user has chosen so.
        if (getAltitudeSource() != AltitudeSource.BAROMETER) {
            return;
        }
        if (altitudeConnection == null) {
            altitudeConnection = new AltitudeConnection();
        }
        altitudeConnection.startAltitudeUpdates();
    }

    private void stopAltitude() {
        if (altitudeConnection != null) {
            altitudeConnection.close();
            altitudeConnection = null;
        }
    }

    private void prepareEnergyConsumptionTask() {
        UserSettings settings = userSettingsController.getSettings();

        Integer weight = settings.getWeightInKg();
        if (weight == null) {
            weight = UserSettings.DEFAULT_WEIGHT_GRAMS / 1000;
        }

        Long birthDate = settings.getBirthDate();
        if (birthDate == null) {
            birthDate = UserSettings.DEFAULT_BIRTH_DATE;
        }

        energyConsumptionCalculator =
            new EnergyConsumptionCalculator(activityType, settings.getGender(), weight,
                DateUtils.calculateAge(birthDate));
    }

    private void handlePauseAction() {
        pausedCount++;
        pauseWorkout();
        notifyRecordingPaused();
        spokeswoman.sayStop();
    }

    /**
     * This method can't be triggered through the service actions but rather internally
     */
    private void handleAutoPause() {
        autoPauseWorkout();
        notifyRecordingAutoPaused();
        spokeswoman.sayAutoPause();
    }

    private void handleResumeAction(boolean isAutoResume) {
        synchronized (ongoingWorkoutLock) {
            if (ongoingWorkout == null) {
                // Resume action may be triggered by a pending intent even in cases where we don't
                // have an ongoing workout. In that case just log an error and do nothing.
                final String msg = "Unable to resume workout: ongoingWorkout is null";
                Timber.w(msg);
                FirebaseCrashlytics.getInstance().log(msg);
                return;
            }
        }

        resumeWorkout();
        notifyRecordingStartedOrResumed();
        if (isAutoResume) {
            spokeswoman.sayAutoResume();
        } else {
            spokeswoman.sayResume();
            //if we resume workout autopause should be triggered
            if (autoPause != AutoPause.OFF && !isSpeedAboveThreshold((float) getCurrentSpeed())) {
                autoPauseWorkout();
            }
        }
    }

    private void handleStopRecordingAction() {
        stopRecordingLocation();
        appStabilityReportingDisposable = appStabilityReportingUseCase.getStabilityReportingCompletable()
            .subscribe(() -> {
                stopRecordingCadence();
                stopRecordingHeartRates();
                stopAltitude();
                endWorkout();
            }, throwable ->
                Timber.w(throwable, "getStabilityReportingCompletable failed"));
    }

    /**
     * Changes the state to continue if the autopause was active and the speed
     * threshold was surpassed or to autopause if the speed is below the
     * threshold
     */
    void autoPauseOrResume(float speed) {
        if (ongoingWorkout.getState() == TrackingState.AUTO_PAUSED && isSpeedAboveThreshold(
            speed)) {
            // We changed from autopaused to running
            handleResumeAction(true);

            consecutivePauseReadings = 0;
        } else if (ongoingWorkout.getState() == TrackingState.RECORDING && !isSpeedAboveThreshold(
            speed)) {
            // if the cadence is available, we don't auto pause it immediately
            if (lastCadenceEvent == null
                || ++consecutivePauseReadings >= AUTO_PAUSE_THRESHOLD_FOR_CADENCE) {
                // We changed from running to autopaused
                handleAutoPause();
            }
        }
    }

    private boolean isSpeedAboveThreshold(float speed) {
        if (autoPause == AutoPause.ZERO_KM_H || autoPause == AutoPause.ZERO_MI_H) {
            return speed > autoPause.getAmountInMetersPerSecond();
        } else {
            return speed >= autoPause.getAmountInMetersPerSecond();
        }
    }

    private void endWorkout() {
        String msg = "Ending workout";
        FirebaseCrashlytics.getInstance().log(msg);
        Timber.i(msg);
        synchronized (ongoingWorkoutLock) {
            if (ongoingWorkout != null) {
                ongoingWorkout.end();
            }
        }
        recordWorkoutStateChanged(TrackingState.NOT_SAVED, false);
        stopAutoSaveOngoingWorkout();
        stopContinuousMonitor();
        saveWorkout();
        wearableController.notifyRecordingStopped(
            userSettingsController.getSettings().getMeasurementUnit(), duration, getTotalDistance(),
            getTotalEnergyKCal(), getAverageSpeed(), getMaxSpeed(), getCurrentHeartRate(),
            getMaxHeartRate());
    }

    private void saveWorkout() {
        UserSettings userSettings = userSettingsController.getSettings();
        String owner = currentUserController.getUsername();
        WorkoutHeader header = getWorkoutHeader(owner);
        if (header != null) {
            header = header.toBuilder()
                .description("")
                .heartRateUserSetMax(userSettings.getHrMaximum())
                .locallyChanged(true)
                .build();
            WorkoutData data = getWorkoutData(userSettings);
            List<ImageInformation> pictures = getWorkoutPictures();
            List<WorkoutExtension> extensions = getWorkoutExtensions();
            Workout workout = new Workout(header, data, pictures, extensions);
            registerRecordedWorkoutSavedBroadcastReceiver(header.getId());
            SaveWorkoutService.enqueueWork(this, workout);
        } else {
            Timber.w("saveWorkout - null WorkoutHeader!");
        }
    }

    private void registerRecordedWorkoutSavedBroadcastReceiver(int workoutId) {
        if (recordedWorkoutSavedReceiver != null) {
            localBM.unregisterReceiver(recordedWorkoutSavedReceiver);
        }

        recordedWorkoutSavedReceiver = new RecordedWorkoutSavedBroadcastReceiver(workoutId);
        localBM.registerReceiver(
            recordedWorkoutSavedReceiver,
            new IntentFilter(STTConstants.BroadcastActions.WORKOUT_SAVED)
        );
    }

    /**
     * Sets ongoing workout to null, stops any system notification and stops this service
     */
    void endService() {
        /* FIXME Should we actually call stopSelf(startId) and in onDestroy do all the cleaning
        instead of here?
        Otherwise there might be some command that we haven't processed yet */
        String msg = "Ending RWS service";
        FirebaseCrashlytics.getInstance().log(msg);
        Timber.i(msg);
        synchronized (ongoingWorkoutLock) {
            ongoingWorkout = null;
            endAutoSaveOngoingWorkout();
        }
        // Stop this service
        stopSelf();
    }

    /**
     * Starts recording a workout
     */
    private void startWorkout() {
        String msg = "Starting workout: type="
            + activityType
            + ", autoPause= "
            + autoPause.getAmountInMetersPerSecond();
        FirebaseCrashlytics.getInstance().log(msg);
        Timber.i(msg);
        Location bestLastLocation = findLastBestLocation(activityType);
        if (bestLastLocation != null) {
            setAltitudeFromPressureSensorIfAvailable(bestLastLocation);
        }
        synchronized (ongoingWorkoutLock) {
            UserSettings settings = userSettingsController.getSettings();
            ongoingWorkout = new OngoingWorkout(activityType, settings.getAltitudeOffset(),
                settings.getCadenceDataSource());
            ongoingWorkout.start(bestLastLocation);
        }

        if (bestLastLocation != null) {
            fetchWeatherConditions(bestLastLocation);
            needToFetchWeatherConditions = false;
        } else {
            needToFetchWeatherConditions = true;
        }
    }

    private Location findLastBestLocation(ActivityType activityType) {
        Location bestLastLocation;
        // Filter start location based on workout activity
        if (activityType.isIndoor()) {
            long fiveMinutesAgo = System.currentTimeMillis() - 5 * 60 * 1000;
            bestLastLocation = locationModel.getLastLocation(LastLocationRequest.builder()
                .skipPassiveProvider(false)
                .timeInMilliSecondsSinceEpoch(fiveMinutesAgo)
                .build());
            if (bestLastLocation != null && bestLastLocation.getTime() < fiveMinutesAgo) {
                // If it's too old discard it.
                Timber.d("Location %s too old for indoor start point", bestLastLocation.toString());
                bestLastLocation = null;
            }

            // We don't want to record locations in indoor mode
            stopRecordingLocation();
            // Stop listening for pressure changes
            stopAltitude();
        } else {
            long twoSecondsAgo = System.currentTimeMillis() - 2 * 1000;
            bestLastLocation = locationModel.getLastLocation(LastLocationRequest.builder()
                .skipPassiveProvider(true)
                .timeInMilliSecondsSinceEpoch(twoSecondsAgo)
                .build());
            if (bestLastLocation != null) {
                Timber.d(
                    "Best location found: %s. Location time %dms and Two seconds ago %d ms ago",
                    bestLastLocation, bestLastLocation.getTime(), twoSecondsAgo);
                if (bestLastLocation.getTime() < twoSecondsAgo || !bestLastLocation.getProvider()
                    .equals(LocationManager.GPS_PROVIDER)) {
                    // If it's too old or not from GPS discard it.
                    Timber.d("Location %s too old or not from GPS to use as start point",
                        bestLastLocation.toString());
                    bestLastLocation = null;
                }
            }
        }
        return bestLastLocation;
    }

    private void startAutoSaveOngoingWorkout() {
        // Recovering a workout already sets the autosave controller so skip its creation if we
        // already have one
        if (autoSaveOngoingWorkoutController == null) {
            autoSaveOngoingWorkoutController = new AutoSaveOngoingWorkoutController(ongoingWorkout);
        }
        autoSaveHandler.removeCallbacks(autoSaveRunnable);
        autoSaveHandler.post(autoSaveRunnable);
    }

    private void stopAutoSaveOngoingWorkout() {
        autoSaveHandler.removeCallbacks(autoSaveRunnable);
        // Force one more save
        if (autoSaveOngoingWorkoutController != null) {
            autoSaveOngoingWorkoutController.save();
        }
    }

    private void endAutoSaveOngoingWorkout() {
        if (autoSaveOngoingWorkoutController != null) {
            autoSaveOngoingWorkoutController.finish();
        }
    }

    private void linkPendingPicturesToOngoingWorkout() {
        ongoingWorkout.addWorkoutPictures(pendingPictures);
        pendingPictures.clear();
    }

    private void autoPauseWorkout() {
        String msg = "Auto pausing workout";
        Timber.d(msg);
        FirebaseCrashlytics.getInstance().log(msg);
        synchronized (ongoingWorkoutLock) {
            ongoingWorkout.autoPause();
        }
        threadedHandler.removeCallbacks(onTimeUpdatedTask);
        recordWorkoutStateChanged(TrackingState.AUTO_PAUSED, false);
    }

    /**
     * Stops recording a workout and sends a broadcast (STOP_WORKOUT_FILTER) to
     * notify all the interested parties
     */
    private void pauseWorkout() {
        String msg = "Pausing workout";
        FirebaseCrashlytics.getInstance().log(msg);
        Timber.i(msg);
        synchronized (ongoingWorkoutLock) {
            if (ongoingWorkout != null) {
                ongoingWorkout.pause();
            } else {
                Timber.w("ongoingWorkout is null!");
            }
        }
        threadedHandler.removeCallbacks(onTimeUpdatedTask);
        recordWorkoutStateChanged(TrackingState.PAUSED, false);
    }

    void stopRecordingLocation() {
        if (locationConnection != null) {
            locationConnection.close();
            locationConnection = null;
        }
    }

    void stopRecordingHeartRates() {
        if (hrmConnection != null) {
            hrmConnection.close();
            hrmConnection = null;
            lastHeartRateEvent = null;
        }
    }

    private void startRecordingHeartRates() {
        if (hrmConnection == null) {
            try {
                Timber.d("Starting bluetooth heart rate monitor connection");
                hrmConnection =
                    HeartRateConnectionMonitor.newInstance(this, heartRateUpdatedReceiver,
                        hrEventListener);
            } catch (IllegalStateException e) {
                Timber.w(e, "Unable to create HRM connection");
            }
        }
    }

    public boolean isRecordingHeartRates() {
        return hrmConnection != null && hrmConnection.isConnected();
    }

    private void startRecordingCadence() {
        if (cadenceConnection == null) {
            cadenceConnection = CadenceConnectionMonitor.newInstance(this, cadenceEventListener);
        }
    }

    void stopRecordingCadence() {
        if (cadenceConnection != null) {
            cadenceConnection.close();
            cadenceConnection = null;
            lastCadenceEvent = null;

            if (totalWheelRevolution > 0) {
                CadenceHelper.saveTotalWheelRevolution(this, totalWheelRevolution);
            }
        }
    }

    public boolean isRecordingCadence() {
        return cadenceConnection != null && cadenceConnection.isConnected();
    }

    public boolean isRecordingStepCount() {
        return stepCountConnection != null && stepCountConnection.hasStepCountSensor();
    }

    void recordWorkoutStateChanged(TrackingState trackingState, boolean synchronous) {
        Intent intent = new Intent(STTConstants.BroadcastActions.RECORDING_STATE_CHANGED).putExtra(
            STTConstants.ExtraKeys.RECORDING_STATE, trackingState);
        if (synchronous) {
            // Needed for save workout so whoever is listening can talk to this
            // RecordWorkoutService instance
            localBM.sendBroadcastSync(intent);
        } else {
            localBM.sendBroadcast(intent);
        }
        recordWorkoutModel.recordingStateChanged(trackingState);
    }

    /**
     * Resumes recording a workout and sends a broadcast (RESUME_WORKOUT_FILTER)
     * to notify all the interested parties
     */
    private void resumeWorkout() {
        String msg = "Resuming workout";
        Timber.i(msg);
        FirebaseCrashlytics.getInstance().log(msg);
        synchronized (ongoingWorkoutLock) {
            // The speed filter uses X amount of previous locations to calculate the speed we
            // need to clear those so
            // the speed is calculated properly after the auto pause
            speedFilter.reset(ongoingWorkout.getActivityType());
            ongoingWorkout.resume();
        }
        threadedHandler.post(onTimeUpdatedTask);
        energyConsumptionCalculator.reset();
        recordWorkoutStateChanged(TrackingState.RECORDING, false);
    }

    @SuppressLint("MissingPermission")
    private void notifyRecordingStartedOrResumed() {
        wearableController.notifyRecordingStartedOrResumed();
        NotificationManagerCompat.from(this).notify(STTConstants.NotificationIds.ONGOING_WORKOUT,
            NotificationBuilder.buildStartedNotification(this, getActivityType(),
                getFollowWorkoutHeader(), getGhostTargetWorkoutHeader(), getFollowRoute()));
    }

    @SuppressLint("MissingPermission")
    private void notifyRecordingPaused() {
        wearableController.notifyRecordingPaused();
        NotificationManagerCompat.from(this).notify(STTConstants.NotificationIds.ONGOING_WORKOUT,
            NotificationBuilder.buildPausedNotification(this, getActivityType(),
                getFollowWorkoutHeader(), getGhostTargetWorkoutHeader(), getFollowRoute()));
    }

    @SuppressLint("MissingPermission")
    private void notifyRecordingAutoPaused() {
        wearableController.notifyRecordingAutoPaused();
        NotificationManagerCompat.from(this).notify(STTConstants.NotificationIds.ONGOING_WORKOUT,
            NotificationBuilder.buildAutoPausedNotification(this, getActivityType(),
                getFollowWorkoutHeader(), getGhostTargetWorkoutHeader(), getFollowRoute()));
    }

    @Nullable
    public WorkoutHeader getWorkoutHeader(String username) {
        int sharingFlags = workoutShareUtils.getDefaultWorkoutBackendShareFlag();
        synchronized (ongoingWorkoutLock) {
            if (ongoingWorkout != null) {
                TSS tss = calculateTSS(ongoingWorkout);
                return ongoingWorkout.getWorkoutHeader(username,
                    userSettingsController.getSettings().getHrMaximum(), sharingFlags, tss, null);
            } else {
                Timber.w("getWorkoutHeader - ongoingWorkout is null");
                return null;
            }
        }
    }

    public WorkoutData getWorkoutData(UserSettings userSettings) {
        Pair<String, String> mccAndMnc = DeviceUtils.getMccAndMnc(telephonyManager);
        synchronized (ongoingWorkoutLock) {
            return new WorkoutData(ongoingWorkout.getRoutePoints(),
                ongoingWorkout.getHeartRateEvents(),
                ongoingWorkout.getManualLaps().getCompleteLaps(), userSettings.getMeasurementUnit(),
                ongoingWorkout.getEvents(), ongoingWorkout.getLongitudeStatistics(),
                ongoingWorkout.getLatitudeStatistics(), ongoingWorkout.getAltitudeStatistics(),
                ongoingWorkout.getSpeedStatistics(), ongoingWorkout.getHeartRateStatistics(),
                ongoingWorkout.getCadenceStatistics(), userSettings.getAltitudeOffset(),
                userSettings.getHrMaximum(), mccAndMnc.second, mccAndMnc.first);
        }
    }

    public TrackingState getCurrentState() {
        synchronized (ongoingWorkoutLock) {
            if (ongoingWorkout == null) return TrackingState.NOT_STARTED;
            return ongoingWorkout.getState();
        }
    }

    /**
     * @return the total distance in meters
     */
    public double getTotalDistance() {
        if (ongoingWorkout == null) {
            synchronized (ongoingWorkoutLock) {
                if (ongoingWorkout == null) {
                    return 0;
                }
            }
        }

        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout.getDistance();
        }
    }

    public double getTotalTimeInSeconds() {
        if (ongoingWorkout == null) {
            synchronized (ongoingWorkoutLock) {
                if (ongoingWorkout == null) {
                    return 0;
                }
            }
        }

        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout.getDurationInSeconds();
        }
    }

    /**
     * @return list of route points. Null if there's no current route
     */
    public List<WorkoutGeoPoint> getCurrentRoutePoints() {
        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout != null ? ongoingWorkout.getRoutePoints() : null;
        }
    }

    public List<WorkoutHrEvent> getCurrentHeartRateEvents() {
        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout != null ? ongoingWorkout.getHeartRateEvents() : null;
        }
    }

    /**
     * @return Average speed in m/s.
     */
    public double getAverageSpeed() {
        if (ongoingWorkout == null) {
            synchronized (ongoingWorkoutLock) {
                if (ongoingWorkout == null) {
                    return 0;
                }
            }
        }

        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout.getAverageSpeed();
        }
    }

    public double getDurationInSeconds() {
        if (ongoingWorkout == null) {
            synchronized (ongoingWorkoutLock) {
                if (ongoingWorkout == null) {
                    return 0;
                }
            }
        }

        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout.getDurationInSeconds();
        }
    }

    public double getOngoingLapDistanceInMeters(Laps.Type lapType, MeasurementUnit unit) {
        OngoingLap lap = getOngoingLap(lapType, unit);
        if (lap == null) {
            return 0;
        }
        return lap.getDistance();
    }

    public double getOngoingLapDurationInSeconds(Laps.Type lapType, MeasurementUnit unit) {
        OngoingLap lap = getOngoingLap(lapType, unit);
        if (lap == null) {
            return 0;
        }
        return lap.getDuration() / 1000;
    }

    public double getOngoingLapAverageSpeed(Laps.Type lapType, MeasurementUnit unit) {
        OngoingLap lap = getOngoingLap(lapType, unit);
        if (lap == null) {
            return 0;
        }
        return lap.getAverageSpeed();
    }

    public Laps getLaps(Laps.Type lapsType, MeasurementUnit unit) {
        if (ongoingWorkout == null) {
            synchronized (ongoingWorkoutLock) {
                if (ongoingWorkout == null) return null;
            }
        }

        synchronized (ongoingWorkoutLock) {
            switch (lapsType) {
                case MANUAL:
                    return ongoingWorkout.getManualLaps();
                case HALF:
                case ONE:
                case TWO:
                case FIVE:
                case TEN:
                    return ongoingWorkout.getAutomaticLaps(unit).getLaps(lapsType);
                default:
                    throw new IllegalArgumentException("Unsupported lap type " + lapsType);
            }
        }
    }

    /**
     * Do not expose this!
     */
    private OngoingLap getOngoingLap(Laps.Type lapsType, MeasurementUnit unit) {
        Laps laps = getLaps(lapsType, unit);
        if (laps == null) {
            return null;
        }
        return laps.getOngoingLap();
    }

    @Nullable
    public Location getUnfilteredLocation() {
        return unfilteredLocation;
    }

    public Location getLastLocation() {
        return lastLocation;
    }

    public int getCurrentHeartRate() {
        if (lastHeartRateEvent == null) {
            return -1;
        }
        return lastHeartRateEvent.getHeartRate();
    }

    public BatteryStatus getCurrentHeartRateMonitorBatteryStatus() {
        if (lastHeartRateEvent == null) {
            return null;
        }
        return lastHeartRateEvent.getBatteryStatus();
    }

    public int getAverageHeartRate() {
        if (ongoingWorkout == null) {
            synchronized (ongoingWorkoutLock) {
                if (ongoingWorkout == null) {
                    return -1;
                }
            }
        }

        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout.getAverageHeartRate();
        }
    }

    public int getMaxHeartRate() {
        if (ongoingWorkout == null) {
            synchronized (ongoingWorkoutLock) {
                if (ongoingWorkout == null) {
                    return -1;
                }
            }
        }

        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout.getMaxHeartRate();
        }
    }

    public int getCurrentCadence() {
        return lastCadenceEvent == null ? -1 : lastCadenceEvent.crankRPM;
    }

    public int getAverageCadence() {
        if (ongoingWorkout == null) {
            synchronized (ongoingWorkoutLock) {
                if (ongoingWorkout == null) {
                    return -1;
                }
            }
        }

        synchronized (ongoingWorkoutLock) {
            return (int) ongoingWorkout.getCadenceStatistics().getAvg();
        }
    }

    public double getCurrentSpeed() {
        return lastSpeed;
    }

    /**
     * @return the last altitude value in meters
     */
    public double getCurrentAltitude() {
        // If the pressure sensor is available let's use it (we don't care if the workout is
        // ongoing, started, stop...)
        if (altitudeConnection != null) {
            float altitudeOffset = userSettingsController.getSettings().getAltitudeOffset();
            try {
                return altitudeConnection.getAltitude() + altitudeOffset;
            } catch (IllegalStateException e) {
                Timber.w("Altitude from pressure sensor not yet available.");
            }
        }

        if (ongoingWorkout == null) {
            synchronized (ongoingWorkoutLock) {
                if (ongoingWorkout == null) {
                    return 0;
                }
            }
        }

        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout.getCurrentAltitude();
        }
    }

    public double getMaxAltitude() {
        if (ongoingWorkout == null) {
            synchronized (ongoingWorkoutLock) {
                if (ongoingWorkout == null) {
                    return 0;
                }
            }
        }
        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout.getMaxAltitude();
        }
    }

    public double getMinAltitude() {
        if (ongoingWorkout == null) {
            synchronized (ongoingWorkoutLock) {
                if (ongoingWorkout == null) {
                    return 0;
                }
            }
        }
        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout.getMinAltitude();
        }
    }

    public double getMaxSpeed() {
        if (ongoingWorkout == null) {
            synchronized (ongoingWorkoutLock) {
                if (ongoingWorkout == null) {
                    return 0;
                }
            }
        }

        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout.getMaxSpeed();
        }
    }

    /**
     * @return the energy consumed up until now in KCal
     */
    public double getTotalEnergyKCal() {
        if (ongoingWorkout == null) {
            synchronized (ongoingWorkoutLock) {
                if (ongoingWorkout == null) {
                    return 0;
                }
            }
        }

        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout.getEnergyConsumed();
        }
    }

    public int getStepCount() {
        if (ongoingWorkout == null) {
            synchronized (ongoingWorkoutLock) {
                if (ongoingWorkout == null) {
                    return 0;
                }
            }
        }

        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout.getRecordedStepCount();
        }
    }

    public int getStepRate() {
        if (ongoingWorkout == null) {
            synchronized (ongoingWorkoutLock) {
                if (ongoingWorkout == null) {
                    return 0;
                }
            }
        }

        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout.getStepRate();
        }
    }

    public double getLastDistanceUnitSpeed(MeasurementUnit unit) {
        if (ongoingWorkout == null) {
            synchronized (ongoingWorkoutLock) {
                if (ongoingWorkout == null) {
                    return 0;
                }
            }
        }
        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout.getLastDistanceUnitSpeed(unit);
        }
    }

    private void addWorkoutPicture(String filePath, String md5Hash, Point location, int width,
        int height) {
        if (ongoingWorkout == null) {
            synchronized (ongoingWorkoutLock) {
                if (ongoingWorkout == null) {
                    // Add the picture to the list of pending ones
                    addPendingWorkoutPicture(filePath, location, md5Hash, width, height);
                    return;
                }
            }
        }
        synchronized (ongoingWorkoutLock) {
            // If there's an ongoing workout then add the picture to it
            ImageInformation imageInfo = new ImageInformation(location, System.currentTimeMillis(),
                ongoingWorkout.getDuration(), filePath, md5Hash,
                currentUserController.getUsername(), width, height);
            ongoingWorkout.addWorkoutPicture(imageInfo);
        }
    }

    /**
     * Adds the given picture file to a temporary list of images that will be
     * linked to the {@link #ongoingWorkout} once it's created
     */
    private void addPendingWorkoutPicture(String filePath, Point location, String md5Hash,
        int width, int height) {
        // totalTime is unknown since the workout hasn't started therefore we use 0
        ImageInformation imageInformation =
            new ImageInformation(location, System.currentTimeMillis(), 0, filePath, md5Hash,
                currentUserController.getUsername(), width, height);
        pendingPictures.add(imageInformation);
    }

    /**
     * @return the list of metadata information of all the pictures taken during
     * the ongoing workout
     */
    public List<ImageInformation> getWorkoutPictures() {
        if (ongoingWorkout == null) {
            synchronized (ongoingWorkoutLock) {
                if (ongoingWorkout == null) {
                    return null;
                }
            }
        }
        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout.getPictures();
        }
    }

    public List<WorkoutExtension> getWorkoutExtensions() {
        if (ongoingWorkout == null) {
            synchronized (ongoingWorkoutLock) {
                if (ongoingWorkout == null) {
                    return Collections.emptyList();
                }
            }
        }
        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout.getExtensions();
        }
    }

    void handleHrEvent(BluetoothHeartRateEvent event) {
        // Always keep the latest HR value for UI purposes
        lastHeartRateEvent = event;

        synchronized (ongoingWorkoutLock) {
            if (ongoingWorkout != null) {
                ongoingWorkout.updateHeartRate(event);
            }
        }
    }

    public ActivityType getActivityType() {
        synchronized (ongoingWorkoutLock) {
            if (ongoingWorkout != null) {
                return ongoingWorkout.getActivityType();
            }
        }
        return activityType;
    }

    public AutoPause getAutoPause() {
        return autoPause;
    }

    public boolean isLocked() {
        return isLocked;
    }

    public SpeedPaceState getCurrentSpeedPaceState() {
        return currentSpeedPaceState;
    }

    @Nullable
    public Route getFollowRoute() {
        return followRoute;
    }

    public WorkoutHeader getFollowWorkoutHeader() {
        return followWorkout;
    }

    public boolean hasGhostTarget() {
        return ghostTarget != null;
    }

    public WorkoutHeader getGhostTargetWorkoutHeader() {
        return ghostTarget == null ? null : ghostTarget.getWorkoutHeader();
    }

    public WorkoutGeoPoint getCurrentGhostPosition() {
        if (ghostTarget != null) {
            return ghostTarget.getLastMatchedPosition();
        }
        return null;
    }

    public WorkoutGeoPoint getGhostPositionAtCurrentDuration() {
        if (ghostTarget != null && ongoingWorkout != null) {
            try {
                return ghostTarget.getPositionAtDuration(ongoingWorkout.getDuration());
            } catch (IllegalStateException e) {
                Timber.w(e, "Invalid ghost state");
                return null;
            } catch (GhostMatchNotFoundException ghostMatchNotFoundException) {
                Timber.w(ghostMatchNotFoundException, "No match found");
                return null;
            }
        }
        return null;
    }

    public GhostDistanceTimeState getCurrentGhostTimeDistanceState() {
        return currentGhostTimeDistanceState;
    }

    public int getRunCount() {
        if (activityType == null || !activityType.isSlopeSki()) {
            return 0;
        }

        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout != null ? ongoingWorkout.getRunCount() : 0;
        }
    }

    public Laps getRuns() {
        if (activityType == null || !activityType.isSlopeSki()) {
            return null;
        }

        synchronized (ongoingWorkoutLock) {
            if (ongoingWorkout == null) {
                return null;
            }

            // TODO optimize me
            SlopeSki slopeSki = ongoingWorkout.calculateRuns();
            MeasurementUnit unit = userSettingsController.getSettings().getMeasurementUnit();
            List<SlopeSki.Run> runs = slopeSki.getRuns();
            List<CompleteLap> completeLaps = new ArrayList<>(runs.size());
            for (SlopeSki.Run run : runs) {
                completeLaps.add(CompleteSkiRun.create(run, R.color.graphlib_altitude, unit));
            }
            return new ManualLaps(completeLaps);
        }
    }

    public double getSkiDurationInSeconds() {
        if (activityType == null || !activityType.isSlopeSki()) {
            return 0.0;
        }

        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout != null ? ongoingWorkout.getSkiDurationInSeconds() : 0.0;
        }
    }

    public double getSkiDistanceInMeters() {
        if (activityType == null || !activityType.isSlopeSki()) {
            return 0.0;
        }

        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout != null ? ongoingWorkout.getSkiDistanceInMeters() : 0.0;
        }
    }

    public double getSkiDescentInMeters() {
        if (activityType == null || !activityType.isSlopeSki()) {
            return 0.0;
        }
        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout != null ? ongoingWorkout.getSkiDescentInMeters() : 0.0;
        }
    }

    public double getSkiAngleDegree() {
        if (activityType == null || !activityType.isSlopeSki()) {
            return 0.0;
        }
        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout != null ? ongoingWorkout.getSkiAngleDegree() : 0.0;
        }
    }

    public double getAverageSkiSpeed() {
        if (activityType == null || !activityType.isSlopeSki()) {
            return 0.0;
        }
        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout != null ? ongoingWorkout.getAverageSkiSpeed() : 0.0;
        }
    }

    public double getMaximumSkiSpeed() {
        if (activityType == null || !activityType.isSlopeSki()) {
            return 0.0;
        }
        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout != null ? ongoingWorkout.getMaximumSkiSpeed() : 0.0;
        }
    }

    public double getCurrentRunDurationInSeconds() {
        if (activityType == null || !activityType.isSlopeSki()) {
            return 0.0;
        }

        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout != null ? ongoingWorkout.getCurrentRunDurationInSeconds() : 0.0;
        }
    }

    public double getCurrentRunDistanceInMeters() {
        if (activityType == null || !activityType.isSlopeSki()) {
            return 0.0;
        }

        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout != null ? ongoingWorkout.getCurrentRunDistanceInMeters() : 0.0;
        }
    }

    public double getCurrentRunDescentInMeters() {
        if (activityType == null || !activityType.isSlopeSki()) {
            return 0.0;
        }

        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout != null ? ongoingWorkout.getCurrentRunDescentInMeters() : 0.0;
        }
    }

    public double getCurrentRunAverageSpeed() {
        if (activityType == null || !activityType.isSlopeSki()) {
            return 0.0;
        }

        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout != null ? ongoingWorkout.getCurrentRunAverageSpeed() : 0.0;
        }
    }

    public double getCurrentRunMaximumSpeed() {
        if (activityType == null || !activityType.isSlopeSki()) {
            return 0.0;
        }

        synchronized (ongoingWorkoutLock) {
            return ongoingWorkout != null ? ongoingWorkout.getCurrentRunMaximumSpeed() : 0.0;
        }
    }

    public boolean hasConnectedWearable() {
        return wearableController != null && wearableController.hasConnectedDevices();
    }

    /**
     * @return the time difference between current location and ghost. Positive means the current is
     * behind (slower) than target, negative means ahead.
     * @throws com.stt.android.exceptions.GhostMatchNotFoundException if there's no ghost match at
     * the moment
     */
    public int getGhostTimeDifference() throws GhostMatchNotFoundException {
        if (ghostTarget == null) {
            synchronized (ongoingWorkoutLock) {
                if (ongoingWorkout == null
                    || ongoingWorkout.getState() == TrackingState.NOT_STARTED) {
                    return 0;
                }
            }
            throw new GhostMatchNotFoundException("There's no match available");
        }

        int currentTime = (int) Math.round(getDurationInSeconds());
        return ghostTarget.calculateCurrentMatchTimeDifference(currentTime);
    }

    public double getGhostDistanceDifference() throws GhostMatchNotFoundException {
        if (ghostTarget == null) {
            throw new GhostMatchNotFoundException("There's no match available");
        }
        int currentTime = (int) Math.round(getDurationInSeconds() * 1000);
        return ghostTarget.calculateCurrentMatchDistanceDifference(currentTime);
    }

    private AltitudeSource getAltitudeSource() {
        // Use barometer always in Suunto flavor, if available. ST flavor has user setting.
        AltitudeSource preferredSource;
        if (isSuuntoFlavor) {
            preferredSource = AltitudeSource.BAROMETER;
        } else {
            preferredSource = userSettingsController.getSettings().getAltitudeSource();
        }

        if (preferredSource == AltitudeSource.BAROMETER &&
            !UpdatePressureTask.hasPressureSensor(sensorManager)) {
            return AltitudeSource.GPS;
        } else {
            return preferredSource;
        }
    }

    @Override
    public void onWorkoutDataLoaded(int workoutId, WorkoutData workoutData) {
        ghostTarget = new OngoingGhostTarget(ghostTargetHeader, workoutData);
    }

    @Override
    public void onWorkoutDataLoadFailed(int workoutId) {
        // TODO: Houston we've a problem! Inform the user, or maybe not?
    }

    private void releaseWakeLock() {
        if (wakeLock != null && wakeLock.isHeld()) {
            wakeLock.release();
            String msg = "Released wake lock.";
            FirebaseCrashlytics.getInstance().log(msg);
            Timber.d(msg);
            wakeLock = null;
        }
    }

    public int getPausedCount() {
        return pausedCount;
    }

    private void fetchWeatherConditions(@NonNull Location location) {
        weatherConditionsProvider.getWeatherConditions(
            location.getLatitude(),
            location.getLongitude()
        );
    }

    @Override
    public void onWeatherConditionsAvailable(@NonNull WeatherConditions weatherConditions) {
        synchronized (ongoingWorkoutLock) {
            if (ongoingWorkout != null) {
                ongoingWorkout.setWeatherConditions(weatherConditions);
            }
        }
    }

    @IntDef({
        ACTION_START_WARM_UP, ACTION_STOP_WARM_UP, ACTION_START_WARM_UP_FROM_WEARABLE,
        ACTION_STOP_WARM_UP_FROM_WEARABLE, ACTION_PREPARE, ACTION_RECOVER_AUTO_SAVED_WORKOUT,
        ACTION_START_RECORDING, ACTION_STOP_RECORDING, ACTION_PAUSE_RECORDING,
        ACTION_RESUME_RECORDING, ACTION_ADD_LAP, ACTION_ADD_PICTURE, ACTION_START_TTS,
        ACTION_WEAR_AMBIENT_MODE_UPDATED, ACTION_PREPARE_WORKOUT_TARGET
    })
    @Retention(RetentionPolicy.SOURCE)
    public @interface Action {
    }

    private static final class ServiceHandler extends Handler {
        private final WeakReference<RecordWorkoutService> recordWorkoutService;

        ServiceHandler(Looper looper, RecordWorkoutService recordWorkoutService) {
            super(looper);
            this.recordWorkoutService = new WeakReference<>(recordWorkoutService);
        }

        @Override
        public void handleMessage(@NonNull Message msg) {
            RecordWorkoutService recordWorkoutService = this.recordWorkoutService.get();
            if (recordWorkoutService != null) {
                recordWorkoutService.onHandleIntent((Intent) msg.obj, msg.arg1);
            }
        }
    }

    /**
     * NOTE We assume the service is always running in the same process as its clients.
     */
    public static class ServiceBinder extends Binder {

        private final WeakReference<RecordWorkoutService> service;

        ServiceBinder(RecordWorkoutService service) {
            this.service = new WeakReference<>(service);
        }

        public RecordWorkoutService getService() {
            return service.get();
        }
    }

    class RecordedWorkoutSavedBroadcastReceiver extends BroadcastReceiver {
        private final int workoutId;

        RecordedWorkoutSavedBroadcastReceiver(int workoutId) {
            this.workoutId = workoutId;
        }

        @Override
        public void onReceive(Context context, Intent intent) {
            if (!intent.hasExtra(STTConstants.ExtraKeys.WORKOUT_ID)) {
                Timber.w("RecordWorkoutService: received workout saved broadcast did not have WORKOUT_ID extra");
                return;
            }

            // Make sure the default value isn't the workoutId we're looking for
            int savedWorkoutId = intent.getIntExtra(STTConstants.ExtraKeys.WORKOUT_ID, workoutId - 1);
            if (savedWorkoutId != workoutId) {
                Timber.d("RecordWorkoutService: workout other than the one being recorded saved");
                return;
            } else {
                Timber.d("RecordWorkoutService: recorded workout saved");
            }

            synchronized (ongoingWorkoutLock) {
                if (ongoingWorkout != null) {
                    ongoingWorkout.markAsSaved();
                    if (autoSaveOngoingWorkoutController != null) {
                        autoSaveOngoingWorkoutController.save();
                    }
                }
            }

            recordWorkoutStateChanged(TrackingState.SAVED, true);
            endService();
        }
    }
}
