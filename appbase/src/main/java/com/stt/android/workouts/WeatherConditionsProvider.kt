package com.stt.android.workouts

import com.stt.android.domain.weather.GetWeatherConditionsUseCase
import com.stt.android.domain.weather.WeatherConditions
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancelChildren
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * Wrapper for using GetWeatherConditionsUseCase from Java code
 */
class WeatherConditionsProvider
@Inject constructor(
    val getWeatherConditionsUseCase: GetWeatherConditionsUseCase
) {
    interface Listener {
        fun onWeatherConditionsAvailable(weatherConditions: WeatherConditions)
    }

    private val serviceJob = Job()
    private val serviceScope = CoroutineScope(Dispatchers.Main + serviceJob)

    var listener: Listener? = null

    fun getWeatherConditions(latitude: Double, longitude: Double) {
        serviceJob.cancelChildren()
        serviceScope.launch {
            val params = GetWeatherConditionsUseCase.Params(latitude, longitude)
            try {
                val weatherConditions = getWeatherConditionsUseCase(params)
                listener?.onWeatherConditionsAvailable(weatherConditions)
            } catch (e: Exception) {
                Timber.w(e, "Failed to get weather conditions")
            }
        }
    }

    fun clear() {
        serviceJob.cancel()
        listener = null
    }
}
