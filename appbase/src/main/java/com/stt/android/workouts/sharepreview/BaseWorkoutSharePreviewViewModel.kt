package com.stt.android.workouts.sharepreview

import android.content.Context
import android.net.Uri
import android.os.Build
import android.util.Size
import androidx.core.net.toUri
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.toLiveData
import androidx.lifecycle.viewModelScope
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.FirebaseAnalyticsTracker
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.DiveExtensionDataModel
import com.stt.android.controllers.ExtensionDataModel
import com.stt.android.controllers.FitnessExtensionDataModel
import com.stt.android.controllers.IntensityExtensionDataModel
import com.stt.android.controllers.JumpRopeExtensionDataModel
import com.stt.android.controllers.PicturesController
import com.stt.android.controllers.SlopeSkiDataModel
import com.stt.android.controllers.SummaryExtensionDataModel
import com.stt.android.controllers.SwimmingExtensionDataModel
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.workout.WorkoutHeaderRepository
import com.stt.android.domain.Point
import com.stt.android.domain.achievements.GetAchievementUseCase
import com.stt.android.domain.review.ReviewState
import com.stt.android.domain.sml.FetchSmlUseCase
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.sml.reader.SmlFactory
import com.stt.android.domain.user.ImageInformation
import com.stt.android.domain.workout.WorkoutData
import com.stt.android.domain.workout.heartRateEventsWithoutPauses
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.exceptions.InternalDataException
import com.stt.android.exceptions.ReviewImageFailedException
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.maps.MapSnapshotSpec
import com.stt.android.models.MapSelectionModel
import com.stt.android.multimedia.sportie.SportieAddPhoto
import com.stt.android.multimedia.sportie.SportieAspectRatio
import com.stt.android.multimedia.sportie.SportieDiveTrack
import com.stt.android.multimedia.sportie.SportieHelper
import com.stt.android.multimedia.sportie.SportieImage
import com.stt.android.multimedia.sportie.SportieInfo
import com.stt.android.multimedia.sportie.SportieItem
import com.stt.android.multimedia.sportie.SportieMap
import com.stt.android.multimedia.sportie.SportieSelection
import com.stt.android.multimedia.sportie.SportieShareSource
import com.stt.android.ui.controllers.WorkoutDataLoaderController
import com.stt.android.ui.controllers.loadWorkout
import com.stt.android.ui.extensions.targetWorkoutVisibility
import com.stt.android.ui.map.MapHelper
import com.stt.android.ui.tasks.BitmapLoadAndResizer
import com.stt.android.ui.tasks.LoadAndResizeResult
import com.stt.android.ui.tasks.WorkoutImageViewModel
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.utils.WorkoutImageFilesHelper
import com.stt.android.utils.awaitFirstNonNull
import com.stt.android.workoutdetail.divetrack.DiveTrackUtil
import com.stt.android.workouts.SyncSingleWorkoutUseCase
import com.stt.android.workouts.setSharingLinkIfPrivate
import io.reactivex.BackpressureStrategy
import io.reactivex.subjects.PublishSubject
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import java.time.Instant
import java.util.concurrent.TimeUnit
import kotlin.math.roundToInt

abstract class BaseWorkoutSharePreviewViewModel(
    private val savedStateHandle: SavedStateHandle,
    private val slopeSkiDataModel: SlopeSkiDataModel,
    private val diveExtensionDataModel: DiveExtensionDataModel,
    private val summaryExtensionDataModel: SummaryExtensionDataModel,
    private val fitnessExtensionDataModel: FitnessExtensionDataModel,
    private val intensityExtensionDataModel: IntensityExtensionDataModel,
    private val picturesController: PicturesController,
    private val dataLoaderController: WorkoutDataLoaderController,
    private val swimmingExtensionDataModel: SwimmingExtensionDataModel,
    private val fetchSmlUseCase: FetchSmlUseCase,
    private val workoutHeaderController: WorkoutHeaderController,
    private val syncSingleWorkoutUseCase: SyncSingleWorkoutUseCase,
    private val infoModelFormatter: InfoModelFormatter,
    private val workoutImageFilesHelper: WorkoutImageFilesHelper,
    private val bitmapLoadAndResizer: BitmapLoadAndResizer,
    private val getAchievementUseCase: GetAchievementUseCase,
    private val mapSelectionModel: MapSelectionModel,
    private val firebaseAnalyticsTracker: FirebaseAnalyticsTracker,
    private val workoutHeaderRepository: WorkoutHeaderRepository,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    private val jumpRopeExtensionDataModel: JumpRopeExtensionDataModel,
    private val coroutinesDispatchers: CoroutinesDispatchers,
    private val sportieHelper: SportieHelper,
) : ViewModel() {
    private val sportieShareSource: SportieShareSource =
        savedStateHandle[WorkoutSharePreviewActivity.EXTRA_SHARE_SOURCE] ?: SportieShareSource.UNKNOWN

    /**
     * a LiveData instance that emits a list of [SportieItem]s with the workout information
     * details for a specific [WorkoutHeader]
     */
    val sportieItemsLiveData = MutableLiveData<List<SportieItem>>()

    /**
     * a LiveData instance that emits the [Uri] for a prepared [SportieItem] containing the workout information
     * overlain onto the specific image or map
     */
    val shareImageLiveData = SingleLiveEvent<Pair<Uri, SportieSelection>>()

    val shareImageErrorEvent = SingleLiveEvent<Throwable>()

    val shareLinkErrorEvent: LiveData<Throwable>
        get() = _shareLinkErrorEvent
    private val _shareLinkErrorEvent = SingleLiveEvent<Throwable>()

    val shareLinkReady: LiveData<Boolean>
        get() = _shareLinkReady
    private val _shareLinkReady = SingleLiveEvent<Boolean>()

    val shareLinkLoading: LiveData<Boolean>
        get() = _shareLinkLoading
    private val _shareLinkLoading = SingleLiveEvent<Boolean>()

    private val _sportieAspectRatio = savedStateHandle.getLiveData(
        CURRENT_ASPECT_RATIO,
        SportieAspectRatio.ORIGINAL
    )
    val sportieAspectRatio: LiveData<SportieAspectRatio> = _sportieAspectRatio

    var mapSnapshotSize: Size? = null

    val setEditModeLiveData = MutableLiveData<Boolean>()
    var currentItemIndex: Int = 0

    val workoutHeaderLiveData: LiveData<WorkoutHeader>
        get() = _workoutHeaderLiveData
    private val _workoutHeaderLiveData = MutableLiveData<WorkoutHeader>()

    private val _initWorkoutHeaderEvent = SingleLiveEvent<WorkoutHeader?>()
    val initWorkoutHeaderEvent: LiveData<WorkoutHeader?> = _initWorkoutHeaderEvent

    val onAddPhotoClicked: LiveData<Unit>
        get() = _onAddPhotoClicked
            .toFlowable(BackpressureStrategy.DROP)
            .throttleFirst(500, TimeUnit.MILLISECONDS)
            .toLiveData()

    private val _onAddPhotoClicked = PublishSubject.create<Unit>()

    private var photoAdded = false

    /**
     * a LiveData instance that emits a [LoadingState] to show when preview images are loaded/ no
     * loading ongoing and user may interact with view/ actual sharable images are being prepared
     */
    val loadingState = MutableLiveData<LoadingState>().apply { value = LoadingState.NOT_LOADING }

    private val _noNetworkErrorEvent = SingleLiveEvent<Any>()
    val noNetworkErrorEvent: LiveData<Any>
        get() = _noNetworkErrorEvent

    private var initialPictureCount = 0
    var numPhotosAdded = 0

    private val _reviewImageFailedEvent = MutableSharedFlow<Boolean>()
    val reviewImageFailedEvent: Flow<Boolean> = _reviewImageFailedEvent.asSharedFlow()

    fun loadWorkoutHeader(workoutId: Int) {
        viewModelScope.launch(coroutinesDispatchers.io) {
            _initWorkoutHeaderEvent.postValue(workoutHeaderRepository.findById(workoutId))
        }
    }

    fun checkWorkoutSyncedAndLoadSportieItems(
        workoutHeader: WorkoutHeader,
        sendAddPhotoAnalytics: Boolean,
        selectPosition: Int,
    ) {
        viewModelScope.launch {
            runSuspendCatching {
                val sportieItems = loadSportieItems(workoutHeader)
                setSportieItems(workoutHeader, sportieItems, sendAddPhotoAnalytics, selectPosition)
            }.onFailure(::handleSportieItemLoadingError)
        }
    }

    /**
     * Trigger change of image aspect ratio
     */
    fun changeSportieAspectRatio() {
        _sportieAspectRatio.value?.let {
            val newValue = it.nextValid()
            _sportieAspectRatio.postValue(newValue)
            savedStateHandle[CURRENT_ASPECT_RATIO] = newValue
        }
    }

    /**
     * Set text edit mode
     */
    fun setEditMode(enabled: Boolean) {
        setEditModeLiveData.postValue(enabled)
    }

    /**
     * Load all the images for the [workoutHeader], creating a [SportieImage] for each one.
     * If the workout header has a route polyline, a [SportieMap] item is prepended to the list of image items.
     * The result is pushed to [sportieItemsLiveData]
     */
    private suspend fun loadSportieItems(
        workoutHeader: WorkoutHeader,
    ): List<SportieItem> = withContext(coroutinesDispatchers.io) {
        val workoutExtensionsAsync = async(coroutinesDispatchers.io) {
            getWorkoutExtensions(workoutHeader)
        }
        val smlAsync = async(coroutinesDispatchers.io) {
            fetchSmlUseCase.fetchSml(workoutHeader.id, workoutHeader.key) ?: SmlFactory.EMPTY
        }
        val workoutData = dataLoaderController.loadWorkout(workoutHeader)

        val workoutExtensions = workoutExtensionsAsync.await()
        val sml = smlAsync.await()

        buildList {
            loadSportieMapItem(workoutHeader, workoutExtensions, sml, workoutData)
                ?.let { add(it) }

            loadSportieDiveTrackItem(workoutHeader, workoutExtensions, sml)
                ?.let { add(it) }

            val sportieImages = loadSportieImageItems(
                workoutHeader,
                workoutExtensions,
                sml,
                workoutData,
            )
            addAll(sportieImages)

            val sportieAddPhoto = SportieAddPhoto(
                workoutHeader,
                workoutExtensions,
                sml,
                infoModelFormatter,
            )
            add(sportieAddPhoto)
        }
    }

    private fun loadSportieMapItem(
        workoutHeader: WorkoutHeader,
        workoutExtensions: List<WorkoutExtension>,
        sml: Sml,
        workoutData: WorkoutData?,
    ): SportieMap? {
        if (workoutHeader.isPolylineEmpty) {
            return null
        }
        val mapSize = mapSnapshotSize
        if (mapSize == null) {
            Timber.w("No size for map snapshots!")
            return null
        }

        val spec = MapSnapshotSpec.Workout(
            workoutId = workoutHeader.id,
            width = mapSize.width,
            height = mapSize.height,
            explicitProviderName = MapHelper.getMapsProviderNameWithoutGoogle(),
            explicitMapType = mapSelectionModel.selectedMapTypeUserHasAccessTo,
            explicitRoutePadding = mapSize.width / 5,
        )

        return SportieMap(
            spec,
            workoutData?.heartRateEvents.orEmpty(),
            workoutHeader,
            workoutExtensions,
            sml,
            infoModelFormatter,
            workoutData?.routePoints.orEmpty(),
            workoutData?.heartRateEventsWithoutPauses.orEmpty(),
        )
    }

    private fun loadSportieDiveTrackItem(
        workoutHeader: WorkoutHeader,
        workoutExtensions: List<WorkoutExtension>,
        sml: Sml,
    ): SportieDiveTrack? {
        if (!workoutHeader.activityType.isDiving) {
            return null
        }

        val diveTrack = DiveTrackUtil.diveTrackFromSml(sml)
            ?: return null

        return SportieDiveTrack(
            diveTrack,
            null,
            workoutHeader,
            workoutExtensions,
            sml,
            infoModelFormatter,
        )
    }

    private suspend fun loadSportieImageItems(
        workoutHeader: WorkoutHeader,
        workoutExtensions: List<WorkoutExtension>,
        sml: Sml,
        workoutData: WorkoutData?,
    ): List<SportieImage> = picturesController.loadImages(workoutHeader)
        .awaitFirstNonNull()
        .filter { it.reviewState == ReviewState.PASS }
        .ifEmpty {
            val defaultImage = DefaultImageResInformation.getDefaultImage(workoutHeader.activityType)
            defaultImage?.let { listOf(it) } ?: emptyList()
        }
        .map { imageInformation ->
            SportieImage(
                imageInformation,
                workoutHeader,
                workoutExtensions,
                sml,
                infoModelFormatter,
                workoutData?.routePoints.orEmpty(),
                workoutData?.heartRateEventsWithoutPauses.orEmpty(),
            )
        }

    private fun setSportieItems(
        workoutHeader: WorkoutHeader,
        items: List<SportieItem>,
        sendAddPhotoAnalytics: Boolean,
        selectPosition: Int
    ) {
        if (!sendAddPhotoAnalytics) {
            // set initialPictureCount only once
            initialPictureCount = sportieItemsLiveData.value?.count { it is SportieImage } ?: workoutHeader.pictureCount
        }

        val adjustedPosition = if (photoAdded) items.size - 2 else selectPosition
        currentItemIndex = adjustedPosition.coerceIn(items.indices)
        photoAdded = false
        sportieItemsLiveData.value = items
        loadingState.value = LoadingState.NOT_LOADING
        numPhotosAdded = items.count { it is SportieImage } - initialPictureCount
        if (sendAddPhotoAnalytics) {
            sendAddPhotoAnalytics(workoutHeader, numPhotosAdded)
        }
    }

    private fun handleSportieItemLoadingError(t: Throwable) {
        Timber.w(t)
        loadingState.value = LoadingState.NOT_LOADING
    }

    /**
     * Adds photo to workout
     */
    fun addWorkoutPhoto(
        photo: File,
        workoutHeader: WorkoutHeader
    ) = addWorkoutPhotoFromUri(photo.toUri(), workoutHeader)

    fun addWorkoutPhotoFromUri(
        uri: Uri,
        workoutHeader: WorkoutHeader,
        position: Point? = null
    ) {
        viewModelScope.launch(coroutinesDispatchers.io) {
            runSuspendCatching {
                loadingState.postValue(LoadingState.LOADING_SHARING_IMAGES)
                val result = getLoadAndResizeResult(uri, position)
                reviewImage(result)
                val imageInfo = ImageInformation(
                    result.positionFromImage ?: workoutHeader.stopPosition,
                    System.currentTimeMillis(),
                    workoutHeader.totalTime,
                    result.name,
                    result.destFileMD5,
                    workoutHeader.id,
                    workoutHeader.key,
                    workoutHeader.username,
                    result.width,
                    result.height
                )
                picturesController.store(imageInfo)
                val header = workoutHeader.toBuilder()
                    .locallyChanged(true)
                    .pictureCount(workoutHeader.pictureCount + 1)
                    .build()
                workoutHeaderController.store(header)
            }.onSuccess {
                photoAdded = true
                sendEditWorkoutSavedAnalytics(it)
                checkWorkoutSyncedAndLoadSportieItems(
                    it,
                    true,
                    sportieItemsLiveData.value?.size?.minus(1) ?: currentItemIndex
                )
                _workoutHeaderLiveData.postValue(it)
                loadingState.postValue(LoadingState.NOT_LOADING)
            }.onFailure {
                Timber.w(it, "addWorkoutPhoto failed")
                loadingState.postValue(LoadingState.NOT_LOADING)
                if (it is ReviewImageFailedException) {
                    _reviewImageFailedEvent.emit(true)
                }
            }
        }
    }

    /**
     * only suunto china need rewrite this fun to review the image
     * review failed, throw ReviewImageFailedException
     */
    open suspend fun reviewImage(result: LoadAndResizeResult) {}


    fun sendScreenAnalytics(workoutHeader: WorkoutHeader) {
        viewModelScope.launch(coroutinesDispatchers.io) {
            runCatching {
                val summaryExtension = summaryExtensionDataModel.findByWorkoutId(workoutHeader.id)
                val analyticsProperties = AnalyticsProperties().apply {
                    put(AnalyticsEventProperty.ACTIVITY_TYPE, workoutHeader.activityType.simpleName)
                    put(AnalyticsEventProperty.DISTANCE_IN_METERS, workoutHeader.totalDistance.roundToInt())
                    put(
                        AnalyticsEventProperty.DURATION_IN_MINUTES,
                        TimeUnit.MINUTES.convert(workoutHeader.totalTime.toLong(), TimeUnit.SECONDS)
                    )
                    put(AnalyticsEventProperty.HAS_DESCRIPTION, !workoutHeader.description.isNullOrBlank())
                    summaryExtension?.let { put(AnalyticsEventProperty.MOOD, summaryExtension.feeling) }
                    put(AnalyticsEventProperty.SOURCE, sportieShareSource.type)
                    put(AnalyticsEventProperty.TARGET_WORKOUT_VISIBILITY, workoutHeader.targetWorkoutVisibility())
                }
                amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.WORKOUT_SHARE_SCREEN, analyticsProperties)
            }.onFailure { e ->
                Timber.w(e, "Failed to load summary extension")
            }
        }
    }

    fun onAddPhotoClicked() = _onAddPhotoClicked.onNext(Unit)

    private fun sendEditWorkoutSavedAnalytics(workoutHeader: WorkoutHeader) {
        val handler = CoroutineExceptionHandler { _, exception ->
            Timber.w(exception, "Tracking edit workout saved event failed")
        }
        viewModelScope.launch(handler) {
            val achievementsCount = workoutHeader.key?.run { getAchievementUseCase(this)?.count } ?: 0
            AnalyticsProperties().apply {
                put(AnalyticsEventProperty.ACTIVITY_TYPE, workoutHeader.activityType.simpleName)
                put(AnalyticsEventProperty.CHANGES_COUNT, 1)
                put(AnalyticsEventProperty.DISTANCE_IN_METERS, workoutHeader.totalDistance)
                put(
                    AnalyticsEventProperty.DURATION_IN_MINUTES,
                    (workoutHeader.totalTime / 60.0f).roundToInt()
                )
                putYesNo(
                    AnalyticsEventProperty.HAS_DESCRIPTION,
                    !workoutHeader.description.isNullOrEmpty()
                )
                put(AnalyticsEventProperty.NUM_COMMENTS, workoutHeader.commentCount)
                put(AnalyticsEventProperty.NUM_LIKES, workoutHeader.reactionCount)
                put(AnalyticsEventProperty.NUM_PHOTOS, workoutHeader.pictureCount)
                put(AnalyticsEventProperty.NUM_PHOTOS_ADDED, 1)
                put(AnalyticsEventProperty.NUMBER_OF_ACHIEVEMENTS, achievementsCount)
                put(
                    AnalyticsEventProperty.SOURCE,
                    AnalyticsPropertyValue.SOURCE_WORKOUT_SHARE_ADD_PHOTOS
                )
                put(
                    AnalyticsEventProperty.TARGET_WORKOUT_VISIBILITY,
                    workoutHeader.targetWorkoutVisibility()
                )
            }.let {
                amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.EDIT_WORKOUT_SAVED, it)
                firebaseAnalyticsTracker.trackEvent(AnalyticsEvent.EDIT_WORKOUT_SAVED, it)
            }
        }
    }

    private fun sendAddPhotoAnalytics(workoutHeader: WorkoutHeader, nPhotosAdded: Int) {
        AnalyticsProperties().apply {
            put(AnalyticsEventProperty.ACTIVITY_TYPE, workoutHeader.activityType.simpleName)
            put(AnalyticsEventProperty.TARGET_WORKOUT_VISIBILITY, workoutHeader.targetWorkoutVisibility())
            put(AnalyticsEventProperty.NUM_PHOTOS_ADDED, nPhotosAdded)
        }.let { amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.WORKOUT_SHARE_ADD_PHOTO, it) }
    }

    /**
     * Prepare the [sportieItem] and build an image containing the workout information overlay, pushing the Uri to the [shareImageLiveData]
     */
    fun shareImage(
        context: Context,
        sportieItem: SportieItem,
        currentSportieInfo: SportieInfo,
        aspectRatio: SportieAspectRatio
    ) {
        loadingState.value = LoadingState.LOADING_SHARING_IMAGES

        viewModelScope.launch {
            runSuspendCatching {
                val sportieShareInfo = sportieHelper.prepareSportie(
                    context,
                    sportieItem,
                    currentSportieInfo,
                    aspectRatio,
                )
                shareImageLiveData.value = sportieShareInfo.uri to
                    sportieShareInfo.sportieSelection.copy(sportieShareSource = sportieShareSource)

                loadingState.value = LoadingState.NOT_LOADING
            }.onFailure { e ->
                Timber.w(e, "Failed to prepare Sportie share info")
                loadingState.value = LoadingState.NOT_LOADING
                shareImageErrorEvent.value = e
            }
        }
    }

    fun setSharingLinkIfPrivate(workoutHeader: WorkoutHeader, isSummary: Boolean = false) {
        _shareLinkLoading.value = true

        viewModelScope.launch {
            runSuspendCatching {
                setSharingLinkIfPrivate(workoutHeader, workoutHeaderController, syncSingleWorkoutUseCase)

                _shareLinkLoading.value = false
                _shareLinkReady.value = isSummary
                // For now the workout information doesn't show the updated status or any notice to user
                // about it so the updated header is just ignored.
            }.onFailure { e ->
                _shareLinkLoading.value = false
                _shareLinkErrorEvent.value = e
                Timber.w(e, "Error while creating sharing link")
            }
        }
    }

    /**
     * @return a list of [WorkoutExtension] for the provided [workoutHeader] (if any)
     */
    @Throws(InternalDataException::class)
    private fun getWorkoutExtensions(workoutHeader: WorkoutHeader): List<WorkoutExtension> {
        val workoutExtensions = ArrayList<WorkoutExtension>()
        sequenceOf(
            diveExtensionDataModel,
            slopeSkiDataModel,
            summaryExtensionDataModel,
            intensityExtensionDataModel,
            fitnessExtensionDataModel,
            swimmingExtensionDataModel,
            jumpRopeExtensionDataModel,
        ).forEach { extensionDataModel: ExtensionDataModel<out WorkoutExtension> ->
            extensionDataModel.findByWorkoutId(workoutHeader.id)?.let {
                workoutExtensions.add(it)
            }
        }
        return workoutExtensions
    }

    private suspend fun getLoadAndResizeResult(uri: Uri, position: Point? = null): LoadAndResizeResult {
        val positionFromImage = position ?: workoutImageFilesHelper.findImagePositionFromUri(uri)
        val filename = getFileName(uri)
        val dest = workoutImageFilesHelper.getDestinationPathForResize(filename)
        Timber.d("Resizing picture: original=${uri.path}, destination=${dest.absolutePath}")
        val resizedBitmap = bitmapLoadAndResizer.loadBitmapAtTargetResolution(
            uri,
            WorkoutImageViewModel.TARGET_1080P_WIDTH,
            WorkoutImageViewModel.TARGET_1080P_HEIGHT
        )
        Timber.d("Resized picture at resolution ${resizedBitmap.width}x${resizedBitmap.height}")
        val destFileMD5 = workoutImageFilesHelper.saveBitmapToJpegFile(resizedBitmap, dest)

        return LoadAndResizeResult(
            dest.name,
            destFileMD5,
            positionFromImage,
            resizedBitmap.width,
            resizedBitmap.height
        )
    }

    private fun getFileName(uri: Uri): String {
        val lastSegment = if (Build.MANUFACTURER == "Xiaomi") {
            // uri.lastPathSegment return full path in Xiaomi phone, so can't use it to get file name
            uri.path?.substringAfterLast("/")
        } else {
            uri.lastPathSegment
        }
        if (lastSegment == null) {
            throw IllegalArgumentException("Invalid URI for getLoadAndResizeResult: $uri")
        }
        return if (lastSegment.endsWith(".jpg")) {
            lastSegment
        } else {
            "${lastSegment}_${Instant.now().toEpochMilli()}.jpg"
        }
    }

    enum class LoadingState {
        NOT_LOADING,
        LOADING_SHARING_IMAGES,
    }

    companion object {
        const val CURRENT_ASPECT_RATIO = "com.stt.android.CURRENT_ASPECT_RATIO"
    }
}
