package com.stt.android.workouts.reaction

import com.stt.android.common.coroutines.ORMLITE
import com.stt.android.controllers.ReactionModel
import com.stt.android.domain.user.ReactionSummary
import com.stt.android.domain.user.ReactionSummary.REACTION_LIKE
import com.stt.android.domain.workouts.reactions.DomainReactionSummary
import com.stt.android.domain.workouts.reactions.ReactionSummaryDataSource
import kotlinx.coroutines.withContext
import javax.inject.Inject

class ReactionSummaryOrmliteDataSource
@Inject constructor(
    private val reactionModel: ReactionModel
) : ReactionSummaryDataSource {
    override suspend fun storeReactions(reactions: List<DomainReactionSummary>) = withContext(ORMLITE) {
        reactionModel.store(reactions.map { it.toReactionSummary() })
    }

    override suspend fun removeReactionsByWorkoutKey(workoutKey: String) = withContext(ORMLITE) {
        reactionModel.removeSyncedReactionsByWorkoutKey(workoutKey, REACTION_LIKE)
    }
}

fun DomainReactionSummary.toReactionSummary(): ReactionSummary {
    return ReactionSummary.remote(this.workoutKey, this.reaction, this.count, this.userReacted)
}
