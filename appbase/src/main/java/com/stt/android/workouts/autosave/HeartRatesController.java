package com.stt.android.workouts.autosave;

import android.content.Context;
import com.google.gson.Gson;
import com.stt.android.domain.workout.WorkoutHrEvent;
import com.stt.android.workouts.OngoingWorkout;
import java.lang.reflect.Type;

public class HeartRatesController extends IncrementalSaveListController<WorkoutHrEvent> {
    static final String ONGOING_HEART_RATES_FILENAME = "ongoing_heart_rates";

    public HeartRatesController(Context appContext, Gson gson) {
        super(ONGOING_HEART_RATES_FILENAME, appContext, gson);
    }

    public static void cleanSavedData(Context context) {
        context.deleteFile(ONGOING_HEART_RATES_FILENAME);
    }

    @Override
    protected Type getListClass() {
        return WorkoutHrEvent.class;
    }

    @Override
    public void addEntryToOngoingWorkout(OngoingWorkout ongoingWorkout,
        WorkoutHrEvent recoveredEntry) {
        ongoingWorkout.addHeartRate(recoveredEntry);
    }
}
