package com.stt.android.workouts.sharepreview

import android.app.Activity
import android.content.Context
import android.content.Intent
import com.stt.android.R
import com.stt.android.workouts.details.values.WorkoutValue
import timber.log.Timber

class WorkoutValuesPickingActivity :
    WorkoutElementPickingActivity<WorkoutValue, WorkoutValuesPickingViewHolder>() {

    override val actionbarTitleId = R.string.select_data

    private val onItemClick: (Int) -> Unit = {
        val selectedTextViewIndex = intent.getBundleExtra(INTENT_EXTRA)
            ?.getInt(INTENT_EXTRA_SELECTED_TEXT_VIEW_INDEX)
        val intent: Intent
        if (selectedTextViewIndex != null) {
            intent = Intent().putExtra(INTENT_EXTRA_WORKOUT_VALUE_FINAL_INDEX_KEY, it)
                .putExtra(INTENT_EXTRA_SELECTED_TEXT_VIEW_INDEX, selectedTextViewIndex)
            setResult(Activity.RESULT_OK, intent)
        } else {
            // This should never happen.
            Timber.w("Selected text view index was missing")
            setResult(Activity.RESULT_CANCELED)
        }
        finish()
    }

    override fun createAdapter(options: List<WorkoutValue>, initialIndex: Int): WorkoutValuesPickingAdapter =
        WorkoutValuesPickingAdapter(options, initialIndex, onItemClick)

    companion object {
        private const val packageName = "com.stt.android.workouts.sharepreview.WorkoutValuesPickingActivity"
        const val INTENT_EXTRA_WORKOUT_VALUE_FINAL_INDEX_KEY = "$packageName.WORKOUT_VALUE_FINAL_INDEX"
        const val INTENT_EXTRA_SELECTED_TEXT_VIEW_INDEX = "$packageName.SELECTED_TEXT_VIEW_INDEX"
        const val INTENT_WORKOUT_VALUE_REQUEST_CODE = 1

        fun newStartIntent(
            context: Context,
            workoutValuesList: List<WorkoutValue>,
            initialIndex: Int,
            selectedTextViewIndex: Int
        ): Intent {
            val data =
                createDataForStartIntent(workoutValuesList, initialIndex).apply {
                    putInt(INTENT_EXTRA_SELECTED_TEXT_VIEW_INDEX, selectedTextViewIndex)
                }

            return Intent(context, WorkoutValuesPickingActivity::class.java).putExtra(INTENT_EXTRA, data)
        }
    }
}
