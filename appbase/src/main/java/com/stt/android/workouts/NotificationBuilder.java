package com.stt.android.workouts;

import android.app.Notification;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.res.Resources;
import androidx.core.app.NotificationCompat;
import androidx.core.app.TaskStackBuilder;
import com.stt.android.R;
import com.stt.android.domain.routes.Route;
import com.stt.android.domain.workout.ActivityType;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.notifications.NotificationChannelIds;
import com.stt.android.ui.activities.WorkoutActivity;

public class NotificationBuilder {
    private static final int NOTIFICATION_DEFAULTS = 0;
    private static final int REQUEST_CODE_PAUSE = 0;
    private static final int REQUEST_CODE_LAP = 1;
    private static final int REQUEST_CODE_RESUME = 2;

    public static Notification buildRecordingWarmUpNotification(Context context, ActivityType activityType) {
        PendingIntent pendingIntent =
            buildPendingIntent(context, activityType, null, null, null);
        Resources resources = context.getResources();
        // TODO - add a separate text for the warmup notification
        NotificationCompat.Builder builder =
            buildBaseActivityRecordingNotification(context, pendingIntent, resources.getString(com.stt.android.core.R.string.paused),
                resources.getColor(R.color.workout_recording));
        return builder.build();
    }

    public static Notification buildStartedNotification(Context context, ActivityType activityType,
        WorkoutHeader followWorkout, WorkoutHeader ghostTarget, Route followRoute) {

        PendingIntent pendingIntent =
            buildPendingIntent(context, activityType, followWorkout, ghostTarget, followRoute);
        Resources resources = context.getResources();
        NotificationCompat.Builder builder =
            buildBaseActivityRecordingNotification(context, pendingIntent, resources.getString(com.stt.android.core.R.string.recording),
                resources.getColor(R.color.workout_recording));
        builder.addAction(R.drawable.pause, resources.getString(R.string.stop),
            getPauseActionPendingIntent(context));
        if (!activityType.isSlopeSki()) {
            builder.addAction(0, resources.getString(R.string.lap), getLapActionPendingIntent(context));
        }
        return builder.build();
    }

    private static PendingIntent buildPendingIntent(Context context, ActivityType activityType,
        WorkoutHeader followWorkout, WorkoutHeader ghostTarget, Route followRoute) {

        Intent startIntent;
        boolean isOutdoor = !activityType.isIndoor();
        if (followWorkout != null) {
            startIntent =
                WorkoutActivity.newStartIntentFollowWorkout(context, activityType, isOutdoor, false,
                    followWorkout);
        } else if (ghostTarget != null) {
            startIntent =
                WorkoutActivity.newStartIntentGhostWorkout(context, activityType, isOutdoor, false,
                    ghostTarget);
        } else if (followRoute != null) {
            startIntent =
                WorkoutActivity.newStartIntentFollowRoute(context, activityType, isOutdoor, false,
                    followRoute.getId());
        } else {
            startIntent = WorkoutActivity.newStartIntent(context, activityType, isOutdoor, false);
        }

        // builds the task stack
        TaskStackBuilder stackBuilder = TaskStackBuilder.create(context);

        // adds the back stack with the top activity
        stackBuilder.addNextIntentWithParentStack(startIntent);
        for (int i = 0; i < stackBuilder.getIntentCount(); ++i) {
            Intent currentIntent = stackBuilder.editIntentAt(i);
            // Set flags to tell that we want to re-use the existing top activity if possible
            currentIntent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP
                | Intent.FLAG_ACTIVITY_SINGLE_TOP
                | Intent.FLAG_ACTIVITY_NEW_TASK);
        }
        // Gets a PendingIntent containing the entire back stack. Use cancel
        // current flag so the extras are re-send properly
        return stackBuilder.getPendingIntent(0,
            PendingIntent.FLAG_CANCEL_CURRENT | PendingIntent.FLAG_IMMUTABLE);
    }

    private static NotificationCompat.Builder buildBaseActivityRecordingNotification(Context context,
        PendingIntent pendingIntent, CharSequence contentText, int ledColor) {

        Resources resources = context.getResources();
        return new NotificationCompat.Builder(context, NotificationChannelIds.CHANNEL_ID_ACTIVITY_RECORDING)
                .setContentIntent(pendingIntent)
                .setDefaults(NOTIFICATION_DEFAULTS)
                .setContentTitle(resources.getString(R.string.brand_name))
                .setContentText(contentText)
                .setTicker(contentText)
                .setOngoing(true)
                .setSmallIcon(R.drawable.icon_notification)
                .setLights(ledColor, 1000, 1000)
                .setPriority(NotificationCompat.PRIORITY_HIGH);
    }

    private static PendingIntent getPauseActionPendingIntent(Context context) {
        return getPendingIntentServiceAction(context,
                RecordWorkoutService.newPauseRecordingIntent(context), REQUEST_CODE_PAUSE);
    }

    private static PendingIntent getLapActionPendingIntent(Context context) {
        return getPendingIntentServiceAction(context,
                RecordWorkoutService.newLapIntent(context), REQUEST_CODE_LAP);
    }

    private static PendingIntent getPendingIntentServiceAction(Context context, Intent startIntent,
                                                               int requestCode) {
        return PendingIntent.getService(context, requestCode, startIntent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
    }

    public static Notification buildPausedNotification(Context context, ActivityType activityType,
        WorkoutHeader followWorkout, WorkoutHeader ghostTarget, Route followRoute) {
        PendingIntent pendingIntent =
            buildPendingIntent(context, activityType, followWorkout, ghostTarget, followRoute);
        NotificationCompat.Builder builder =
            buildBaseActivityRecordingNotification(context, pendingIntent, context.getString(com.stt.android.core.R.string.paused), 0);
        builder.addAction(0, context.getString(R.string.resume),
            getResumeActionPendingIntent(context));
        return builder.build();
    }

    private static PendingIntent getResumeActionPendingIntent(Context context) {
        return getPendingIntentServiceAction(context,
                RecordWorkoutService.newResumeRecordingIntent(context), REQUEST_CODE_RESUME);
    }

    public static Notification buildAutoPausedNotification(Context context,
        ActivityType activityType, WorkoutHeader followWorkout, WorkoutHeader ghostTarget,
        Route followRoute) {
        PendingIntent pendingIntent =
            buildPendingIntent(context, activityType, followWorkout, ghostTarget, followRoute);
        Resources resources = context.getResources();
        return buildBaseActivityRecordingNotification(context, pendingIntent,
            resources.getString(com.stt.android.core.R.string.auto_paused),
            resources.getColor(com.stt.android.core.R.color.auto_paused)).build();
    }
}
