package com.stt.android.workouts.tts;

import android.content.Context;

import com.stt.android.domain.user.LapSettingHelper;
import com.stt.android.domain.user.MeasurementUnit;
import com.stt.android.domain.user.VoiceFeedbackSettings;
import com.stt.android.domain.user.VoiceFeedbackSettingsHelper;
import com.stt.android.domain.workout.ActivityType;
import com.stt.android.laps.CompleteLap;
import com.stt.android.domain.workout.GhostDistanceTimeState;
import com.stt.android.laps.Laps;
import com.stt.android.domain.workout.SpeedPaceState;

import java.util.List;

// To be fair, most TTS engine uses a female voice.
public class Spokeswoman {
    private Context context;
    private int activityTypeId = -1;
    private WorkoutTextToSpeech workoutTextToSpeech;
    private VoiceFeedbackSettings voiceFeedbackSettings;

    public Spokeswoman() {
        // do nothing
    }

    public void setActivityType(ActivityType activityType) {
        activityTypeId = activityType.getId();
    }

    public void start(Context context, String language) throws IllegalArgumentException {
        if (workoutTextToSpeech == null) {
            this.context = context;
            workoutTextToSpeech = new WorkoutTextToSpeech(context, language);
            voiceFeedbackSettings = VoiceFeedbackSettingsHelper.getVoiceFeedbackSettings(context, activityTypeId);
        }
    }

    public void shutdown() {
        if (workoutTextToSpeech != null) {
            workoutTextToSpeech.shutdown();
            workoutTextToSpeech = null;
        }
    }

    public void sayStart() {
        if (voiceFeedbackEnabled()) {
            workoutTextToSpeech.sayStart();
        }
    }

    private boolean voiceFeedbackEnabled() {
        return workoutTextToSpeech != null
                && voiceFeedbackSettings != null && voiceFeedbackSettings.enabled;
    }

    public void sayAutoPause() {
        if (voiceFeedbackEnabled() && voiceFeedbackSettings.autoPauseEnabled) {
            workoutTextToSpeech.sayAutoPause();
        }
    }

    public void sayAutoResume() {
        if (voiceFeedbackEnabled() && voiceFeedbackSettings.autoPauseEnabled) {
            workoutTextToSpeech.sayAutoResume();
        }
    }

    public void sayResume() {
        if (voiceFeedbackEnabled()) {
            workoutTextToSpeech.sayResume();
        }
    }

    public void sayStop() {
        if (voiceFeedbackEnabled()) {
            workoutTextToSpeech.sayStop();
        }
    }

    public void sayLap(MeasurementUnit measurementUnit, CompleteLap lap, double currentSpeed,
                       double averageSpeed, SpeedPaceState speedPaceState,
                       double energy, int currentHeartRate, int averageHeartRate,
                       int currentCadence, int averageCadence,
                       GhostDistanceTimeState ghostDistanceTimeState,
                       double ghostDifference) {
        if (!voiceFeedbackEnabled()) {
            return;
        }

        if (voiceFeedbackSettings.ghost.perLap && ghostDistanceTimeState != null) {
            workoutTextToSpeech.sayGhostDifference(
                    ghostDistanceTimeState, ghostDifference, measurementUnit);
        }

        if (voiceFeedbackSettings.distance.perLap) {
            double totalDistance = lap.getWorkoutDistanceOnEnd();
            workoutTextToSpeech.sayTotalDistance(totalDistance, measurementUnit);
        }

        if (voiceFeedbackSettings.duration.perLap) {
            double totalTime = lap.getWorkoutDurationOnEnd() / 1000.0;
            workoutTextToSpeech.sayTotalTime(totalTime);
        }

        if (voiceFeedbackSettings.lapTimeEnabled) {
            double lapTime = lap.getDuration() / 1000.0;
            workoutTextToSpeech.sayLapTime(lapTime);
        }

        if (voiceFeedbackSettings.lapSpeedPaceEnabled) {
            double lapSpeed = lap.getAverageSpeed();
            switch (speedPaceState) {
                case SPEED:
                    workoutTextToSpeech.sayLapSpeed(lapSpeed, measurementUnit);
                    break;
                case PACE:
                    workoutTextToSpeech.sayLapPace(lapSpeed, measurementUnit);
                    break;
                default:
                    throw new IllegalArgumentException("Invalid speed/pace state: " + speedPaceState);
            }
        }

        if (voiceFeedbackSettings.energy.perLap) {
            workoutTextToSpeech.sayEnergy(energy);
        }

        if (voiceFeedbackSettings.currentSpeedPace.perLap) {
            switch (speedPaceState) {
                case SPEED:
                    workoutTextToSpeech.sayCurrentSpeed(currentSpeed, measurementUnit);
                    break;
                case PACE:
                    workoutTextToSpeech.sayCurrentPace(currentSpeed, measurementUnit);
                    break;
                default:
                    throw new IllegalArgumentException("Invalid speed/pace state: " + speedPaceState);
            }
        }

        if (voiceFeedbackSettings.averageSpeedPace.perLap) {
            switch (speedPaceState) {
                case SPEED:
                    workoutTextToSpeech.sayAverageSpeed(averageSpeed, measurementUnit);
                    break;
                case PACE:
                    workoutTextToSpeech.sayAveragePace(averageSpeed, measurementUnit);
                    break;
                default:
                    throw new IllegalArgumentException("Invalid speed/pace state: " + speedPaceState);
            }
        }

        if (voiceFeedbackSettings.currentHeartRate.perLap && currentHeartRate > 0) {
            workoutTextToSpeech.sayCurrentHeartRate(currentHeartRate);
        }

        if (voiceFeedbackSettings.averageHeartRate.perLap && averageHeartRate > 0) {
            workoutTextToSpeech.sayAverageHeartRate(averageHeartRate);
        }

        if (voiceFeedbackSettings.currentCadence.perLap && currentCadence > 0) {
            workoutTextToSpeech.sayCurrentCadence(currentCadence);
        }

        if (voiceFeedbackSettings.averageCadence.perLap && averageCadence > 0) {
            workoutTextToSpeech.sayAverageCadence(averageCadence);
        }
    }

    public void sayCurrentStatus(MeasurementUnit measurementUnit, List<CompleteLap> completedLaps,
                                 double previousDistance, double currentDistance,
                                 double previousDuration, double currentDuration,
                                 double currentSpeed, double averageSpeed,
                                 SpeedPaceState speedPaceState, double energy,
                                 int currentHeartRate, int averageHeartRate, int currentCadence,
                                 int averageCadence, GhostDistanceTimeState ghostDistanceTimeState,
                                 double ghostDifference) {
        if (!voiceFeedbackEnabled()) {
            return;
        }

        if (completedLaps != null && completedLaps.size() > 0) {
            Laps.Type currentSelectedLapType = VoiceFeedbackSettingsHelper.getVoiceFeedbackSettings
                (context, activityTypeId).lapType;
            for (CompleteLap completedLap : completedLaps) {
                Laps.Type lapType = completedLap.getLapType();
                MeasurementUnit lapUnit = completedLap.getLapUnit();
                if (lapUnit.equals(measurementUnit) && lapType.equals(currentSelectedLapType)) {
                    sayLap(measurementUnit, completedLap, currentSpeed, averageSpeed, speedPaceState,
                            energy, currentHeartRate, averageHeartRate, currentCadence, averageCadence,
                            ghostDistanceTimeState, ghostDifference);
                    break;
                }
            }
        }

        if (ghostDistanceTimeState != null && shouldSayContent(voiceFeedbackSettings.ghost,
                previousDistance, currentDistance, previousDuration, currentDuration)) {
            workoutTextToSpeech.sayGhostDifference(ghostDistanceTimeState, ghostDifference,
                    measurementUnit);
        }

        if (shouldSayContent(voiceFeedbackSettings.distance,
                previousDistance, currentDistance, previousDuration, currentDuration)) {
            workoutTextToSpeech.sayTotalDistance(currentDistance, measurementUnit);
        }

        if (shouldSayContent(voiceFeedbackSettings.duration,
                previousDistance, currentDistance, previousDuration, currentDuration)) {
            workoutTextToSpeech.sayTotalTime(currentDuration);
        }

        if (shouldSayContent(voiceFeedbackSettings.energy,
                previousDistance, currentDistance, previousDuration, currentDuration)) {
            workoutTextToSpeech.sayEnergy(energy);
        }

        if (shouldSayContent(voiceFeedbackSettings.currentSpeedPace,
                previousDistance, currentDistance, previousDuration, currentDuration)) {
            switch (speedPaceState) {
                case SPEED:
                    workoutTextToSpeech.sayCurrentSpeed(currentSpeed, measurementUnit);
                    break;
                case PACE:
                    workoutTextToSpeech.sayCurrentPace(currentSpeed, measurementUnit);
                    break;
                default:
                    throw new IllegalArgumentException("Invalid speed/pace state: " + speedPaceState);
            }
        }

        if (shouldSayContent(voiceFeedbackSettings.averageSpeedPace,
                previousDistance, currentDistance, previousDuration, currentDuration)) {
            switch (speedPaceState) {
                case SPEED:
                    workoutTextToSpeech.sayAverageSpeed(averageSpeed, measurementUnit);
                    break;
                case PACE:
                    workoutTextToSpeech.sayAveragePace(averageSpeed, measurementUnit);
                    break;
                default:
                    throw new IllegalArgumentException("Invalid speed/pace state: " + speedPaceState);
            }
        }

        if (currentHeartRate > 0 && shouldSayContent(voiceFeedbackSettings.currentHeartRate,
                previousDistance, currentDistance, previousDuration, currentDuration)) {
            workoutTextToSpeech.sayCurrentHeartRate(currentHeartRate);
        }
        if (averageHeartRate > 0 && shouldSayContent(voiceFeedbackSettings.averageHeartRate,
                previousDistance, currentDistance, previousDuration, currentDuration)) {
            workoutTextToSpeech.sayAverageHeartRate(averageHeartRate);
        }

        if (currentCadence > 0 && shouldSayContent(voiceFeedbackSettings.currentCadence,
                previousDistance, currentDistance, previousDuration, currentDuration)) {
            workoutTextToSpeech.sayCurrentCadence(currentCadence);
        }
        if (averageCadence > 0 && shouldSayContent(voiceFeedbackSettings.averageCadence,
                previousDistance, currentDistance, previousDuration, currentDuration)) {
            workoutTextToSpeech.sayAverageCadence(averageCadence);
        }
    }

    private static boolean shouldSayContent(VoiceFeedbackSettings.Frequency frequency,
                                            double previousDistance, double currentDistance,
                                            double previousDuration, double currentDuration) {
        // to check if we need to say for distance or duration, we check e.g.
        // suppose the frequency is 3km
        // if the previous distance is 2.4, and current distance is 2.5, do nothing
        // in this case, previous distance / frequency == current distance / frequency
        // if the previous distance is 2.9, and current distance is 3, we say it
        // in this case, previous distance / frequency < current distance / frequency
        if (frequency.distance > 0 && (int) previousDistance / frequency.distance
                < (int) currentDistance / frequency.distance) {
            return true;
        }
        if (frequency.duration > 0 && (int) previousDuration / frequency.duration
                < (int) currentDuration / frequency.duration) {
            return true;
        }
        return false;
    }
}
