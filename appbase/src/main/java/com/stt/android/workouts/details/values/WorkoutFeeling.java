package com.stt.android.workouts.details.values;

import androidx.annotation.DrawableRes;
import androidx.annotation.IntRange;
import com.stt.android.R;

/**
 * The Workout feeling coming from Suunto Watches. There is already the same enum in
 * SuuntoLogbookFeeling in connectivity library but we can't access that in ST flavor.
 */
public enum WorkoutFeeling {
    UNDEFINED(0, 0),
    POOR(1, R.drawable.ic_feeling_poor),
    AVERAGE(2, R.drawable.ic_feeling_average),
    GOOD(3, R.drawable.ic_feeling_good),
    VERY_GOOD(4, R.drawable.ic_feeling_very_good),
    EXCELLENT(5, R.drawable.ic_feeling_excellent);

    public int getResId() {
        return resId;
    }

    public int getValue() {
        return value;
    }

    @DrawableRes
    private final int resId;

    private final int value;

    WorkoutFeeling(int value, int resId) {
        this.resId = resId;
        this.value = value;
    }

    public static WorkoutFeeling getFeelingByValue(@IntRange(from = 0, to = 5) int value) {
        for (WorkoutFeeling workoutFeeling : values()) {
            if (workoutFeeling.value == value) {
                return workoutFeeling;
            }
        }
        return UNDEFINED;
    }
}
