package com.stt.android.workouts.reaction

import androidx.work.WorkManager
import com.stt.android.common.coroutines.ORMLITE
import com.stt.android.controllers.ReactionModel
import com.stt.android.data.reactions.ReactionRemoteSyncJob
import com.stt.android.domain.workouts.reactions.Reaction
import com.stt.android.domain.workouts.reactions.ReactionDataSource
import dagger.Lazy
import kotlinx.coroutines.withContext
import javax.inject.Inject
import com.stt.android.domain.user.Reaction as OldReaction

class ReactionOrmLiteDataSource
@Inject constructor(
    private val reationModel: ReactionModel,
    private val workManager: Lazy<WorkManager>
) : ReactionDataSource {
    override suspend fun findUnsyncedNotDeletedReactionsByUserName(
        username: String
    ): List<Reaction> = withContext(ORMLITE) {
        reationModel.findUnsyncedNotDeletedReactionsByUserName(username)
            .map { it.toDomainEntity() }
    }

    override suspend fun findDeletedReactions(username: String): List<Reaction> = withContext(ORMLITE) {
        reationModel.findDeletedReactions(username)
            .map { it.toDomainEntity() }
    }

    override suspend fun storeReactions(reactions: List<Reaction>) = withContext(ORMLITE) {
        reationModel.storeReactions(reactions.map { it.toOldDomainEntity() })
    }

    override suspend fun removeDuplicateReactions(reactions: List<Reaction>) = withContext(ORMLITE) {
        reationModel.removeDuplicates(reactions.map { it.toOldDomainEntity() })
    }

    override suspend fun removeReactions(reactions: List<Reaction>) = withContext(ORMLITE) {
        reationModel.remove(reactions.map { it.toOldDomainEntity() })
    }

    override fun syncReactions() {
        ReactionRemoteSyncJob.enqueue(workManager.get())
    }
}

internal fun OldReaction.toDomainEntity(): Reaction {
    return Reaction(
        key = this.key,
        workoutKey = this.workoutKey,
        reaction = this.reaction,
        userName = this.userName,
        userRealOrUsername = this.userRealOrUserName,
        userProfilePictureUrl = userProfilePictureUrl,
        timestamp = this.timestamp
    )
}

internal fun Reaction.toOldDomainEntity(): OldReaction {
    return OldReaction.remote(
        key,
        workoutKey,
        reaction,
        userName,
        userRealOrUsername,
        userProfilePictureUrl,
        timestamp
    )
}
