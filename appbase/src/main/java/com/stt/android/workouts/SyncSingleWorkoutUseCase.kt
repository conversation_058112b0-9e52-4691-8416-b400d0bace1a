package com.stt.android.workouts

import com.stt.android.data.workout.pictures.SyncNewPictures
import com.stt.android.data.workout.sync.SyncManuallyCreatedWorkouts
import com.stt.android.data.workout.sync.SyncTrackedWorkouts
import com.stt.android.data.workout.sync.SyncUpdatedWorkouts
import com.stt.android.data.workout.videos.SyncNewVideos
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.utils.runSuspendCatchingEach
import timber.log.Timber
import javax.inject.Inject

class SyncSingleWorkoutUseCase @Inject constructor(
    private val syncUpdatedWorkouts: SyncUpdatedWorkouts,
    private val syncManuallyCreatedWorkouts: SyncManuallyCreatedWorkouts,
    private val syncTrackedWorkouts: SyncTrackedWorkouts,
    private val syncNewPictures: SyncNewPictures,
    private val syncNewVideos: SyncNewVideos,
) {
    /**
     *  true: sync successfully, otherwise return false
     */
    suspend fun sync(workoutHeader: WorkoutHeader?): Boolean =
        listOf(
            syncUpdatedWorkouts::sync to "Error during workout update sync",
            syncManuallyCreatedWorkouts::sync to "Error during new manual workout sync",
            syncTrackedWorkouts::sync to "Error during new tracked workout sync",
            syncNewPictures::sync to "Error during new pictures sync",
            syncNewVideos::sync to "Error during new videos sync",
        ).runSuspendCatchingEach { (task, errorMessage) -> task(workoutHeader) to errorMessage }
            .onEach {
                val throwable = it.exceptionOrNull()
                if (throwable != null) {
                    val errorMessage = it.getOrNull()?.second
                    Timber.w(
                        throwable,
                        "Failed to push workoutHeader(${workoutHeader?.id}). $errorMessage"
                    )
                }
            }.count { it.isFailure } == 0
}
