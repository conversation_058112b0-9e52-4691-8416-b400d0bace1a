package com.stt.android.workouts.hardware;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.IntentFilter;
import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import com.stt.android.bluetooth.HrEventListener;
import com.stt.android.hr.HeartRateDeviceManager;
import com.stt.android.hr.HeartRateMonitorType;
import com.stt.android.utils.STTConstants;
import java.io.Closeable;
import timber.log.Timber;

public abstract class HeartRateConnectionMonitor implements Closeable {
    @Nullable
    public static HeartRateConnectionMonitor newInstance(Context context,
        BroadcastReceiver receiver, HrEventListener listener) {

        if (!HeartRateDeviceManager.hasPairedDevice(context)) {
            Timber.d("No known heart rate devices");
            return null;
        }

        HeartRateMonitorType type = HeartRateDeviceManager.getPairedDeviceType(context);
        if (type == null) {
            Timber.d("Missing heart rate device type");
            return null;
        }

        try {
            switch (type) {
                case SMART:
                    return BleHeartRateConnectionMonitor.requestUpdates(context, listener);
                case HRM1:
                case HRM2:
                case POLAR:
                    return BluetoothHeartRateConnectionMonitor.requestUpdates(context, receiver);
                default:
                    Timber.d("Unknown heart rate device type: %s", type);
                    return null;
            }
        } catch (IllegalStateException e) {
            Timber.w(e, "Can't connect to HR");
        }
        return null;
    }

    public abstract boolean isConnected();

    @Override
    public abstract void close();

    public static class DummyHeartRateConnectionMonitor extends HeartRateConnectionMonitor {

        public DummyHeartRateConnectionMonitor() {

        }

        DummyHeartRateConnectionMonitor(Context context, BroadcastReceiver receiver) {
            Timber.d("Registering dummy bluetooth HR receiver for location updates.");
            IntentFilter intentFilter =
                new IntentFilter(STTConstants.BroadcastActions.HEART_RATE_UPDATE);
            LocalBroadcastManager.getInstance(context).registerReceiver(receiver, intentFilter);
        }

        @Override
        public boolean isConnected() {
            return true;
        }

        @Override
        public void close() {
            // do nothing
        }
    }
}
