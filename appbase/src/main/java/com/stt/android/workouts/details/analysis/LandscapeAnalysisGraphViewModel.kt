package com.stt.android.workouts.details.analysis

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.DiveExtensionDataModel
import com.stt.android.controllers.SummaryExtensionDataModel
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.sml.FetchSmlUseCase
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.sml.reader.SmlFactory
import com.stt.android.domain.workout.WorkoutData
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.DiveExtension
import com.stt.android.extensions.loadExtension
import com.stt.android.ui.controllers.WorkoutDataLoaderController
import com.stt.android.ui.controllers.loadWorkout
import com.stt.android.utils.awaitFirstNonNull
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class LandscapeAnalysisGraphViewModel @Inject constructor(
    private val diveExtensionDataModel: DiveExtensionDataModel,
    private var fetchSmlUseCase: FetchSmlUseCase,
    private val dataLoaderController: WorkoutDataLoaderController,
    private val coroutinesDispatchers: CoroutinesDispatchers,
    private val summaryExtensionDataModel: SummaryExtensionDataModel,
) : ViewModel() {
    val showProgress = MutableLiveData<Boolean>()

    suspend fun loadWorkoutData(
        workoutHeader: WorkoutHeader,
    ): Pair<WorkoutData, Sml> = withContext(coroutinesDispatchers.computation) {
        withContext(coroutinesDispatchers.main) {
            showProgress.value = true
        }

        runSuspendCatching {
            val workoutDataAsync = async { dataLoaderController.loadWorkout(workoutHeader) }
            val sml = fetchSmlUseCase.fetchSml(workoutHeader.id, workoutHeader.key)
                ?: SmlFactory.EMPTY
            val workoutData = workoutDataAsync.await()

            withContext(coroutinesDispatchers.main) {
                showProgress.value = false
            }

            workoutData to sml
        }.onFailure { e ->
            Timber.w(e, "Error loading workout data")
        }.getOrThrow()
    }

    suspend fun loadSummaryExtensionData(workoutHeader: WorkoutHeader) = withContext(coroutinesDispatchers.computation) {
        runSuspendCatching {
            val summaryExtensionAsync = async(coroutinesDispatchers.io) {
                summaryExtensionDataModel.loadExtension(workoutHeader)
            }
            summaryExtensionAsync.await()
        }.onFailure { e ->
            Timber.w(e, "Error loading summary extension data")
        }.getOrThrow()
    }

    suspend fun loadDiveExtensionData(
        workoutHeader: WorkoutHeader,
    ): Pair<DiveExtension?, Sml> = withContext(coroutinesDispatchers.computation) {
        withContext(coroutinesDispatchers.main) {
            showProgress.value = true
        }

        runSuspendCatching {
            val diveExtensionAsync = async { diveExtensionDataModel.load(workoutHeader).awaitFirstNonNull() }
            val sml = fetchSmlUseCase.fetchSml(workoutHeader.id, workoutHeader.key)
                ?: SmlFactory.EMPTY
            val diveExtension = diveExtensionAsync.await()

            withContext(coroutinesDispatchers.main) {
                showProgress.value = false
            }

            diveExtension to sml
        }.onFailure { e ->
            Timber.w(e, "Error loading dive data")
        }.getOrThrow()
    }
}
