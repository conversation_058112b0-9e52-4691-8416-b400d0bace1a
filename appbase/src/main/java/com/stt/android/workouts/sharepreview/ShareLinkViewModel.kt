package com.stt.android.workouts.sharepreview

import androidx.lifecycle.ViewModel
import com.stt.android.R
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

@HiltViewModel
class ShareLinkViewModel @Inject constructor() : ViewModel() {
    private val _shareLinkConfigFlow = MutableStateFlow(ShareLinkConfig())
    val shareLinkConfigFlow: StateFlow<ShareLinkConfig> = _shareLinkConfigFlow.asStateFlow()

    fun updateConfig(reduce: ShareLinkConfig.() -> ShareLinkConfig) {
        _shareLinkConfigFlow.value = shareLinkConfigFlow.value.reduce()
    }
}

data class ShareLinkConfig(
    val showSummaryLink: Boolean = false,
    val show3DLink: Boolean = false
)
