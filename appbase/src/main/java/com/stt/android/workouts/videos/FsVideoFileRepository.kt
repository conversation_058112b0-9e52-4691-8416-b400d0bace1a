package com.stt.android.workouts.videos

import com.stt.android.data.workout.videos.FileInfo
import com.stt.android.data.workout.videos.VideoFileRepository
import com.stt.android.utils.FileUtils
import com.stt.android.utils.STTConstants
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import javax.inject.Inject

class FsVideoFileRepository
@Inject constructor(
    private val fileUtils: FileUtils
) : VideoFileRepository {
    override suspend fun getVideoFile(videoFilename: String): FileInfo = withContext(Dispatchers.IO) {
        val file = fileUtils.getInternalFilePath(STTConstants.DIRECTORY_VIDEOS, videoFilename)
        FileInfo(
            file = file,
            md5hash = FileUtils.calculateMd5(file.absolutePath)
        )
    }

    override suspend fun getVideoThumbnailFile(thumbnailFilename: String): File = withContext(Dispatchers.IO) {
        fileUtils.getInternalFilePath(STTConstants.DIRECTORY_VIDEOS, thumbnailFilename)
    }
}
