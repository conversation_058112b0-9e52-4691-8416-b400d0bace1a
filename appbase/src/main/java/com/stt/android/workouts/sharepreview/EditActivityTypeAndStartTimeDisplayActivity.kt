package com.stt.android.workouts.sharepreview

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import com.stt.android.R
import com.stt.android.databinding.ActivityEditActivityTypeStartTimeBinding
import com.stt.android.infomodel.SummaryItem
import com.stt.android.ui.utils.ThrottlingOnClickListener
import com.stt.android.workouts.details.values.WorkoutValue

/**
 * Edit  whether to display the type of workout activity type and start time in WorkoutSharePreviewFragment
 */
class EditActivityTypeAndStartTimeDisplayActivity : AppCompatActivity() {
    private lateinit var binding: ActivityEditActivityTypeStartTimeBinding
    private val checkItemState = mutableMapOf(
        CheckType.Nothing to false,
        CheckType.ActivityType to true,
        CheckType.ActivityStartTime to true
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityEditActivityTypeStartTimeBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            title = getString(R.string.sport_info_title)
        }

        onBackPressedDispatcher.addCallback(object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                setResult(
                    RESULT_OK,
                    Intent().apply {
                        putExtra(CHECK_NOTHING, checkItemState[CheckType.Nothing] ?: false)
                        putExtra(CHECK_ACTIVITY_TYPE, checkItemState[CheckType.ActivityType] ?: false)
                        putExtra(
                            CHECK_ACTIVITY_START_TIME,
                            checkItemState[CheckType.ActivityStartTime] ?: false
                        )
                    }
                )
                finish()
            }
        })

        binding.toolbar.setNavigationOnClickListener {
            onBackPressedDispatcher.onBackPressed()
        }
        binding.apply {
            layoutNothing.setOnClickListener(
                ThrottlingOnClickListener {
                    handleCheckImage(CheckType.Nothing)
                }
            )
            layoutSportType.setOnClickListener(
                ThrottlingOnClickListener {
                    handleCheckImage(CheckType.ActivityType)
                }
            )
            layoutSportStartTime.setOnClickListener(
                ThrottlingOnClickListener {
                    handleCheckImage(CheckType.ActivityStartTime)
                }
            )
        }
        initCheckState()
    }

    private fun initCheckState() {
        val workoutValue = intent.getParcelableExtra<WorkoutValue>(WORK_VALUE)
        workoutValue?.apply {
            binding.sportType.text = value
            binding.startTime.text = label
            val checkNothing = item == SummaryItem.NONE
            val checkSportStartTime = showActivityStartTime
            val checkSportType = showActivityType
            checkItemState[CheckType.Nothing] = checkNothing
            checkItemState[CheckType.ActivityType] = checkSportType
            checkItemState[CheckType.ActivityStartTime] = checkSportStartTime

            if (checkNothing) {
                binding.nothingCheck.visibility = View.VISIBLE
                binding.activityStartTimeCheck.visibility = View.INVISIBLE
                binding.activityTypeCheck.visibility = View.INVISIBLE
            } else {
                binding.nothingCheck.visibility = View.INVISIBLE
                binding.activityStartTimeCheck.visibility =
                    if (checkSportStartTime) View.VISIBLE else View.INVISIBLE
                binding.activityTypeCheck.visibility =
                    if (checkSportType) View.VISIBLE else View.INVISIBLE
            }
        }
    }

    private fun handleCheckImage(type: CheckType) {
        checkItemState[type] = checkItemState[type]?.not() ?: false

        val checkedNothing = checkItemState[CheckType.Nothing] == true
        val checkedSportType = checkItemState[CheckType.ActivityType] == true
        val checkedSportStartTime = checkItemState[CheckType.ActivityStartTime] == true
        when (type) {
            CheckType.Nothing -> {
                checkItemState[CheckType.ActivityType] = !checkedNothing
                checkItemState[CheckType.ActivityStartTime] = !checkedNothing
            }
            CheckType.ActivityType, CheckType.ActivityStartTime -> {
                checkItemState[CheckType.Nothing] = !checkedSportType && !checkedSportStartTime
            }
        }
        onBackPressedDispatcher.onBackPressed()
    }

    enum class CheckType {
        Nothing, ActivityType, ActivityStartTime
    }

    companion object {
        const val CHECK_NOTHING = "check_nothing"
        const val CHECK_ACTIVITY_TYPE = "check_activityType"
        const val CHECK_ACTIVITY_START_TIME = "check_activityStartTime"
        const val WORK_VALUE = "work_value"
        const val REQUEST_CODE = 1022
    }
}
