package com.stt.android.workouts.sharepreview

import android.content.Context
import android.net.Uri
import androidx.core.net.toUri
import com.stt.android.domain.user.ImageInformation

class DefaultImageInformation(
    private val imageUrl: String,
    with: Int = DEFAULT_IMAGE_WIDTH,
    height: Int = DEFAULT_IMAGE_HEIGHT
) : ImageInformation(
    null,
    0,
    .0,
    "",
    "",
    "",
    with,
    height
) {

    override fun getFeedUri(context: Context): Uri = imageUrl.toUri()

    override fun getHighResUri(context: Context): Uri = imageUrl.toUri()

    companion object {
        // These should match the image asset resolution
        private const val DEFAULT_IMAGE_WIDTH = 1125
        private const val DEFAULT_IMAGE_HEIGHT = 1125
    }
}
