package com.stt.android.workouts;

import android.annotation.SuppressLint;
import android.content.Context;
import android.location.Location;
import android.os.Looper;
import androidx.annotation.NonNull;
import com.google.android.gms.common.ConnectionResult;
import com.google.android.gms.common.GoogleApiAvailability;
import com.google.android.gms.location.FusedLocationProviderClient;
import com.google.android.gms.location.LocationCallback;
import com.google.android.gms.location.LocationRequest;
import com.google.android.gms.location.LocationResult;
import com.google.android.gms.location.LocationServices;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.stt.android.FeatureFlags;
import com.stt.android.STTApplication;
import com.stt.android.location.LocationModel;
import com.stt.android.location.LocationUpdateRequest;
import java.io.Closeable;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import javax.inject.Inject;

import com.stt.android.remote.remoteconfig.STFusedLocationParameters;
import timber.log.Timber;

/**
 * Helper class responsible to start and stop listening for Locations {@link LocationModel}.
 * At the same in order to avoid issues with not receiving locations due to device entering power
 * save mode (e.g. Doze) it registers itself for fused locations using Google Play's
 * {@link FusedLocationProviderClient}. The location provided by the fused location provider is
 * ignored.
 */
public class LocationConnection implements Closeable {
    private final LocationModel.Listener listener;

    private FusedLocationProviderClient fusedLocationProviderClient;

    /**
     * Callback that just logs the received list of locations nothing else.
     *
     * The purpose of having this dummy fused location callback is to avoid Doze mode in our app
     * by using the fused location provider from Google Play Services in parallel to the normal
     * LocationModel
     */
    private static class DummyLocationCallback extends LocationCallback {
        @Override
        public void onLocationResult(@NonNull LocationResult locationResult) {
            for (Location location : locationResult.getLocations()) {
                Timber.d("Got fused location: %s with provider: %s", location,
                    location.getProvider());
            }
        }
    }
    private final LocationCallback logFusedLocationCallback = new DummyLocationCallback();

    private final AtomicBoolean fusedLocationUpdates = new AtomicBoolean(false);

    @Inject
    LocationModel locationModel;
    @Inject
    Context context;
    @Inject
    FeatureFlags featureFlags;

    LocationConnection(LocationModel.Listener listener, long intervalInMilliSeconds,
        float smallestDistanceInMeters) {
        STTApplication.getComponent().inject(this);
        this.listener = listener;

        String msg = "Starting location updates.";
        Timber.d(msg);
        locationModel.addListener(listener);
        try {
            locationModel.requestLocationUpdate(LocationUpdateRequest.builder()
                .intervalInMilliSeconds(intervalInMilliSeconds)
                .smallestDistanceInMeters(smallestDistanceInMeters)
                .build());
            startFusedLocation();
        } catch (SecurityException | IllegalArgumentException e) {
            String message = "Unable to start location provider!";
            FirebaseCrashlytics.getInstance().log(message);
            Timber.e(e, message);
        }
    }

    @SuppressLint("MissingPermission")
    private void startFusedLocation() {
        try {
            String message;
            if (isGooglePlayAvailable()) {
                STFusedLocationParameters params = featureFlags.getSTFusedLocationParameters();
                if (params.getEnabled()) {
                    LocationRequest locationRequest = LocationRequest.create();
                    long interval = isValidTimeParam(params.getIntervalMs()) ?
                        params.getIntervalMs() : TimeUnit.SECONDS.toMillis(1);
                    locationRequest.setInterval(interval);
                    // default fastest interval is 1/6 of the interval
                    long fastestInterval = isValidTimeParam(params.getFastestIntervalMs()) ?
                        params.getFastestIntervalMs() : (interval / 6);
                    locationRequest.setFastestInterval(fastestInterval);
                    // default maxWaitTime is 0
                    long maxWaitTime = isValidTimeParam(params.getMaxWaitTime()) ?
                        params.getMaxWaitTime() : 0L;
                    locationRequest.setMaxWaitTime(maxWaitTime);
                    int priority = isValidPriority(params.getPriority()) ?
                        params.getPriority() : LocationRequest.PRIORITY_LOW_POWER;
                    locationRequest.setPriority(priority);

                    fusedLocationProviderClient =
                        LocationServices.getFusedLocationProviderClient(context);
                    fusedLocationProviderClient.requestLocationUpdates(locationRequest,
                        logFusedLocationCallback, Looper.myLooper());
                    fusedLocationUpdates.set(true);
                    message = "Fused location with logging callback started";
                } else {
                    message = "Fused location not started because it is disabled by remote config";
                }
            } else {
                message = "Fused location not started because play services are not available";
            }
            Timber.d(message);
        } catch (Throwable e) {
            // Just log any error. We don't want to block normal functionality
            FirebaseCrashlytics.getInstance().log("Unable to start fused location. [" + e.getMessage() + "]");
            Timber.w(e, "Unable to start fused location");
        }
    }

    private boolean isValidPriority(Integer priority) {
        return priority != null && (
            priority == LocationRequest.PRIORITY_LOW_POWER ||
                priority == LocationRequest.PRIORITY_BALANCED_POWER_ACCURACY ||
                priority == LocationRequest.PRIORITY_HIGH_ACCURACY ||
                priority == LocationRequest.PRIORITY_NO_POWER);
    }

    private boolean isValidTimeParam(Long timeParam) {
        return timeParam != null && timeParam > 0;
    }

    @Override
    public void close() {
        String message = "Stopping location updates.";
        Timber.d(message);
        try {
            locationModel.removeListener(listener);
            locationModel.stopLocationUpdate();
            stopFusedLocation();
        } catch (Throwable e) {
            Timber.e(e, "Failed to stop location updates");
        }
    }

    private void stopFusedLocation() {
        try {
            if (isGooglePlayAvailable() && fusedLocationUpdates.getAndSet(false)) {
                fusedLocationProviderClient.removeLocationUpdates(logFusedLocationCallback);
                Timber.d("Fused location with logging callback stopped");
            }
        } catch (Throwable e) {
            // Just log any error. We don't want to block normal functionality
            FirebaseCrashlytics.getInstance().log("Unable to stop fused location. [" + e.getMessage() + "]");
            Timber.w(e, "Unable to stop fused location");
        }
    }

    private boolean isGooglePlayAvailable() {
        try {
            GoogleApiAvailability apiAvailability = GoogleApiAvailability.getInstance();
            int googlePlayServicesAvailable =
                apiAvailability.isGooglePlayServicesAvailable(context);
            switch (googlePlayServicesAvailable) {
                case ConnectionResult.SUCCESS:
                    String msg = "Google Play Services is available";
                    Timber.d(msg);
                    return true;
                default:
                    String message = "Google play services is not available. Reason [" +
                        googlePlayServicesAvailable + "]";
                    Timber.w(message);
                    return false;
            }
        } catch (Throwable e) {
            // Just log any error. We don't want to block normal functionality
            FirebaseCrashlytics.getInstance().log("Unable to check google play availability. [" + e.getMessage() + "]");
            Timber.w(e, "Unable to check google play availability");
        }
        return false;
    }
}
