package com.stt.android.workouts.tts;

import android.speech.tts.TextToSpeech;

import java.util.Locale;
import java.util.Set;

import timber.log.Timber;

public class TextToSpeechHelper {
    /**
     * Checks if the specified language is supported. If not, it checks if English is supported.
     *
     * @return TextToSpeech.LANG_AVAILABLE if the specific language or English is supported,
     * TextToSpeech.LANG_MISSING_DATA if the user needs to download the language data,
     * TextToSpeech.LANG_NOT_SUPPORTED if neither the specific language nor English is supported.
     */
    public static int isLanguageSupported(TextToSpeech tts, String language, boolean checksDefaultLanguage) {
        Locale locale = new Locale(language);
        int isLanguageAvailable = tts.isLanguageAvailable(locale);
        // In ICS and above engines can have extra features and use the network to synthesize text. We don't want to
        // use the network so let's make sure the TTS allows offline mode
        boolean supportOfflineMode = supportsOfflineMode(tts, language, locale);
        Timber.d("TTS language %s available? %d Offline support? %s", language, isLanguageAvailable, supportOfflineMode);
        if (supportOfflineMode && isLanguageAvailable == TextToSpeech.LANG_MISSING_DATA) {
            Timber.w("Missing TTS data for language %s", language);
            // Ask the user to install the language package if offline is supported but the language data is missing
            return TextToSpeech.LANG_MISSING_DATA;
        } else if (!supportOfflineMode || isLanguageAvailable == TextToSpeech.LANG_NOT_SUPPORTED) {
            if (checksDefaultLanguage) {
                // If the local language is not supported or there's no offline mode then check for english support
                Timber.w("TTS language %s not supported. Checking for English", language);
                return supportsEnglish(tts);
            } else {
                return TextToSpeech.LANG_NOT_SUPPORTED;
            }
        } else {
            return TextToSpeech.LANG_AVAILABLE;
        }
    }

    private static boolean supportsOfflineMode(TextToSpeech tts, String localTtsLanguage, Locale localLocale) {
        // By default assume they do support offline mode
        boolean offlineSupport = true;
        Set<String> features = tts.getFeatures(localLocale);
        Timber.d("TTS engine features for language %s: %s", localTtsLanguage, features);
        // Some old engines do not provide any result. We assume that those support embedded mode
        if (features != null && !features.isEmpty()) {
            // On the other hand if it has features we want embedded support (Google engine
            // supports some
            // languages only through network (e.g. Finnish, Catalan...))
            offlineSupport = features.contains(TextToSpeech.Engine.KEY_FEATURE_EMBEDDED_SYNTHESIS);
        }
        return offlineSupport;
    }

    private static int supportsEnglish(TextToSpeech tts) {
        int isEnglishAvailable = tts.isLanguageAvailable(Locale.ENGLISH);
        Timber.d("English availability %d", isEnglishAvailable);
        if (isEnglishAvailable == TextToSpeech.LANG_MISSING_DATA) {
            // If there's no English support then ask user to install TTS support
            Timber.w("English text to speech is not available");
            return TextToSpeech.LANG_MISSING_DATA;
        } else if (isEnglishAvailable == TextToSpeech.LANG_NOT_SUPPORTED) {
            Timber.e("English tts not supported!");
            return TextToSpeech.LANG_NOT_SUPPORTED;
        } else {
            return TextToSpeech.LANG_AVAILABLE;
        }
    }
}
