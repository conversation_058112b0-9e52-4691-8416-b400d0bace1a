package com.stt.android.workouts.sharepreview

import com.stt.android.domain.user.ImageInformation
import com.stt.android.domain.workout.ActivityType
import com.stt.android.utils.ImageSizeUtils

class DefaultImageResInformation(
    val defaultImageResId: Int,
    resWidth: Int = DEFAULT_IMAGE_WIDTH,
    resHeight: Int = DEFAULT_IMAGE_HEIGHT
) : ImageInformation(
    null,
    0,
    .0,
    "",
    "",
    "",
    resWidth,
    resHeight
) {

    companion object {
        // These should match the image asset resolution
        // activity placeholder image
        private const val DEFAULT_IMAGE_WIDTH = 1125
        private const val DEFAULT_IMAGE_HEIGHT = 1125

        fun getDefaultImage(activityType: ActivityType): DefaultImageResInformation? {
            val imageId = activityType.placeholderImageId
            val imageSize = ImageSizeUtils.getImageSize(imageId, null)

            return DefaultImageResInformation(
                defaultImageResId = imageId,
                resWidth = if (imageSize.width <= 0) DEFAULT_IMAGE_WIDTH else imageSize.width,
                resHeight = if (imageSize.height <= 0) DEFAULT_IMAGE_HEIGHT else imageSize.height
            )
        }
    }
}
