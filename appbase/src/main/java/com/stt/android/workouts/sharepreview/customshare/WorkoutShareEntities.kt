package com.stt.android.workouts.sharepreview.customshare

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.stt.android.R

data class WorkoutShareLinkTargets(
    val list: List<TargetEpoxyContainer>
)

data class TargetEpoxyContainer(
    val id: String,
    val target: ShareTarget,
    val onSelect: (ShareTarget) -> Unit
)

sealed class ShareTarget {
    data class CustomTarget(
        val appId: String,
        val targetId: String,
        @DrawableRes val iconRes: Int,
        @StringRes val nameRes: Int
    ) : ShareTarget()

    object SaveToMedia : ShareTarget()
    object DelegateToOS : ShareTarget()
}

fun ShareTarget.getIconRes(): Int {
    return when (this) {
        is ShareTarget.CustomTarget -> iconRes
        ShareTarget.SaveToMedia -> R.drawable.share_save
        ShareTarget.DelegateToOS -> R.drawable.share_other
    }
}

fun ShareTarget.getNameRes(): Int {
    return when (this) {
        is ShareTarget.CustomTarget -> nameRes
        ShareTarget.SaveToMedia -> R.string.save
        ShareTarget.DelegateToOS -> R.string.settings_other
    }
}
