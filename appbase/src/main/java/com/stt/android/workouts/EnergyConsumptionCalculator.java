package com.stt.android.workouts;

import com.stt.android.domain.user.Sex;
import com.stt.android.domain.workout.ActivityType;

import static com.stt.android.domain.user.MeasurementUnitKt.METERS_PER_SECOND_TO_KILOMETERS_PER_HOUR;

public class EnergyConsumptionCalculator {
    private static final double MILLISECONDS_TO_MINUTES = 1.0 / (60.0 * 1000.0);
    private static final double MILLISECONDS_TO_HOURS = 1.0 / (60.0 * 60.0 * 1000.0);
    private static final double KJ_TO_KCAL = 1.0 / 4.1868;

    private final ActivityType activityType;
    private final Sex sex;
    private final int weight;
    private final int age;

    private long lastCalculatedTimestamp;

    EnergyConsumptionCalculator(ActivityType activityType, Sex sex, int weight, int age) {
        this.activityType = activityType;
        this.sex = sex;
        this.weight = weight;
        this.age = age;
        lastCalculatedTimestamp = -1L;
    }

    void reset() {
        lastCalculatedTimestamp = -1L;
    }

    double update(int currentHeartRate, double currentSpeed) {
        if (lastCalculatedTimestamp <= 0L) {
            lastCalculatedTimestamp = System.currentTimeMillis();
            return 0.0;
        }

        long now = System.currentTimeMillis();
        long elapsedMilliseconds = now - lastCalculatedTimestamp;
        lastCalculatedTimestamp = now;

        if (currentHeartRate > 50 && sex != null && weight > 0 && age > 0) {
            return updateBasedOnHeartRate(currentHeartRate, elapsedMilliseconds);
        }
        return updateBasedOnSpeed(currentSpeed, elapsedMilliseconds);
    }

    private double updateBasedOnHeartRate(int currentHeartRate, long elapsedMilliseconds) {
        return updateBasedOnHeartRate(currentHeartRate, elapsedMilliseconds, sex, weight, age);
    }

    public static double updateBasedOnHeartRate(int currentHeartRate, long elapsedMilliseconds,
        Sex sex, int weight, int age) {
        double energyKJPerMinute = 0;
        switch (sex) {
            case MALE:
                energyKJPerMinute =
                    -55.0969 + 0.6309 * currentHeartRate + 0.1988 * weight + 0.2017 * age;
                break;
            case FEMALE:
                energyKJPerMinute =
                    -20.4022 + 0.4472 * currentHeartRate - 0.1263 * weight + 0.074 * age;
                break;
        }
        return energyKJPerMinute * (elapsedMilliseconds * MILLISECONDS_TO_MINUTES) * KJ_TO_KCAL;
    }

    private double updateBasedOnSpeed(double currentSpeed, long elapsedMilliseconds) {
        return updateBasedOnSpeed(activityType, weight, currentSpeed, elapsedMilliseconds);
    }

    public static double updateBasedOnSpeed(ActivityType activityType, int weight,
        double currentSpeed, long elapsedMilliseconds) {
        return weight * metsForSpeed(activityType, currentSpeed) * (elapsedMilliseconds
            * MILLISECONDS_TO_HOURS);
    }

    private static double metsForSpeed(ActivityType activityType, double speed) {
        double kph = speed * METERS_PER_SECOND_TO_KILOMETERS_PER_HOUR;
        if (activityType.equals(ActivityType.RUNNING)
            || activityType.equals(ActivityType.WALKING)
            || activityType.equals(ActivityType.TRAIL_RUNNING)) {
            return runningWalkingMet(kph);
        } else if (activityType.equals(ActivityType.CYCLING) ||
            activityType.equals(ActivityType.GRAVEL_CYCLING)) {
            return cyclingMet(kph);
        } else if (activityType.equals(ActivityType.CROSS_COUNTRY_SKIING)) {
            return crossCountryMet(kph);
        } else if (activityType.equals(ActivityType.ROLLER_SKATING)) {
            return rollerSkatingMet(kph);
        } else if (activityType.equals(ActivityType.NORDIC_WALKING)) {
            return nordicWalkingMet(kph);
        } else if (activityType.equals(ActivityType.MOUNTAIN_BIKING)) {
            return 7;
        } else if (activityType.equals(ActivityType.HIKING)) {
            return 7;
        } else if (activityType.equals(ActivityType.DOWNHILL_SKIING)) {
            return 6;
        } else if (activityType.equals(ActivityType.PADDLING)) {
            return 5;
        } else if (activityType.equals(ActivityType.ROWING)) {
            return 5;
        } else if (activityType.equals(ActivityType.GOLF)) {
            return 4;
        } else if (activityType.equals(ActivityType.INDOOR)) {
            return 5.0;
        } else if (activityType.equals(ActivityType.SWIMMING)) {
            return 8.0;
        } else if (activityType.equals(ActivityType.BALLGAMES)) {
            return 6.0;
        } else if (activityType.equals(ActivityType.HORSEBACK_RIDING)) {
            return 5.0;
        } else if (activityType.equals(ActivityType.MOTOR_SPORTS)) {
            return 5.0;
        } else if (activityType.equals(ActivityType.SKATEBOARDING)) {
            return 5;
        } else if (activityType.equals(ActivityType.WATER_SPORTS)) {
            return 4.0;
        } else if (activityType.equals(ActivityType.CLIMBING)) {
            return 5.0;
        } else if (activityType.equals(ActivityType.SNOWBOARDING)) {
            return 6.0;
        } else if (activityType.equals(ActivityType.SKI_TOURING)) {
            return 8.0;
        } else if (activityType.equals(ActivityType.SOCCER)) {
            return 8.0;
        } else if (activityType.equals(ActivityType.GYM)) {
            return 8.0;
        } else if (activityType.equals(ActivityType.FITNESS_CLASS)) {
            return 6.0;
        } else if (activityType.isOther() || activityType.isUnknown()) {
            return 5.0;
        } else {
            return 5.0;
        }
    }

    private static double rollerSkatingMet(double kph) {
        kph = Math.min(kph, 40);
        if (kph > 8) {
            return 0.5873 * kph - 2.2529;
        } else {
            return 2.4455;
        }
    }

    private static double crossCountryMet(double kph) {
        kph = Math.min(kph, 30.0);
        return 0.6841 * kph + 3.3704;
    }

    private static double cyclingMet(double kph) {
        kph = Math.min(kph, 50.0);
        return 0.0117 * Math.pow(kph, 2) - 0.0359 * kph + 4.5587;
    }

    private static double runningWalkingMet(double kph) {
        kph = Math.min(kph, 25.0);
        if (kph > 8.5949) {
            return 1.0177 * kph + 0.0598;
        }
        if (kph > 5.7259 && kph <= 8.5949) {
            return 1.7274 * kph - 6.04;
        }
        if (kph > 2 && kph <= 5.7259) {
            return 0.5468 * kph + 0.72;
        }
        return 1.8136;
    }

    private static double nordicWalkingMet(double kph) {
        return 1.2 * runningWalkingMet(kph);
    }
}
