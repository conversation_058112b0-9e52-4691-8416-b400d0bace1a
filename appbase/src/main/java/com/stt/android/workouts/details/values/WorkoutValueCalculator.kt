package com.stt.android.workouts.details.values

import com.stt.android.domain.advancedlaps.WindowType
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.sml.SmlExtensionStreamPoint
import kotlin.math.max

internal fun Sml.calculateMovingTimeInSeconds(): Double {
    val speed = streamData.speed
        .takeIf { it.size >= 2 }
        ?: return 0.0

    var prev = speed.first()
    var movingTimeInMillis = 0L
    speed.forEach { current ->
        if (current.value > 0.0F) {
            movingTimeInMillis += (current.timestamp - prev.timestamp)
        }
        prev = current
    }
    return movingTimeInMillis / 1000.0
}

internal fun Sml.calculateMovingTimeInSecondsForSwimming(): Double? {
    val windows = summary.windows.filter { it.type == WindowType.POOLLENGTH.type }
    if (windows.isEmpty()) return null
    return windows.sumOf { it.duration?.toDouble() ?: 0.0 }
}

internal fun List<SmlExtensionStreamPoint>.calculatePeakValueInXMillis(windowInMillis: Long): Double {
    if (size < 2) {
        return 0.0
    }
    val randomAccess = if (this is RandomAccess) this else ArrayList(this)

    var peakValue = 0.0F
    var startIndex = 0
    var start = randomAccess[startIndex]
    var sum = 0.0F
    repeat(randomAccess.size) { endIndex ->
        val end = randomAccess[endIndex]

        if (end.timestamp - start.timestamp > windowInMillis) {
            val count = endIndex - startIndex
            if (count > 1) {
                peakValue = max(peakValue, sum / count)
            }
        }

        sum += end.value

        while (end.timestamp - start.timestamp > windowInMillis) {
            sum -= start.value

            startIndex++
            start = randomAccess[startIndex]
        }
    }

    if (startIndex != randomAccess.lastIndex) {
        val count = randomAccess.lastIndex - startIndex
        if (count > 1) {
            peakValue = max(peakValue, sum / count)
        }
    }

    return peakValue.toDouble()
}

internal fun Sml.calculatePeakVerticalSpeedInXMillis(windowInMillis: Long): Double {
    val altitude = streamData.altitude
        .takeIf { it.size >= 2 }
        ?: return 0.0
    val randomAccess = if (altitude is RandomAccess) altitude else ArrayList(altitude)

    var peakVerticalSpeed = 0.0F
    var startIndex = 0
    var start = randomAccess[startIndex]
    var prev = start
    var ascent = 0.0F
    var timeInMillis = 0L
    repeat(randomAccess.size) { endIndex ->
        if (endIndex == 0) {
            return@repeat
        }
        val end = randomAccess[endIndex]

        if (end.timestamp - start.timestamp > windowInMillis) {
            if (timeInMillis > 0L) {
                peakVerticalSpeed = max(peakVerticalSpeed, ascent / timeInMillis)
            }
        }

        ascent += (end.value - prev.value)
        timeInMillis += (end.timestamp - prev.timestamp)
        prev = end

        while (end.timestamp - start.timestamp > windowInMillis) {
            startIndex++
            val next = randomAccess[startIndex]

            ascent -= (next.value - start.value)
            timeInMillis -= (next.timestamp - start.timestamp)

            start = next
        }
    }

    if (startIndex != randomAccess.lastIndex) {
        if (timeInMillis > 0L) {
            peakVerticalSpeed = max(peakVerticalSpeed, ascent / timeInMillis)
        }
    }

    return peakVerticalSpeed * 1000.0
}
