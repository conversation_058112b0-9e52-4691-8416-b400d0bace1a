package com.stt.android.workouts.reaction;

import com.squareup.moshi.Moshi;
import com.stt.android.data.reactions.ReactionRemoteSyncJobModule;
import com.stt.android.domain.workouts.reactions.ReactionDataSource;
import com.stt.android.domain.workouts.reactions.ReactionSummaryDataSource;
import com.stt.android.remote.AuthProvider;
import com.stt.android.remote.BaseUrl;
import com.stt.android.remote.SharedOkHttpClient;
import com.stt.android.remote.UserAgent;
import com.stt.android.remote.di.BrandOkHttpConfigFactory;
import com.stt.android.remote.di.RestApiFactory;
import com.stt.android.remote.reactions.ReactionRestApi;
import dagger.Binds;
import dagger.Module;
import dagger.Provides;
import okhttp3.OkHttpClient;

@Module(includes = { ReactionRemoteSyncJobModule.class })
public abstract class ReactionDataSourceModule {
    @Binds
    abstract ReactionDataSource bindReactionDataSource(ReactionOrmLiteDataSource dataSource);

    @Binds
    abstract ReactionSummaryDataSource bindReactionSummaryDataSource(ReactionSummaryOrmliteDataSource dataSource);

    @Provides
    static ReactionRestApi provideWorkoutRestApi(
        @SharedOkHttpClient OkHttpClient sharedClient,
        @BaseUrl String baseUrl,
        @UserAgent String userAgent,
        AuthProvider authProvider,
        Moshi moshi) {
        return RestApiFactory.buildRestApi(
            sharedClient,
            baseUrl,
            ReactionRestApi.class,
            BrandOkHttpConfigFactory.getStOkHttpConfig(authProvider, userAgent),
            moshi);
    }
}
