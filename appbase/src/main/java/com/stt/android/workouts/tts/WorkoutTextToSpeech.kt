package com.stt.android.workouts.tts

import android.content.Context
import android.content.res.Resources
import android.media.AudioManager
import android.speech.tts.TextToSpeech
import android.speech.tts.TextToSpeech.OnInitListener
import android.speech.tts.UtteranceProgressListener
import androidx.annotation.StringRes
import androidx.media.AudioFocusRequestCompat
import androidx.media.AudioManagerCompat
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.stt.android.R
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workout.GhostDistanceTimeState
import com.stt.android.ui.utils.TextFormatter
import com.suunto.algorithms.data.HeartRate
import timber.log.Timber
import java.util.Locale
import java.util.concurrent.atomic.AtomicInteger
import kotlin.math.abs
import kotlin.math.roundToInt
import com.stt.android.core.R as CR

/**
 * Initializes the right texts for the voice feedback feature. That is, if the language is
 * [Locale.ENGLISH]
 * then english texts ([com.stt.android.R.string.english_tts_start] and others) will be
 * used,
 * otherwise the current locale texts will be used ([com.stt.android.R.string.tts_start]
 * and others).
 *
 * @param language has to be the current app value for [ .string#tts_language][com.stt.android.R]
 * or "en" (English)
 * @throws java.lang.IllegalArgumentException if the given language doesn't match the current
 * app locale
 */
class WorkoutTextToSpeech(
    private val context: Context,
    private val language: String
) : OnInitListener, UtteranceProgressListener() {
    private val resources: Resources = context.resources
    private val textToSpeech: TextToSpeech?
    private val forcedEnglish = language == Locale.ENGLISH.language
    private val audioFocusRequest = AudioFocusRequestCompat
        .Builder(AudioManagerCompat.AUDIOFOCUS_GAIN_TRANSIENT_MAY_DUCK)
        .setOnAudioFocusChangeListener { Timber.d("Audio focus changed: $it") }
        .build()

    private val speakCounter = AtomicInteger(0)

    private val startText = resId(R.string.english_tts_start, R.string.tts_start)

    private val stopText = resId(R.string.english_tts_stop, R.string.tts_stop)

    private val resumeText = resId(R.string.english_tts_resume, R.string.tts_resume)

    private val autoPauseText = resId(R.string.english_tts_autopause, R.string.tts_autopause)

    private val autoResumeText = resId(R.string.english_tts_autoresume, R.string.tts_autoresume)

    private val currentHeartRateTextId =
        resId(R.string.english_tts_current_heart_rate, R.string.tts_current_heart_rate)

    private val currentHeartRateWithCurrentTextId =
        resId(R.string.english_tts_current_heart_rate_with_current, R.string.tts_current_heart_rate_with_current)

    private val averageHeartRateTextId =
        resId(R.string.english_tts_average_heart_rate, R.string.tts_average_heart_rate)

    private val lapTimeTextId = resId(R.string.english_tts_lap_duration, R.string.tts_lap_duration)

    private val totalTimeTextId = resId(R.string.english_tts_total_time, R.string.tts_total_time)

    private val totalTimeTextIdWithTitle = resId(R.string.english_tts_total_time_with_title, R.string.tts_total_time_with_title)

    private val totalDistanceTextId =
        resId(R.string.english_tts_total_distance, R.string.tts_total_distance)

    private val imperialDistanceQuantityUnitId =
        resId(R.plurals.english_tts_miles, R.plurals.tts_miles)

    private val imperialDistancePluralUnitId = resId(R.string.english_tts_miles, R.string.tts_miles)

    private val metricDistanceQuantityUnitId =
        resId(R.plurals.english_tts_kilometers, R.plurals.tts_kilometers)

    private val metricDistancePluralUnitId =
        resId(R.string.english_tts_kilometers, R.string.tts_kilometers)

    private val nauticalDistanceQuantityUnitId =
        resId(R.plurals.english_tts_nautical_miles, R.plurals.tts_nautical_miles)

    private val nauticalDistancePluralUnitId = resId(R.string.english_tts_nautical_miles, R.string.tts_nautical_miles)

    private val lapPaceImperialTextId =
        resId(R.string.english_tts_lap_pace_imperial, R.string.tts_lap_pace_imperial)

    private val lapPaceMetricTextId =
        resId(R.string.english_tts_lap_pace_metric, R.string.tts_lap_pace_metric)

    private val lapSwimPaceImperialTextId =
        resId(R.string.english_tts_lap_swim_pace_imperial, R.string.tts_lap_swim_pace_imperial)

    private val lapSwimPaceMetricTextId =
        resId(R.string.english_tts_lap_swim_pace_metric, R.string.tts_lap_swim_pace_metric)

    private val lapSpeedTextId = resId(R.string.english_tts_lap_speed, R.string.tts_lap_speed)

    private val energyTextId = resId(R.plurals.english_tts_energy, R.plurals.tts_energy)

    private val currentSpeedTextId =
        resId(R.string.english_tts_current_speed, R.string.tts_current_speed)

    private val currentPaceImperialTextId =
        resId(R.string.english_tts_current_pace_imperial, R.string.tts_current_pace_imperial)

    private val currentPaceMetricTextId =
        resId(R.string.english_tts_current_pace_metric, R.string.tts_current_pace_metric)

    private val averageSpeedTextId =
        resId(R.string.english_tts_average_speed, R.string.tts_average_speed)

    private val averagePaceImperialTextId =
        resId(R.string.english_tts_average_pace_imperial, R.string.tts_average_pace_imperial)

    private val averagePaceMetricTextId =
        resId(R.string.english_tts_average_pace_metric, R.string.tts_average_pace_metric)

    private val hoursTextId = resId(R.plurals.english_tts_hours, R.plurals.tts_hours)

    private val minutesTextId = resId(R.plurals.english_tts_minutes, R.plurals.tts_minutes)

    private val secondsTextId = resId(R.plurals.english_tts_seconds, R.plurals.tts_seconds)

    private val ghostAheadTextId = resId(R.string.english_tts_ghost_ahead, R.string.tts_ghost_ahead)

    private val ghostBehindTextId =
        resId(R.string.english_tts_ghost_behind, R.string.tts_ghost_behind)

    private val ghostNeutralTextId =
        resId(R.string.english_tts_ghost_neutral, R.string.tts_ghost_neutral)

    private val currentCadenceTextId =
        resId(R.string.english_tts_current_cadence, R.string.tts_current_cadence)

    private val averageCadenceTextId =
        resId(R.string.english_tts_lap_cadence, R.string.tts_lap_cadence)

    private val lapNumberTextId = resId(R.string.english_tts_lap_number, R.string.tts_lap_number)

    private val slipDistanceTextId =
        resId(R.string.english_tts_lap_distance, R.string.tts_lap_distance)

    private val maxHeartRateTextId =
        resId(R.string.english_tts_lap_max_heart_rate, R.string.tts_lap_max_heart_rate)

    private val lapHeartRateTextId = resId(R.string.english_tts_lap_heart_rate, R.string.tts_lap_heart_rate)

    private val heartRateZoneTextId =
        resId(R.string.english_tts_heart_zone, R.string.tts_heart_zone)

    private val lapAscentTextId = resId(R.string.english_tts_lap_ascent, R.string.tts_lap_ascent)
    private val lapDescentTextId = resId(R.string.english_tts_lap_descent, R.string.tts_lap_descent)
    private val lapPowerTextId = resId(R.string.english_tts_lap_power, R.string.tts_lap_power)
    private val lapPowerUnitTextId = CR.string.watt
    private val decimalSeparatorText by lazy {
        String.format(
            Locale.getDefault(),
            " %s ",
            resources.getString(
                resId(R.string.english_tts_decimal_separator, R.string.tts_decimal_separator)
            )
        )
    }

    private var ready: Boolean

    override fun onInit(status: Int) {
        if (textToSpeech == null || status != TextToSpeech.SUCCESS) {
            return
        }

        val languageAvailable = textToSpeech.isLanguageAvailable(Locale(language))
        Timber.d("TTS language %s available? %d", language, languageAvailable)
        if (languageAvailable == TextToSpeech.LANG_MISSING_DATA || languageAvailable == TextToSpeech.LANG_NOT_SUPPORTED) {
            Timber.w("%s text to speech language is not available", language)
            ready = false
        } else {
            try {
                textToSpeech.language = Locale(language)
                val ttsLanguage: Locale? = textToSpeech.voice.locale
                if (ttsLanguage == null) {
                    Timber.w("TTS fails to get the language to be used.")
                } else {
                    Timber.d(
                        "TTS language set to: %s and country: %s",
                        ttsLanguage.language,
                        ttsLanguage.country
                    )
                }
                ready = true
            } catch (e: Exception) {
                // there's some crash on Samsung devices, but I can't reproduce, so let's log a bit
                // more details here
                // ref. https://fabric.io/sporst-tracker/android/apps/com.stt
                // .android/issues/5662a26ff5d3a7f76b1b2739
                logMessage("Error occured while initializing TTS")
                logMessage("TTS Engine: " + textToSpeech.defaultEngine)
                logMessage("Language: $language")
                ready = false
            }
        }
    }

    private fun logMessage(message: String) {
        Timber.d(message)
        FirebaseCrashlytics.getInstance().log(message)
    }

    private fun speakNow(text: String?) = speak(text, TextToSpeech.QUEUE_FLUSH)

    private fun addToSpeakQueue(text: String) = speak(text, TextToSpeech.QUEUE_ADD)

    init {
        ready = false
        if (language != Locale.ENGLISH.language) {
            val currentLocaleLanguage = resources.getString(R.string.tts_language)
            require(language == currentLocaleLanguage) {
                String.format(
                    Locale.getDefault(),
                    "Given language %s does not match current locale %s",
                    language,
                    currentLocaleLanguage
                )
            }
        }

        // the callback might be triggered immediately, so TextToSpeech must be constructed the
        // latest
        textToSpeech = TextToSpeech(this.context, this)
        textToSpeech.setOnUtteranceProgressListener(this)
    }

    private fun speak(text: String?, queueMode: Int) {
        if (ready) {
            speakCounter.incrementAndGet()
            val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
            AudioManagerCompat.requestAudioFocus(
                audioManager,
                audioFocusRequest
            )
            textToSpeech
                ?.apply { language = Locale.forLanguageTag(resources.getString(R.string.tts_language))}
                ?.speak(text, queueMode, null, UTTERANCE_ID)
        }
    }

    fun sayStart() = speakNow(resources.getString(startText))

    fun sayStop() = speakNow(resources.getString(stopText))

    fun sayResume() = speakNow(resources.getString(resumeText))

    fun sayAutoPause() = speakNow(resources.getString(autoPauseText))

    fun sayAutoResume() = speakNow(resources.getString(autoResumeText))

    fun sayGhostDifference(
        ghostDistanceTimeState: GhostDistanceTimeState?,
        ghostDifference: Double,
        unit: MeasurementUnit?
    ) {
        var difference = ghostDifference
        if (difference == 0.0) {
            addToSpeakQueue(resources.getString(ghostNeutralTextId))
        } else {
            val sentenceId: Int =
                if (difference < 0) { // Use ahead even though the user is neither ahead nor behind
                    ghostAheadTextId
                } else {
                    ghostBehindTextId
                }
            // Discard the sign which told us if ahead or behind
            difference = abs(difference)
            val sentence = when (ghostDistanceTimeState) {
                GhostDistanceTimeState.DISTANCE -> createDistanceSentence(
                    difference,
                    unit,
                    sentenceId
                )

                GhostDistanceTimeState.TIME -> resources.getString(
                    sentenceId,
                    calculateTimeSentence(difference)
                )

                else -> resources.getString(sentenceId, calculateTimeSentence(difference))
            }
            addToSpeakQueue(sentence)
        }
    }

    fun sayCurrentHeartRate(heartRate: Int) =
        addToSpeakQueue(resources.getString(currentHeartRateTextId, heartRate))

    fun sayCurrentHeartRateWithCurrent(heartRate: HeartRate) =
        addToSpeakQueue(resources.getString(currentHeartRateWithCurrentTextId, heartRate.inBpm.roundToInt()))

    fun sayAverageHeartRate(heartRate: Int) =
        addToSpeakQueue(resources.getString(averageHeartRateTextId, heartRate))

    fun sayLapHeartRate(heartRate: HeartRate) =
        addToSpeakQueue(resources.getString(lapHeartRateTextId, heartRate.inBpm.roundToInt()))

    fun sayCurrentCadence(cadence: Int) =
        addToSpeakQueue(resources.getString(currentCadenceTextId, cadence))

    fun sayAverageCadence(cadence: Int) =
        addToSpeakQueue(resources.getString(averageCadenceTextId, cadence))

    fun sayTotalDistance(totalDistance: Double, unit: MeasurementUnit?) =
        addToSpeakQueue(createDistanceSentence(totalDistance, unit, totalDistanceTextId))

    private fun createDistanceSentence(
        distance: Double,
        unit: MeasurementUnit?,
        @StringRes sentenceId: Int,
        useNauticalMiles: Boolean = false
    ): String {
        requireNotNull(unit) { "Missing measurement unit" }
        val quantityUnitId: Int
        val pluralsUnitId: Int
        if (useNauticalMiles) {
            quantityUnitId = nauticalDistanceQuantityUnitId
            pluralsUnitId = nauticalDistancePluralUnitId
        } else {
            when (unit) {
                MeasurementUnit.IMPERIAL -> {
                    quantityUnitId = imperialDistanceQuantityUnitId
                    pluralsUnitId = imperialDistancePluralUnitId
                }

                MeasurementUnit.METRIC -> {
                    quantityUnitId = metricDistanceQuantityUnitId
                    pluralsUnitId = metricDistancePluralUnitId
                }
            }
        }
        val distanceSentence: String
        // Format the distanceInCurrentUnit with two decimals
        val distanceInCurrentUnit = if (useNauticalMiles) unit.fromMetersToNauticalMile(distance) else unit.toDistanceUnit(distance)
        var distanceStr = TextFormatter.formatDistance(distanceInCurrentUnit)
        // If the decimals are 00 remove them
        if (distanceStr.endsWith(TWO_ZERO_DECIMALS)) {
            distanceStr = distanceStr.substring(0, distanceStr.length - TWO_ZERO_DECIMALS.length)
            val distanceInt = Integer.valueOf(distanceStr)
            // We use quantity because we want to say "one kilometer" and "three kilometers"
            distanceSentence = resources.getQuantityString(quantityUnitId, distanceInt, distanceInt)
        } else {
            // If the decimals are x0, remove the trailing 0
            if (distanceStr.endsWith(ZERO)) {
                distanceStr = distanceStr.substring(0, distanceStr.length - ZERO.length)
            }
            if (language != Locale.CHINESE.language) {
                distanceStr = buildDecimalString(distanceStr)
            }
            distanceSentence = resources.getString(pluralsUnitId, distanceStr)
        }
        return resources.getString(sentenceId, distanceSentence)
    }

    private fun dealDistanceSentenceWithZero(
        distance: String,
        infoModelFormatterUnitId: Int,
        sentenceId: Int
    ): String {
        var distanceStr = distance
        val distanceSentence: String
        // If the decimals are 00 remove them
        if (distanceStr.endsWith(TWO_ZERO_DECIMALS)) {
            distanceStr = distanceStr.substring(0, distanceStr.length - TWO_ZERO_DECIMALS.length)
            val distanceInt = Integer.valueOf(distanceStr)
            // We use quantity because we want to say "one kilometer" and "three kilometers"
            distanceSentence = "${distanceInt}${resources.getString(infoModelFormatterUnitId)}"
        } else {
            // If the decimals are x0, remove the trailing 0
            if (distanceStr.endsWith(ZERO) && distanceStr.contains(".")) {
                distanceStr = distanceStr.substring(0, distanceStr.length - ZERO.length)
            }
            if (language != Locale.CHINESE.language) {
                distanceStr = buildDecimalString(distanceStr)
            }
            distanceSentence = "${distanceStr}${resources.getString(infoModelFormatterUnitId)}"
        }
        Timber.d("read aloud: ${resources.getString(sentenceId, distanceSentence)}")
        return resources.getString(sentenceId, distanceSentence)
    }

    /**
     * Replaces English decimal separator '.' with the right decimal mark name. That is,
     * '3.12' becomes '3 point 12'
     */
    private fun buildDecimalString(decimalString: String): String =
        decimalString.replace(".", decimalSeparatorText)

    fun sayLapPace(lapSpeed: Double, unit: MeasurementUnit?) {
        requireNotNull(unit) { "Missing measurement unit" }
        sayPace(lapSpeed, unit, lapPaceImperialTextId, lapPaceMetricTextId)
    }

    fun sayLapSwimPace(lapSpeed: Double, unit: MeasurementUnit?) {
        requireNotNull(unit) { "Missing measurement unit" }
        sayPace(lapSpeed, unit, lapSwimPaceImperialTextId, lapSwimPaceMetricTextId, true)
    }

    private fun sayPace(
        speed: Double,
        unit: MeasurementUnit,
        @StringRes imperialTextId: Int,
        @StringRes metricTextId: Int,
        isSwimming: Boolean = false
    ) {
        val stringId: Int = when (unit) {
            MeasurementUnit.IMPERIAL -> imperialTextId
            MeasurementUnit.METRIC -> metricTextId
        }
        val totalSeconds = if (isSwimming) {
            unit.toSwimPaceUnit(speed) * 60
        } else {
            unit.toPaceUnit(speed) * 60
        }
        val timeSentence = calculateTimeSentence(totalSeconds)
        addToSpeakQueue(resources.getString(stringId, timeSentence))
    }

    fun sayCurrentPace(currentSpeed: Double, unit: MeasurementUnit) =
        sayPace(currentSpeed, unit, currentPaceImperialTextId, currentPaceMetricTextId)

    fun sayAveragePace(averageSpeed: Double, unit: MeasurementUnit) =
        sayPace(averageSpeed, unit, averagePaceImperialTextId, averagePaceMetricTextId)

    fun sayLapSpeed(lapSpeed: Double, unit: MeasurementUnit?) {
        requireNotNull(unit) { "Missing measurement unit" }
        saySpeed(lapSpeed, unit, lapSpeedTextId)
    }

    private fun saySpeed(speed: Double, unit: MeasurementUnit, @StringRes speedTextId: Int) {
        var speedStr = TextFormatter.formatSpeed(unit.toSpeedUnit(speed))
        // If the decimals are 0 remove them
        if (speedStr.endsWith(ONE_ZERO_DECIMALS)) {
            speedStr = speedStr.substring(0, speedStr.length - ONE_ZERO_DECIMALS.length)
        }
        if (language != Locale.CHINESE.language) {
            speedStr = buildDecimalString(speedStr)
        }
        val sentence = resources.getString(speedTextId, speedStr)
        addToSpeakQueue(sentence)
    }

    fun sayCurrentSpeed(currentSpeed: Double, unit: MeasurementUnit) =
        saySpeed(currentSpeed, unit, currentSpeedTextId)

    fun sayAverageSpeed(averageSpeed: Double, unit: MeasurementUnit) =
        saySpeed(averageSpeed, unit, averageSpeedTextId)

    fun sayTotalTime(timeInSeconds: Double) = sayTime(timeInSeconds, totalTimeTextId)
    fun sayTotalTimeWithTitle(timeInSeconds: Double) = sayTime(timeInSeconds, totalTimeTextIdWithTitle)

    fun sayLapTime(timeInSeconds: Double) = sayTime(timeInSeconds, lapTimeTextId)

    fun sayLapNumber(number: Int) = addToSpeakQueue(resources.getString(lapNumberTextId, number))

    fun saySplitDistance(distance: Double, unit: MeasurementUnit?, isSailing: Boolean) =
        addToSpeakQueue(
            createDistanceSentence(
                distance = distance,
                unit = unit,
                sentenceId = slipDistanceTextId,
                useNauticalMiles = isSailing
            )
        )

    fun saySwimDistance(distanceStr: String, unit: Int) = addToSpeakQueue(dealDistanceSentenceWithZero(distanceStr, unit, slipDistanceTextId))

    fun sayMaxHeartRate(maxHeartRate: HeartRate) =
        addToSpeakQueue(resources.getString(maxHeartRateTextId, maxHeartRate.inBpm.roundToInt()))

    fun sayHeartRateZone(heartRateZone: Int) =
        addToSpeakQueue(resources.getString(heartRateZoneTextId, heartRateZone))

    /**
     * @param sentenceId the id of the string to say the actual time measurement
     */
    private fun sayTime(timeInSeconds: Double, @StringRes sentenceId: Int) {
        val sentence = resources.getString(sentenceId, calculateTimeSentence(timeInSeconds))
        addToSpeakQueue(sentence)
    }

    private fun calculateTimeSentence(totalSeconds: Double): String {
        val stringBuilder = StringBuilder()
        val hours = (totalSeconds / 3600).toInt()
        val remainder = (totalSeconds % 3600).toInt()
        val minutes = remainder / 60
        val seconds = remainder % 60
        if (hours > 0) {
            stringBuilder.append(resources.getQuantityString(hoursTextId, hours, hours))
            stringBuilder.append(" ")
        }
        if (minutes > 0) {
            stringBuilder.append(resources.getQuantityString(minutesTextId, minutes, minutes))
            stringBuilder.append(" ")
        }
        if (seconds > 0 || stringBuilder.isEmpty()) {
            stringBuilder.append(resources.getQuantityString(secondsTextId, seconds, seconds))
        }
        return stringBuilder.toString()
    }

    fun sayEnergy(calories: Double) = addToSpeakQueue(
        resources.getQuantityString(
            energyTextId,
            calories.toInt(),
            calories.toInt()
        )
    )

    fun shutdown() {
        textToSpeech?.shutdown()
        ready = false
    }

    override fun onStart(utteranceId: String?) {
        // do nothing
    }

    override fun onDone(utteranceId: String?) {
        if (UTTERANCE_ID == utteranceId && speakCounter.decrementAndGet() <= 0) {
            abandonAudioFocus()
        }
    }

    @Suppress("OVERRIDE_DEPRECATION")
    override fun onError(utteranceId: String?) {
        // do nothing
    }

    override fun onStop(utteranceId: String?, interrupted: Boolean) {
        if (UTTERANCE_ID == utteranceId && speakCounter.decrementAndGet() <= 0) {
            abandonAudioFocus()
        }
    }

    override fun onError(utteranceId: String?, errorCode: Int) =
        Timber.w("Error with utteranceId:$utteranceId errorCode:$errorCode")

    private fun abandonAudioFocus() {
        val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
        AudioManagerCompat.abandonAudioFocusRequest(audioManager, audioFocusRequest)

        // Reset the counter just to make sure it won't go into negatives and
        // break queuing up speeches.
        speakCounter.set(0)
    }

    private fun resId(englishResId: Int, resId: Int): Int =
        if (forcedEnglish) englishResId else resId

    fun sayPower(power: String) = addToSpeakQueue("${resources.getString(lapPowerTextId, power)}${resources.getString(lapPowerUnitTextId)}")

    fun sayAscent(splitAscent: String, unit: Int) = addToSpeakQueue(dealDistanceSentenceWithZero(splitAscent, unit, lapAscentTextId))

    fun sayDescent(splitDescent: String, unit: Int) = addToSpeakQueue(dealDistanceSentenceWithZero(splitDescent, unit, lapDescentTextId))
    companion object {
        private const val TWO_ZERO_DECIMALS = ".00"
        private const val ONE_ZERO_DECIMALS = ".0"
        private const val ZERO = "0"
        private const val UTTERANCE_ID = "STT_TEXT_TO_SPEECH"
    }
}
