package com.stt.android.workouts.filters;

import android.location.Location;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class DistanceFilter {
    private static final float MINIMUM_DISTANCE_IN_METERS = 8.0F;
    private static final int MAXIMUM_CONSECUTIVE_OBSERVATIONS_TO_DROP = 6;

    private Location lastAcceptedLocation;
    private int consecutiveDroppedObservations;

    @Inject
    public DistanceFilter() {
        reset();
    }

    public void reset() {
        lastAcceptedLocation = null;
        consecutiveDroppedObservations = 0;
    }

    public boolean filter(Location location) {
        boolean shouldFilter = false;

        if (location.getSpeed() == 0.0F) {
            // the user currently stops, in order to update the UI, we need to let it go through
            shouldFilter = false;
        } else if (lastAcceptedLocation != null) {
            // if there is already accepted location, we accept new ones if one of the following
            // conditions is met:
            // 1) the movement is more than {@link MINIMUM_DISTANCE_IN_METERS}
            // 2) there are more than {@link MAXIMUM_CONSECUTIVE_OBSERVATIONS_TO_DROP} consecutive
            // dropped observations
            shouldFilter = location.distanceTo(lastAcceptedLocation) < MINIMUM_DISTANCE_IN_METERS
                && ++consecutiveDroppedObservations <= MAXIMUM_CONSECUTIVE_OBSERVATIONS_TO_DROP;
        }

        if (!shouldFilter) {
            lastAcceptedLocation = location;
            consecutiveDroppedObservations = 0;
        }

        return shouldFilter;
    }
}
