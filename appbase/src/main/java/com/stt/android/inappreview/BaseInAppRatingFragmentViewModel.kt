package com.stt.android.inappreview

import android.app.Activity
import android.content.SharedPreferences
import androidx.core.content.edit
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.android.play.core.ktx.launchReview
import com.google.android.play.core.ktx.requestReview
import com.google.android.play.core.review.ReviewManager
import com.helpshift.support.ApiConfig
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.controllers.UserSettingsController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.help.LoadSupportMetadataUseCase
import com.stt.android.utils.STTConstants
import com.stt.android.utils.STTConstants.InAppReviewPreferences.IN_APP_REVIEW_FORCE_SCHEDULE
import com.stt.android.utils.STTConstants.InAppReviewPreferences.KEY_ASK_ME_LATER_CLICKED
import com.stt.android.utils.STTConstants.InAppReviewPreferences.KEY_IN_APP_RATING_ALREADY_SHOWN
import com.stt.android.utils.STTConstants.InAppReviewPreferences.KEY_NEVER_SHOW_AGAIN_CLICKED
import com.stt.android.utils.STTConstants.InAppReviewPreferences.KEY_POSITIVE_FEELING_CLICKED
import com.stt.android.utils.STTConstants.InAppReviewPreferences.KEY_TIMESTAMP_WHEN_ASK_ME_LATER_CLICKED
import com.stt.android.utils.STTConstants.InAppReviewPreferences.KEY_TIMESTAMP_WHEN_POSITIVE_FEELING_CLICKED
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber

abstract class BaseInAppRatingFragmentViewModel(
    private val sharedPreferences: SharedPreferences,
    private val reviewManager: ReviewManager,
    private val emarsysAnalytics: EmarsysAnalytics,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    private val loadSupportMetadataUseCase: LoadSupportMetadataUseCase,
    private val userSettingsController: UserSettingsController
) : ViewModel() {
    private val _uiState = MutableStateFlow(
        InAppRatingUiState(
            // If we already showed the dialog for the user, then we need to ask him what he feels about the app (Feeling icons)
            // If not, we show the initial dialog that contains Rate/ask me later/never show again
            inAppRatingType = if (sharedPreferences.getBoolean(
                    KEY_IN_APP_RATING_ALREADY_SHOWN,
                    false
                )
            ) {
                InAppRatingType.AskForRating
            } else {
                InAppRatingType.AskWhichRating
            }
        )
    )
    val uiState: StateFlow<InAppRatingUiState> = _uiState.asStateFlow()

    fun onNegativeOrNaturalRatingClick() {
        _uiState.update {
            it.copy(
                inAppRatingType = InAppRatingType.AskForFeedback
            )
        }
    }

    fun onPositiveRatingClick() {
        sharedPreferences.edit {
            putBoolean(
                KEY_POSITIVE_FEELING_CLICKED,
                true
            )
            putLong(
                KEY_TIMESTAMP_WHEN_POSITIVE_FEELING_CLICKED,
                System.currentTimeMillis()
            )
        }
    }

    private fun markAsNeverShowAgain() {
        sharedPreferences.edit {
            putBoolean(KEY_NEVER_SHOW_AGAIN_CLICKED, true)
            // In case we forced the schedule, from Braze as example, we must set the flag back to false to avoid force showing the dialog again
            putBoolean(IN_APP_REVIEW_FORCE_SCHEDULE, false)
        }
        userSettingsController.storeSettings(
            userSettingsController.settings.setDisabledAppRatingSuggestions(
                listOf(
                    disabledInAppRatingUserSettingsKey()
                ).toTypedArray()
            )
        )
    }

    fun markDialogAsShown() {
        sharedPreferences.edit {
            putBoolean(KEY_IN_APP_RATING_ALREADY_SHOWN, true)
        }
    }

    fun onAskMeLaterClicked() {
        sharedPreferences.edit {
            putBoolean(
                KEY_ASK_ME_LATER_CLICKED,
                true
            )
            putLong(
                KEY_TIMESTAMP_WHEN_ASK_ME_LATER_CLICKED,
                System.currentTimeMillis()
            )
        }
    }

    fun onRateNowClicked(activity: Activity) {
        viewModelScope.launch {
            runSuspendCatching {
                val reviewInfo = reviewManager.requestReview()
                reviewManager.launchReview(activity, reviewInfo)
                logInAppReviewRequested()
            }.onFailure { e ->
                Timber.e(e, "Error in rate now")
                logInAppReviewError(e)
            }
            sharedPreferences.edit {
                putBoolean(
                    STTConstants.InAppReviewPreferences.KEY_TRIGGER_IN_APP_REVIEW,
                    false
                )
            }
            markAsNeverShowAgain()
        }
    }

    fun onCloseClicked() {
        // Customer is not happy, it is better to not show the dialog again
        markAsNeverShowAgain()
    }

    fun onGiveFeedbackClicked(onApiConfigLoaded: (ApiConfig) -> Unit) {
        viewModelScope.launch {
            runSuspendCatching {
                val apiConfig = loadSupportMetadataUseCase.run()
                onApiConfigLoaded(apiConfig)
            }.onFailure { e ->
                Timber.e(e, "Failed to load helpshift config")
            }
            markAsNeverShowAgain()
        }
    }

    fun onGiveFeedbackWithMetadataJsonClicked(onMetadataJsonLoaded: (String) -> Unit) {
        viewModelScope.launch {
            runSuspendCatching {
                val metadata = loadSupportMetadataUseCase.getMetadataJson()
                onMetadataJsonLoaded(metadata)
            }.onFailure { e ->
                Timber.e(e, "Failed to load helpshift config")
            }
            markAsNeverShowAgain()
        }
    }

    fun onNeverAskAgainClicked() {
        markAsNeverShowAgain()
    }

    private fun logInAppReviewRequested() {
        val event = AnalyticsEvent.IN_APP_REVIEW_REQUESTED
        amplitudeAnalyticsTracker.trackEvent(event)
        emarsysAnalytics.trackEvent(event)
    }

    private fun logInAppReviewError(e: Throwable) {
        val event = AnalyticsEvent.IN_APP_REVIEW_REQUEST_ERROR
        val properties = AnalyticsProperties()
            .put(AnalyticsEventProperty.ERROR_CAUSE, "${e.javaClass.name}: ${e.message})")
        amplitudeAnalyticsTracker.trackEvent(event, properties)
        emarsysAnalytics.trackEventWithProperties(event, properties.map)
    }

    abstract fun disabledInAppRatingUserSettingsKey(): String
}

data class InAppRatingUiState(
    val inAppRatingType: InAppRatingType
)

sealed class InAppRatingType {
    object AskForRating : InAppRatingType()
    object AskWhichRating : InAppRatingType()
    object AskForFeedback : InAppRatingType()
}
