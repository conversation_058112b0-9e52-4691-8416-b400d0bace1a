package com.stt.android.inappreview

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.activity.addCallback
import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Star
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.Outline
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentTransaction
import androidx.fragment.app.viewModels
import com.helpshift.support.Support
import com.stt.android.R
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.bodyXLargeBold
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.help.BaseSupportHelper
import com.stt.android.utils.FlavorUtils
import dagger.hilt.android.AndroidEntryPoint
import java.util.Locale
import javax.inject.Inject

@AndroidEntryPoint
class InAppRatingFragment : DialogFragment() {

    private val viewModel: InAppRatingFragmentViewModel by viewModels()

    @Inject
    lateinit var supportHelper: BaseSupportHelper

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        requireActivity().onBackPressedDispatcher.addCallback(owner = this) {
            // To make sure to not show the dialog again if the user closed the dialog using physical back button
            dismiss()
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                AppTheme {
                    Body(
                        viewModel = viewModel,
                        onRateNowClicked = {
                            viewModel.onRateNowClicked(requireActivity())
                        },
                        onDismissRequest = {
                            viewModel.markDialogAsShown()
                            dismiss()
                        },
                        onGiveFeedbackClicked = {
                            toFeedback()
                            dismiss()
                        }
                    )
                }
            }
        }
    }

    /**
     * make sportstracker jump to helpShift, suunto to new page (SupportActivity)
     */
    private fun toFeedback() {
        if (FlavorUtils.isSuuntoApp) {
            viewModel.onGiveFeedbackWithMetadataJsonClicked { metadata ->
                supportHelper.showMainSupport(requireActivity(), metadata)
            }
        } else {
            viewModel.onGiveFeedbackClicked { apiConfig ->
                Support.showFAQs(requireActivity(), apiConfig)
            }
        }
    }

    companion object {
        private const val TAG = "InAppRatingFragment"

        @JvmStatic
        fun show(
            parentFragmentManager: FragmentManager,
        ) {
            parentFragmentManager
                .beginTransaction()
                .setTransition(FragmentTransaction.TRANSIT_FRAGMENT_OPEN)
                .add(android.R.id.content, InAppRatingFragment(), TAG)
                .addToBackStack(null)
                .commit()
        }
    }
}

@Composable
private fun Body(
    viewModel: InAppRatingFragmentViewModel,
    onRateNowClicked: () -> Unit,
    onGiveFeedbackClicked: () -> Unit,
    onDismissRequest: () -> Unit
) {
    val uiState = viewModel.uiState.collectAsState()
    RatingDialog(
        uiState = uiState.value,
        onSadClicked = viewModel::onNegativeOrNaturalRatingClick,
        onNaturalClicked = viewModel::onNegativeOrNaturalRatingClick,
        onHappyClicked = {
            viewModel.onPositiveRatingClick()
            onDismissRequest()
        },
        onNeverAskAgainClicked = {
            viewModel.onNeverAskAgainClicked()
            onDismissRequest()
        },
        onAskMeLaterClicked = {
            viewModel.onAskMeLaterClicked()
            onDismissRequest()
        },
        onRateNowClicked = {
            onRateNowClicked()
            onDismissRequest()
        },
        onCloseClicked = {
            viewModel.onCloseClicked()
            onDismissRequest()
        },
        onGiveFeedbackClicked = onGiveFeedbackClicked,
    )
}

@OptIn(ExperimentalComposeUiApi::class)
@Composable
private fun RatingDialog(
    uiState: InAppRatingUiState,
    onSadClicked: () -> Unit,
    onNaturalClicked: () -> Unit,
    onHappyClicked: () -> Unit,
    onNeverAskAgainClicked: () -> Unit,
    onAskMeLaterClicked: () -> Unit,
    onRateNowClicked: () -> Unit,
    onGiveFeedbackClicked: () -> Unit,
    onCloseClicked: () -> Unit
) {
    Dialog(
        onDismissRequest = {},
        properties = DialogProperties(
            usePlatformDefaultWidth = false, // Workaround to make sure the dialog's size is updated when we change the inner content
            dismissOnBackPress = false,
            dismissOnClickOutside = false
        )
    ) {
        Box(
            modifier = Modifier
                .width(280.dp)
                .clip(RoundedCornerShape(10.dp))
                .background(color = Color.White)
        ) {
            Image(
                painter = painterResource(R.drawable.in_app_review_background_image),
                contentDescription = null,
                modifier = Modifier
                    .height(80.dp)
                    .fillMaxWidth(),
                contentScale = ContentScale.FillWidth
            )
            Column(
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .fillMaxWidth()
                    .verticalScroll(rememberScrollState())
                    .padding(
                        top = MaterialTheme.spacing.xxlarge,
                        bottom = MaterialTheme.spacing.xlarge,
                        start = MaterialTheme.spacing.large,
                        end = MaterialTheme.spacing.large
                    )
            ) {
                StarsContainer()
                Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
                when (uiState.inAppRatingType) {
                    InAppRatingType.AskForFeedback -> {
                        AskForFeedback(
                            onGiveFeedbackClicked = onGiveFeedbackClicked,
                            onCloseClicked = onCloseClicked
                        )
                    }

                    InAppRatingType.AskForRating -> {
                        AskForRating(
                            onRateNowClicked = onRateNowClicked,
                            onAskMeLaterClicked = onAskMeLaterClicked,
                            onNeverAskAgainClicked = onNeverAskAgainClicked
                        )
                    }

                    InAppRatingType.AskWhichRating -> {
                        AskWhichRating(
                            onSadClicked = onSadClicked,
                            onNaturalClicked = onNaturalClicked,
                            onHappyClicked = onHappyClicked
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun StarsContainer() {
    Column(
        modifier = Modifier
            .width(150.dp)
            .height(60.dp)
    ) {
        Row(
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
                .shadow(elevation = 20.dp)
                .clip(RoundedCornerShape(10.dp))
                .height(50.dp)
                .background(Color.White)
        ) {
            repeat(5) {
                Image(
                    Icons.Default.Star,
                    contentDescription = null,
                    colorFilter = ColorFilter.tint(color = Color(0XFFFDD300)),
                    modifier = Modifier.size(MaterialTheme.iconSizes.small)
                )
            }
        }
        Row {
            Spacer(modifier = Modifier.width(120.dp))
            Box(
                modifier = Modifier
                    .size(12.dp)
                    .clip(TriangleShape())
                    .background(Color.White)
            )
        }
    }
}

@Composable
private fun AskForRating(
    onRateNowClicked: () -> Unit,
    onAskMeLaterClicked: () -> Unit,
    onNeverAskAgainClicked: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
            .fillMaxWidth()
    ) {
        Title(text = stringResource(R.string.thank_you))
        Description(text = stringResource(R.string.in_app_review_ask_for_review_description))
        PrimaryButton(
            onClick = onRateNowClicked,
            text = stringResource(R.string.rate_it_now),
            modifier = Modifier
                .fillMaxWidth()
        )
        CustomTextButton(
            onClick = onAskMeLaterClicked,
            text = stringResource(R.string.ask_me_later)
        )
        CustomTextButton(
            onClick = onNeverAskAgainClicked,
            text = stringResource(R.string.never_ask_again)
        )
    }
}

@Composable
private fun Title(
    text: String
) {
    Text(
        text = text.uppercase(Locale.getDefault()),
        modifier = Modifier.padding(
            horizontal = MaterialTheme.spacing.medium,
            vertical = MaterialTheme.spacing.xsmaller
        ),
        textAlign = TextAlign.Center,
        style = MaterialTheme.typography.bodyXLargeBold,
        color = MaterialTheme.colors.onSurface
    )
}

@Composable
private fun Description(
    text: String,
    modifier: Modifier = Modifier
) {
    Text(
        text = text,
        modifier = modifier.padding(
            start = MaterialTheme.spacing.medium,
            end = MaterialTheme.spacing.medium,
            top = MaterialTheme.spacing.small,
            bottom = MaterialTheme.spacing.medium,
        ),
        textAlign = TextAlign.Center,
        style = MaterialTheme.typography.bodyLarge,
        color = MaterialTheme.colors.onSurface
    )
}

@Composable
private fun AskWhichRating(
    onSadClicked: () -> Unit,
    onNaturalClicked: () -> Unit,
    onHappyClicked: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
            .fillMaxWidth()
    ) {
        Title(text = stringResource(R.string.in_app_review_ask_for_opinion_title))
        Description(text = stringResource(R.string.in_app_review_ask_for_opinion_description))
        Row(
            verticalAlignment = Alignment.Bottom,
            horizontalArrangement = Arrangement.Center,
            modifier = Modifier
                .fillMaxWidth()
        ) {
            FeelingIcon(R.drawable.ic_feeling_sad, onClicked = onSadClicked)
            FeelingIcon(R.drawable.ic_feeling_natural, onClicked = onNaturalClicked)
            FeelingIcon(R.drawable.ic_feeling_happy, onClicked = onHappyClicked)
        }
    }
}

@Composable
private fun FeelingIcon(
    @DrawableRes iconRes: Int,
    onClicked: () -> Unit,
    modifier: Modifier = Modifier
) {
    IconButton(
        onClick = onClicked,
        modifier = modifier.padding(horizontal = MaterialTheme.spacing.xsmaller)
    ) {
        Icon(
            painter = painterResource(
                iconRes
            ),
            contentDescription = null,
            modifier = Modifier.size(MaterialTheme.iconSizes.xlarge),
            tint = Color.Unspecified
        )
    }
}

@Composable
private fun AskForFeedback(
    onGiveFeedbackClicked: () -> Unit,
    onCloseClicked: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
            .fillMaxWidth()
    ) {
        Title(stringResource(R.string.thank_you))
        Description(text = stringResource(R.string.in_app_review_ask_for_feedback_description))
        PrimaryButton(
            onClick = onGiveFeedbackClicked,
            text = stringResource(R.string.customer_support),
            modifier = Modifier
                .fillMaxWidth()
        )
        CustomTextButton(
            text = stringResource(R.string.close),
            onClick = onCloseClicked
        )
    }
}

@Composable
private fun CustomTextButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    TextButton(onClick = onClick, modifier = modifier.fillMaxWidth()) {
        Text(
            text = text,
            style = MaterialTheme.typography.bodyBold,
            color = MaterialTheme.colors.primary
        )
    }
}

@Preview(showBackground = false)
@Composable
private fun StarsContainerPreview() {
    AppTheme {
        StarsContainer()
    }
}

@Preview(showBackground = true)
@Composable
private fun AskForRatingPreview() {
    AppTheme {
        AskForRating(
            onRateNowClicked = {},
            onAskMeLaterClicked = {},
            onNeverAskAgainClicked = {}
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun AskWhichRatingPreview() {
    AppTheme {
        AskWhichRating(
            onSadClicked = {},
            onNaturalClicked = {},
            onHappyClicked = {}
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun AskForFeedbackPreview() {
    AppTheme {
        AskForFeedback(onGiveFeedbackClicked = { }, onCloseClicked = { })
    }
}

@Preview(showBackground = true)
@Composable
private fun RatingDialogPreview() {
    AppTheme {
        RatingDialog(
            uiState = InAppRatingUiState(inAppRatingType = InAppRatingType.AskForRating),
            onCloseClicked = {},
            onSadClicked = {},
            onHappyClicked = {},
            onNaturalClicked = {},
            onNeverAskAgainClicked = {},
            onAskMeLaterClicked = {},
            onRateNowClicked = {},
            onGiveFeedbackClicked = {}
        )
    }
}

class TriangleShape : Shape {
    override fun createOutline(
        size: Size,
        layoutDirection: LayoutDirection,
        density: Density
    ): Outline {
        val path = Path().apply {
            lineTo(size.width, 0f)
            lineTo(size.width / 2, size.height)
            lineTo(0f, 0f)
            close()
        }
        return Outline.Generic(path)
    }
}
