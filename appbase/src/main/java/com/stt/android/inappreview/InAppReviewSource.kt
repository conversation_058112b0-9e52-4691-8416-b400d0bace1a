package com.stt.android.inappreview

/**
 * Examples of a source:
 * - User visits map screen + taps something there + exists map screen
 * - User visits trends screen + taps something there + exists trends screen
 * - User visits workout details screen + exists the screen
 */
enum class InAppReviewSource(val storageKey: String) {
    HOME("IN_APP_REVIEW_SOURCE_HOME"),
    MAP("IN_APP_REVIEW_SOURCE_MAP"),
    DIARY("IN_APP_REVIEW_SOURCE_DIARY"),
    WORKOUT_DETAILS("IN_APP_REVIEW_SOURCE_WORKOUT_DETAILS")
}
