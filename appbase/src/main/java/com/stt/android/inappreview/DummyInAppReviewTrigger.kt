package com.stt.android.inappreview

import androidx.fragment.app.FragmentManager
import javax.inject.Inject

/**
 * NO-OP, we don't support showing In-app review dialog in ST/Suunto China
 */
class DummyInAppReviewTrigger @Inject constructor() : InAppReviewTrigger {
    override fun canShowInAppReviewDialog(): Boolean = false

    override fun scheduleInAppReviewIfPossible(forceSchedule: Boolean) = Unit

    override fun showInAppReviewDialog(supportFragmentManager: FragmentManager) = Unit

    override fun incNumberOfVisitsForSource(inAppReviewSource: InAppReviewSource) = Unit
}
