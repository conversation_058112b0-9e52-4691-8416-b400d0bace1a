package com.stt.android.inappreview

import androidx.fragment.app.FragmentManager

interface InAppReviewTrigger {
    fun canShowInAppReviewDialog(): Boolean

    /**
     * Schedule the in-app review to be shown to user, the main idea of this function when we need to show the in-app review dialog in another screen on what the user is currently using
     * for example, the user passed through a happy path in the maps screen, but we need to wait him to get back to the home screen to show the dialog
     * another example, we trigger in-app review from braze, and then app is not visible which means we can't show (trigger) the in-app dialog but we schedule it for the next opening
     */
    fun scheduleInAppReviewIfPossible(forceSchedule: Boolean = false)

    /**
     * This function is responsible for showing the dialog if it is scheduled previously using scheduleInAppReview
     */
    fun showInAppReviewDialog(supportFragmentManager: FragmentManager)

    /**
     * We have many source that might be the cause for showing the in-app review dialog, example: User navigates to Map, change map style, leave the screen
     */
    fun incNumberOfVisitsForSource(inAppReviewSource: InAppReviewSource)
}
