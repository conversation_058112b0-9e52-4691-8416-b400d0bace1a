package com.stt.android.inappreview

import android.content.SharedPreferences
import androidx.core.content.edit
import androidx.fragment.app.FragmentManager
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.coroutines.LoggingExceptionHandler
import com.stt.android.utils.STTConstants
import com.stt.android.utils.STTConstants.InAppReviewPreferences.IN_APP_REVIEW_FORCE_SCHEDULE
import com.stt.android.utils.STTConstants.InAppReviewPreferences.KEY_ASK_ME_LATER_CLICKED
import com.stt.android.utils.STTConstants.InAppReviewPreferences.KEY_NEVER_SHOW_AGAIN_CLICKED
import com.stt.android.utils.STTConstants.InAppReviewPreferences.KEY_POSITIVE_FEELING_CLICKED
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import java.util.concurrent.TimeUnit

abstract class BaseInAppReviewTrigger(
    private val sharedPreferences: SharedPreferences,
    private val currentUserController: CurrentUserController,
    coroutinesDispatchers: CoroutinesDispatchers,
) : InAppReviewTrigger {
    private val scope =
        CoroutineScope(coroutinesDispatchers.io + SupervisorJob() + LoggingExceptionHandler)

    companion object {
        private val ONE_WEEK_IN_MILLIS = TimeUnit.DAYS.toMillis(7)
        private val ONE_HOUR_IN_MILLIS = TimeUnit.MINUTES.toMillis(60)
        private const val MINIMUM_PATTERN_REPETITIONS_TO_SHOW_THE_DIALOG = 3
    }

    private val hasUserSelectedNeverShowAgain: Boolean
        get() = sharedPreferences.getBoolean(KEY_NEVER_SHOW_AGAIN_CLICKED, false)

    private val hasUserSelectedAskMeLater: Boolean
        get() = sharedPreferences.getBoolean(KEY_ASK_ME_LATER_CLICKED, false)

    private val hasUserSelectedPositiveFeeling: Boolean
        get() = sharedPreferences.getBoolean(KEY_POSITIVE_FEELING_CLICKED, false)

    private val isAppReviewTriggerScheduled: Boolean
        get() = sharedPreferences.getBoolean(
            STTConstants.InAppReviewPreferences.KEY_TRIGGER_IN_APP_REVIEW,
            false
        )

    private val itBeenAWhileSinceAskMeLaterClicked: Boolean
        get() = sharedPreferences.getLong(
            STTConstants.InAppReviewPreferences.KEY_TIMESTAMP_WHEN_ASK_ME_LATER_CLICKED,
            0
        ).plus(ONE_WEEK_IN_MILLIS) < System.currentTimeMillis()

    private val itBeenAWhileSincePositiveFeelingClicked: Boolean
        get() = sharedPreferences.getLong(
            STTConstants.InAppReviewPreferences.KEY_TIMESTAMP_WHEN_POSITIVE_FEELING_CLICKED,
            0
        ).plus(ONE_HOUR_IN_MILLIS) < System.currentTimeMillis()

    private suspend fun canScheduleInAppReview(): Boolean {
        return when {
            isAppRatingSuggestionsDisabled() -> false
            !currentUserController.isLoggedIn -> false
            !isEnabledForBrand() -> false
            !isAnOldUser() -> false
            !hasLoggedInRecently() -> false
            !hasUsedTheAppManyTimes() -> false
            visitedWorkoutDetailsManyTimes() -> true
            interactedWithDiary() -> true
            interactedWithMap() -> true
            else -> false
        }
    }

    override fun canShowInAppReviewDialog(): Boolean = when {
        isForceSchedule() -> true
        isAppRatingSuggestionsDisabled() -> false
        hasUserSelectedNeverShowAgain -> false
        hasUserSelectedAskMeLater && !itBeenAWhileSinceAskMeLaterClicked -> false
        hasUserSelectedPositiveFeeling && !itBeenAWhileSincePositiveFeelingClicked -> false // We don't show the dialog directly after asking the user's own opinion about the app
        else -> isAppReviewTriggerScheduled
    }

    /**
     * @param forceSchedule: In case we need to force scheduling the in-app dialog (example: from braze)
     */
    override fun scheduleInAppReviewIfPossible(forceSchedule: Boolean) {
        scope.launch {
            if (!forceSchedule && !canScheduleInAppReview()) return@launch

            if (forceSchedule) {
                sharedPreferences.edit {
                    putBoolean(IN_APP_REVIEW_FORCE_SCHEDULE, true)
                }
            }

            sharedPreferences.edit {
                putBoolean(
                    STTConstants.InAppReviewPreferences.KEY_TRIGGER_IN_APP_REVIEW,
                    true
                )
            }
        }
    }

    override fun incNumberOfVisitsForSource(inAppReviewSource: InAppReviewSource) {
        // isAppRatingSuggestionsDisabledForSuunto is for that specific user
        if (isAppRatingSuggestionsDisabled()) return // There is no point to keep incrementing the number of visits if the rating is disabled for that user

        sharedPreferences.edit {
            putInt(
                inAppReviewSource.storageKey,
                sharedPreferences.getInt(inAppReviewSource.storageKey, 0).plus(1)
            )
        }
    }

    override fun showInAppReviewDialog(supportFragmentManager: FragmentManager) {
        InAppRatingFragment.show(
            parentFragmentManager = supportFragmentManager
        )
    }

    private fun isAnOldUser(): Boolean {
        val createdDate = currentUserController.currentUser.createdDate ?: return true // If the createdDate is null, we don't risk showing the dialog the new user

        return System.currentTimeMillis() - createdDate >= 5f * ONE_WEEK_IN_MILLIS
    }

    private fun hasLoggedInRecently(): Boolean {
        val lastLogin = currentUserController.currentUser.lastLogin ?: return false

        return System.currentTimeMillis() - lastLogin < 2f * ONE_WEEK_IN_MILLIS
    }

    private fun hasUsedTheAppManyTimes(): Boolean =
        sharedPreferences.getInt(InAppReviewSource.HOME.storageKey, 0) >= 10

    private fun visitedWorkoutDetailsManyTimes(): Boolean =
        sharedPreferences.getInt(
            InAppReviewSource.WORKOUT_DETAILS.storageKey,
            0
        ) >= MINIMUM_PATTERN_REPETITIONS_TO_SHOW_THE_DIALOG

    private fun interactedWithDiary(): Boolean =
        sharedPreferences.getInt(
            InAppReviewSource.DIARY.storageKey,
            0
        ) >= MINIMUM_PATTERN_REPETITIONS_TO_SHOW_THE_DIALOG

    private fun interactedWithMap(): Boolean =
        sharedPreferences.getInt(
            InAppReviewSource.MAP.storageKey,
            0
        ) >= MINIMUM_PATTERN_REPETITIONS_TO_SHOW_THE_DIALOG

    private fun isForceSchedule(): Boolean =
        sharedPreferences.getBoolean(IN_APP_REVIEW_FORCE_SCHEDULE, false)

    abstract suspend fun isEnabledForBrand(): Boolean
    abstract fun isAppRatingSuggestionsDisabled(): Boolean
}
