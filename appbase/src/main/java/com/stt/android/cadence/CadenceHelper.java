package com.stt.android.cadence;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.stt.android.utils.STTConstants;

public class Cadence<PERSON><PERSON>per {
    @SuppressLint("MissingPermission")
    public static void saveCadence(@NonNull Context context, @NonNull BluetoothDevice device) {
        getSharedPreferences(context).edit()
            .putString(STTConstants.BluetoothPreferences.KEY_LAST_CADENCE_ADDR, device.getAddress())
            .putString(STTConstants.BluetoothPreferences.KEY_LAST_CADENCE_NAME, device.getName())
            .apply();
    }

    @NonNull
    private static SharedPreferences getSharedPreferences(@NonNull Context context) {
        return context.getSharedPreferences(STTConstants.BluetoothPreferences.PREFS_NAME,
            Context.MODE_PRIVATE);
    }

    public static boolean hasKnownCadence(@NonNull Context context) {
        return !TextUtils.isEmpty(getSharedPreferences(context).getString(
            STTConstants.BluetoothPreferences.KEY_LAST_CADENCE_ADDR, null));
    }

    @Nullable
    public static String getKnownCadenceAddress(@NonNull Context context) {
        return getSharedPreferences(context).getString(
            STTConstants.BluetoothPreferences.KEY_LAST_CADENCE_ADDR, null);
    }

    @Nullable
    public static String getKnownCadenceName(@NonNull Context context) {
        return getSharedPreferences(context).getString(
            STTConstants.BluetoothPreferences.KEY_LAST_CADENCE_NAME, null);
    }

    public static float getTotalDistance(Context context, int wheelCircumference) {
        int totalWheelRevolution = getSharedPreferences(context).getInt(
            STTConstants.BluetoothPreferences.KEY_LAST_CADENCE_TOTAL_WHEEL_REVOLUTION, 0);
        return wheelCircumference * totalWheelRevolution / 1000;
    }

    public static void saveTotalWheelRevolution(Context context, int totalWheelRevolution) {
        getSharedPreferences(context).edit()
            .putInt(STTConstants.BluetoothPreferences.KEY_LAST_CADENCE_TOTAL_WHEEL_REVOLUTION,
                totalWheelRevolution)
            .apply();
    }
}
