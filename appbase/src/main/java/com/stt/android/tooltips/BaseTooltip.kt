package com.stt.android.tooltips

import android.content.Context
import android.content.SharedPreferences
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import androidx.core.content.edit
import com.github.xizzhu.simpletooltip.ToolTipView
import com.stt.android.R
import com.stt.android.ui.utils.ToolTipHelper
import com.stt.android.utils.STTConstants.TooltipPreferences.KEY_SHOULD_SHOW_TRACKING_CO2_EMISSIONS_REDUCED_TOOLTIP
import timber.log.Timber
import java.lang.ref.WeakReference

open class BaseTooltip constructor(private val tooltipPreferences: SharedPreferences) {

    internal fun shouldShowTooltip(key: String): Boolean = tooltipPreferences.getBoolean(key, false)

    internal fun markToolTipAsShown(context: Context, key: String) {
        ToolTipHelper.markToolTipAsShown(
            context,
            key
        )
        tooltipPreferences.edit {
            putBoolean(key, false)
        }
    }

    fun showTrackingCO2EmissionsReducedTooltipIfNeeded(
        targetView: View,
        parentLayout: ViewGroup,
        forceShow: Boolean = false
    ): ToolTipView? {
        val targetViewRef = WeakReference(targetView)
        try {
            val alreadyShown =
                ToolTipHelper.hasShownToolTip(parentLayout.context, ToolTipHelper.KEY_TRACK_CO2_EMISSIONS_REDUCED_TOOLTIP)
            if (forceShow || (shouldShowTooltip(KEY_SHOULD_SHOW_TRACKING_CO2_EMISSIONS_REDUCED_TOOLTIP) && !alreadyShown)) {
                markToolTipAsShown(
                    parentLayout.context,
                    ToolTipHelper.KEY_TRACK_CO2_EMISSIONS_REDUCED_TOOLTIP
                )
                return ToolTipHelper.createToolTipView(
                    parentLayout.context,
                    targetViewRef.get(),
                    parentLayout,
                    R.string.tool_tip_start_tracking_saved_co2e,
                    Gravity.TOP
                ).apply {
                    show()
                }
            }
        } catch (exception: Exception) {
            Timber.w(exception, "Show tracking-co2-emissions-reduced tooltip failed")
        }

        return null
    }

    fun showDashboardCustomizationTooltip(
        targetView: View,
        parentLayout: ViewGroup
    ): ToolTipView {
        return ToolTipHelper.createToolTipView(
            parentLayout.context,
            targetView,
            parentLayout,
            R.string.dashboard_long_press_to_customize_tooltip,
            Gravity.BOTTOM
        ).apply {
            show()
        }
    }
}
