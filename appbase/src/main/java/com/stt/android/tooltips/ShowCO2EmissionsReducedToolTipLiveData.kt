package com.stt.android.tooltips

import androidx.lifecycle.LiveData
import com.stt.android.ui.utils.SingleLiveEvent
import dagger.hilt.android.scopes.ActivityScoped
import javax.inject.Inject

@ActivityScoped
class ShowCO2EmissionsReducedToolTipLiveData @Inject constructor() {
    private val _showTooltip = SingleLiveEvent<Boolean>()
    val showTooltip: LiveData<Boolean>
        get() = _showTooltip

    fun sendShowTooltipEvent(forceShowing: Boolean = false) {
        _showTooltip.value = forceShowing
    }
}
