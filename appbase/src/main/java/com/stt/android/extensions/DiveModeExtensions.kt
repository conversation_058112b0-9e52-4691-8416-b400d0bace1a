package com.stt.android.extensions

import android.content.res.Resources
import androidx.annotation.StringRes
import com.stt.android.R
import com.stt.android.domain.workouts.extensions.DiveMode

val DiveMode.locString: Int
    @StringRes get() = when (this) {
        DiveMode.Gauge -> R.string.dive_gauge_mode
        DiveMode.Free -> R.string.dive_free_mode
        DiveMode.Air -> R.string.dive_air_mode
        DiveMode.EAN -> R.string.dive_ean_mode
        DiveMode.Mixed -> R.string.dive_mixed_mode
        DiveMode.CCR -> R.string.dive_ccr_mode
        DiveMode.Nitrox -> R.string.dive_nitrox_mode
        DiveMode.Trimix -> R.string.dive_trimix_mode
        DiveMode.CCRNitrox -> R.string.dive_ccr_nitrox_mode
        DiveMode.CCRTrimix -> R.string.dive_ccr_trimix_mode
    }

fun DiveMode.diveModeLocalized(resources: Resources): String {
    return try {
        resources.getString(this.locString)
    } catch (exception: Resources.NotFoundException) {
        this.value
    }
}
