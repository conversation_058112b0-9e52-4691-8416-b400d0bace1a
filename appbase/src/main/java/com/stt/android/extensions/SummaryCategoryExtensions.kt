package com.stt.android.extensions

import android.content.Context
import android.content.res.Resources
import androidx.annotation.StringRes
import com.stt.android.core.R
import com.stt.android.core.domain.AdvancedLapsSelectDataCategoryItem
import com.stt.android.infomodel.SummaryCategory

@StringRes
fun AdvancedLapsSelectDataCategoryItem.stringRes(): Int = when (this) {
    is AdvancedLapsSelectDataCategoryItem.Summary -> when (summaryCategory) {
        SummaryCategory.CADENCE -> R.string.summary_item_category_cadence
        SummaryCategory.DISTANCE -> R.string.summary_item_category_distance
        SummaryCategory.DIVE -> R.string.summary_item_category_dive
        SummaryCategory.DURATION -> R.string.summary_item_category_duration
        SummaryCategory.HEARTRATE -> R.string.summary_item_category_heartRate
        SummaryCategory.HUNTINGANDFISHING -> 0
        SummaryCategory.OTHER -> R.string.summary_item_category_other
        SummaryCategory.PHYSIOLOGY -> R.string.summary_item_category_physiology
        SummaryCategory.POWER -> R.string.summary_item_category_power
        SummaryCategory.SPEEDANDPACE -> R.string.summary_item_category_speed_and_pace
        SummaryCategory.VERTICAL -> R.string.summary_item_category_vertical
        SummaryCategory.BREATH -> R.string.summary_item_category_breath
        SummaryCategory.HEADANGLE -> R.string.summary_item_category_head_angle
    }
    is AdvancedLapsSelectDataCategoryItem.SuuntoPlus -> R.string.suunto_plus_category
}

fun AdvancedLapsSelectDataCategoryItem.localize(context: Context): String {
    return try {
        context.getString(this.stringRes())
    } catch (exception: Resources.NotFoundException) {
        this.key
    }
}
