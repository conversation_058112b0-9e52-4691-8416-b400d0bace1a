package com.stt.android.extensions

import androidx.annotation.StringRes
import com.stt.android.R
import com.stt.android.infomodel.SummaryGroup

@get:StringRes
val SummaryGroup.getResId: Int
    get() = when (this) {
        SummaryGroup.DEPTH -> R.string.summary_group_depth
        SummaryGroup.DURATION -> R.string.summary_group_duration
        SummaryGroup.DISTANCE -> R.string.summary_group_distance
        SummaryGroup.PACE -> R.string.summary_group_pace
        SummaryGroup.ELEVATION -> R.string.summary_group_elevation
        SummaryGroup.SPEED -> R.string.summary_group_speed
        SummaryGroup.VERTICALSPEED -> R.string.summary_group_verticalspeed
        SummaryGroup.POWER -> R.string.summary_group_power
        SummaryGroup.HEARTRATE -> R.string.summary_group_heartrate
        SummaryGroup.PHYSIOLOGY -> R.string.summary_group_physiology
        SummaryGroup.CLIMBS -> R.string.summary_group_climbs
        SummaryGroup.DOWNHILL -> R.string.summary_group_downhill
        SummaryGroup.TECHNIQUE -> R.string.summary_group_technique
        SummaryGroup.CONFIGURATION -> R.string.summary_group_configuration
        SummaryGroup.GAS -> R.string.summary_group_gas
        SummaryGroup.CATCH -> R.string.summary_group_catch
        SummaryGroup.CONDITIONS -> R.string.summary_group_conditions
        SummaryGroup.EXPERIENCE -> R.string.summary_group_experience
        SummaryGroup.OTHER -> R.string.summary_group_other
    }
