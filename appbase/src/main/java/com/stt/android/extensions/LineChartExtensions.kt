package com.stt.android.extensions

import android.content.res.Resources
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.view.View
import androidx.annotation.ColorInt
import androidx.annotation.ColorRes
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import com.github.mikephil.charting.charts.BarLineChartBase
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.BarLineScatterCandleBubbleData
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.interfaces.datasets.IBarLineScatterCandleBubbleDataSet
import com.stt.android.FontRefs
import com.stt.android.R
import com.stt.android.ThemeColors
import com.stt.android.extensions.LineChartExtensionConstants.GRAPH_AXIS_LABEL_FONT_SIZE_DP
import com.stt.android.extensions.LineChartExtensionConstants.GRAPH_LINE_WIDTH_DEFAULT_DP
import com.stt.android.extensions.LineChartExtensionConstants.GRAPH_LINE_WIDTH_MONTHLY_DP
import com.stt.android.extensions.LineChartExtensionConstants.GRAPH_LINE_WIDTH_YEARLY_DP
import timber.log.Timber
import kotlin.math.roundToInt

typealias LineChartBaseParam = BarLineScatterCandleBubbleData<out IBarLineScatterCandleBubbleDataSet<out Entry>>

fun <T : LineChartBaseParam> BarLineChartBase<T>.drawPhaseIndicatorBar(
    canvas: Canvas,
    widthInPixels: Float,
    minYValue: Float,
    maxYValue: Float,
    paint: Paint,
) {
    canvas.drawRect(
        graphStartX,
        yForValue(maxYValue),
        graphStartX + widthInPixels,
        yForValue(minYValue),
        paint
    )
}

fun <T : LineChartBaseParam> BarLineChartBase<T>.drawPhaseIndicatorBarSeparator(
    canvas: Canvas,
    widthInPixels: Float,
    separatorHeight: Float,
    yValue: Float,
    paint: Paint,
) {
    canvas.drawRect(
        graphStartX,
        yForValue(yValue) + separatorHeight / 2f,
        graphStartX + widthInPixels,
        yForValue(yValue) - separatorHeight / 2f,
        paint
    )
}

fun <T : LineChartBaseParam> BarLineChartBase<T>.drawHighlightForRegion(
    canvas: Canvas,
    xMinValue: Float,
    yMinValue: Float,
    xMaxValue: Float,
    yMaxValue: Float,
    paint: Paint
) {
    canvas.drawRect(
        xForValue(xMinValue),
        yForValue(yMaxValue),
        xForValue(xMaxValue),
        yForValue(yMinValue),
        paint
    )
}

fun View.fillPaint(@ColorRes colorRes: Int) = Paint().apply {
    color = ContextCompat.getColor(context, colorRes)
    style = Paint.Style.FILL
}

val <T : LineChartBaseParam> BarLineChartBase<T>.graphStartX: Float
    get() = xForValue(0f)

fun <T : LineChartBaseParam> BarLineChartBase<T>.graphEndX(
    valueCount: Int
): Float =
    xForValue(valueCount.minus(1).toFloat())

fun <T : LineChartBaseParam> BarLineChartBase<T>.yForValue(
    value: Float
): Float =
    getTransformer(YAxis.AxisDependency.RIGHT).getPixelForValues(0f, value).y.toFloat()

fun <T : LineChartBaseParam> BarLineChartBase<T>.xForValue(
    value: Float
): Float =
    getTransformer(YAxis.AxisDependency.RIGHT).getPixelForValues(value, 0f).x.toFloat()

fun LineChart.setupXAxisAndYAxisOnRight() {
    minOffset = 0f
    extraRightOffset = 0f
    extraLeftOffset = 16f
    extraBottomOffset = 16f
    extraTopOffset = 8f
    axisRight.xOffset = 12f
    description.isEnabled = false
    legend.isEnabled = false
    axisLeft.isEnabled = false
    axisRight.setDrawAxisLine(false)
    xAxis.position = XAxis.XAxisPosition.BOTTOM
    xAxis.setDrawGridLines(false)
    try {
        val typeface = ResourcesCompat.getFont(
            context,
            FontRefs.CHART_FONT_REF
        )
        if (typeface != null) {
            axisLeft.textSize = GRAPH_AXIS_LABEL_FONT_SIZE_DP
            axisLeft.typeface = typeface
            axisRight.typeface = typeface
            axisRight.textSize = GRAPH_AXIS_LABEL_FONT_SIZE_DP
            xAxis.typeface = typeface
            xAxis.textSize = GRAPH_AXIS_LABEL_FONT_SIZE_DP
        }
    } catch (e: Resources.NotFoundException) {
        Timber.w(e, "Unable to set TSS chart font")
    }
}

fun LineChart.disableGestures() {
    isDoubleTapToZoomEnabled = false
    setScaleEnabled(false)
}

fun LineChart.buildDataSet(
    entries: List<Float>,
    @ColorInt color: Int,
    xOffset: Int = 0,
    isFilled: Boolean = false,
    @ColorRes fillColor: Int = com.stt.android.core.R.color.suunto_unspecified
) = LineDataSet(
    entries.mapIndexed { index, value -> Entry((index + xOffset).toFloat(), value) },
    ""
).apply {
    this.color = if (isFilled) Color.TRANSPARENT else color
    this.fillColor = ContextCompat.getColor(context, fillColor)
    setDrawFilled(isFilled)
    setDrawValues(false)
    setDrawCircles(false)
    mode = LineDataSet.Mode.LINEAR
    setDrawHighlightIndicators(true)
    highLightColor = ThemeColors.primaryTextColor(context)
    highlightLineWidth = 1.0f
    setDrawHorizontalHighlightIndicator(false)
    axisDependency = YAxis.AxisDependency.RIGHT
    val numDays = entries.size + xOffset
    // Line width depends on number of days shown in graph
    lineWidth = when {
        numDays > 2 * 365 -> GRAPH_LINE_WIDTH_YEARLY_DP // Yearly graph
        numDays > 365 -> GRAPH_LINE_WIDTH_MONTHLY_DP // Monthly graph
        else -> GRAPH_LINE_WIDTH_DEFAULT_DP // Default
    }
}

val LineChart.currentlyHighlightedIndex: Int?
    get() = highlighted?.firstOrNull()?.x?.roundToInt()

fun LineChart.highlightIndex(index: Int?) {
    if (index != null) {
        highlightValue(index.toFloat(), 0)
    } else {
        highlightValues(emptyArray())
    }
}

private object LineChartExtensionConstants {
    const val GRAPH_LINE_WIDTH_DEFAULT_DP = 2.0f
    const val GRAPH_LINE_WIDTH_MONTHLY_DP = 1.5f
    const val GRAPH_LINE_WIDTH_YEARLY_DP = 1.0f
    const val GRAPH_AXIS_LABEL_FONT_SIZE_DP = 12f
}
