package com.stt.android.extensions

import kotlin.math.roundToInt

/**
 * Converts the given index of the source range into an index in the target range.
 * E.g. 0..2.convert(1, 0..4) == 2, applying to the whole range results in [0, 2, 4].
 */
fun IntRange.convert(index: Int, target: IntRange): Int {
    val ratio = index.toFloat() / (endInclusive - start)
    return (ratio * (target.last - target.first)).roundToInt()
}
