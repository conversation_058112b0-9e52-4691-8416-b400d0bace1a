package com.stt.android.extensions

import androidx.annotation.StringRes
import com.amersports.formatter.Unit
import com.stt.android.R
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.weather.WeatherConditions
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import kotlin.math.roundToInt

val WeatherConditions?.weatherDrawableRes: Int?
    get() = this?.run {
        // See https://openweathermap.org/weather-conditions
        when (weatherIcon) {
            "01d", "01n" -> R.drawable.ic_weather_clear_sky_fill
            "02d", "02n" -> R.drawable.ic_weather_few_clouds_fill
            "03d", "03n" -> R.drawable.ic_weather_scattered_clouds_fill
            "04d", "04n" -> R.drawable.ic_weather_broken_clouds_fill
            "09d", "09n" -> R.drawable.ic_weather_shower_rain_fill
            "10d", "10n" -> R.drawable.ic_weather_rain_fill
            "11d", "11n" -> R.drawable.ic_weather_thunderstorm_fill
            "13d", "13n" -> R.drawable.ic_weather_snow_fill
            "50d", "50n" -> R.drawable.ic_weather_mist_fill
            else -> null
        }
    }

fun WeatherConditions?.getWindSpeedText(
    infoModelFormatter: InfoModelFormatter
): String? = this?.windSpeed?.run {
    val value: String?

    @StringRes val unitRes: Int?
    val unitString: String?
    if (infoModelFormatter.unit == MeasurementUnit.METRIC) {
        // Format wind speed as meters per second when using metric units (SIM formatter
        // does not have format for wind speed yet).
        value = this.roundToInt().toString()
        unitString = null
        unitRes = InfoModelFormatter.getUnitResId(Unit.M_PER_S)
    } else {
        // Use SIM formatter to format speed (mph)
        val workoutValue = infoModelFormatter.formatValue(SummaryItem.AVGSPEED, this)
        value = workoutValue.value
        unitString = workoutValue.unitString
        unitRes = workoutValue.unit
    }
    val unitStringValue = unitString ?: unitRes?.let { infoModelFormatter.getString(it) } ?: ""
    "$value $unitStringValue".trim()
}
