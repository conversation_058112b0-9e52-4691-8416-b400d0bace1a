import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.domain.sml.SmlTimedExtensionStreamPoint
import com.stt.android.domain.workout.ActivityType

/**
 * This helps when you want to sanitise/filter stroke rate values.
 * We want for swimming and open water swimming to filter stroke rates that are above 100
 * Stroke rate above 100/min is impossible for swimming and sometimes the watch produces those outliers
 */
private fun sanitiseStrokeRatePoints(value: Float?, activityType: ActivityType): Boolean {
    if (value == null) {
        return true
    }
    // If we have an max stroke rate we should use that
    return when (activityType.id) {
        CoreActivityType.SWIMMING.id, CoreActivityType.OPENWATER_SWIMMING.id -> value < 100
        else -> true
    }
}

fun List<SmlTimedExtensionStreamPoint>?.sanitiseStokeRateStreamPoints(activityType: ActivityType): List<SmlTimedExtensionStreamPoint> {
    if (this == null) return emptyList()

    return this.filter { sanitiseStrokeRatePoints(it.streamPoint.value, activityType) }
}
