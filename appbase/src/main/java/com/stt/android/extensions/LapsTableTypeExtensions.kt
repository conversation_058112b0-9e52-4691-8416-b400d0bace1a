package com.stt.android.extensions

import androidx.annotation.StringRes
import com.stt.android.core.R
import com.stt.android.domain.advancedlaps.LapsTableType

@StringRes
fun LapsTableType.stringRes(): Int = when (this) {
    LapsTableType.MANUAL -> R.string.laps_table_type_manual
    LapsTableType.INTERVAL -> R.string.laps_table_type_interval
    LapsTableType.DURATION_AUTO_LAP -> R.string.laps_table_type_autolap_duration
    LapsTableType.ONE_KM_AUTO_LAP,
    LapsTableType.FIVE_KM_AUTO_LAP,
    LapsTableType.TEN_KM_AUTO_LAP,
    LapsTableType.ONE_MILE_AUTO_LAP,
    LapsTableType.FIVE_MILE_AUTO_LAP,
    LapsTableType.TEN_MILE_AUTO_LAP,
    LapsTableType.DISTANCE_AUTO_LAP -> R.string.laps_table_type_autolap_distance_format
    LapsTableType.DOWNHILL -> R.string.laps_table_type_downhill
    LapsTableType.DIVE -> R.string.laps_table_type_dive
}
