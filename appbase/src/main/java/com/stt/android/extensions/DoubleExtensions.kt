package com.stt.android.extensions

import kotlin.math.roundToInt
import kotlin.math.roundToLong

/**
 * Rounds this [Double] value to the nearest integer and converts the result to [Int].
 * NaN value is converted to zero.
 */
fun Double.roundToIntOrZero(): Int = when {
    isNaN() -> 0
    else -> roundToInt()
}

/**
 * Rounds this [Double] value to the nearest integer and converts the result to [Long].
 * NaN value is converted to zero.
 */
fun Double.roundToLongOrZero(): Long = when {
    isNaN() -> 0L
    else -> roundToLong()
}
