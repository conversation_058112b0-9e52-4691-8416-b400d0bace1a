package com.stt.android.extensions

import com.google.android.gms.maps.model.LatLng
import com.stt.android.data.TimeUtils
import com.stt.android.domain.workout.SharingOption
import com.stt.android.domain.workout.WorkoutData
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.ui.controllers.WorkoutDataLoaderController
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.time.format.DateTimeFormatter
import kotlin.coroutines.resume

val WorkoutHeader.fitFilename: String
    get() = "$workoutFilename.fit"

val WorkoutHeader.jsonFilename: String
    get() = "$workoutFilename.json"

private val WorkoutHeader.workoutFilename: String
    get() {
        val activityName: String = activityType.simpleName
        // rounding millis
        val startTime: Long = startTime / 1000 * 1000
        val time = TimeUtils.epochToLocalZonedDateTime(startTime)
            .format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
            .replace(':', '_')
        return "${activityName}_$time"
    }

val WorkoutHeader.isShared: Boolean
    get() = this.sharingFlags and SharingOption.SHARED_MASK != 0

val WorkoutHeader.startOrStopPositionIgnoreNullIsland: LatLng?
    get() {
        val startPosition = startPosition
        val stopPosition = stopPosition
        if (startPosition?.isOrigin == false) {
            return LatLng(startPosition.latitude, startPosition.longitude)
        }
        if (stopPosition?.isOrigin == false) {
            return LatLng(stopPosition.latitude, stopPosition.longitude)
        }
        return null
    }

suspend fun WorkoutHeader.getWorkoutData(workoutDataLoaderController: WorkoutDataLoaderController): WorkoutData? =
    withContext(Dispatchers.IO) {
        suspendCancellableCoroutine { continuation ->
            workoutDataLoaderController.loadWorkout(
                this@getWorkoutData,
                object : WorkoutDataLoaderController.Listener {
                    override fun onWorkoutDataLoaded(
                        workoutId: Int,
                        workoutData: WorkoutData?
                    ) {
                        if (continuation.isActive) {
                            continuation.resume(workoutData)
                        }
                    }

                    override fun onWorkoutDataLoadFailed(workoutId: Int) {
                        Timber.w("Failed to load WorkoutData for workoutId:$workoutId")
                        if (continuation.isActive) {
                            continuation.resume(null)
                        }
                    }
                }
            )
        }
    }
