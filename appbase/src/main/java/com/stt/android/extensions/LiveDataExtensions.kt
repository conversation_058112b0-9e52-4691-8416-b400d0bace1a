package com.stt.android.extensions

import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData

/**
 * Combine two LiveDatas as a Pair. Will emit null value whenever either of the sources emit null.
 */
fun <A, B> combineLatest(a: LiveData<A>, b: LiveData<B>): LiveData<Pair<A, B>> {
    return MediatorLiveData<Pair<A, B>>().apply {
        var lastA: A? = null
        var lastB: B? = null

        addSource(a) {
            if (it == null && value != null) value = null
            lastA = it
            if (lastA != null && lastB != null) value = lastA!! to lastB!!
        }

        addSource(b) {
            if (it == null && value != null) value = null
            lastB = it
            if (lastA != null && lastB != null) value = lastA!! to lastB!!
        }
    }
}

/**
 * Combine two LiveDatas as a Pair. The values in the resulting Pair are nullable and may
 * contain null values if sources emit nulls or have not yet emitted a value.
 */
fun <A, B> combineLatestAllowNulls(
    a: LiveData<A>,
    b: LiveData<B>
): LiveData<Pair<A?, B?>> {
    return MediatorLiveData<Pair<A?, B?>>().apply {
        var lastA: A? = null
        var lastB: B? = null

        fun emit() {
            value = lastA to lastB
        }

        addSource(a) {
            lastA = it
            emit()
        }

        addSource(b) {
            lastB = it
            emit()
        }
    }
}

/**
 * Combine three LiveDatas as a Triple. Will emit null value whenever any of the sources emit null.
 */
fun <A, B, C> combineLatest(
    a: LiveData<A>,
    b: LiveData<B>,
    c: LiveData<C>
): LiveData<Triple<A, B, C>> {
    return MediatorLiveData<Triple<A, B, C>>().apply {
        var lastA: A? = null
        var lastB: B? = null
        var lastC: C? = null

        fun emitIfNotNull() {
            if (lastA != null && lastB != null && lastC != null) {
                value = Triple(lastA!!, lastB!!, lastC!!)
            }
        }

        addSource(a) {
            if (it == null && value != null) value = null
            lastA = it
            emitIfNotNull()
        }

        addSource(b) {
            if (it == null && value != null) value = null
            lastB = it
            emitIfNotNull()
        }

        addSource(c) {
            if (it == null && value != null) value = null
            lastC = it
            emitIfNotNull()
        }
    }
}

/**
 * Combine three LiveDatas as a Triple. The values in the resulting Triple are nullable and may
 * contain null values if sources emit nulls or have not yet emitted a value.
 */
fun <A, B, C> combineLatestAllowNulls(
    a: LiveData<A>,
    b: LiveData<B>,
    c: LiveData<C>
): LiveData<Triple<A?, B?, C?>> {
    return MediatorLiveData<Triple<A?, B?, C?>>().apply {
        var lastA: A? = null
        var lastB: B? = null
        var lastC: C? = null

        fun emit() {
            value = Triple(lastA, lastB, lastC)
        }

        addSource(a) {
            lastA = it
            emit()
        }

        addSource(b) {
            lastB = it
            emit()
        }

        addSource(c) {
            lastC = it
            emit()
        }
    }
}

/**
 * Combine a number of LiveData objects of same type into a single LiveData that emits a list
 * any time any of the source LiveData objects emit a new value. If any of the source LiveData
 * values is null, this won't emit anything.
 */
fun <T : Any> combineLatestAsList(vararg liveData: LiveData<T>): LiveData<List<T>> {
    return MediatorLiveData<List<T>>().apply {
        val lastValues: MutableList<T?> = liveData.map { it.value }.toMutableList()

        liveData.forEachIndexed { index, data ->
            addSource(data) { newValue ->
                lastValues[index] = newValue

                val nonNulls = lastValues.filterNotNull()
                if (nonNulls.size == liveData.size) {
                    // Only emit values if all source values are non-null
                    value = nonNulls
                }
            }
        }
    }
}
