package com.stt.android.extensions

import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.pictures.Picture
import com.stt.android.domain.workouts.videos.Video
import kotlin.math.roundToLong

fun Picture.bindToWorkout(workout: WorkoutHeader, index: Int): Picture =
    copy(
        workoutId = workout.id,
        workoutKey = workout.key,
        totalTime = workout.totalTime,
        username = workout.username,
        indexInWorkoutHeader = index,
    )

fun Video.bindToWorkout(workout: WorkoutHeader): Video =
    copy(
        workoutId = workout.id,
        workoutKey = workout.key,
        totalTime = workout.totalTime.roundToLong(),
        location = workout.stopPosition,
        username = workout.username,
    )
