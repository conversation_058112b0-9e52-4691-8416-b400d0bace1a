package com.stt.android.extensions

import androidx.annotation.StringRes
import com.stt.android.R
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.domain.localization.Localizable

val GraphTimeRange.stringRes: Int
    @StringRes
    get() = when (this) {
        GraphTimeRange.THIRTEEN_MONTHS -> R.string.diary_graph_13_months
        GraphTimeRange.EIGHT_WEEKS -> R.string.diary_graph_8_weeks
        GraphTimeRange.EIGHT_MONTHS -> R.string.diary_graph_8_months
        GraphTimeRange.EIGHT_YEARS -> R.string.diary_graph_8_years
        GraphTimeRange.CURRENT_WEEK -> R.string.diary_graph_week
        GraphTimeRange.CURRENT_MONTH -> R.string.diary_graph_month
        GraphTimeRange.CURRENT_YEAR -> R.string.diary_graph_year
        GraphTimeRange.SIX_WEEKS -> R.string.diary_graph_6_weeks
        GraphTimeRange.SIX_MONTHS -> R.string.diary_graph_6_months
        GraphTimeRange.SEVEN_DAYS -> R.string.diary_graph_7_days
        GraphTimeRange.THIRTY_DAYS -> R.string.diary_graph_30_days
        GraphTimeRange.ONE_YEAR -> R.string.diary_graph_one_year
    }

val GraphTimeRange.shortStringRes: Int
    @StringRes
    get() = when (this) {
        GraphTimeRange.THIRTEEN_MONTHS -> R.string.diary_graph_13_months_short
        GraphTimeRange.EIGHT_WEEKS -> R.string.diary_graph_8_weeks_short
        GraphTimeRange.EIGHT_MONTHS -> R.string.diary_graph_8_months_short
        GraphTimeRange.EIGHT_YEARS -> R.string.diary_graph_8_years_short
        GraphTimeRange.CURRENT_WEEK -> R.string.diary_graph_week_short
        GraphTimeRange.CURRENT_MONTH -> R.string.diary_graph_month_short
        GraphTimeRange.CURRENT_YEAR -> R.string.diary_graph_year_short
        GraphTimeRange.SIX_WEEKS -> R.string.diary_graph_6_weeks_short
        GraphTimeRange.SIX_MONTHS -> R.string.diary_graph_6_months_short
        GraphTimeRange.SEVEN_DAYS -> R.string.diary_graph_7_days_short
        GraphTimeRange.THIRTY_DAYS -> R.string.diary_graph_30_days_short
        GraphTimeRange.ONE_YEAR -> R.string.diary_graph_one_year_short
    }

val GraphTimeRange.intervalDescriptionRes: Int
    @StringRes
    get() = when (this) {
        GraphTimeRange.CURRENT_WEEK,
        GraphTimeRange.CURRENT_MONTH,
        GraphTimeRange.SEVEN_DAYS,
        GraphTimeRange.THIRTY_DAYS,
        GraphTimeRange.SIX_WEEKS,
        GraphTimeRange.EIGHT_WEEKS -> R.string.diary_graph_daily_interval
        GraphTimeRange.SIX_MONTHS,
        GraphTimeRange.EIGHT_MONTHS -> R.string.diary_graph_weekly_interval
        GraphTimeRange.ONE_YEAR,
        GraphTimeRange.CURRENT_YEAR,
        GraphTimeRange.THIRTEEN_MONTHS -> R.string.diary_graph_monthly_interval
        GraphTimeRange.EIGHT_YEARS -> R.string.diary_graph_yearly_interval
    }

fun GraphTimeRange.toLocalizable() =
    Localizable { resources ->
        resources.getString(stringRes)
    }
