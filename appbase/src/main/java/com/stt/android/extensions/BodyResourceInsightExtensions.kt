package com.stt.android.extensions

import androidx.annotation.StringRes
import com.stt.android.R
import com.stt.android.domain.sleep.BodyResourcesInsight

@StringRes
fun BodyResourcesInsight?.stringRes(): Int? = when (this) {
    BodyResourcesInsight.AWAKE_POOR_HIGH_STRESS -> R.string.awake_poor_high_stress
    BodyResourcesInsight.AWAKE_POOR_HARD_EXERCISE -> R.string.awake_poor_hard_exercise
    BodyResourcesInsight.AWAKE_POOR_LATE_EXERCISE -> R.string.awake_poor_late_exercise
    BodyResourcesInsight.AWAKE_POOR_LATE_BEDTIME -> R.string.awake_poor_late_bedtime
    BodyResourcesInsight.AWAKE_POOR_ACCLIMATIZATION -> R.string.awake_poor_acclimatization
    BodyResourcesInsight.AWAKE_POOR_OTHER -> R.string.awake_poor_other
    BodyResourcesInsight.AWAKE_POOR_BAD_SLEEP -> R.string.awake_poor_bad_sleep
    BodyResourcesInsight.AWAKE_FAIR_HIGH_STRESS -> R.string.awake_fair_high_stress
    BodyResourcesInsight.AWAKE_FAIR_HARD_EXERCISE -> R.string.awake_fair_hard_exercise
    BodyResourcesInsight.AWAKE_FAIR_LATE_EXERCISE -> R.string.awake_fair_late_exercise
    BodyResourcesInsight.AWAKE_FAIR_LATE_BEDTIME -> R.string.awake_fair_late_bedtime
    BodyResourcesInsight.AWAKE_FAIR_ACCLIMATIZATION -> R.string.awake_fair_acclimatization
    BodyResourcesInsight.AWAKE_FAIR_OTHER -> R.string.awake_fair_other
    BodyResourcesInsight.AWAKE_FAIR_BAD_SLEEP -> R.string.awake_fair_bad_sleep
    BodyResourcesInsight.AWAKE_GOOD -> R.string.awake_good
    BodyResourcesInsight.AWAKE_GOOD_BAD_SLEEP -> R.string.awake_good_bad_sleep
    BodyResourcesInsight.AWAKE_GREAT -> R.string.awake_great
    BodyResourcesInsight.AWAKE_GREAT_BAD_SLEEP -> R.string.awake_great_bad_sleep
    BodyResourcesInsight.AWAKE_FULL -> R.string.awake_full
    BodyResourcesInsight.NONE,
    null -> null
}
