package com.stt.android.extensions

import com.stt.android.controllers.ExtensionDataModel
import com.stt.android.coroutines.await
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber

suspend inline fun <reified T : ExtensionDataModel<C>, reified C : WorkoutExtension> T.loadExtension(
    workoutHeader: WorkoutHeader
): C? = withContext(Dispatchers.IO) {
    runSuspendCatching {
        load(workoutHeader)
            .toSingle()
            .await()
    }.onFailure { e ->
        // Not passing the throwable to <PERSON><PERSON> to prevent polluting the log too much
        Timber.w("Loading extension failed for workoutId:${workoutHeader.id} msg=${e.message}")
    }.getOrNull()
}
