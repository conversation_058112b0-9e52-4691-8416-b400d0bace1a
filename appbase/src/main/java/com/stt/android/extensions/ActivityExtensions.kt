package com.stt.android.extensions

import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Context.INPUT_METHOD_SERVICE
import android.content.DialogInterface
import android.content.Intent
import android.os.Build
import android.provider.Settings
import android.view.Display
import android.view.inputmethod.InputMethodManager
import androidx.appcompat.app.AlertDialog
import com.stt.android.R
import timber.log.Timber

fun Activity.hideKeyboard() {
    currentFocus?.let {
        val imm = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager?
        imm?.hideSoftInputFromWindow(it.windowToken, 0)
    }
}

fun Activity.showKeyboard() {
    currentFocus?.let {
        val imm = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager?
        imm?.toggleSoftInput(InputMethodManager.SHOW_FORCED, 0)
    }
}

val Activity.displayCompat: Display?
    get() {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            display
        } else {
            @Suppress("DEPRECATION")
            windowManager.defaultDisplay
        }
    }

fun Activity.openLocationSourceSettings(positiveListener: DialogInterface.OnClickListener? = null, negativeListener: DialogInterface.OnClickListener? = null) {
    val finalPositiveListener = positiveListener ?: DialogInterface.OnClickListener { _, _ ->
        try {
            startActivity(Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS))
        } catch (e: ActivityNotFoundException) {
            Timber.d("Can't open location source provider settings")
        }
    }
    AlertDialog.Builder(this)
        .setTitle(R.string.settings)
        .setMessage(R.string.gps_disabled_enable)
        .setPositiveButton(R.string.settings, finalPositiveListener)
        .setNegativeButton(R.string.cancel, negativeListener)
        .show()
}
