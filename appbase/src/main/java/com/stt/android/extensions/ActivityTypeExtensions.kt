package com.stt.android.extensions

import android.content.Context
import com.amersports.formatter.Unit
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.ActivityGroup
import com.stt.android.home.diary.diarycalendar.activitygroups.ActivityTypeToGroupMapper
import com.stt.android.infomodel.ActivityMapping
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.utils.TextFormatter

val ActivityType.isMultisport: Boolean
    get() = listOf(
        ActivityMapping.TRIATHLON.stId,
        ActivityMapping.DUATHLON.stId,
        ActivityMapping.SWIMRUN.stId,
        ActivityMapping.AQUATHLON.stId,
        ActivityMapping.MULTISPORT.stId
    ).contains(this.id)

val ActivityType.useSkiMapStyle: Boolean
    get() = listOf(
        ActivityMapping.NORDICSKIING.stId,
        ActivityMapping.DOWNHILLSKIING.stId,
        ActivityMapping.ICEHOCKEY.stId,
        ActivityMapping.ICESKATING.stId,
        ActivityMapping.SNOWBOARDING.stId,
        ActivityMapping.SNOWSHOEING.stId,
        ActivityMapping.TELEMARKSKIING.stId,
        ActivityMapping.SKITOURING.stId,
        ActivityMapping.BACKCOUNTRYSKIING.stId,
        ActivityMapping.SPLITBOARDING.stId,
        ActivityMapping.BIATHLON.stId,
        ActivityMapping.SKIMOUNTAINEERING.stId,
        ActivityMapping.SKATESKIING.stId,
        ActivityMapping.CLASSICSKIING.stId,
    ).contains(this.id)

val Int.isRunningSeriesForMcId: Boolean
    get() = listOf(
        ActivityMapping.RUNNING.mcId,
        ActivityMapping.OBSTACLERACING.mcId,
        ActivityMapping.ORIENTEERING.mcId,
        ActivityMapping.TRAILRUNNING.mcId,
        ActivityMapping.TREADMILL.mcId,
        ActivityMapping.TRACKRUNNING.mcId,
        ActivityMapping.VERTICALRUN.mcId,
    ).contains(this)

val Int.isSwimmingSeriesForMcId: Boolean
    get() = listOf(
        ActivityMapping.SWIMMING.mcId,
        ActivityMapping.OPENWATERSWIMMING.mcId,
    ).contains(this)

val Int.isSailingSeriesForMcId: Boolean
    get() = listOf(
        ActivityMapping.SAILING.mcId,
        ActivityMapping.WINDSURFING.mcId,
        ActivityMapping.KITESURFINGKITING.mcId,
    ).contains(this)

val Int.isVerticalSportsForMcId: Boolean
    get() = listOf(
        ActivityMapping.TRAILRUNNING.mcId,
        ActivityMapping.MOUNTAINBIKING.mcId,
        ActivityMapping.TREKKING.mcId,
        ActivityMapping.CLIMBING.mcId,
        ActivityMapping.DOWNHILLSKIING.mcId,
        ActivityMapping.SNOWBOARDING.mcId,
        ActivityMapping.ADVENTURERACING.mcId,
        ActivityMapping.MOUNTAINEERING.mcId,
        ActivityMapping.SKITOURING.mcId,
        ActivityMapping.TELEMARKSKIING.mcId,
        ActivityMapping.SNOWSHOEING.mcId,
        ActivityMapping.PARAGLIDING.mcId,
        ActivityMapping.HIKING.mcId
    ).contains(this)

fun ActivityType.shouldUseShortDistanceUnitForType(): Boolean {
    return listOf(
        ActivityType.FREEDIVING.id
    ).contains(this.id)
}

fun ActivityType.supportsLaps() = this != ActivityType.SCUBADIVING

@JvmOverloads
fun ActivityType.getFormattedDistanceForType(
    measurementUnit: MeasurementUnit,
    distance: Double,
    isForceShortDistance: Boolean = false
): String {
    return when {
        shouldUseShortDistanceUnitForType() || isForceShortDistance -> {
            TextFormatter.formatShortDistance(
                measurementUnit.toShortDistanceUnit(distance),
                measurementUnit
            )
        }
        usesNauticalUnits -> {
            TextFormatter.formatDistance(measurementUnit.fromMetersToNauticalMile(distance))
        }
        else -> {
            TextFormatter.formatDistance(measurementUnit.toDistanceUnit(distance))
        }
    }
}

fun ActivityType.getDistanceUnitTextForType(
    measurementUnit: MeasurementUnit,
    isForceShortDistance: Boolean
): Int {
    return if (isForceShortDistance || shouldUseShortDistanceUnitForType()) {
        measurementUnit.shortDistanceUnit
    } else if (usesNauticalUnits) {
        InfoModelFormatter.getUnitResId(Unit.NMI)!!
    } else {
        measurementUnit.distanceUnit
    }
}

fun Array<ActivityType>.groupAndSort(
    context: Context,
    groupOrder: List<ActivityGroup>
): List<ActivityType> {
    val grouper = ActivityTypeToGroupMapper()
    val map = groupBy { grouper.activityTypeIdToGroup(it.id) }
    return groupOrder.map { group ->
        map.getOrDefault(group, emptyList()).sortedBy { it.getLocalizedName(context.resources) }
    }.flatten()
}

val ActivityType.isTeamAndRacketSports: Boolean
    get() = ActivityTypeToGroupMapper().activityTypeIdToGroup(id) == ActivityGroup.TeamAndRacketSports
