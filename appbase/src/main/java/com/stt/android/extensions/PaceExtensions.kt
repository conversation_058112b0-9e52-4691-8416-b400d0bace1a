
import com.stt.android.domain.sml.SmlTimedExtensionStreamPoint
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workout.WorkoutGeoPoint

/**
 * This helps when you want to sanitise/filter pace values deriving from speed.
 * - Removes all speed values of 0 that result to a pace of infinity or 0
 * - Removes speed values that are close to standstill
 * TODO: Should be more clever, just removing outliers
 */
private fun sanitisePaceGeoPoints(value: Float?, activityType: ActivityType): Boolean {
    return when (value) {
        null -> true
        0.0f -> false
        // When recorded with watch, the lowest value for pace is 40min/km i.e. speed is
        // at min ~0.42 m/s in SML.
        else -> value > 0.1f // Pace of 166mins/km
    }
}

fun List<WorkoutGeoPoint>?.sanitisePaceGeoPoints(activityType: ActivityType): List<WorkoutGeoPoint> {
    if (this == null) return emptyList()

    return this.filter { sanitisePaceGeoPoints(it.speedMetersPerSecond, activityType) }
}

fun List<SmlTimedExtensionStreamPoint>?.sanitisePaceStreamPoints(activityType: ActivityType): List<SmlTimedExtensionStreamPoint> {
    if (this == null) return emptyList()

    return this.filter { sanitisePaceGeoPoints(it.streamPoint.value, activityType) }
}
