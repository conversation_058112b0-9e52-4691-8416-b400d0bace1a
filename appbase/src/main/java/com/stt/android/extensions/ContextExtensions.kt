package com.stt.android.extensions

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.core.app.ActivityCompat
import com.stt.android.R
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.domain.STTErrorCodes
import com.stt.android.utils.isNearbyDevicesPermissionGranted
import timber.log.Timber

fun Context?.getLocalizedErrorMessageRes(error: STTErrorCodes): Int {
    val id = error.getLocalizedMessageRes(this?.resources, this?.packageName)
    return if (id == 0) R.string.error_generic else id
}

fun Context.isNearbyDevicesPermissionGrantedResult(): String {
    val nearbyDevicesGranted = this.isNearbyDevicesPermissionGranted()
    return if (nearbyDevicesGranted) {
        AnalyticsPropertyValue.PermissionResult.ALLOW
    } else {
        AnalyticsPropertyValue.PermissionResult.DONT_AllOW
    }
}

fun Context.hasPermission(permission: String): Boolean {
    return ActivityCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED
}

fun Context.getLocationPermissionResult(): String {
    val hasAccessFineLocationPermission = hasPermission(Manifest.permission.ACCESS_FINE_LOCATION)
    val hasAccessCoarseLocationPermission = hasPermission(Manifest.permission.ACCESS_COARSE_LOCATION)
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        val hasBackgroundLocationPermission = hasPermission(Manifest.permission.ACCESS_BACKGROUND_LOCATION)
        when {
            hasBackgroundLocationPermission -> AnalyticsPropertyValue.PermissionResult.ALLOW
            hasAccessFineLocationPermission -> AnalyticsPropertyValue.PermissionResult.ONLY_WHILE_USING_APP
            hasAccessCoarseLocationPermission -> AnalyticsPropertyValue.PermissionResult.ONLY_COARSE_LOCATION
            else -> AnalyticsPropertyValue.PermissionResult.DONT_AllOW
        }
    } else {
        when {
            hasAccessFineLocationPermission -> AnalyticsPropertyValue.PermissionResult.ALLOW
            hasAccessCoarseLocationPermission -> AnalyticsPropertyValue.PermissionResult.ONLY_COARSE_LOCATION
            else -> AnalyticsPropertyValue.PermissionResult.DONT_AllOW
        }
    }
}

fun Context.openAppSettings() {
    val intent = Intent()
    intent.data = Uri.fromParts("package", packageName, null)
    intent.action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
    try {
        startActivity(intent)
    } catch (e: Exception) {
        Timber.w("Could not open application default settings")
    }
}
