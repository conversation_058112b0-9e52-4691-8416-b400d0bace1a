package com.stt.android.systemwidget

import android.app.Application
import android.content.Context
import android.content.SharedPreferences
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.di.initializer.AppInitializer
import com.stt.android.domain.goaldefinition.GoalDefinitionRepository
import com.stt.android.utils.STTConstants
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class GoalSystemWidgetDataChangeListener @Inject constructor(
    private val sharedPreferences: SharedPreferences,
    private val goalDefinitionRepository: GoalDefinitionRepository,
    coroutinesDispatchers: CoroutinesDispatchers,
) : AppInitializer {
    private val coroutineScope = CoroutineScope(coroutinesDispatchers.io)

    private var sharedPrefsListener: SharedPreferences.OnSharedPreferenceChangeListener? = null
    private var goalDefinitionsChangedJob: Job? = null

    override fun init(app: Application) {
        if (sharedPrefsListener == null) {
            sharedPrefsListener = SharedPreferences.OnSharedPreferenceChangeListener { _, key ->
                if (key == STTConstants.SuuntoPreferences.KEY_SUUNTO_WATCH_WEEKLY_TARGET_DURATION) {
                    sendBroadcastUpdate(app)
                }
            }
            sharedPreferences.registerOnSharedPreferenceChangeListener(sharedPrefsListener)
        }

        if (goalDefinitionsChangedJob == null) {
            goalDefinitionsChangedJob = coroutineScope.launch {
                goalDefinitionRepository.definitionsChangedFlow().collect {
                    sendBroadcastUpdate(app)
                }
            }
        }
    }

    private fun sendBroadcastUpdate(context: Context) {
        GoalDashboardWidgetAsSystemWidgetProvider.sendUpdateBroadcast(context)
    }
}
