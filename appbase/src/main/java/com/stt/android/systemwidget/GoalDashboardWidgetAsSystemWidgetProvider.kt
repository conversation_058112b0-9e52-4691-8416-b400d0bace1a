package com.stt.android.systemwidget

import android.content.Context
import android.util.Size
import com.stt.android.R
import com.stt.android.coroutines.nullOnError
import com.stt.android.home.dashboard.widget.goal.GoalWidget
import com.stt.android.home.dashboard.widget.goal.GoalWidgetData
import com.stt.android.home.dashboard.widget.goal.WeeklyGoalWidgetDataFetcher
import com.stt.android.mapping.InfoModelFormatter
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.first
import javax.inject.Inject

@AndroidEntryPoint
class GoalDashboardWidgetAsSystemWidgetProvider :
    DashboardWidgetAsSystemWidgetProvider<GoalWidgetData>() {
    @Inject
    lateinit var weeklyGoalWidgetDataFetcher: WeeklyGoalWidgetDataFetcher

    @Inject
    lateinit var infoModelFormatter: InfoModelFormatter

    override val widgetType: SystemWidgetType = SystemWidgetType.GOAL
    override val veryLimitedHeightThreshold = R.dimen.progresswidget_very_limited_height_threshold
    override val minimalHeightThreshold = R.dimen.progresswidget_minimal_height_threshold

    override suspend fun fetchWidgetData() =
        weeklyGoalWidgetDataFetcher.weeklyGoalWidgetDataFlow().nullOnError().first()

    override fun createWidgetView(
        widgetData: GoalWidgetData?,
        size: Size,
        context: Context
    ) = GoalWidget(context).apply {
        setWheelAnimationEnabled(false)
        data = widgetData
        measurementUnit = infoModelFormatter.unit
        displayedAsEnabled = true
        customizationModeEnabled = false
        showRemoveButton = false

        unlockSize()
        val heightMode = getHeightMode(size.height, context)
        setWheelHidden(heightMode.ordinal <= HeightMode.LIMITED.ordinal)
        setPeriodAndTargetHidden(heightMode.ordinal <= HeightMode.MINIMAL.ordinal)

        bindProps()
    }

    companion object {
        fun sendUpdateBroadcast(context: Context) {
            sendUpdateBroadcast(context, GoalDashboardWidgetAsSystemWidgetProvider::class)
        }
    }
}
