package com.stt.android.systemwidget

import android.content.SharedPreferences
import androidx.core.content.edit
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent.SYSTEM_WIDGET_ADDED
import com.stt.android.analytics.AnalyticsEvent.SYSTEM_WIDGET_REMOVED
import com.stt.android.analytics.AnalyticsEvent.SYSTEM_WIDGET_RESIZED
import com.stt.android.analytics.AnalyticsEventProperty.SYSTEM_WIDGET_NEW_LAYOUT
import com.stt.android.analytics.AnalyticsEventProperty.SYSTEM_WIDGET_TYPE
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.di.SystemWidgetPreferences
import com.stt.android.utils.STTConstants
import javax.inject.Inject

class SystemWidgetAnalytics @Inject constructor(
    @SystemWidgetPreferences private val sharedPreferences: SharedPreferences,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
) {
    /**
     * There's no event / broadcast when a widget has been added, only when the first widget
     * for type is added. To get around this, we store all previously seen non-deleted widget
     * IDs in shared prefs, and if a widget with unknown ID is updated, we send the analytics
     * event for widget being added and store the ID
     */
    fun onWidgetsUpdated(typeName: String, appWidgetIds: List<Int>) {
        val cachedIds = fetchCachedIdsForWidgetType(typeName)
        val newIds = appWidgetIds.filterNot { cachedIds.contains(it) }

        if (newIds.isNotEmpty()) {
            for (id in newIds) {
                amplitudeAnalyticsTracker.trackEvent(
                    SYSTEM_WIDGET_ADDED,
                    AnalyticsProperties().apply {
                        put(SYSTEM_WIDGET_TYPE, typeName)
                    }
                )
            }

            writeCachedIdsForWidgetType(typeName, cachedIds + newIds)
        }
    }

    fun onWidgetsDeleted(typeName: String, appWidgetIds: List<Int>) {
        for (id in appWidgetIds) {
            amplitudeAnalyticsTracker.trackEvent(
                SYSTEM_WIDGET_REMOVED,
                AnalyticsProperties().apply {
                    put(SYSTEM_WIDGET_TYPE, typeName)
                }
            )
        }

        val idsToCache = fetchCachedIdsForWidgetType(typeName).toMutableList()
        idsToCache.removeAll(appWidgetIds)
        writeCachedIdsForWidgetType(typeName, idsToCache)
    }

    fun onWidgetResized(
        typeName: String,
        heightMode: DashboardWidgetAsSystemWidgetProvider.HeightMode
    ) {
        val analyticsLayout = if (heightMode == DashboardWidgetAsSystemWidgetProvider.HeightMode.FULL) {
            AnalyticsPropertyValue.SystemWidgetLayoutType.NORMAL
        } else {
            AnalyticsPropertyValue.SystemWidgetLayoutType.COMPACT
        }

        amplitudeAnalyticsTracker.trackEvent(
            SYSTEM_WIDGET_RESIZED,
            AnalyticsProperties().apply {
                put(SYSTEM_WIDGET_TYPE, typeName)
                put(SYSTEM_WIDGET_NEW_LAYOUT, analyticsLayout)
            }
        )
    }

    private fun fetchCachedIdsForWidgetType(type: String): List<Int> = sharedPreferences
        .getStringSet(getPrefsKeyForCachedIds(type), emptySet())
        ?.map { it.toInt() }
        ?: emptyList()

    private fun writeCachedIdsForWidgetType(type: String, ids: List<Int>) = sharedPreferences.edit {
        putStringSet(getPrefsKeyForCachedIds(type), ids.map { it.toString() }.toSet())
    }

    private fun getPrefsKeyForCachedIds(type: String) =
        STTConstants.SystemWidgetPreferences.KEY_WIDGET_ID_CACHE_PREFIX + type
}
