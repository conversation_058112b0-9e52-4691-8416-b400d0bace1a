package com.stt.android.systemwidget

import android.app.Application
import android.content.Context
import androidx.work.WorkManager
import com.jakewharton.rxrelay2.PublishRelay
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.di.initializer.AppInitializer
import com.stt.android.glance.DailyOverviewHomeWidget
import com.stt.android.glance.DailyOverviewHomeWidgetReceiver
import com.stt.android.glance.MonthlyCalendarHomeWidget
import com.stt.android.glance.MonthlyCalendarHomeWidgetReceiver
import com.stt.android.glance.ProgressHomeWidget
import com.stt.android.glance.ProgressHomeWidgetReceiver
import com.stt.android.glance.ext.forceUpdateAll
import com.stt.android.glance.ext.updatePreview
import com.stt.android.home.WorkoutBroadcastActionListener
import com.stt.android.utils.FlavorUtils
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class WorkoutSystemWidgetDataChangeListener @Inject constructor(
    val workoutBroadcastActionListener: WorkoutBroadcastActionListener,
    val context: Context,
    val workManager: WorkManager,
    private val dispatchers: CoroutinesDispatchers,
) : AppInitializer {
    @OptIn(DelicateCoroutinesApi::class)
    override fun init(app: Application) {
        val workoutSyncRelay = PublishRelay.create<Unit>()
        // We listen to the relay through process lifecycle, no need to keep the Disposable
        @Suppress("CheckResult")
        workoutSyncRelay
            .debounce(1, TimeUnit.SECONDS)
            .subscribe {
                TrainingDashboardWidgetAsSystemWidgetProvider.sendUpdateBroadcast(context)
                AscentDashboardWidgetAsSystemWidgetProvider.sendUpdateBroadcast(context)
                CommuteThisMonthDashboardWidgetAsSystemWidgetProvider.sendUpdateBroadcast(context)

                // For other purposes Goal widget is in its own group, but needs to update when
                // workouts change so update it here so a duplicated workout listener isn't needed for it
                GoalDashboardWidgetAsSystemWidgetProvider.sendUpdateBroadcast(context)

                GlobalScope.launch(dispatchers.io) {
                    ProgressHomeWidget().apply {
                        forceUpdateAll(context)
                        updatePreview(context, ProgressHomeWidgetReceiver::class.java)
                    }
                    MonthlyCalendarHomeWidget().apply {
                        forceUpdateAll(context)
                        updatePreview(context, MonthlyCalendarHomeWidgetReceiver::class.java)
                    }
                    if (!FlavorUtils.isSportsTracker) {
                        DailyOverviewHomeWidget().apply {
                            forceUpdateAll(context)
                            updatePreview(context, DailyOverviewHomeWidgetReceiver::class.java)
                        }
                    }
                }
            }

        workoutBroadcastActionListener.listener = object : WorkoutBroadcastActionListener.Listener {
            override fun onSyncFinished() {
                workoutSyncRelay.accept(Unit)
            }

            override fun onWorkoutSynced() {
                workoutSyncRelay.accept(Unit)
            }

            override fun onWorkoutSavedOrUpdated() {
                workoutSyncRelay.accept(Unit)
            }

            override fun onUserStatusChanged() {
                workoutSyncRelay.accept(Unit)
            }

            override fun onPictureStored() {}
        }
        workoutBroadcastActionListener.startListening()
    }
}
