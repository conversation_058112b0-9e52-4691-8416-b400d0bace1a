package com.stt.android.systemwidget

import android.content.Context
import android.util.Size
import com.stt.android.R
import com.stt.android.controllers.CurrentUserController
import com.stt.android.coroutines.nullOnError
import com.stt.android.home.dashboard.widget.workout.AscentWidget
import com.stt.android.home.dashboard.widget.workout.AscentWidgetData
import com.stt.android.home.dashboard.widget.workout.AscentWidgetDataFetcher
import com.stt.android.home.dashboard.widget.workout.CommuteWidget
import com.stt.android.home.dashboard.widget.workout.CommuteWidgetData
import com.stt.android.home.dashboard.widget.workout.CommuteWidgetDataFetcher
import com.stt.android.home.dashboard.widget.workout.TrainingWidget
import com.stt.android.home.dashboard.widget.workout.TrainingWidgetData
import com.stt.android.home.dashboard.widget.workout.TrainingWidgetDataFetcher
import com.stt.android.mapping.InfoModelFormatter
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.first
import java.time.LocalDate
import javax.inject.Inject

@AndroidEntryPoint
class TrainingDashboardWidgetAsSystemWidgetProvider :
    DashboardWidgetAsSystemWidgetProvider<TrainingWidgetData>() {
    @Inject
    lateinit var trainingWidgetDataFetcher: TrainingWidgetDataFetcher

    @Inject
    lateinit var currentUserController: CurrentUserController

    override val widgetType: SystemWidgetType = SystemWidgetType.TRAINING

    override suspend fun fetchWidgetData() = trainingWidgetDataFetcher
        .fetchTrainingWidgetData(currentUserController.username, LocalDate.now())

    override fun createWidgetView(
        widgetData: TrainingWidgetData?,
        size: Size,
        context: Context
    ) = TrainingWidget(context).apply {
        postInit()
        data = widgetData
        displayedAsEnabled = true
        customizationModeEnabled = false
        showRemoveButton = false
        val heightMode = getHeightMode(size.height, context)
        setHideDescriptionText(heightMode.ordinal < HeightMode.FULL.ordinal)
        setHideBars(heightMode == HeightMode.MINIMAL)
        setHeightKeepingBarchartProportionalHeight(size.height)
        setWidth(size.width)
        bindProps()
        executePendingBindings()
    }

    companion object {
        fun sendUpdateBroadcast(context: Context) {
            sendUpdateBroadcast(context, TrainingDashboardWidgetAsSystemWidgetProvider::class)
        }
    }
}

@AndroidEntryPoint
class AscentDashboardWidgetAsSystemWidgetProvider :
    DashboardWidgetAsSystemWidgetProvider<AscentWidgetData>() {
    @Inject
    lateinit var ascentWidgetDataFetcher: AscentWidgetDataFetcher

    @Inject
    lateinit var currentUserController: CurrentUserController

    @Inject
    lateinit var infoModelFormatter: InfoModelFormatter

    override val widgetType: SystemWidgetType = SystemWidgetType.ASCENT

    override suspend fun fetchWidgetData() = ascentWidgetDataFetcher
        .fetchAscentWidgetData(currentUserController.username, LocalDate.now())

    override fun createWidgetView(
        widgetData: AscentWidgetData?,
        size: Size,
        context: Context
    ) = AscentWidget(context).apply {
        postInit()
        measurementUnit = infoModelFormatter.unit
        data = widgetData
        displayedAsEnabled = true
        customizationModeEnabled = false
        showRemoveButton = false
        val heightMode = getHeightMode(size.height, context)
        setHideDescriptionText(heightMode.ordinal < HeightMode.FULL.ordinal)
        setHideBars(heightMode == HeightMode.MINIMAL)
        setHeightKeepingBarchartProportionalHeight(size.height)
        setWidth(size.width)
        bindProps()
        executePendingBindings()
    }

    companion object {
        fun sendUpdateBroadcast(context: Context) {
            sendUpdateBroadcast(context, AscentDashboardWidgetAsSystemWidgetProvider::class)
        }
    }
}

@AndroidEntryPoint
class CommuteThisMonthDashboardWidgetAsSystemWidgetProvider :
    DashboardWidgetAsSystemWidgetProvider<CommuteWidgetData>() {
    @Inject
    lateinit var commuteWidgetDataFetcher: CommuteWidgetDataFetcher

    @Inject
    lateinit var currentUserController: CurrentUserController

    @Inject
    lateinit var infoModelFormatter: InfoModelFormatter

    override val widgetType: SystemWidgetType = SystemWidgetType.COMMUTE_THIS_MONTH

    override val minimalHeightThreshold: Int
        get() = R.dimen.commutewidget_minimal_height_threshold

    override suspend fun fetchWidgetData() = commuteWidgetDataFetcher
        .getCommuteWidgetData(currentUserController.username, LocalDate.now()).nullOnError().first()

    override fun createWidgetView(
        widgetData: CommuteWidgetData?,
        size: Size,
        context: Context
    ) = CommuteWidget(context).apply {
        data = widgetData
        displayedAsEnabled = true
        customizationModeEnabled = false
        showRemoveButton = false
        unlockSize()

        val heightMode = getHeightMode(size.height, context)
        setTextsHidden(
            hideTitle = heightMode.ordinal <= HeightMode.LIMITED.ordinal,
            hideDescription = heightMode.ordinal <= HeightMode.MINIMAL.ordinal
        )

        bindProps()
    }

    companion object {
        fun sendUpdateBroadcast(context: Context) {
            sendUpdateBroadcast(context, CommuteThisMonthDashboardWidgetAsSystemWidgetProvider::class)
        }
    }
}
