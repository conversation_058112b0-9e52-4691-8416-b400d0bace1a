package com.stt.android.systemwidget

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.graphics.Canvas
import android.os.Bundle
import android.util.Size
import android.view.View
import android.widget.RemoteViews
import androidx.annotation.DimenRes
import androidx.annotation.IdRes
import androidx.appcompat.view.ContextThemeWrapper
import androidx.core.graphics.createBitmap
import com.stt.android.R
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.home.dashboard.widget.customization.BuyPremiumWidgetData
import com.stt.android.home.dashboard.widget.customization.BuyPremiumWidgetView
import com.stt.android.home.dashboard.widget.customization.CheckPremiumSubscriptionForWidgetTypeUseCase
import com.stt.android.launcher.BaseProxyActivity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.Dispatchers.Main
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import kotlin.math.roundToInt
import kotlin.reflect.KClass

/**
 * Base class for updating system widgets by generating the widgets view, rendering it to
 * a bitmap and then showing that image as widget's view.
 *
 * Adds a generic click handler that opens the app to the widgets.
 *
 * Expects up-to-date data being available from [fetchWidgetData] fast enough
 * to be handled in a BroadcastReceiver that calls [goAsync].
 * If data needs to be synced from remote sources then do the sync separately first
 * and then tell the widgets to update
 */
abstract class DashboardWidgetAsSystemWidgetProvider<T> : AppWidgetProvider() {
    @Inject
    lateinit var systemWidgetAnalytics: SystemWidgetAnalytics

    @Inject
    lateinit var checkWidgetPremiumSubscriptionForWidgetTypeUseCase: CheckPremiumSubscriptionForWidgetTypeUseCase

    abstract val widgetType: SystemWidgetType

    @DimenRes
    protected open val limitedHeightThreshold = R.dimen.systemwidget_limited_height_threshold

    // Most widgets use only minimal and limited, having very limited same as minimal ignores it
    @DimenRes
    protected open val veryLimitedHeightThreshold: Int = R.dimen.systemwidget_minimal_height_threshold

    @DimenRes
    protected open val minimalHeightThreshold = R.dimen.systemwidget_minimal_height_threshold

    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        updateWidgets(context, appWidgetManager, appWidgetIds)
        systemWidgetAnalytics.onWidgetsUpdated(widgetType.analyticsType, appWidgetIds.toList())
    }

    override fun onDeleted(context: Context, appWidgetIds: IntArray) {
        systemWidgetAnalytics.onWidgetsDeleted(widgetType.analyticsType, appWidgetIds.toList())
    }

    override fun onAppWidgetOptionsChanged(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetId: Int,
        newOptions: Bundle
    ) {
        updateWidgets(context, appWidgetManager, intArrayOf(appWidgetId))

        val heightMode = getHeightMode(
            getWidgetPixelSize(context.isPortrait, newOptions, context).height,
            context
        )
        systemWidgetAnalytics.onWidgetResized(widgetType.analyticsType, heightMode)
    }

    private fun updateWidgets(
        appContext: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        val themedContext = ContextThemeWrapper(appContext, R.style.AppTheme)
        val pendingResult = goAsync()

        CoroutineScope(Main.immediate + SupervisorJob()).launch {
            runSuspendCatching {
                val (widgetData, premiumRequiredPlaceholderData) = withContext(IO) {
                    if (checkWidgetPremiumSubscriptionForWidgetTypeUseCase.hasValidSubscription(widgetType.dashboardWidgetType)) {
                        fetchWidgetData() to null
                    } else {
                        null to createDataForBuyPremiumPlaceholder(themedContext)
                    }
                }

                appWidgetIds.forEach { widgetId ->
                    val widgetOptions = appWidgetManager.getAppWidgetOptions(widgetId)

                    val pendingIntentFlags = PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                    val pendingIntent = PendingIntent.getActivity(
                        appContext,
                        widgetType.pendingIntentRequestCode,
                        BaseProxyActivity.newStartIntentClearStackWithOpenFromSystemWidgetAnalytics(
                            appContext,
                            widgetType.analyticsType
                        ),
                        pendingIntentFlags
                    )

                    val remoteViews = RemoteViews(
                        appContext.packageName,
                        R.layout.systemwidget_image
                    ).apply {
                        setOnClickPendingIntent(R.id.systemwidget_image_container, pendingIntent)
                    }

                    fun renderPortrait(isFirst: Boolean) {
                        val portraitWidgetSize =
                            getWidgetPixelSize(true, widgetOptions, themedContext)
                        val portraitView = if (premiumRequiredPlaceholderData != null) {
                            createPremiumRequiredPlaceholderView(premiumRequiredPlaceholderData, portraitWidgetSize, themedContext)
                        } else {
                            createWidgetView(widgetData, portraitWidgetSize, themedContext)
                        }

                        sendToRemoteViews(
                            remoteViews,
                            portraitView,
                            portraitWidgetSize,
                            R.id.systemwidget_image_bitmap_portrait,
                            isFirst,
                            appWidgetManager,
                            widgetId
                        )
                    }

                    fun renderLandscape(isFirst: Boolean) {
                        val landscapeWidgetSize =
                            getWidgetPixelSize(false, widgetOptions, themedContext)

                        val landscapeView = if (premiumRequiredPlaceholderData != null) {
                            createPremiumRequiredPlaceholderView(premiumRequiredPlaceholderData, landscapeWidgetSize, themedContext)
                        } else {
                            createWidgetView(widgetData, landscapeWidgetSize, themedContext)
                        }

                        sendToRemoteViews(
                            remoteViews,
                            landscapeView,
                            landscapeWidgetSize,
                            R.id.systemwidget_image_bitmap_landscape,
                            isFirst,
                            appWidgetManager,
                            widgetId
                        )
                    }

                    if (themedContext.isPortrait) {
                        renderPortrait(true)
                        renderLandscape(false)
                    } else {
                        renderLandscape(true)
                        renderPortrait(false)
                    }
                }
            }.onFailure { e ->
                Timber.w(e, "Error updating system widget")
            }

            try {
                pendingResult.finish()
            } catch (ignored: Exception) {
                // there is a race condition against closing of this broadcast even though we goAsync on the main thread
            }
        }
    }

    private fun sendToRemoteViews(
        remoteViews: RemoteViews,
        view: View,
        size: Size,
        @IdRes toResId: Int,
        isFirstUpdate: Boolean,
        appWidgetManager: AppWidgetManager,
        appWidgetId: Int
    ) {
        view.measure(
            View.MeasureSpec.makeMeasureSpec(
                size.width,
                View.MeasureSpec.EXACTLY
            ),
            View.MeasureSpec.makeMeasureSpec(
                size.height,
                View.MeasureSpec.EXACTLY
            )
        )
        view.layout(0, 0, size.width, size.height)

        val bitmap = createBitmap(size.width, size.height)
        val canvas = Canvas(bitmap)
        view.draw(canvas)
        remoteViews.setImageViewBitmap(toResId, bitmap)

        if (isFirstUpdate) {
            appWidgetManager.updateAppWidget(appWidgetId, remoteViews)
        } else {
            appWidgetManager.partiallyUpdateAppWidget(appWidgetId, remoteViews)
        }
    }

    private fun getWidgetPixelSize(
        portrait: Boolean,
        widgetOptions: Bundle,
        context: Context
    ): Size {
        val density = context.resources.displayMetrics.density
        val minSizeDp =
            (context.resources.getDimensionPixelSize(R.dimen.systemwidget_default_min_size) / density).roundToInt()
        val widthDp = if (portrait) {
            widgetOptions[AppWidgetManager.OPTION_APPWIDGET_MIN_WIDTH]
        } else {
            widgetOptions[AppWidgetManager.OPTION_APPWIDGET_MAX_WIDTH]
        } as? Int ?: minSizeDp

        val heightDp = if (portrait) {
            widgetOptions[AppWidgetManager.OPTION_APPWIDGET_MAX_HEIGHT]
        } else {
            widgetOptions[AppWidgetManager.OPTION_APPWIDGET_MIN_HEIGHT]
        } as? Int ?: minSizeDp

        val widthPx = (density * widthDp).roundToInt()
        val heightPx = (density * heightDp).roundToInt()

        return Size(widthPx, heightPx)
    }

    protected open fun getHeightMode(heightPx: Int, context: Context): HeightMode {
        val limitedThreshold =
            context.resources.getDimensionPixelSize(limitedHeightThreshold)
        val veryLimitedThreshold = context.resources.getDimensionPixelSize(veryLimitedHeightThreshold)
        val minimalThreshold =
            context.resources.getDimensionPixelSize(minimalHeightThreshold)

        return when {
            heightPx < minimalThreshold -> HeightMode.MINIMAL
            heightPx < veryLimitedThreshold -> HeightMode.VERY_LIMITED
            heightPx < limitedThreshold -> HeightMode.LIMITED
            else -> HeightMode.FULL
        }
    }

    private fun createPremiumRequiredPlaceholderView(
        widgetData: BuyPremiumWidgetData,
        size: Size,
        context: Context
    ) = BuyPremiumWidgetView(context).apply {
        data = widgetData
        width = size.width
        height = size.height
        bindProps()
        executePendingBindings()
    }

    protected open fun createDataForBuyPremiumPlaceholder(context: Context) =
        BuyPremiumWidgetData.createForUnknownWidget()

    protected val Context.isPortrait: Boolean
        get() = resources.configuration.orientation == Configuration.ORIENTATION_PORTRAIT

    protected abstract suspend fun fetchWidgetData(): T?
    protected abstract fun createWidgetView(widgetData: T?, size: Size, context: Context): View

    companion object {
        fun sendUpdateBroadcast(context: Context, providerClass: KClass<*>) {
            val appWidgetManager =
                context.getSystemService(Context.APPWIDGET_SERVICE) as AppWidgetManager
            val widgetIds = appWidgetManager.getAppWidgetIds(
                ComponentName(
                    context,
                    providerClass.java
                )
            )

            if (widgetIds.isNotEmpty()) {
                context.sendBroadcast(
                    Intent(
                        context,
                        providerClass.java
                    ).apply {
                        action = AppWidgetManager.ACTION_APPWIDGET_UPDATE
                        putExtra(AppWidgetManager.EXTRA_APPWIDGET_IDS, widgetIds)
                    }
                )
            }
        }
    }

    enum class HeightMode {
        MINIMAL,
        VERY_LIMITED,
        LIMITED,
        FULL,
    }
}
