package com.stt.android.systemwidget

import com.stt.android.di.initializer.AppInitializer
import dagger.Binds
import dagger.Module
import dagger.multibindings.IntoSet

@Module
abstract class SystemWidgetModule {
    @Binds
    @IntoSet
    abstract fun bindSystemWidgetDataChangeListener(workoutSystemWidgetDataChangeListener: WorkoutSystemWidgetDataChangeListener): AppInitializer

    @Binds
    @IntoSet
    abstract fun bindGoalSystemWidgetDataChangeListener(goalSystemWidgetDataChangeListener: GoalSystemWidgetDataChangeListener): AppInitializer
}
