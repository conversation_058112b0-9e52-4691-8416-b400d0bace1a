package com.stt.android.systemwidget

import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.home.dashboard.widget.WidgetType

enum class SystemWidgetType {
    CALORIES,
    RESOURCES,
    SLEEP,
    STEPS,
    TRAINING,
    ASCENT,
    MINIMUM_HEART_RATE,
    COMMUTE_THIS_MONTH,
    GOAL,
}

val SystemWidgetType.analyticsType: String get() = when (this) {
    SystemWidgetType.CALORIES -> AnalyticsPropertyValue.SystemWidgetType.CALORIES
    SystemWidgetType.RESOURCES -> AnalyticsPropertyValue.SystemWidgetType.RESOURCES
    SystemWidgetType.SLEEP -> AnalyticsPropertyValue.SystemWidgetType.SLEEP
    SystemWidgetType.STEPS -> AnalyticsPropertyValue.SystemWidgetType.STEPS
    SystemWidgetType.TRAINING -> AnalyticsPropertyValue.SystemWidgetType.TRAINING
    SystemWidgetType.ASCENT -> AnalyticsPropertyValue.SystemWidgetType.ASCENT
    SystemWidgetType.MINIMUM_HEART_RATE -> AnalyticsPropertyValue.SystemWidgetType.MINIMUM_HEART_RATE
    SystemWidgetType.COMMUTE_THIS_MONTH -> AnalyticsPropertyValue.SystemWidgetType.COMMUTE_THIS_MONTH
    SystemWidgetType.GOAL -> AnalyticsPropertyValue.SystemWidgetType.GOAL
}

val SystemWidgetType.dashboardWidgetType: WidgetType get() = when (this) {
    SystemWidgetType.CALORIES -> WidgetType.CALORIES
    SystemWidgetType.RESOURCES -> WidgetType.RESOURCES
    SystemWidgetType.SLEEP -> WidgetType.SLEEP
    SystemWidgetType.STEPS -> WidgetType.STEPS
    SystemWidgetType.TRAINING -> WidgetType.TRAINING
    SystemWidgetType.ASCENT -> WidgetType.ASCENT
    SystemWidgetType.MINIMUM_HEART_RATE -> WidgetType.MINIMUM_HEART_RATE
    SystemWidgetType.COMMUTE_THIS_MONTH -> WidgetType.COMMUTE_THIS_MONTH
    SystemWidgetType.GOAL -> WidgetType.GOAL
}

val SystemWidgetType.pendingIntentRequestCode: Int get() = ordinal
