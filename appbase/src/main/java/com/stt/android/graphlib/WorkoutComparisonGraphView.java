package com.stt.android.graphlib;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Typeface;
import android.util.AttributeSet;
import androidx.core.content.ContextCompat;
import com.stt.android.R;
import com.stt.android.controllers.UserSettingsController;
import com.stt.android.graphlib.adapters.WorkoutComparisonAdapter;
import com.stt.android.utils.STTConstants;
import dagger.hilt.android.AndroidEntryPoint;
import javax.inject.Inject;

@AndroidEntryPoint
public class WorkoutComparisonGraphView extends GraphView {
    private static final float MINIMUM_X_AXIS_RANGE = 0.5f;
    private static final float MINIMUM_Y_AXIS_RANGE = -27.0f;
    private static final float MAXIMUM_Y_AXIS_RANGE = 27.0f;

    private final Paint positiveValuePaint = new Paint();
    private final Paint negativeValuePaint = new Paint();

    @Inject
    UserSettingsController userSettingsController;

    public WorkoutComparisonGraphView(Context context, AttributeSet attrs) {
        super(context, attrs);

        positiveValuePaint.setColor(ContextCompat.getColor(context, com.stt.android.core.R.color.comparison_color_increase));
        negativeValuePaint.setColor(ContextCompat.getColor(context, com.stt.android.core.R.color.comparison_color_decrease));

        initAxis();
    }

    @Override
    protected void init() {}

    @Override
    protected void initAxis() {
        if (isInEditMode()) {
            return;
        }

        setRangeX(MINIMUM_X_AXIS_RANGE);
        setMinimumRangeX(MINIMUM_X_AXIS_RANGE);

        Resources resources = getResources();
        WorkoutComparisonAdapter adapter = new WorkoutComparisonAdapter(
                userSettingsController.getSettings().getMeasurementUnit(), null, resources);
        AxisSettings axisSettings = graphModel.setAxis(STTConstants.AXIS_WORKOUT_COMPARISON, adapter,
                new RectF(0, MINIMUM_Y_AXIS_RANGE, MINIMUM_X_AXIS_RANGE, MAXIMUM_Y_AXIS_RANGE));
        axisSettings.setVisualType(AxisSettings.TYPE_CURVE);
        axisSettings.setClampToZeroIfOnlyPositiveValues(false);
        axisSettings.setStartFromZero(false);
        prepareAxisLabel(axisSettings, resources, R.dimen.ab_label_text_size, R.color.dark_gray,
                AxisSettings.LABEL_LEFT, R.dimen.ab_label_left_pad, R.dimen.ab_label_right_pad);
        axisSettings.getLabelPaint().setTypeface(Typeface.DEFAULT);
        prepareAxisLine(axisSettings, resources, R.dimen.ab_line_stroke_width, com.stt.android.core.R.color.white);
    }

    @Override
    protected int getGridSizeY() {
        return 5;
    }

    @Override
    protected void drawBackground(Canvas canvas) {
        super.drawBackground(canvas);

        Rect gridRect = getGridRect();
        AxisData axis = (AxisData) graphModel.getInternalData().valueAt(0);
        RectF valuesBounds = getAxisCurrentRange(axis);
        int yPos = (int) ((valuesBounds.bottom / valuesBounds.height()) * gridRect.height());
        canvas.drawRect(gridRect.left, gridRect.top, gridRect.right, gridRect.top + yPos, positiveValuePaint);
        canvas.drawRect(gridRect.left, gridRect.top + yPos, gridRect.right, gridRect.bottom, negativeValuePaint);
    }
}
