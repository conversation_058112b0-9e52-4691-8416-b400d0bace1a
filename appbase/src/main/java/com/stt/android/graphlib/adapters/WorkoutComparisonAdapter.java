package com.stt.android.graphlib.adapters;

import android.content.res.Resources;
import com.stt.android.domain.user.MeasurementUnit;
import java.util.Locale;

public class WorkoutComparisonAdapter implements ValueAdapter<WorkoutComparisonAdapter.AheadBehindByDistance> {
    /**
     * Class to hold the ahead behind value at a given distance
     */
    public static class AheadBehindByDistance {
        private final double distance;
        private final float aheadBehindValue;

        public AheadBehindByDistance(double distance, float aheadBehindValue) {
            this.distance = distance;
            this.aheadBehindValue = aheadBehindValue;
        }

        public double getDistance() {
            return distance;
        }

        public float getAheadBehindValue() {
            return aheadBehindValue;
        }
    }
    private static final String FORMAT_X = "%.1f";
    private static final String FORMAT_Y = "%.0f";

    private final MeasurementUnit measurementUnit;
    private final String yLabel;
    private final Resources resources;

    public WorkoutComparisonAdapter(MeasurementUnit measurementUnit, String yLabel,
        Resources resources) {
        this.measurementUnit = measurementUnit;
        this.yLabel = yLabel;
        this.resources = resources;
    }

    @Override
    public float getValueX(AheadBehindByDistance item) {
        return (float) measurementUnit.toDistanceUnit(item.getDistance());
    }

    @Override
    public float getValueY(AheadBehindByDistance item) {
        return item.getAheadBehindValue();
    }

    @Override
    public int getSection(AheadBehindByDistance item) {
        return 0;
    }

    @Override
    public String getLabelValueX(float value) {
        return String.format(Locale.US, FORMAT_X, value);
    }

    @Override
    public String getLabelValueY(float value) {
        return String.format(Locale.US, FORMAT_Y, value);
    }

    @Override
    public String getLabelUnitX() {
        return resources.getString(measurementUnit.getDistanceUnit());
    }

    @Override
    public String getLabelUnitY() {
        return yLabel;
    }
}
