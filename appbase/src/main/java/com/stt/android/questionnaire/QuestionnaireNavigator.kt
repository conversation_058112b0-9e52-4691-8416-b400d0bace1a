package com.stt.android.questionnaire

import android.content.Context
import android.content.Intent
import com.stt.android.analytics.AnalyticsPropertyValue

enum class QuestionnaireMode {
    SPORTS_QUESTIONNAIRE,
    MOTIVATION_QUESTIONNAIRE,
    SPORTS_AND_MOTIVATION_QUESTIONNAIRE
}

interface QuestionnaireNavigator {
    /**
     * @param context Context
     * @param mode Mode defines which questionnaire(s) should be shown
     * @param activityFinishAnimations Optional enter and exit animation resource ids
     * @param analyticsContext Analytics Context property value
     */
    fun newStartIntent(
        context: Context,
        mode: QuestionnaireMode,
        activityFinishAnimations: Pair<Int, Int>? = null,
        analyticsContext: String = AnalyticsPropertyValue.SurveySkippedContext.OTHER
    ): Intent
}
