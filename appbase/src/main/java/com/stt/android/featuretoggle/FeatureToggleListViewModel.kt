package com.stt.android.featuretoggle

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import com.stt.android.FeatureFlags
import com.stt.android.analytics.tencent.TencentAnalytics
import com.stt.android.common.viewstate.LoadingStateViewModel
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.di.EmarsysCustomAttributePreferences
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.featuretoggle.FeatureEnabledStateUseCase
import com.stt.android.remote.di.BaseUrlConfiguration
import com.stt.android.remote.di.UrlConfigurationRepository
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.utils.STTConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import io.reactivex.Scheduler
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.rx2.await
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * View Model for building the list data for a feature toggle list.
 * The data can be of any type. It's a pure kotlin object. It is recommended to have a container,
 * which holds a list of the data and if we want to have each item in the list clickable, this container
 * will also hold the click listener - a kotlin high order function.
 *
 * The View Model is a show case of View State pattern. Follow the rest of the comments in this file
 * for more info.
 *
 * @see [FeatureToggleController]
 * @see [FeatureToggleListFragment]
 * @see [FeatureItemContainer]
 */
@HiltViewModel
class FeatureToggleListViewModel @Inject constructor(
    @ApplicationContext appContext: Context,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    userSettingsController: UserSettingsController,
    tencentAnalytics: TencentAnalytics,
    private val featureEnabledStateUseCase: FeatureEnabledStateUseCase,
    val currentUserController: CurrentUserController,
    @EmarsysCustomAttributePreferences private val emarsysSharedPreferences: SharedPreferences,
    private val baseUrlConfiguration: BaseUrlConfiguration,
    private val urlConfigurationRepository: UrlConfigurationRepository,
    private val featureFlags: FeatureFlags,
    private val sharedPreferences: SharedPreferences,
) : LoadingStateViewModel<FeatureItemContainer>(ioThread, mainThread) {
    val debugValues: String = """
        Analytics UID: ${userSettingsController.settings.analyticsUUID}
        Tencent Token: ${tencentAnalytics.getToken(appContext)} 
        Emarsys Token: ${emarsysSharedPreferences.getString(STTConstants.EmarsysCustomAttributePreferences.CONTACT_ID, "null")} 
    """.trimIndent()

    // Unlike the deprecated LoadingViewModel, loading trigger is entirely left for the developer.
    // In this case, I chose to do it right when this class is instantiated.
    init {
        launch {
            loadData()
        }
    }

    override fun retryLoading() {
        launch {
            loadData()
        }
    }

    val requestKillProcesses = SingleLiveEvent<Any>()
    val requestPhoneReboot = SingleLiveEvent<Any>()
    val requestConfirmSwitchBaseUrlConfiguration = SingleLiveEvent<BaseUrlConfiguration>()

    private val saveOnlyListener: (String, Boolean) -> Unit = { key, enabled ->
        launch(io) {
            try {
                featureEnabledStateUseCase.saveFeatureEnabledState(key, enabled).await()
            } catch (e: Exception) {
                notifyError(e)
            }
        }
    }

    private val saveAndKillProcessesListener: (String, Boolean) -> Unit = { key, enabled ->
        launch(io) {
            try {
                featureEnabledStateUseCase.saveFeatureEnabledState(key, enabled).await()
                requestKillProcesses.postCall()
            } catch (e: Exception) {
                notifyError(e)
            }
        }
    }

    private val saveAndRequestPhoneRebootListener: (String, Boolean) -> Unit = { key, enabled ->
        launch(io) {
            try {
                featureEnabledStateUseCase.saveFeatureEnabledState(key, enabled).await()
                requestPhoneReboot.postCall()
            } catch (e: Exception) {
                notifyError(e)
            }
        }
    }

    private suspend fun loadData() = withContext(io) {
        runSuspendCatching {
            // 1. We call notifyLoading() to signal that the UI should indicate that data is loading in the UI.
            // This method takes an optional data in case we need to pass initial data or we already have previously
            // loaded data.
            notifyLoading()

            // 2. We load the data.
            // With LoadingStateViewModel as the base class we can use RxJava2 or Coroutines to load the data async.
            val fieldTester = currentUserController.isFieldTester

            // Brand specific features
            val brandKeys = featureKeys(fieldTester)
            val brandFeatureItems = brandKeys.map { key ->
                val feature = getStaticFeatures(key)
                val enabled =
                    featureEnabledStateUseCase.fetchFeatureEnabledState(key, feature.enabled).await()

                feature.copy(enabled = enabled)
            }

            // this delay is to show ui later not to prevent accidental clicks on the list
            delay(500)

            notifyDataLoaded(
                FeatureItemContainer(
                    brandFeatureItems,
                    saveOnlyListener,
                    saveAndKillProcessesListener,
                    saveAndRequestPhoneRebootListener,
                    baseUrlConfiguration,
                    ::onSelectBaseUrlConfiguration,
                    listOf(featureFlags.graphhopperBaseUrl.url) + featureFlags.graphhopperBaseUrl.alternatives,
                    selectedGraphhopperBaseUrl(),
                    ::onGraphhopperBaseUrlSelected,
                )
            )
        }.onFailure { e ->
            notifyError(e)
        }
    }

    private fun onSelectBaseUrlConfiguration(baseUrlConfiguration: BaseUrlConfiguration) {
        if (baseUrlConfiguration == this.baseUrlConfiguration) return
        requestConfirmSwitchBaseUrlConfiguration.postValue(baseUrlConfiguration)
    }

    fun confirmSelectBaseUrlConfiguration(baseUrlConfiguration: BaseUrlConfiguration) {
        urlConfigurationRepository.setCustomBaseUrlConfiguration(baseUrlConfiguration, commit = true)
    }

    private fun selectedGraphhopperBaseUrl(): String = sharedPreferences.getString(
        STTConstants.DefaultPreferences.KEY_GRAPHHOPPER_BASE_URL,
        featureFlags.graphhopperBaseUrl.url
    ) ?: featureFlags.graphhopperBaseUrl.url

    private fun onGraphhopperBaseUrlSelected(selected: String) {
        sharedPreferences.edit {
            putString(STTConstants.DefaultPreferences.KEY_GRAPHHOPPER_BASE_URL, selected)
        }
    }
}
