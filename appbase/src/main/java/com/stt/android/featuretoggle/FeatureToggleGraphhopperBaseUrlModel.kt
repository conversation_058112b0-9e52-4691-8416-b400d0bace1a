package com.stt.android.featuretoggle

import android.widget.AdapterView.OnItemClickListener
import android.widget.ArrayAdapter
import android.widget.AutoCompleteTextView
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.stt.android.R

@EpoxyModelClass
abstract class FeatureToggleGraphhopperBaseUrlModel : EpoxyModelWithHolder<Holder>() {
    @EpoxyAttribute
    var baseUrls: List<String>? = null

    @EpoxyAttribute
    var selectedBaseUrl: String? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var onBaseUrlSelected: ((String) -> Unit)? = null

    override fun getDefaultLayout() = R.layout.feature_toggle_base_url_selector

    override fun bind(holder: Holder) {
        super.bind(holder)

        with(holder.backendSwitcher) {
            hint = "Switch routing URL"

            val baseUrls = baseUrls ?: return
            val adapter = ArrayAdapter(context, android.R.layout.simple_list_item_1, baseUrls)
            (editText as? AutoCompleteTextView)?.apply {
                setText(selectedBaseUrl ?: "", false)
                setAdapter(adapter)
                onItemClickListener = OnItemClickListener { _, _, position, _ ->
                    onBaseUrlSelected?.invoke(baseUrls[position])
                }
            }
        }
    }

    override fun unbind(holder: Holder) {
        super.unbind(holder)

        (holder.backendSwitcher.editText as? AutoCompleteTextView)?.onItemClickListener = null
    }

    override fun shouldSaveViewState(): Boolean = true
}
