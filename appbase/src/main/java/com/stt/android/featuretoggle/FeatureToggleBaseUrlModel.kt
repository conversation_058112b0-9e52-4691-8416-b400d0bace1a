package com.stt.android.featuretoggle

import android.widget.AdapterView.OnItemClickListener
import android.widget.ArrayAdapter
import android.widget.AutoCompleteTextView
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.google.android.material.textfield.TextInputLayout
import com.stt.android.R
import com.stt.android.common.KotlinEpoxyHolder
import com.stt.android.remote.di.BaseUrlConfiguration

@EpoxyModelClass
abstract class FeatureToggleBaseUrlModel : EpoxyModelWithHolder<Holder>() {

    @EpoxyAttribute
    var selectedConfiguration: BaseUrlConfiguration? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var onSelectConfiguration: ((BaseUrlConfiguration) -> Unit)? = null

    override fun getDefaultLayout() = R.layout.feature_toggle_base_url_selector

    override fun bind(holder: Holder) {
        super.bind(holder)
        holder.apply {
            val items = BaseUrlConfiguration.entries.map { it.key }
            val adapter =
                ArrayAdapter(backendSwitcher.context, android.R.layout.simple_list_item_1, items)
            (backendSwitcher.editText as? AutoCompleteTextView)?.apply {
                setText(selectedConfiguration?.key ?: "", false)
                setAdapter(adapter)
                onItemClickListener = OnItemClickListener { _, _, position, _ ->
                    onSelectConfiguration?.invoke(BaseUrlConfiguration.entries[position])
                }
            }
        }
    }

    override fun unbind(holder: Holder) {
        super.unbind(holder)
        holder.apply {
            (backendSwitcher.editText as? AutoCompleteTextView)?.onItemClickListener = null
        }
    }

    override fun shouldSaveViewState(): Boolean = true
}

class Holder : KotlinEpoxyHolder() {
    val backendSwitcher: TextInputLayout by bind(R.id.backend_switcher)
}
