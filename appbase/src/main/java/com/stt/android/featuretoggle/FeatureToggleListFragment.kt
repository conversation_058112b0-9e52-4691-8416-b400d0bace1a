package com.stt.android.featuretoggle

import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.viewModels
import com.stt.android.R
import com.stt.android.common.ui.observeK
import com.stt.android.common.ui.observeNotNull
import com.stt.android.common.viewstate.LoadingStateViewModel
import com.stt.android.common.viewstate.ViewStateEpoxyController
import com.stt.android.common.viewstate.ViewStateListFragment2
import com.stt.android.ui.tasks.LogoutTask
import com.stt.android.utils.FeatureToggleUtils.killProcesses
import dagger.Lazy
import dagger.hilt.android.AndroidEntryPoint
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.rxkotlin.subscribeBy
import timber.log.Timber
import javax.inject.Inject

/**
 * Fragment that shows a list of features that can be toggled on/off.
 *
 * This fragment is using the View State pattern by extending [ViewStateListFragment]. When implementing this pattern
 * there are a 3 classes that we need to implement:
 *
 * 1. The view - this class for example. The easiest way is to extend [ViewStateListFragment] for screens that
 * need to show a list of items. It is observing the state emitted by the view model and dispatches the
 * needed updates to the UI.
 * 2. the view model - the class that takes care of loading the data along with business logic. This class
 * should extend [LoadingStateViewModel]. The data is just plain old kotlin objects (POKOs) and can be of
 * any type or structure.
 * 3. The epoxy controller - [ViewStateListFragment] uses a RecyclerView with Epoxy library under the hood to
 * to feed the RecyclerView its data and tell it how it should be laid out. Note that it is **not necessary** to know how
 * Epoxy works. All you need to do is extend [ViewStateEpoxyController] and override its
 * [ViewStateEpoxyController.buildModels] method. See [ViewStateEpoxyController] documentation for more info.
 *
 * That's it!
 *
 * @see [FeatureToggleViewModel]
 * @see [FeatureToggleController]
 */
@AndroidEntryPoint
class FeatureToggleListFragment : ViewStateListFragment2<
    FeatureItemContainer,
    FeatureToggleListViewModel,
    >() {
    override val layoutId: Int = R.layout.fragment_feature_toggle_list

    override val viewModel: FeatureToggleListViewModel by viewModels()

    @Inject
    lateinit var logoutTask: Lazy<LogoutTask>

    private val disposable = CompositeDisposable()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.requestKillProcesses.observeK(viewLifecycleOwner) {
            AlertDialog.Builder(requireContext())
                .setTitle("App restart required")
                .setMessage("Selection requires app restart. Click OK to continue")
                .setPositiveButton("OK") { _, _ -> killProcesses(requireContext().applicationContext) }
                .setCancelable(false)
                .show()
        }

        viewModel.requestPhoneReboot.observeK(viewLifecycleOwner) {
            AlertDialog.Builder(requireContext())
                .setTitle("Device reboot might be needed")
                .setMessage("If the selection doesn't seem to take effect, please reboot your device.")
                .setPositiveButton("OK") { _, _ -> /* intentionally empty */ }
                .setCancelable(false)
                .show()
        }

        viewModel.requestConfirmSwitchBaseUrlConfiguration.observeNotNull(viewLifecycleOwner) {
            AlertDialog.Builder(requireContext())
                .setTitle("Logout and app restart required")
                .setMessage("Switching backend will logout the current user and restart the app.")
                .setPositiveButton("OK") { _, _ ->
                    viewModel.confirmSelectBaseUrlConfiguration(it)

                    if (viewModel.currentUserController.isLoggedIn) {
                        disposable.add(
                            logoutTask.get()
                                .logoutWithProgressDialog(requireContext(), childFragmentManager)
                                .subscribeBy(
                                    onComplete = {
                                        Timber.d("Logout success")
                                        requireActivity().finishAndRemoveTask()
                                        killProcesses(requireContext().applicationContext)
                                    },
                                    onError = { Timber.w(it, "Logout failed") }
                                )
                        )
                    } else {
                        requireActivity().finishAndRemoveTask()
                        killProcesses(requireContext().applicationContext)
                    }
                }.setNegativeButton("Cancel") { _, _ ->
                    viewModel.retryLoading()
                }
                .setCancelable(false)
                .show()
        }
    }
}
