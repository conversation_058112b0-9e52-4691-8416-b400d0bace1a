package com.stt.android.featuretoggle

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import com.stt.android.R
import com.stt.android.common.ui.ViewModelActivity2
import com.stt.android.databinding.ActivityFeatureToggleBinding
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class FeatureToggleActivity : ViewModelActivity2() {

    override val viewModel: FeatureToggleViewModel by viewModels()

    private val viewDataBinding: ActivityFeatureToggleBinding get() = requireBinding()

    override fun getLayoutResId() = R.layout.activity_feature_toggle

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setUpToolBar()
    }

    private fun setUpToolBar() {
        setSupportActionBar(viewDataBinding.toolbarFeatureToggle)
        supportActionBar?.apply {
            setDisplayShowHomeEnabled(false)
            setDisplayHomeAsUpEnabled(true)
            title = "Feature Toggle"
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        finish()
        return true
    }

    companion object {
        @JvmStatic
        fun newStartIntent(context: Context) = Intent(context, FeatureToggleActivity::class.java)
    }
}
