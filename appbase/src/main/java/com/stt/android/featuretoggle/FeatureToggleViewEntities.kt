package com.stt.android.featuretoggle

import com.stt.android.remote.di.BaseUrlConfiguration

typealias OnCheckedChanged = (key: String, status: Boolean) -> Unit

data class FeatureItem(
    val name: String,
    val key: String,
    val enabled: Boolean,
    val requireProcessKill: Boolean = false,
    val requireDeviceReboot: Boolean = false,
)

data class FeatureItemContainer(
    val list: List<FeatureItem>,
    val saveOnlyListener: OnCheckedChanged,
    val saveAndKillProcessListener: OnCheckedChanged,
    val saveAndRequestPhoneRebootListener: OnCheckedChanged,
    val selectedBaseUrlConfiguration: BaseUrlConfiguration,
    val onSelectBaseUrlConfiguration: (BaseUrlConfiguration) -> Unit,
    val graphhopperBaseUrls: List<String>,
    val selectedGraphhopperBaseUrl: String,
    val onGraphhopperBaseUrlSelected: (String) -> Unit,
)
