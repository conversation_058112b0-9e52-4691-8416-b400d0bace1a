package com.stt.android.featuretoggle

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.view.View
import com.google.android.gms.maps.model.LatLng
import com.stt.android.BuildConfig
import java.util.concurrent.atomic.AtomicInteger

class OpenFeatureToggleHandlerV2() {
    private val mainHandler = Handler(Looper.getMainLooper())
    private val resetCounter = Runnable { clickCounter.set(0) }
    private val clickCounter = AtomicInteger(0)
    private val clickListener = OnClickListener { context ->
        mainHandler.removeCallbacks(resetCounter)
        if (clickCounter.incrementAndGet() >= CLICKS_TO_OPEN_FEATURE_TOGGLE_ACTIVITY) {
            clickCounter.set(0)
            context.startActivity(FeatureToggleActivity.newStartIntent(context))
        } else {
            mainHandler.postDelayed(resetCounter, RESET_COUNTER_DELAY_IN_MILLIS)
        }
    }

    fun onClick(context: Context) {
        clickListener.onClick(context)
    }

    fun interface OnClickListener {
        fun onClick(context: Context)
    }

    private companion object {
        private val RESET_COUNTER_DELAY_IN_MILLIS = if (BuildConfig.DEBUG) 1000L else 250L
        private val CLICKS_TO_OPEN_FEATURE_TOGGLE_ACTIVITY = if (BuildConfig.DEBUG) 3 else 7
    }
}
