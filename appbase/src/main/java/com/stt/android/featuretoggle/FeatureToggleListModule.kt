package com.stt.android.featuretoggle

import com.stt.android.common.viewstate.ViewStateEpoxyController
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.android.components.FragmentComponent

@Module
@InstallIn(FragmentComponent::class)
abstract class FeatureToggleListModule {
    @Binds
    abstract fun bindFeatureToggleController(
        featureToggleController: FeatureToggleController
    ): ViewStateEpoxyController<FeatureItemContainer?>
}
