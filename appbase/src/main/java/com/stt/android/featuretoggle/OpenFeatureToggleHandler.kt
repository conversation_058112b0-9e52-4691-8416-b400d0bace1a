package com.stt.android.featuretoggle

import android.os.Handler
import android.os.Looper
import android.view.View
import com.stt.android.BuildConfig
import java.util.concurrent.atomic.AtomicInteger

@Deprecated(
    message = "Use OpenFeatureToggleHandlerV2 instead",
    replaceWith = ReplaceWith(
        expression = "OpenFeatureToggleHandlerV2()",
        imports = ["com.stt.android.featuretoggle.OpenFeatureToggleHandlerV2"]
    )
)
class OpenFeatureToggleHandler(view: View) {
    private val mainHandler = Handler(Looper.getMainLooper())
    private val resetCounter = Runnable { clickCounter.set(0) }
    private val clickCounter = AtomicInteger(0)
    private val clickListener = View.OnClickListener { view ->
        mainHandler.removeCallbacks(resetCounter)
        if (clickCounter.incrementAndGet() >= CLICKS_TO_OPEN_FEATURE_TOGGLE_ACTIVITY) {
            clickCounter.set(0)

            val context = view.context
            context.startActivity(FeatureToggleActivity.newStartIntent(context))
        } else {
            mainHandler.postDelayed(resetCounter, RESET_COUNTER_DELAY_IN_MILLIS)
        }
    }

    init {
        view.setOnClickListener(clickListener)
    }

    private companion object {
        private val RESET_COUNTER_DELAY_IN_MILLIS = if (BuildConfig.DEBUG) 1000L else 250L
        private val CLICKS_TO_OPEN_FEATURE_TOGGLE_ACTIVITY = if (BuildConfig.DEBUG) 3 else 7
    }
}
