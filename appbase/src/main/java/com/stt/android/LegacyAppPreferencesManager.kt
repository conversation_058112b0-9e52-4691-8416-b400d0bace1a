package com.stt.android

import android.content.Context
import android.content.pm.PackageManager
import androidx.core.content.edit
import androidx.preference.PreferenceManager
import com.stt.android.home.settings.PreferencesUpgrade0to1Helper
import com.stt.android.home.settings.PreferencesUpgrade1to2Helper
import com.stt.android.home.settings.PreferencesUpgrade2to3Helper
import com.stt.android.home.settings.PreferencesUpgrade3to4Helper
import com.stt.android.home.settings.PreferencesUpgrade4to5Helper
import com.stt.android.utils.STTConstants

object LegacyAppPreferencesManager {
    /**
     * Checks the current preference version and calls the right
     * PreferencesUpgradeHelper class as needed.
     * If it's the first time the app is started then we just store some initial values.
     *
     * ***NOTE***: This method must be called before any shared preference is set
     */
    fun upgradePreferencesIfNeeded(context: Context) {
        val sharedPreferences = PreferenceManager.getDefaultSharedPreferences(context)
        val emptyPreferences = sharedPreferences.all.isEmpty()
        if (emptyPreferences) {
            // There's nothing to do if it's the first time we create preferences.
            // Just mark the shared preferences version to the latest value
            sharedPreferences.edit {
                putInt(
                    STTConstants.DefaultPreferences.KEY_PREFERENCES_VERSION,
                    STTConstants.DefaultPreferences.CURRENT_PREFERENCES_VERSION
                )
            }
            return
        }
        // Unfortunately the first BETA releases didn't have preferences version so those are
        // considered to be in version 0
        when (sharedPreferences.getInt(STTConstants.DefaultPreferences.KEY_PREFERENCES_VERSION, 0)) {
            0 -> {
                PreferencesUpgrade0to1Helper(sharedPreferences).upgrade()
                PreferencesUpgrade1to2Helper(sharedPreferences).upgrade()
                PreferencesUpgrade2to3Helper(sharedPreferences).upgrade()
                PreferencesUpgrade3to4Helper(sharedPreferences).upgrade()
                PreferencesUpgrade4to5Helper(sharedPreferences).upgrade()
            }
            1 -> {
                PreferencesUpgrade1to2Helper(sharedPreferences).upgrade()
                PreferencesUpgrade2to3Helper(sharedPreferences).upgrade()
                PreferencesUpgrade3to4Helper(sharedPreferences).upgrade()
                PreferencesUpgrade4to5Helper(sharedPreferences).upgrade()
            }
            2 -> {
                PreferencesUpgrade2to3Helper(sharedPreferences).upgrade()
                PreferencesUpgrade3to4Helper(sharedPreferences).upgrade()
                PreferencesUpgrade4to5Helper(sharedPreferences).upgrade()
            }
            3 -> {
                PreferencesUpgrade3to4Helper(sharedPreferences).upgrade()
                PreferencesUpgrade4to5Helper(sharedPreferences).upgrade()
            }
            4 -> PreferencesUpgrade4to5Helper(sharedPreferences).upgrade()
            else -> {}
        }
    }

    /**
     * Starts an async task that will store the current app version in the shared preferences.
     * Useful in case we need to detect version upgrades.
     */
    fun storeCurrentAppVersion(context: Context) {
        val appVersion = getAppVersion(context)
        PreferenceManager.getDefaultSharedPreferences(context).edit {
            putInt(STTConstants.DefaultPreferences.KEY_APP_VERSION_ID, appVersion)
        }
    }

    /**
     * @return Application's version code from the `PackageManager`.
     */
    private fun getAppVersion(context: Context): Int {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            packageInfo.versionCode
        } catch (e: PackageManager.NameNotFoundException) {
            // should never happen
            throw RuntimeException("Could not get package name: $e")
        }
    }
}
