package com.stt.android

import android.app.Application
import com.stt.android.logs.CrashlyticsTree
import timber.log.Timber

// Set up Timber and any flavor specific crash logging
abstract class BaseLoggingInitializer {

    /**
     * This is executed before any Dagger injection.
     * Trees that require accessing user UUID shouldn't be used here,
     * see [initializeTimberPostFirstLaunchTermsCheck] for such use cases.
     */
    fun initializeTimberAtProcessStart() {
        if (BuildConfig.DEBUG) {
            Timber.plant(Timber.DebugTree())
            Timber.d("Timber debug tree planted")
        }
    }

    /**
     * This is executed after check that user has accepted first launch terms
     * or the build type doesn't need it. Use for Trees that require access
     * to user UUIDs such as Crashlytics.
     */
    fun initializeTimberPostFirstLaunchTermsCheck() {
        if (!BuildConfig.DEBUG) {
            Timber.plant(CrashlyticsTree())
            Timber.d("Timber Crashlytics tree planted")
        }
    }

    /**
     * This is executed after [initializeTimberPostFirstLaunchTermsCheck].
     * Use to initialize build type specific logging tools, can access UUIDs.
     */
    abstract fun initializeForBuildType(app: Application)
}
