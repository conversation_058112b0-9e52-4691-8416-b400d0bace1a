package com.stt.android.notifications.noui

import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.ListenableWorker
import androidx.work.WorkerParameters
import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import dagger.Lazy
import timber.log.Timber
import javax.inject.Inject

class RemoteSyncNotificationWorker(
    appContext: Context,
    params: WorkerParameters,
    private val syncTriggers: Map<String, SyncTrigger>
) : CoroutineWorker(appContext, params) {
    override suspend fun doWork(): Result {
        val syncTargets =
            inputData.getStringArray(RemoteSyncNotification.KEY_SYNC_TARGETS_FOR_INPUT_DATA)

        syncTargets?.forEach {
            try {
                syncTriggers[it]?.triggerSync()
            } catch (e: Exception) {
                Timber.e(e, "Failed to trigger $it synchronization")
            }
        }

        return Result.success()
    }

    class Factory @Inject constructor(
        // need lazy injection cause the SyncTriggers depend on the WorkManager themselves
        private var syncTriggers: Lazy<Map<String, @JvmSuppressWildcards SyncTrigger>>
    ) : CoroutineWorkerAssistedFactory {
        override fun create(context: Context, params: WorkerParameters): ListenableWorker {
            return RemoteSyncNotificationWorker(
                context,
                params,
                syncTriggers.get()
            )
        }
    }
}
