package com.stt.android.notifications

import android.content.Context
import androidx.core.content.edit
import androidx.preference.PreferenceManager
import com.stt.android.utils.STTConstants

object FCMUtil {

    private fun sharedPreferences(context: Context) =
        PreferenceManager.getDefaultSharedPreferences(context)

    @JvmStatic
    fun getFcmTokenSyncedStatus(context: Context, token: String): <PERSON><PERSON>an {
        return sharedPreferences(context)
            .getBoolean(
                "${STTConstants.DefaultPreferences.FCM_TOKEN_SYNCED_TO_BACKEND}$token",
                false
            )
    }

    @JvmStatic
    fun setFcmTokenAsSynced(context: Context, token: String) {
        sharedPreferences(context).edit {
            putBoolean("${STTConstants.DefaultPreferences.FCM_TOKEN_SYNCED_TO_BACKEND}$token", true)
        }
    }

    @JvmStatic
    fun saveToken(context: Context, token: String) {
        sharedPreferences(context).edit {
            putString(STTConstants.DefaultPreferences.FCM_TOKEN, token)
        }
    }

    @JvmStatic
    fun getFcmToken(context: Context): String? {
        return sharedPreferences(context).getString(STTConstants.DefaultPreferences.FCM_TOKEN, null)
    }

    @JvmStatic
    fun cleanCached(context: Context) {
        sharedPreferences(context).edit {
            val cached = getFcmToken(context)
            remove(STTConstants.DefaultPreferences.FCM_TOKEN)
            cached?.let {
                remove("${STTConstants.DefaultPreferences.FCM_TOKEN_SYNCED_TO_BACKEND}$cached")
            }
        }
    }
}
