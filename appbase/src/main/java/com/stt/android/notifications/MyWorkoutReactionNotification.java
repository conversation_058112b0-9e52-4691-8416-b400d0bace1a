package com.stt.android.notifications;

import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import androidx.core.app.NotificationCompat;
import androidx.core.app.TaskStackBuilder;
import com.stt.android.R;
import static com.stt.android.analytics.AnalyticsPropertyValue.AppOpenSourceType.PUSH_MESSAGE;
import static com.stt.android.analytics.AnalyticsPropertyValue.AppOpenSourceTypeDetail.LIKES;
import com.stt.android.analytics.AppOpenAnalyticsActivity;
import com.stt.android.domain.workout.ActivityType;
import com.stt.android.exceptions.InternalDataException;
import java.util.Objects;

class MyWorkoutReactionNotification extends STTNotificationUI {

    MyWorkoutReactionNotification(Context context, PushAttr pushAttr) {
        super(context, pushAttr, NotificationChannelIds.CHANNEL_ID_MY_ACTIVITY_LIKES,
            NotificationGroup.GROUP_ID_MY_ACTIVITY_LIKES);
    }

    @Override
    protected NotificationCompat.Builder getBuilder() throws InternalDataException {
        NotificationCompat.Builder builder = super.getBuilder();
        if (builder != null) {
            String text =
                context.getString(R.string.single_like_your_notification, pushAttr.getRealName(),
                    ActivityType.getLocalizedNameByActivityId(context.getResources(),
                        pushAttr.getActivityId()));
            builder = builder.setContentText(text)
                .setStyle(new NotificationCompat.BigTextStyle().bigText(text).setSummaryText(text));
            return builder;
        } else {
            return null;
        }
    }

    @Override
    protected boolean isEnabled() {
        return getNotificationSettings().workoutReactionPushEnabled();
    }

    @Override
    protected int getNotificationId() {
        return createUniqueNotificationId(R.string.single_like_your_notification,
            pushAttr.getUsername());
    }

    @Override
    public boolean handleAction(String action) throws InternalDataException {
        return true;
    }

    @Override
    protected PendingIntent getContentIntent() {

        Intent intent = rewriteNavigator.createIntent(
            context,
            pushAttr.getUsername(),
            null,
            pushAttr.getWorkoutKey(),
            null,
            true,
            false,
            false,
            false
        );
        TaskStackBuilder stackBuilder = TaskStackBuilder.create(context);
        stackBuilder.addNextIntentWithParentStack(Objects.requireNonNull(intent));
        stackBuilder.addNextIntent(AppOpenAnalyticsActivity.Companion.newStartIntent(context, PUSH_MESSAGE, LIKES));
        for (int i = 0; i < stackBuilder.getIntentCount(); ++i) {
            Intent editing = stackBuilder.editIntentAt(i);
            if (editing != null) {
                editing.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
            }
        }

        return stackBuilder.getPendingIntent(getNotificationId(),
            getFlagsWithImmutable(PendingIntent.FLAG_UPDATE_CURRENT));
    }
}
