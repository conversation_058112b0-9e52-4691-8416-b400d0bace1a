package com.stt.android.notifications.noui

import androidx.work.Data
import androidx.work.ExistingWorkPolicy
import androidx.work.OneTimeWorkRequestBuilder
import com.stt.android.notifications.PushAttr
import com.stt.android.notifications.STTNotificationNoUI

class RemoteSyncNotification(val pushAttr: PushAttr?) : STTNotificationNoUI() {

    companion object {
        const val KEY_SYNC_TARGETS_FOR_WORK_MANAGER = "KEY_SYNC_TARGETS_FOR_WORK_MANAGER"
        const val KEY_SYNC_TARGETS_FOR_INPUT_DATA = "KEY_SYNC_TARGETS_FOR_INPUT_DATA"
        const val SYNC_TARGET_ROUTES = "ROUTES"
        const val SYNC_TARGET_WORKOUTS = "WORKOUTS"
        const val SYNC_TARGET_SUUNTO_PLUS_GUIDE = "SUUNTOPLUS_GUIDES"
    }

    override fun handleCustomNotificationWithoutUI() {
        pushAttr?.syncTargets?.let {
            workManager.enqueueUniqueWork(
                KEY_SYNC_TARGETS_FOR_WORK_MANAGER,
                ExistingWorkPolicy.APPEND_OR_REPLACE,
                OneTimeWorkRequestBuilder<RemoteSyncNotificationWorker>()
                    .setInputData(
                        Data.Builder()
                            .putStringArray(KEY_SYNC_TARGETS_FOR_INPUT_DATA, it)
                            .build()
                    )
                    .build()
            )
        }
    }
}
