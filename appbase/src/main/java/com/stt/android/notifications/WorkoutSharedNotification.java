package com.stt.android.notifications;

import android.annotation.SuppressLint;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;
import androidx.core.app.TaskStackBuilder;
import androidx.work.WorkManager;
import coil3.Image;
import coil3.ImageLoaders_nonJsCommonKt;
import coil3.Image_androidKt;
import coil3.SingletonImageLoader;
import coil3.request.ImageRequest;
import com.stt.android.R;
import com.stt.android.analytics.AmplitudeAnalyticsTracker;
import com.stt.android.analytics.AnalyticsEvent;
import com.stt.android.analytics.AnalyticsProperties;
import static com.stt.android.analytics.AnalyticsPropertyValue.AppOpenSourceType.PUSH_MESSAGE;
import static com.stt.android.analytics.AnalyticsPropertyValue.AppOpenSourceTypeDetail.FRIENDS_ACTIVITIES;
import com.stt.android.analytics.AppOpenAnalyticsActivity;
import com.stt.android.controllers.ReactionModel;
import com.stt.android.data.reactions.ReactionRemoteSyncJob;
import com.stt.android.domain.user.ImageInformation;
import com.stt.android.domain.user.ReactionSummary;
import com.stt.android.domain.workout.SharingOption;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.exceptions.BackendException;
import com.stt.android.exceptions.InternalDataException;
import com.stt.android.workouts.edit.SaveWorkoutHeaderService;
import io.reactivex.Completable;
import io.reactivex.schedulers.Schedulers;
import java.util.List;
import java.util.Objects;
import timber.log.Timber;

class WorkoutSharedNotification extends STTNotificationUI {
    protected final WorkoutHeader workoutHeader;
    protected final ReactionSummary reactionSummary;
    protected final Bundle extras;

    WorkoutSharedNotification(Context context, PushAttr pushAttr, Bundle extras) {
        super(context, pushAttr, NotificationChannelIds.CHANNEL_ID_FRIEND_ACTIVITY_SHARED,
            NotificationGroup.GROUP_ID_FRIEND_ACTIVITY_SHARED);
        final String workoutKey = pushAttr.getWorkoutKey();
        try {
            workoutHeader = workoutHeaderController.getOrFetch(workoutKey);
        } catch (BackendException e) {
            throw new IllegalArgumentException("Failed to fetch workout", e);
        }
        if (workoutHeader == null) {
            throw new IllegalArgumentException("No workout for key: " + workoutKey);
        }

        try {
            if (workoutHeader.getKey() != null) {
                this.reactionSummary = reactionModel.findSummary(
                    workoutHeader.getKey(),
                    ReactionSummary.REACTION_LIKE
                );
            } else {
                reactionSummary = null;
            }
        } catch (InternalDataException e) {
            throw new RuntimeException(e);
        }
        this.extras = extras;
    }

    @Override
    protected NotificationCompat.Action getAction() {
        if (reactionSummary == null || !reactionSummary.isUserReacted()) {
            return new NotificationCompat.Action.Builder(R.drawable.ic_thumb_up_white_24dp,
                context.getString(R.string.notification_workout_like_action),
                getActionIntent()).build();
        }
        return null;
    }

    private PendingIntent getActionIntent() {
        // Use a broadcast receiver as the target for the pending intent so that
        // PushNotificationHandler's job can be explicitly enqueued to job manager.
        // Using PushNotificationHandler service as the target for the pending intent
        // directly does not work.

        // Note that Android 12 does not allow launching an intent from a broadcast receiver sent
        // from a notification or notification action (notification trampoline restrictions).
        // However, the like action does not show any UI so it is still fine.
        Intent likeIntent = new Intent(context, NotificationActionReceiver.class);
        likeIntent.setAction(PushNotificationHandler.LIKE_ACTION);
        likeIntent.putExtra(PushNotificationHandler.KEY_EXTRAS, extras);
        return PendingIntent.getBroadcast(context, getNotificationId(), likeIntent, getFlagsWithImmutable(PendingIntent.FLAG_ONE_SHOT));
    }

    @Override
    protected PendingIntent getContentIntent() {
        Intent intent = rewriteNavigator.createIntent(
            context,
            workoutHeader.getUsername(),
            null,
            workoutHeader.getKey(),
            null,
            true,
            false,
            false,
            false
        );

        TaskStackBuilder stackBuilder = TaskStackBuilder.create(context);
        stackBuilder.addNextIntentWithParentStack(Objects.requireNonNull(intent));
        stackBuilder.addNextIntent(AppOpenAnalyticsActivity.Companion.newStartIntent(context, PUSH_MESSAGE, FRIENDS_ACTIVITIES));
        for (int i = 0; i < stackBuilder.getIntentCount(); ++i) {
            Intent editing = stackBuilder.editIntentAt(i);
            if (editing != null) {
                editing.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
            }
        }

        return stackBuilder.getPendingIntent(getNotificationId(),
            getFlagsWithImmutable(PendingIntent.FLAG_UPDATE_CURRENT));
    }

    @Override
    public boolean isEnabled() {
        return getNotificationSettings().workoutSharePushEnabled();
    }

    @Override
    @Nullable
    protected NotificationCompat.Builder getBuilder() throws InternalDataException {
        NotificationCompat.Builder builder = super.getBuilder();
        if (builder != null) {
            String text = context.getString(R.string.single_new_share, pushAttr.getRealName());
            builder = builder.setContentText(text)
                .setStyle(new NotificationCompat.BigTextStyle().bigText(text));
            return buildBigPicture(builder, text);
        } else {
            return null;
        }
    }

    @Override
    protected int getNotificationId() {
        String key = workoutHeader.getKey() != null ? workoutHeader.getKey() : "";
        return createUniqueNotificationId(R.string.single_new_share, key);
    }

    @Override
    public boolean handleAction(String action) throws InternalDataException {
        if (PushNotificationHandler.LIKE_ACTION.equals(action)) {
            handleReaction(builder, workoutHeader, reactionModel, context, workManager, amplitudeAnalyticsTracker);
            return true;
        }
        return false;
    }

    private NotificationCompat.Builder buildBigPicture(NotificationCompat.Builder builder,
        String text) {
        try {
            if (workoutHeader == null || workoutHeader.getPictureCount() == 0) {
                return builder;
            }
            List<ImageInformation> images =
                picturesController.loadImages(workoutHeader).toBlocking().firstOrDefault(null);
            if (images == null || images.size() == 0) {
                return builder;
            }
            // we're currently uploading bigger images, so only load a small one into memory
            ImageRequest request = new ImageRequest.Builder(context)
                .data(images.get(0))
                .size(640)
                .build();
            Image image = ImageLoaders_nonJsCommonKt
                .executeBlocking(SingletonImageLoader.get(context), request)
                .getImage();
            Bitmap bitmap = image != null
                ? Image_androidKt.toBitmap(image, image.getWidth(), image.getHeight())
                : null;
            if (bitmap == null) {
                return builder;
            }
            builder.setStyle(
                new NotificationCompat.BigPictureStyle().bigPicture(bitmap).setSummaryText(text));
        } catch (Throwable e) {
            Timber.w(e, "Failed to fetch images");
        }
        return builder;
    }

    @SuppressLint({"RestrictedApi", "CheckResult"})
    private static void handleReaction(NotificationCompat.Builder builder,
        final WorkoutHeader workoutHeader, ReactionModel reactionModel, final Context context,
        WorkManager workManager, AmplitudeAnalyticsTracker amplitudeAnalyticsTracker)
        throws InternalDataException {
        // FIXME we're calling library private API here. This will require creating a new builder
        // instead of reusing the existing one.
        // https://suunto.tpondemand.com/entity/98257-remove-call-to-library-private-api
        builder.mActions.clear();
        Completable completable = null;
        final ReactionSummary likesSummary;
        if (workoutHeader.getKey() != null) {
            likesSummary =
                reactionModel.findSummary(workoutHeader.getKey(), ReactionSummary.REACTION_LIKE);
        } else {
            likesSummary = null;
        }
        if (likesSummary != null) {
            if (likesSummary.isUserReacted()) {
                completable = reactionModel.removeReaction(likesSummary);
            } else {
                completable = reactionModel.addReaction(likesSummary);
            }
        }
        if (completable == null && workoutHeader.getKey() != null) {
            // first like
            completable = reactionModel.addReaction(
                ReactionSummary.local(workoutHeader.getKey(), ReactionSummary.REACTION_LIKE));
        }

        if (completable == null) {
            completable = Completable.complete();
        }

        // Return value is ignored. We don't have a proper context here to handle disposing.
        // TODO: Use a proper background job to handle this operation
        //noinspection ResultOfMethodCallIgnored
        completable
            .subscribeOn(Schedulers.io())
            .subscribe(
                () -> {
                    int reactionCount;
                    if (likesSummary == null || !likesSummary.isUserReacted()) {
                        reactionCount = workoutHeader.getReactionCount() + 1;
                        String sharing = "Private";
                        if (workoutHeader.getSharingOptions().contains(
                            SharingOption.EVERYONE)) {
                            sharing = "Public";
                        } else if (workoutHeader.getSharingOptions().contains(
                            SharingOption.FOLLOWERS)) {
                            sharing = "Followers";
                        }

                        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.LIKE_WORKOUT,
                            new AnalyticsProperties()
                                .put("Source", "Notification")
                                .put("TargetAccountType", "Normal")
                                .put("TargetWorkoutVisibility", sharing)
                                .put("NumberOfPhotos", workoutHeader.getPictureCount())
                                .put("NumberOfLikes", workoutHeader.getReactionCount())
                                .put("NumberOfComments", workoutHeader.getCommentCount())
                                .putYesNo("HasDescription", workoutHeader.getDescription() != null
                                    && !workoutHeader.getDescription().isEmpty())
                                .put("ActivityType",
                                    workoutHeader.getActivityType().getSimpleName())
                                .put("DurationInMinutes", workoutHeader.getTotalTime())
                                .put("DistanceInMeters", workoutHeader.getTotalDistance()));

                        WorkoutHeader updated = workoutHeader.toBuilder()
                            .reactionCount(reactionCount)
                            .locallyChanged(true)
                            .build();
                        SaveWorkoutHeaderService.enqueueWork(context, updated, false);
                        ReactionRemoteSyncJob.enqueue(workManager);
                    }
                },
                // we can't do much here
                throwable -> Timber.w(throwable, "handleReaction failed")
            );
    }
}
