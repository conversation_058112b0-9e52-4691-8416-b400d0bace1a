package com.stt.android.notifications;

import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;
import androidx.core.app.TaskStackBuilder;
import com.stt.android.R;
import static com.stt.android.analytics.AnalyticsPropertyValue.AppOpenSourceTypeDetail.NEW_FOLLOWERS;
import com.stt.android.domain.user.User;
import com.stt.android.exceptions.InternalDataException;
import com.stt.android.social.following.PeopleActivity;

class FollowRequestReceivedNotification extends FollowNotification {

    private static final String KEY_USER = "com.stt.android.KEY_USER";
    private static final int USER_PROFILE_INTENT_INDEX = 1;

    FollowRequestReceivedNotification(Context context, PushAttr pushAttr) {
        super(context, pushAttr, NotificationChannelIds.CHANNEL_ID_NEW_FOLLOWERS,
            NotificationGroup.GROUP_ID_NEW_FOLLOWERS);
    }

    @Override
    protected NotificationCompat.Builder getBuilder() throws InternalDataException {
        NotificationCompat.Builder builder = super.getBuilder();
        if (builder != null) {
            String text =
                context.getString(R.string.follow_req_notification, pushAttr.getRealName());
            return builder.setContentText(text)
                .setStyle(new NotificationCompat.BigTextStyle().bigText(text));
        } else {
            return null;
        }
    }

    @Override
    protected int getNotificationId() {
        return createUniqueNotificationId(R.string.follow_req_notification, pushAttr.getUsername());
    }

    @Override
    protected PendingIntent getContentIntent() {
        Intent intentToPeopleActivity = PeopleActivity.newIntent(context, true);
        TaskStackBuilder stackBuilder = TaskStackBuilder.create(context);
        stackBuilder.addNextIntentWithParentStack(intentToPeopleActivity);
        User user = currentUserController.getCurrentUser();
        Intent userProfileIntent = stackBuilder.editIntentAt(USER_PROFILE_INTENT_INDEX);
        if (userProfileIntent != null) {
            userProfileIntent.putExtra(KEY_USER, user);
        }

        for (int i = 0; i < stackBuilder.getIntentCount(); ++i) {
            Intent editing = stackBuilder.editIntentAt(i);
            if (editing != null) {
                editing.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
            }
        }

        return stackBuilder.getPendingIntent(getNotificationId(),
            getFlagsWithImmutable(PendingIntent.FLAG_UPDATE_CURRENT));
    }

    @Nullable
    @Override
    protected String getSourceTypeDetail() {
        return NEW_FOLLOWERS;
    }
}
