package com.stt.android.notifications;

import android.app.PendingIntent;
import androidx.annotation.NonNull;
import androidx.annotation.StringRes;
import androidx.work.WorkManager;
import com.stt.android.STTApplication;
import com.stt.android.analytics.AmplitudeAnalyticsTracker;
import com.stt.android.controllers.CurrentUserController;
import com.stt.android.controllers.PicturesController;
import com.stt.android.controllers.ReactionModel;
import com.stt.android.controllers.UserSettingsController;
import com.stt.android.controllers.WorkoutHeaderController;
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator;
import com.stt.android.domain.user.NotificationSettings;
import javax.inject.Inject;

public abstract class STTNotification {
    @Inject
    protected UserSettingsController userSettingsController;
    @Inject
    protected WorkoutHeaderController workoutHeaderController;
    @Inject
    protected PicturesController picturesController;
    @Inject
    protected ReactionModel reactionModel;
    @Inject
    protected CurrentUserController currentUserController;
    @Inject
    protected WorkManager workManager;
    @Inject
    protected WorkoutDetailsRewriteNavigator rewriteNavigator;
    @Inject
    protected AmplitudeAnalyticsTracker amplitudeAnalyticsTracker;

    protected STTNotification() {
        STTApplication.getComponent().inject(this);
    }

    @NonNull
    protected NotificationSettings getNotificationSettings() {
        return userSettingsController.getSettings().getNotificationSettings();
    }

    protected int createUniqueNotificationId(@StringRes int notificationText,
        String notificationAttr) {
        int h = 1;
        h *= 1000003;
        h ^= notificationText;
        h *= 1000003;
        h ^= notificationAttr.hashCode();
        return h;
    }

    protected int getFlagsWithImmutable(int flags) {
        return flags | PendingIntent.FLAG_IMMUTABLE;
    }
}
