package com.stt.android.notifications

import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import androidx.core.app.NotificationCompat
import androidx.core.app.TaskStackBuilder
import com.stt.android.R
import com.stt.android.analytics.AnalyticsPropertyValue.AppOpenSourceType.PUSH_MESSAGE
import com.stt.android.analytics.AnalyticsPropertyValue.AppOpenSourceTypeDetail.COMMENTS
import com.stt.android.analytics.AppOpenAnalyticsActivity
import com.stt.android.domain.workout.ActivityType

internal class WorkoutCommentNotification(
    context: Context,
    pushAttr: PushAttr,
) : STTNotificationUI(
    context,
    pushAttr,
    CHANNEL_ID_FRIEND_ACTIVITY_COMMENT,
    NotificationGroup.GROUP_ID_FRIEND_ACTIVITY_COMMENT,
) {
    override fun isEnabled() = notificationSettings.workoutCommentPushEnabled

    override fun getNotificationId(): Int =
        createUniqueNotificationId(R.string.single_commenter_notification, pushAttr.workoutKey)

    override fun getBuilder(): NotificationCompat.Builder? {
        val builder = super.getBuilder() ?: return null

        val text = if (workoutHeaderController.isOwnWorkout(pushAttr.workoutKey)) {
            context.getString(
                R.string.single_commenter_your_notification,
                pushAttr.realName,
                ActivityType.getLocalizedNameByActivityId(context.resources, pushAttr.activityId),
            )
        } else {
            context.getString(
                R.string.single_commenter_notification,
                pushAttr.realName,
                pushAttr.workoutOwnerRealName,
                ActivityType.getLocalizedNameByActivityId(context.resources, pushAttr.activityId),
            )
        }

        return builder.setContentText(text)
            .setStyle(NotificationCompat.BigTextStyle().bigText(text))
    }

    override fun getContentIntent(): PendingIntent? {
        val intent = rewriteNavigator.createIntent(
            context = context,
            username = pushAttr.username,
            workoutId = null,
            workoutKey = pushAttr.workoutKey
        )
        val stackBuilder = TaskStackBuilder.create(context)
        stackBuilder.addNextIntentWithParentStack(intent)
        stackBuilder.addNextIntent(AppOpenAnalyticsActivity.newStartIntent(context, PUSH_MESSAGE, COMMENTS))
        for (i in 0 until stackBuilder.intentCount) {
            stackBuilder.editIntentAt(i)?.flags =
                Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
        }

        return stackBuilder.getPendingIntent(
            notificationId,
            getFlagsWithImmutable(PendingIntent.FLAG_UPDATE_CURRENT)
        )
    }
}
