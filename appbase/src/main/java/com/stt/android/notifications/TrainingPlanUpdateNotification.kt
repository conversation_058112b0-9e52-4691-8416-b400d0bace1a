package com.stt.android.notifications

import android.app.PendingIntent
import android.content.Context
import androidx.core.app.NotificationCompat
import com.stt.android.R
import com.stt.android.exceptions.InternalDataException
import com.stt.android.home.HomeActivityNavigator

class TrainingPlanUpdateNotification(
    context: Context,
    pushAttr: PushAttr,
    private val homeActivityNavigator: HomeActivityNavigator
) : STTNotificationUI(
    context,
    pushAttr,
    CHANNEL_ID_TRAINING_PLAN_UPDATES,
    NotificationGroup.GROUP_ID_TRAINING_PLAN_UPDATES
) {
    override fun getContentIntent(): PendingIntent {
        return PendingIntent.getActivity(
            context,
            notificationId,
            homeActivityNavigator.newStartIntentToAiPlanner(context),
            getFlagsWithImmutable(PendingIntent.FLAG_UPDATE_CURRENT)
        )
    }

    override fun isEnabled(): Boolean = true

    override fun getNotificationId(): Int {
        return createUniqueNotificationId(
            R.string.notifications_training_plan_updates_title,
            pushAttr.username
        )
    }

    @Throws(InternalDataException::class)
    override fun getBuilder(): NotificationCompat.Builder? {
        val builder = super.getBuilder()
        return if (builder != null) {
            val title = context.getString(R.string.notifications_training_plan_updates_title)
            val contentText =
                context.getString(R.string.notifications_training_plan_updates_content)
            builder
                .setContentTitle(title)
                .setContentText(contentText)
                .setStyle(NotificationCompat.BigTextStyle().bigText(contentText))
        } else {
            null
        }
    }
}
