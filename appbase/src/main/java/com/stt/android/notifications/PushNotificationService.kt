package com.stt.android.notifications

import com.emarsys.Emarsys
import com.emarsys.service.EmarsysFirebaseMessagingServiceUtils
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import com.helpshift.Core
import com.stt.android.FeatureFlags
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsPropertyValue.PushMessageType
import com.stt.android.eventtracking.EventTracker
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

private const val HELPSHIFT_MESSAGE_ORIGIN_VALUE = "helpshift"
private const val HELPSHIFT_MESSAGE_ORIGIN_KEY = "origin"

@AndroidEntryPoint
class PushNotificationService : FirebaseMessagingService() {

    @Inject
    lateinit var featureFlags: FeatureFlags

    @Inject
    lateinit var eventTracker: EventTracker

    override fun onNewToken(token: String) {
        super.onNewToken(token)
        Timber.d("Refreshed token: %s", token)
        FCMUtil.saveToken(applicationContext, token)
        Core.registerDeviceToken(this, token)
        Emarsys.push.setPushToken(token)
    }

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        Timber.d("Received message from: %s", remoteMessage.from)
        if (featureFlags.isEmarsysEnabled && EmarsysFirebaseMessagingServiceUtils.handleMessage(this, remoteMessage)) {
            Timber.d("Emarsys notification handled")
            eventTracker.trackEvent(
                AnalyticsEvent.PUSH_DELIVERED,
                mapOf(AnalyticsEventProperty.PUSH_MESSAGE_TYPE to PushMessageType.MARKETING),
            )
        } else {
            // Check if message contains a data payload. We are currently using only data messages
            // instead of Firebase notification messages. See difference between notification message
            // and data message from: https://firebase.google.com/docs/cloud-messaging/concept-options
            if (remoteMessage.data.isNotEmpty()) {
                val data = remoteMessage.data
                Timber.d("Message data payload: %s", data)

                // See https://developers.helpshift.com/android/notifications/
                val helpShiftOrigin = data[HELPSHIFT_MESSAGE_ORIGIN_KEY]
                if (helpShiftOrigin != null && helpShiftOrigin == HELPSHIFT_MESSAGE_ORIGIN_VALUE) {
                    Core.handlePush(this, data)
                } else {
                    PushNotificationHandler.enqueueWork(this, remoteMessage)
                }
            }
        }
    }
}
