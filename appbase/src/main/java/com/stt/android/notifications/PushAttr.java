package com.stt.android.notifications;

import androidx.annotation.Nullable;
import com.google.gson.annotations.SerializedName;

public class PushAttr {
    @SerializedName("username")
    private final String username;

    @SerializedName("name")
    private final String realName;

    @SerializedName("image")
    private final String profileImageUrl;

    @SerializedName("Waid")
    private final int activityId;

    @SerializedName("Wid")
    private final String workoutKey;

    @SerializedName("Wuser")
    private final String workoutOwnerRealName;

    @Nullable
    @SerializedName("syncTargets")
    private final String[] syncTargets;

    private PushAttr(String username, String realName, String profileImageUrl, int activityId,
        String workoutKey, String workoutOwnerRealName, @Nullable String[] syncTargets) {
        this.username = username;
        this.realName = realName;
        this.profileImageUrl = profileImageUrl;
        this.activityId = activityId;
        this.workoutKey = workoutKey;
        this.workoutOwnerRealName = workoutOwnerRealName;
        this.syncTargets = syncTargets;
    }

    public String getUsername() {
        return username;
    }

    public String getRealName() {
        return realName;
    }

    public String getProfileImageUrl() {
        return profileImageUrl;
    }

    public int getActivityId() {
        return activityId;
    }

    public String getWorkoutKey() {
        return workoutKey;
    }

    public String getWorkoutOwnerRealName() {
        return workoutOwnerRealName;
    }

    public String[] getSyncTargets() {
        return syncTargets;
    }
}
