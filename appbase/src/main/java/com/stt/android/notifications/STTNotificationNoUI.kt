package com.stt.android.notifications

import com.stt.android.notifications.noui.RemoteSyncNotification

abstract class STTNotificationNoUI : STTNotification() {

    companion object {
        fun getNotification(pushAttr: PushAttr): STTNotificationNoUI {
            return RemoteSyncNotification(pushAttr)
        }
    }

    // This is used to call/trigger function on STTNotificationNoUI subclasses
    abstract fun handleCustomNotificationWithoutUI()
}
