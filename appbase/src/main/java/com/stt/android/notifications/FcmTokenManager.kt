package com.stt.android.notifications

import android.content.Context
import com.google.firebase.messaging.FirebaseMessaging
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.runSuspendCatchingWithTimeout
import com.stt.android.data.notifications.FcmTokenDataSource
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Named
import kotlin.time.Duration.Companion.seconds

class FcmTokenManager @Inject constructor(
    private val context: Context,
    private val dispatchers: CoroutinesDispatchers,
    @Named("GooglePlayServicesAvailable") private val isGooglePlayServicesAvailable: Boolean,
) : FcmTokenDataSource {

    override suspend fun save(token: String) = withContext(dispatchers.io) {
        FCMUtil.saveToken(context, token)
    }

    override suspend fun cleanCached() = withContext(dispatchers.io) {
        FCMUtil.cleanCached(context)
    }

    override suspend fun fetch(): String? = withContext(dispatchers.io) {
        if (!isGooglePlayServicesAvailable) return@withContext null
        // 5 seconds is plenty, otherwise may stay on the login view for a long time
        runSuspendCatchingWithTimeout(5.seconds.inWholeMilliseconds) {
            // In China, if cannot access Firebase, will be blocked for a long time
            FirebaseMessaging.getInstance().token.await()
                .also { FCMUtil.saveToken(context, it) }
        }.getOrElse { e ->
            val message = "Error while fetching Firebase messaging token"
            if (e is TimeoutCancellationException) {
                Timber.d(message)
            } else {
                Timber.w(e, message)
            }
            FCMUtil.getFcmToken(context)
        }
    }

    override suspend fun getFcmTokenSyncedStatus(token: String): Boolean =
        withContext(dispatchers.io) {
            FCMUtil.getFcmTokenSyncedStatus(context, token)
        }

    override suspend fun setFcmTokenAsSynced(token: String): Unit =
        withContext(dispatchers.io) {
            FCMUtil.setFcmTokenAsSynced(context, token)
        }
}
