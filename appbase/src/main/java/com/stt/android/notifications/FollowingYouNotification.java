package com.stt.android.notifications;

import android.content.Context;
import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;
import com.stt.android.R;
import static com.stt.android.analytics.AnalyticsPropertyValue.AppOpenSourceTypeDetail.NEW_FOLLOWERS;
import com.stt.android.exceptions.InternalDataException;

public class FollowingYouNotification extends FollowNotification {

    protected FollowingYouNotification(Context context, PushAttr pushAttr) {
        super(context, pushAttr, NotificationChannelIds.CHANNEL_ID_NEW_FOLLOWERS,
            NotificationGroup.GROUP_ID_NEW_FOLLOWERS);
    }

    @Override
    protected int getNotificationId() {
        return createUniqueNotificationId(R.string.user_started_following, pushAttr.getUsername());
    }

    @Override
    protected NotificationCompat.Builder getBuilder() throws InternalDataException {
        NotificationCompat.Builder builder = super.getBuilder();
        if (builder != null) {
            String text =
                context.getString(R.string.user_started_following, pushAttr.getRealName());
            return builder.setContentText(text)
                .setStyle(new NotificationCompat.BigTextStyle().bigText(text));
        } else {
            return null;
        }
    }

    @Nullable
    @Override
    protected String getSourceTypeDetail() {
        return NEW_FOLLOWERS;
    }
}
