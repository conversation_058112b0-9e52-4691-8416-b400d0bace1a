package com.stt.android.notifications;

import android.content.Context;
import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;
import com.stt.android.R;
import com.stt.android.exceptions.InternalDataException;

class FollowRequestAcceptedNotification extends FollowNotification {

    FollowRequestAcceptedNotification(Context context, PushAttr pushAttr) {
        super(context, pushAttr, NotificationChannelIds.CHANNEL_ID_NEW_FOLLOWERS,
            NotificationGroup.GROUP_ID_NEW_FOLLOWERS);
    }

    @Override
    protected int getNotificationId() {
        return createUniqueNotificationId(R.string.follow_req_accepted, pushAttr.getUsername());
    }

    @Override
    @Nullable
    protected NotificationCompat.Builder getBuilder() throws InternalDataException {
        NotificationCompat.Builder builder = super.getBuilder();
        if (builder != null) {
            String text = context.getString(R.string.follow_req_accepted, pushAttr.getRealName());
            return builder.setContentText(text)
                .setStyle(new NotificationCompat.BigTextStyle().bigText(text));
        } else {
            return null;
        }
    }
}
