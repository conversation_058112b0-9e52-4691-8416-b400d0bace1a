package com.stt.android.notifications.noui

import com.stt.android.domain.sync.SyncRequest
import com.stt.android.domain.sync.SyncRequestHandler
import javax.inject.Inject

class WorkoutsSyncTrigger @Inject constructor(
    private val syncRequestHandler: SyncRequestHandler,
) : SyncTrigger {
    override suspend fun triggerSync() {
        syncRequestHandler.runRequestInQueue(SyncRequest.pullOwnWorkouts())
    }
}
