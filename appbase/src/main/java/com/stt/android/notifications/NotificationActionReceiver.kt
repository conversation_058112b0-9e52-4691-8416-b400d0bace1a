package com.stt.android.notifications

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import timber.log.Timber

class NotificationActionReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context?, intent: Intent?) {
        // Enqueue an action handler via PushNotificationHandler to handle a user initiated action from a notification.
        // This is necessary because using PushNotificationHandler as a target for a pending intent directly does not
        // seem to work. Using a broadcast receiver allows the job to be explicitly enqueued, which fixes the issue.
        Timber.d("onReceive $context $intent")
        if (context != null && intent != null) {
            PushNotificationHandler.enqueueWork(context, intent)
        }
    }
}
