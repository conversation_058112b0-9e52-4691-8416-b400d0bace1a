package com.stt.android.notifications;

import android.Manifest;
import android.app.Notification;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import androidx.annotation.CallSuper;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;
import androidx.core.content.ContextCompat;
import coil3.Image;
import coil3.ImageLoaders_nonJsCommonKt;
import coil3.Image_androidKt;
import coil3.SingletonImageLoader;
import coil3.request.ImageRequest;
import com.google.firebase.messaging.FirebaseMessaging;
import com.stt.android.R;
import com.stt.android.analytics.AnalyticsPropertyValue.PushMessageType;
import com.stt.android.exceptions.InternalDataException;
import com.stt.android.home.HomeActivityNavigator;
import static com.stt.android.notifications.NotificationChannelIds.CHANNEL_ID_CRITICAL_INFORMATION;
import static com.stt.android.notifications.PushNotificationHandler.KEY_EXTRAS;
import com.stt.android.utils.STTConstants;
import timber.log.Timber;

public abstract class STTNotificationUI extends STTNotification {
    private static final String MESSAGE_TYPE_WORKOUT_COMMENT = "WORKOUT_COMMENT";
    private static final String MESSAGE_TYPE_WORKOUT_SHARED = "WORKOUT_SHARED";
    private static final String MESSAGE_TYPE_FRIEND_JOINED_FACEBOOK = "FRIEND_JOINED_FACEBOOK";
    private static final String MESSAGE_TYPE_MY_WORKOUT_REACTION = "MY_WORKOUT_REACTION";
    private static final String MESSAGE_TYPE_FOLLOWING_YOU = "FOLLOW";
    private static final String MESSAGE_TYPE_FOLLOW_REQUEST = "FOLLOW_REQUEST";
    private static final String MESSAGE_TYPE_FOLLOW_ACCEPT = "FOLLOW_ACCEPT";
    private static final String MESSAGE_TYPE_TRAINING_PLAN_UPDATE = "TRAINING_PLAN_UPDATE";

    protected final Context context;
    protected final PushAttr pushAttr;
    protected final String channelId;
    private final NotificationGroup notificationGroup;
    protected NotificationCompat.Builder builder;
    protected NotificationCompat.Builder groupBuilder;

    protected STTNotificationUI(
        @NonNull Context context,
        @NonNull PushAttr pushAttr,
        @NonNull @ChannelId String channelId,
        @NonNull NotificationGroup notificationGroup) {
        this.context = context;
        this.pushAttr = pushAttr;
        this.channelId = channelId;
        this.notificationGroup = notificationGroup;
    }

    @Nullable
    static STTNotificationUI getNotification(Context context, String messageType, PushAttr pushAttr,
        Bundle extras, HomeActivityNavigator homeActivityNavigator) {
        return switch (messageType) {
            case MESSAGE_TYPE_WORKOUT_COMMENT ->
                // Deeplinks into WorkDetailsActivity
                new WorkoutCommentNotification(context, pushAttr);
            case MESSAGE_TYPE_WORKOUT_SHARED ->
                // Deeplinks into WorkDetailsActivity
                new WorkoutSharedNotification(context, pushAttr, extras);
            case MESSAGE_TYPE_FRIEND_JOINED_FACEBOOK ->
                // Deeplinks into UserProfileActivity of another user
                new FacebookFriendNotification(context, pushAttr);
            case MESSAGE_TYPE_MY_WORKOUT_REACTION ->
                // Deeplinks into WorkDetailsActivity
                new MyWorkoutReactionNotification(context, pushAttr);
            case MESSAGE_TYPE_FOLLOW_ACCEPT ->
                // Deeplinks into UserProfileActivity of another user
                new FollowRequestAcceptedNotification(context, pushAttr);
            case MESSAGE_TYPE_FOLLOW_REQUEST ->
                // Deeplinks into PeopleActivity
                new FollowRequestReceivedNotification(context, pushAttr);
            case MESSAGE_TYPE_FOLLOWING_YOU ->
                // Deeplinks into UserProfileActivity of another user
                new FollowingYouNotification(context, pushAttr);
            case MESSAGE_TYPE_TRAINING_PLAN_UPDATE ->
                new TrainingPlanUpdateNotification(context, pushAttr, homeActivityNavigator);
            default -> null;
        };
    }

    @Nullable
    static String getAnalyticsPushMessageType(String messageType) {
        return switch (messageType) {
            case MESSAGE_TYPE_WORKOUT_COMMENT -> PushMessageType.COMMENTS;
            case MESSAGE_TYPE_WORKOUT_SHARED -> PushMessageType.FRIENDS_ACTIVITIES;
            case MESSAGE_TYPE_MY_WORKOUT_REACTION -> PushMessageType.LIKES;
            case MESSAGE_TYPE_FOLLOW_REQUEST,
                 MESSAGE_TYPE_FOLLOW_ACCEPT,
                 MESSAGE_TYPE_FOLLOWING_YOU -> PushMessageType.NEW_FOLLOWERS;
            case MESSAGE_TYPE_FRIEND_JOINED_FACEBOOK -> PushMessageType.FACEBOOK;
            default -> null;
        };
    }

    @NonNull
    public static NotificationCompat.Builder buildBasicNotificationBuilder(Context context,
        String channelId) {
        return new NotificationCompat.Builder(context, channelId)
            .setAutoCancel(true)
            .setSmallIcon(R.drawable.icon_notification)
            .setContentTitle(context.getString(R.string.brand_name))
            .setVisibility(NotificationCompat.VISIBILITY_PRIVATE)
            .setOnlyAlertOnce(true);
    }

    public boolean buildContent(String action) throws InternalDataException {
        Timber.d("Building notification content for action: %s", action);
        builder = getBuilder();
        groupBuilder = getGroupBuilder();
        if (builder == null || groupBuilder == null) {
            return false;
        }
        if (!handleAction(action)) {
            Timber.d("Adding notification action for action: %s", action);
            builder.addAction(getAction());
        }
        extend();
        return true;
    }

    public boolean shouldShow() {
        // Ignore our own notification settings in Android O and later, where user can change
        // notification settings in system UI.
        return isEnabled();
    }

    public boolean showNotification(Context context) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU
            || ContextCompat.checkSelfPermission(context, Manifest.permission.POST_NOTIFICATIONS)
            == PackageManager.PERMISSION_GRANTED) {
            Notification notification = builder.build();
            Notification groupNotification = groupBuilder.build();
            NotificationManagerCompat nm = NotificationManagerCompat.from(context);
            nm.notify(notificationGroup.getId(), groupNotification);
            nm.notify(getNotificationId(), notification);
            return true;
        }
        return false;
    }

    @CallSuper
    @Nullable
    protected NotificationCompat.Builder getBuilder() throws InternalDataException {
        NotificationCompat.Builder builder = buildBasicNotificationBuilder(context, channelId);
        final PendingIntent pendingIntent = getContentIntent();
        if (pendingIntent == null) {
            return null;
        }
        builder.setContentIntent(pendingIntent);
        builder.setLargeIcon(getLargeIcon());
        builder.setGroup(notificationGroup.getGroupName());
        return builder;
    }

    @CallSuper
    @Nullable
    protected NotificationCompat.Builder getGroupBuilder() throws InternalDataException {
        NotificationCompat.Builder builder = getBuilder();
        if (builder != null) {
            builder.setGroupSummary(true);
        }
        return builder;
    }

    protected boolean handleAction(String action) throws InternalDataException {
        return true;
    }

    protected NotificationCompat.Action getAction() {
        return null;
    }

    private Bitmap getLargeIcon() {
        return buildLargeIcon();
    }

    @Nullable
    private Bitmap buildLargeIcon() {
        try {
            String profileImageUrl = pushAttr.getProfileImageUrl();
            Bitmap bitmap = null;
            // todo move this stuff out of the main thread
            if (!TextUtils.isEmpty(profileImageUrl)) {
                ImageRequest request = new ImageRequest.Builder(context)
                    .data(profileImageUrl)
                    .build();
                Image image = ImageLoaders_nonJsCommonKt
                    .executeBlocking(SingletonImageLoader.get(context), request)
                    .getImage();
                bitmap = image != null
                    ? Image_androidKt.toBitmap(image, image.getWidth(), image.getHeight())
                    : null;
            }
            return bitmap;
        } catch (Throwable e) {
            Timber.e(e, "Failed to load icon.");
            return null;
        }
    }

    private void createDebugNotification(Context context, Intent intent) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU
            || ContextCompat.checkSelfPermission(context, Manifest.permission.POST_NOTIFICATIONS)
            == PackageManager.PERMISSION_GRANTED) {
            NotificationCompat.Builder builder =
                new NotificationCompat.Builder(context, CHANNEL_ID_CRITICAL_INFORMATION)
                    .setSmallIcon(R.drawable.application_icon_android)
                    .setContentTitle(context.getString(R.string.brand_name));

            FirebaseMessaging.getInstance().getToken().addOnCompleteListener(task -> {
                Bundle extras = intent.getBundleExtra(KEY_EXTRAS);
                if (extras == null) {
                    Timber.w("createDebugNotification: missing KEY_EXTRAS from intent");
                    return;
                }

                String debugText = String.format("Registration token: %s\n", task.getResult())
                    + String.format("Message type: %s\n", extras.getString("type"))
                    + String.format("Attrs: %s\n", extras.getString("attrs"));
                NotificationCompat.BigTextStyle style =
                    new NotificationCompat.BigTextStyle().bigText(debugText);
                builder.setStyle(style);
                NotificationManagerCompat.from(context)
                    .notify(STTConstants.NotificationIds.DEBUG, builder.build());
            });
        }
    }

    private void extend() {
        NotificationCompat.WearableExtender wearableExtender =
            new NotificationCompat.WearableExtender();
        wearableExtender.setBackground(getLargeIcon());
        builder.extend(wearableExtender);
        groupBuilder.extend(wearableExtender);
    }

    protected void handleNotification(Context context, Intent intent) {
        if (STTConstants.DEBUG_NOTIFICATION) {
            createDebugNotification(context, intent);
        }

        if (!shouldShow()) {
            return;
        }
        try {
            if (!buildContent(intent.getAction())) {
                return;
            }
        } catch (InternalDataException e) {
            Timber.e(e, "Failed to handle build content based on action");
        }

        if (!showNotification(context)) {
            Timber.w("Failed to show notification");
        }
    }

    protected abstract PendingIntent getContentIntent();

    protected abstract boolean isEnabled();

    /**
     * Notification id should be unique within app so that they don't replace each other
     */
    protected abstract int getNotificationId();
}
