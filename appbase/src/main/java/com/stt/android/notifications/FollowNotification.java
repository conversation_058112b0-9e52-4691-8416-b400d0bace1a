package com.stt.android.notifications;

import android.app.PendingIntent;
import android.content.Context;
import androidx.annotation.Nullable;
import androidx.core.app.TaskStackBuilder;
import static com.stt.android.analytics.AnalyticsPropertyValue.AppOpenSourceType.PUSH_MESSAGE;
import com.stt.android.analytics.AppOpenAnalyticsActivity;
import com.stt.android.social.userprofile.UserProfileActivity;

public abstract class FollowNotification extends STTNotificationUI {

    FollowNotification(Context context, PushAttr pushAttr, String channelId,
        NotificationGroup notificationGroup) {
        super(context, pushAttr, channelId, notificationGroup);
    }

    @Override
    protected boolean isEnabled() {
        /* It is possible that this notification is sent to a user who logged in to this device
        before. If the current user receives a follow related notification with that user, then
        this device will still receive the notification. so we should filter out because current
        user cannot send/accept a follow request with himself.
        . */
        return getNotificationSettings().newFollowerPushEnabled() && !pushAttr.getUsername()
            .equals(currentUserController.getUsername());
    }

    @Override
    protected PendingIntent getContentIntent() {
        TaskStackBuilder stackBuilder = TaskStackBuilder.create(context);
        stackBuilder.addNextIntent(UserProfileActivity.newStartIntent(context, pushAttr.getUsername(), true));
        String sourceTypeDetail = getSourceTypeDetail();
        if (sourceTypeDetail != null) {
            stackBuilder.addNextIntent(AppOpenAnalyticsActivity.Companion.newStartIntent(context, PUSH_MESSAGE, sourceTypeDetail));
        }
        return stackBuilder.getPendingIntent(getNotificationId(), getFlagsWithImmutable(PendingIntent.FLAG_UPDATE_CURRENT));
    }

    @Nullable
    protected String getSourceTypeDetail() {
        return null;
    }
}
