package com.stt.android.notifications

import android.app.NotificationChannel
import android.app.NotificationChannelGroup
import android.app.NotificationManager
import android.content.Context
import com.stt.android.FeatureFlags
import com.stt.android.R
import javax.inject.Inject
import com.stt.android.core.R as CR

/**
 * Notification channels common to all build variants.
 */
abstract class BaseNotificationChannels {

    @Inject
    lateinit var featureFlags: FeatureFlags

    fun create(context: Context) {
        val notificationManager =
            context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        createChannelGroups(context, notificationManager)
        createChannels(context, notificationManager)
    }

    open fun createChannelGroups(context: Context, notificationManager: NotificationManager) {
        notificationManager.createNotificationChannelGroup(
            NotificationChannelGroup(
                CHANNEL_GROUP_ID_MY_ACTIVITIES,
                context.getString(R.string.notification_channel_group_my_activities)
            )
        )

        notificationManager.createNotificationChannelGroup(
            NotificationChannelGroup(
                CHANNEL_GROUP_ID_SOCIAL,
                context.getString(R.string.notification_channel_group_social)
            )
        )

        notificationManager.createNotificationChannelGroup(
            NotificationChannelGroup(
                CHANNEL_GROUP_ID_BRAND,
                context.getString(R.string.brand_name)
            )
        )
    }

    open fun createChannels(context: Context, notificationManager: NotificationManager) {
        notificationManager.createNotificationChannel(
            NotificationChannel(
                CHANNEL_ID_ACTIVITY_RECORDING,
                context.getString(CR.string.notification_channel_activity_recording),
                NotificationManager.IMPORTANCE_DEFAULT
            )
                .apply {
                    group = CHANNEL_GROUP_ID_MY_ACTIVITIES
                }
        )

        notificationManager.createNotificationChannel(
            NotificationChannel(
                CHANNEL_ID_MY_ACTIVITY_LIKES,
                context.getString(R.string.notification_channel_my_activity_likes),
                NotificationManager.IMPORTANCE_HIGH
            )
                .apply {
                    group = CHANNEL_GROUP_ID_MY_ACTIVITIES
                }
        )

        notificationManager.createNotificationChannel(
            NotificationChannel(
                CHANNEL_ID_MY_ACTIVITY_COMMENTS,
                context.getString(R.string.notification_channel_my_activity_comments),
                NotificationManager.IMPORTANCE_HIGH
            )
                .apply {
                    group = CHANNEL_GROUP_ID_MY_ACTIVITIES
                }
        )

        notificationManager.createNotificationChannel(
            NotificationChannel(
                CHANNEL_ID_PERSONAL_ACHIEVEMENTS,
                context.getString(R.string.notification_channel_personal_achievements),
                NotificationManager.IMPORTANCE_HIGH
            )
                .apply {
                    group = CHANNEL_GROUP_ID_MY_ACTIVITIES
                }
        )

        notificationManager.createNotificationChannel(
            NotificationChannel(
                CHANNEL_ID_NEW_FOLLOWERS,
                context.getString(R.string.notification_channel_new_followers),
                NotificationManager.IMPORTANCE_DEFAULT
            )
                .apply {
                    group = CHANNEL_GROUP_ID_SOCIAL
                }
        )

        if (context.resources.getBoolean(R.bool.showSocialWorkoutSharing)) {
            notificationManager.createNotificationChannel(
                NotificationChannel(
                    CHANNEL_ID_FACEBOOK_FRIEND_JOINED,
                    context.getString(R.string.notification_channel_facebook_friend_joined),
                    NotificationManager.IMPORTANCE_DEFAULT
                )
                    .apply {
                        group = CHANNEL_GROUP_ID_SOCIAL
                    }
            )
        }

        notificationManager.createNotificationChannel(
            NotificationChannel(
                CHANNEL_ID_FRIEND_ACTIVITY_SHARED,
                context.getString(R.string.notification_channel_friend_activity_shared),
                NotificationManager.IMPORTANCE_DEFAULT
            )
                .apply {
                    group = CHANNEL_GROUP_ID_SOCIAL
                }
        )

        notificationManager.createNotificationChannel(
            NotificationChannel(
                CHANNEL_ID_FRIEND_ACTIVITY_COMMENT,
                context.getString(R.string.notification_channel_friend_activity_commented),
                NotificationManager.IMPORTANCE_DEFAULT
            )
                .apply {
                    group = CHANNEL_GROUP_ID_SOCIAL
                }
        )

        notificationManager.createNotificationChannel(
            NotificationChannel(
                CHANNEL_ID_CRITICAL_INFORMATION,
                context.getString(R.string.notification_channel_critical_information),
                NotificationManager.IMPORTANCE_HIGH
            )
                .apply {
                    group = CHANNEL_GROUP_ID_BRAND
                }
        )

        notificationManager.createNotificationChannel(
            NotificationChannel(
                CHANNEL_ID_APP_UPDATES,
                context.getString(R.string.notification_channel_app_updates),
                NotificationManager.IMPORTANCE_HIGH
            )
                .apply {
                    group = CHANNEL_GROUP_ID_BRAND
                }
        )

        notificationManager.createNotificationChannel(
            NotificationChannel(
                CHANNEL_ID_EVENTS_AND_CHALLENGES,
                context.getString(R.string.notification_channel_events_and_challenges),
                NotificationManager.IMPORTANCE_DEFAULT
            )
                .apply {
                    group = CHANNEL_GROUP_ID_BRAND
                }
        )

        notificationManager.createNotificationChannel(
            NotificationChannel(
                CHANNEL_ID_UPDATES_FROM_COMMUNITY,
                context.getString(R.string.notification_channel_updates_from_community),
                NotificationManager.IMPORTANCE_DEFAULT
            )
                .apply {
                    group = CHANNEL_GROUP_ID_BRAND
                }
        )

        notificationManager.createNotificationChannel(
            NotificationChannel(
                CHANNEL_ID_FOREGROUND_SYNC,
                context.getString(R.string.notification_channel_foreground_sync),
                NotificationManager.IMPORTANCE_LOW
            )
        )

        if (featureFlags.isAiPlannerEnabled()) {
            notificationManager.createNotificationChannel(
                NotificationChannel(
                    CHANNEL_ID_TRAINING_PLAN_UPDATES,
                    context.getString(R.string.notification_channel_training_planner_updates),
                    NotificationManager.IMPORTANCE_DEFAULT
                ).apply {
                    group = CHANNEL_GROUP_ID_BRAND
                }
            )
        } else {
            try {
                notificationManager.deleteNotificationChannel(CHANNEL_ID_TRAINING_PLAN_UPDATES)
            } catch (e: Exception) {
                // Ignore
            }
        }

        notificationManager.createNotificationChannel(
            NotificationChannel(
                CHANNEL_ID_UPCOMING_PERIOD,
                context.getString(R.string.notification_channel_upcoming_period),
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                group = CHANNEL_GROUP_ID_BRAND
            }
        )

        notificationManager.createNotificationChannel(
            NotificationChannel(
                CHANNEL_ID_LOG_PERIOD_REMINDER,
                context.getString(R.string.notification_channel_log_period_reminder),
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                group = CHANNEL_GROUP_ID_BRAND
            }
        )
    }
}
