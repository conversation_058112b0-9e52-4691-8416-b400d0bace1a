package com.stt.android;

import android.app.Application;
import android.content.Context;
import android.content.res.AssetManager;
import android.content.res.Resources;
import android.hardware.SensorManager;
import android.location.LocationManager;
import android.telephony.TelephonyManager;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import com.google.gson.Gson;
import com.squareup.moshi.JsonAdapter;
import com.squareup.moshi.Moshi;
import com.stt.android.bluetooth.BleCadenceModel;
import com.stt.android.bluetooth.BleCadenceScanner;
import com.stt.android.bluetooth.BleHelper;
import com.stt.android.bluetooth.BleHrModel;
import com.stt.android.bluetooth.BleHrScanner;
import com.stt.android.common.content.AssetManagerModule;
import com.stt.android.common.coroutines.CoroutineDispatchersModule;
import com.stt.android.core.utils.AndroidTimeProvider;
import com.stt.android.core.utils.TimeProvider;
import com.stt.android.di.ContextModule;
import com.stt.android.di.InternalFilesDir;
import com.stt.android.di.IsSTFlavor;
import com.stt.android.di.IsSuuntoFlavor;
import com.stt.android.di.featureflags.FeatureFlagsModule;
import com.stt.android.di.firebase.FirebaseModule;
import com.stt.android.di.sharedprefs.SharedPrefsModule;
import com.stt.android.follow.UserFollowStatus;
import com.stt.android.hr.BluetoothHeartRateDeviceManager;
import com.stt.android.hr.HeartRateDeviceConnectionManager;
import com.stt.android.hr.HeartRateManager;
import com.stt.android.hr.HeartRateUpdateProvider;
import com.stt.android.injection.GsonProvider;
import com.stt.android.mapbox.MapboxKey;
import com.stt.android.network.NetworkStatusProvider;
import com.stt.android.network.OkHttpNetworkProvider;
import com.stt.android.network.interfaces.ANetworkProvider;
import com.stt.android.network.interfaces.NetworkStatus;
import com.stt.android.remote.AssetsBaseUrl;
import com.stt.android.remote.BaseUrl;
import com.stt.android.remote.BaseUrlV2;
import com.stt.android.remote.BaseUrlWithoutApiVersion;
import com.stt.android.remote.BaseUrlWithoutPath;
import com.stt.android.remote.MoshiAdapter;
import com.stt.android.remote.RemoteConfigBaseUrl;
import com.stt.android.remote.RoutingBaseUrl;
import com.stt.android.remote.ServerStatusBaseUrl;
import com.stt.android.remote.SuuntoCloudApiUrl;
import com.stt.android.remote.UserAgent;
import com.stt.android.remote.di.BaseUrlConfiguration;
import com.stt.android.remote.di.RestApiFactory;
import com.stt.android.remote.interceptors.MobileAgentInterceptor;
import com.stt.android.utils.CalendarProvider;
import com.stt.android.utils.FileUtils;
import com.stt.android.utils.FixedFirstDayOfTheWeekCalendarProvider;
import com.stt.android.workouts.filters.DistanceFilter;
import com.stt.android.workouts.filters.LocationFilter;
import com.stt.android.workouts.filters.SpeedFilter;
import dagger.Binds;
import dagger.Module;
import dagger.Provides;
import java.io.File;
import java.lang.reflect.Type;
import java.time.Clock;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import javax.inject.Named;
import javax.inject.Singleton;
import okhttp3.OkHttpClient;
import rx.subjects.PublishSubject;
import rx.subjects.Subject;

@SuppressWarnings("WeakerAccess")
@Module(includes = {
    ContextModule.class,
    FirebaseModule.class,
    SharedPrefsModule.class,
    AssetManagerModule.class,
    CoroutineDispatchersModule.class,
    FeatureFlagsModule.class
})
public abstract class STTBaseModule {
    @Provides
    public static AssetManager provideAssetManager(Context context) {
        return context.getAssets();
    }

    @Provides
    @BaseUrlWithoutApiVersion
    public static String provideBaseUrl(
        BaseUrlConfiguration baseUrlConfiguration
    ) {
        return baseUrlConfiguration.getAskoUrl();
    }

    @Provides
    @BaseUrlWithoutPath
    public static String provideBaseUrlWithoutPath(
        BaseUrlConfiguration baseUrlConfiguration
    ) {
        return baseUrlConfiguration.getAskoBaseUrl();
    }

    @Provides
    @BaseUrl
    public static String provideApiUrlV1(
        BaseUrlConfiguration baseUrlConfiguration
    ) {
        return provideBaseUrl(baseUrlConfiguration) + "v1/";
    }

    @Provides
    @BaseUrlV2
    public static String provideApiUrlV2(
        BaseUrlConfiguration baseUrlConfiguration
    ) {
        return provideBaseUrl(baseUrlConfiguration) + "v2/";
    }

    @Provides
    @ServerStatusBaseUrl
    public static String provideServerStatusBaseUrl(
        BaseUrlConfiguration baseUrlConfiguration
    ) {
        return baseUrlConfiguration.getServerStatusUrl();
    }

    @Provides
    @RemoteConfigBaseUrl
    public static String provideRemoteConfigBaseUrl(BaseUrlConfiguration baseUrlConfiguration) {
        return baseUrlConfiguration.getRemoteConfigUrl();
    }

    @Provides
    @AssetsBaseUrl
    public static String provideAssetsBaseUrl(BaseUrlConfiguration baseUrlConfiguration) {
        return baseUrlConfiguration.getAssetsUrl();
    }

    @Provides
    @RoutingBaseUrl
    public static String provideRoutingBaseUrl(BaseUrlConfiguration baseUrlConfiguration) {
        return baseUrlConfiguration.getRoutingBaseUrl();
    }

    @Provides
    @SuuntoCloudApiUrl
    public static String provideOpenWeatherBaseUrl(BaseUrlConfiguration baseUrlConfiguration) {
        return baseUrlConfiguration.getSuuntoCloudApiUrl();
    }

    @Provides
    @Singleton
    public static ANetworkProvider provideANetworkProvider(OkHttpClient.Builder okHttpClientBuilder, Gson gson) {
        return new OkHttpNetworkProvider(okHttpClientBuilder.build(), gson);
    }

    @Provides
    @Singleton
    public static Gson provideGson() {
        return GsonProvider.getInstance();
    }

    @Provides
    @Singleton
    public static Moshi provideMoshi(
        @MoshiAdapter Set<Object> jsonAdapters,
        Map<Type, JsonAdapter<?>> jsonTypeAdapters,
        Set<JsonAdapter.Factory> jsonAdapterFactories) {
        return RestApiFactory.createMoshi(jsonAdapters, jsonTypeAdapters, jsonAdapterFactories);
    }

    /**
     * @return a Gson instance which supports writing special double values (NaN, Infinity,
     * -Infinity).
     * <p/>
     * It's not clear yet if the backend supports those so keep it local.
     */
    @Provides
    @Named("Float")
    @Singleton
    public static Gson provideSpecialFloatValuesGson() {
        return GsonProvider.INSTANCE.createGsonWithSpecialFloatingPointValues();
    }

    @Provides
    @Nullable
    public static BleCadenceScanner provideBleCadenceDeviceManager(Context context) {
        return BleHelper.supportsBle(context) ? new BleCadenceScanner(context) : null;
    }

    @Provides
    @Singleton
    @Nullable
    public static BleCadenceModel provideBleCadenceModel(Context context) {
        return BleHelper.supportsBle(context) ? new BleCadenceModel(context) : null;
    }

    @Provides
    @Nullable
    public static BleHrScanner provideBleHrScanner(Context context) {
        return BleHelper.supportsBle(context) ? new BleHrScanner(context) : null;
    }

    @Provides
    @Singleton
    @Nullable
    public static BleHrModel provideBleHrModel(Context context) {
        return BleHelper.supportsBle(context) ? new BleHrModel(context) : null;
    }

    @Provides
    @Singleton
    public static LayoutInflater provideLayoutInflater(Context context) {
        return LayoutInflater.from(context);
    }

    @Provides
    @Singleton
    public static Resources provideResources(Context context) {
        return context.getResources();
    }

    @Provides
    @Singleton
    public static LocalBroadcastManager provideLocalBroadcastManager(Context context) {
        return LocalBroadcastManager.getInstance(context);
    }

    @Provides
    @Singleton
    public static FileUtils provideFileUtils(Application application) {
        return new FileUtils(application);
    }

    @Provides
    @Singleton
    public static SensorManager provideSensorManager(Context context) {
        return (SensorManager) context.getSystemService(Context.SENSOR_SERVICE);
    }

    @Provides
    @Singleton
    public static BluetoothHeartRateDeviceManager provideBluetoothHeartRateDeviceManager() {
        return new BluetoothHeartRateDeviceManager();
    }

    @Provides
    @Singleton
    public static HeartRateDeviceConnectionManager provideHeartRateDeviceConnectionManager() {
        return new HeartRateDeviceConnectionManager();
    }

    @Provides
    @Singleton
    public static HeartRateManager provideHeartRateManager() {
        return new HeartRateManager();
    }

    @Provides
    @Singleton
    public static HeartRateUpdateProvider provideHeartRateUpdateProvider(HeartRateManager heartRateManager, Context context) {
        return new HeartRateUpdateProvider(context, heartRateManager);
    }

    @Provides
    @Singleton
    public static LocationManager provideLocationManager(Context context) {
        return (LocationManager) context.getSystemService(Context.LOCATION_SERVICE);
    }

    @Provides
    @Singleton
    public static LocationFilter provideLocationFilter() {
        return new LocationFilter();
    }

    @Provides
    @Singleton
    public static DistanceFilter provideDistanceFilter() {
        return new DistanceFilter();
    }

    @Provides
    @Singleton
    public static SpeedFilter provideSpeedFilter() {
        return new SpeedFilter();
    }

    @Singleton
    @Provides
    public static OkHttpClient.Builder provideBaseOkHttpBuilder(
        @UserAgent String userAgent) {
        return new OkHttpClient.Builder().connectTimeout(30L, TimeUnit.SECONDS)
                .readTimeout(30L, TimeUnit.SECONDS)
                .writeTimeout(30L, TimeUnit.SECONDS)
                .addNetworkInterceptor(new MobileAgentInterceptor(userAgent));
    }

    @Singleton
    @Provides
    public static ReadWriteLock providesSessionLock() {
        return new ReentrantReadWriteLock(true);
    }

    // TODO: Should this be Singleton? Is Android N changing display metrics on the fly?
    @Provides
    public static DisplayMetrics provideDisplayMetrics(Context context) {
        return context.getResources().getDisplayMetrics();
    }

    @Provides
    @Singleton
    @Named("FOLLOWERS")
    public static Subject<UserFollowStatus, UserFollowStatus> provideFollowersSubject() {
        return PublishSubject.<UserFollowStatus>create().toSerialized();
    }

    @Provides
    @Singleton
    @Named("FOLLOWING")
    public static Subject<UserFollowStatus, UserFollowStatus> provideFollowingSubject() {
        return PublishSubject.<UserFollowStatus>create().toSerialized();
    }

    @Provides
    public static Clock provideClock() {
        return Clock.systemDefaultZone();
    }

    @Provides
    public static Locale provideLocale() {
        return Locale.getDefault();
    }

    @Provides
    @IsSuuntoFlavor
    public static boolean provideIsSuuntoFlavor(Context context) {
        return context.getResources().getBoolean(R.bool.suuntoFlavorSpecific);
    }

    @Provides
    @IsSTFlavor
    public static boolean provideIsSTFlavor(Context context) {
        return context.getResources().getBoolean(R.bool.sportsTrackerFlavorSpecific);
    }

    @Provides
    @Nullable
    public static TelephonyManager provideTelephonyManager(Context context) {
        return ContextCompat.getSystemService(context, TelephonyManager.class);
    }

    @Binds
    public abstract NetworkStatus bindNetworkStatus(NetworkStatusProvider networkStatusProvider);

    @Provides
    @Singleton
    public static TimeProvider provideTimeProvider() {
        return new AndroidTimeProvider();
    }

    @Binds
    public abstract CalendarProvider bindCalendarProvider(
        FixedFirstDayOfTheWeekCalendarProvider fixedFirstDayOfTheWeekCalendarProvider
    );

    @Provides
    @MapboxKey
    public static String provideMapboxKey(Resources resources) {
        return resources.getString(R.string.mapbox_access_token);
    }

    @Provides
    @InternalFilesDir
    public static File provideInternalFilesDir(Context context) {
        return context.getFilesDir();
    }
}
