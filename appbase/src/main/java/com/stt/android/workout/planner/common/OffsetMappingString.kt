package com.stt.android.workout.planner.common

import androidx.annotation.VisibleForTesting
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.input.OffsetMapping
import androidx.compose.ui.text.input.TransformedText

/**
 * Helper class for keeping track of index mapping when using VisualTransformation to add formatting
 * to a text in an input field.
 *
 * Any existing string annotations are lost in the transformation. Any inserted pieces of string
 * are styled with given [SpanStyle].
 *
 * For example, formatting characters can be inserted to transform the string "12345" to "1:23'45"
 * for inputting a duration in "hh:mm'ss" format.
 */
class OffsetMappingString(
    originalString: AnnotatedString,
    private val spanStyle: SpanStyle
) : OffsetMapping {
    // Mappings include one past end (lastIndex + 1)
    @VisibleForTesting
    internal val originalToMapped = (0..originalString.length).toMutableList()

    @VisibleForTesting
    internal val mappedToOriginal = originalToMapped.toMutableList()

    var string = originalString

    val length: Int
        get() = string.length

    // Insert a new character to the visually transformed string.
    // Index refers to the transformed string in case characters have been added or removed already.
    fun insert(index: Int, stringToInsert: String) {
        string = string.subSequence(0, index)
            .plus(AnnotatedString(stringToInsert, spanStyle))
            .plus(string.subSequence(index, string.length))

        val originalIndex = mappedToOriginal[index]

        // Increase mapped indices for characters after insertion point
        for (i in originalIndex..originalToMapped.lastIndex) {
            originalToMapped[i] += stringToInsert.length
        }

        // Repeat previous index for all new characters
        repeat(stringToInsert.length) {
            mappedToOriginal.add(index, mappedToOriginal.getOrElse(index - 1) { 0 })
        }
    }

    // Remove a character from the visually transformed string.
    // Index refers to the transformed string in case characters have been added or removed already.
    fun removeAt(index: Int) {
        string = string.subSequence(0, index)
            .plus(string.subSequence(index + 1, string.length))

        val originalIndex = mappedToOriginal[index]

        // Decrease mapped indices for characters after insertion point
        for (i in originalIndex + 1..originalToMapped.lastIndex) {
            originalToMapped[i] = originalToMapped[i].minus(1).coerceAtLeast(0)
        }

        mappedToOriginal.removeAt(index)
    }

    // Filter out all characters where predicate returns false
    fun filter(predicate: (Char) -> Boolean) {
        string.indices.reversed().forEach { index ->
            if (!predicate(string[index])) {
                removeAt(index)
            }
        }
    }

    fun asTransformedText() = TransformedText(
        text = string,
        offsetMapping = this
    )

    override fun originalToTransformed(offset: Int): Int = originalToMapped[offset]
    override fun transformedToOriginal(offset: Int): Int = mappedToOriginal[offset]
}
