package com.stt.android.workout.planner.common

import androidx.compose.foundation.layout.Column
import androidx.compose.material.Surface
import androidx.compose.material.TextField
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.input.TransformedText
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme

/**
 * Visual transform for entering duration in hh:mm'ss format. Note that the actual input value
 * is expected to be in hhmmss format and limited to 6 characters.
 *
 * Can be use for pace input as well when text is limited to 4 characters.
 */
class DurationVisualTransformation(private val separatorStyle: SpanStyle) : VisualTransformation {
    override fun filter(text: AnnotatedString): TransformedText =
        with(OffsetMappingString(text, separatorStyle)) {
            filter { it.isDigit() }
            when {
                length >= 6 -> {
                    insert(2, ":") // 01:2345
                    insert(5, "\'") // 01:23'45
                }

                length == 5 -> {
                    insert(1, ":") // 1:2345
                    insert(4, "\'") // 1:23'45
                }

                length == 4 -> {
                    insert(2, "\'") // 12'34
                }

                length == 3 -> {
                    insert(1, "\'") // 1'23
                }

                length == 2 -> {
                    insert(0, "0\'") // 12 -> 0'12
                }

                length == 1 -> {
                    insert(0, "0\'0") // 1 -> 0'01
                }

                else -> {}
            }

            asTransformedText()
        }
}

@Composable
@Preview
private fun DurationVisualTransformationPreview() {
    AppTheme {
        Surface {
            Column {
                val transform = DurationVisualTransformation(SpanStyle(color = Color.Gray))
                TextField(value = "", onValueChange = {}, visualTransformation = transform)
                TextField(value = "1", onValueChange = {}, visualTransformation = transform)
                TextField(value = "12", onValueChange = {}, visualTransformation = transform)
                TextField(value = "123", onValueChange = {}, visualTransformation = transform)
                TextField(value = "1234", onValueChange = {}, visualTransformation = transform)
                TextField(value = "12345", onValueChange = {}, visualTransformation = transform)
                TextField(value = "123456", onValueChange = {}, visualTransformation = transform)
            }
        }
    }
}
