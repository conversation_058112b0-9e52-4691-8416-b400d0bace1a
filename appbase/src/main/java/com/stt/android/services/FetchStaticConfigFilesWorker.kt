package com.stt.android.services

import android.content.Context
import android.os.SystemClock
import androidx.work.Constraints
import androidx.work.CoroutineWorker
import androidx.work.ExistingWorkPolicy
import androidx.work.ListenableWorker
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequest
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.models.MapSelectionModel
import timber.log.Timber
import javax.inject.Inject

/**
 * Fetches various static configuration files from the server.
 */
class FetchStaticConfigFilesWorker(
    appContext: Context,
    params: WorkerParameters,
    private val mapSelectionModel: MapSelectionModel
) : CoroutineWorker(appContext, params) {
    override suspend fun doWork(): Result = runSuspendCatching {
        val start = SystemClock.elapsedRealtime()
        mapSelectionModel.fetchAndStoreDynamicMapTypes()
        val delta = SystemClock.elapsedRealtime() - start
        Timber.d("It took %d ms to fetch static configuration files from backend", delta)
        Result.success()
    }.getOrElse { e ->
        // it crashes several times with a NullPointerException for some reason
        // https://rink.hockeyapp.net/manage/apps/47264/app_versions/61/crash_reasons/20523436
        // just swallow it here, as it's not a critical issue at all (the map types will be
        // anyway loaded in the map selection fragment)
        Timber.w(e, "Failed to load static config files")
        Result.failure() // no need to retry
    }

    class Factory @Inject constructor(private val mapSelectionModel: MapSelectionModel) :
        CoroutineWorkerAssistedFactory {
        override fun create(
            context: Context,
            params: WorkerParameters
        ): ListenableWorker {
            return FetchStaticConfigFilesWorker(context, params, mapSelectionModel)
        }
    }

    companion object {
        const val TAG = "FetchStaticConfigFilesJob"
        fun schedule(workManager: WorkManager) {
            workManager.enqueueUniqueWork(
                TAG,
                ExistingWorkPolicy.REPLACE,
                OneTimeWorkRequest.Builder(FetchStaticConfigFilesWorker::class.java)
                    .setConstraints(
                        Constraints.Builder()
                            .setRequiredNetworkType(NetworkType.CONNECTED)
                            .build()
                    )
                    .build()
            )
        }
    }
}
