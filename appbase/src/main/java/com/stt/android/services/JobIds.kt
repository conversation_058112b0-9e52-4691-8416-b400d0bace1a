package com.stt.android.services

/**
 * Job ID must be unique across all clients of the same uid (not just the same package).
 * IDs need to be stable across app updates. This file is the central place to handle IDs to avoid
 * ID collisions.
 */

private const val JOB_ID_OFFSET_INTENT_SERVICE = 10_000

const val JOB_ID_SAVE_WORKOUT_SERVICE =
    JOB_ID_OFFSET_INTENT_SERVICE

const val JOB_ID_SAVE_WORKOUT_HEADER_SERVICE =
    JOB_ID_OFFSET_INTENT_SERVICE + 1

const val JOB_ID_REMOVE_WORKOUT_SERVICE =
    JOB_ID_OFFSET_INTENT_SERVICE + 2

const val JOB_ID_PUSH_NOTIFICATION_HANDLER_SERVICE =
    JOB_ID_OFFSET_INTENT_SERVICE + 3

const val JOB_ID_WEARABLE_CLEANUP_SERVICE =
    JOB_ID_OFFSET_INTENT_SERVICE + 4

// Job ids starting from this value are reserved for Evernote Android-Job library
const val JOB_ID_OFFSET_EVERNOTE_ANDROID_JOB = 1_000_000
