package com.stt.android.network;

import androidx.annotation.Nullable;
import okhttp3.Headers;

/**
 * Signals a non 2xx HTTP response.
 */
public class HttpResponseException extends Exception {
    private static final long serialVersionUID = 1371634012022737029L;
    private static final String STT_ACTIONS = "STTActions";
    private final int statusCode;
    private final String sttActionsHeaders;

    public HttpResponseException(int statusCode,
                                 Headers headers,
                                 String detailMessage) {
        super(detailMessage);
        this.sttActionsHeaders = headers.get(STT_ACTIONS);
        this.statusCode = statusCode;
    }

    public int getStatusCode() {
        return statusCode;
    }

    @Nullable
    public String getSttActionsHeaders() {
        return sttActionsHeaders;
    }
}
