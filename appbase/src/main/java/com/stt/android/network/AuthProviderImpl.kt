package com.stt.android.network

import com.stt.android.domain.UserSession
import com.stt.android.domain.user.GetCurrentUserUseCase
import com.stt.android.remote.AuthProvider
import javax.inject.Inject

class AuthProviderImpl
@Inject constructor(
    private val getCurrentUserUseCase: GetCurrentUserUseCase
) : AuthProvider {
    override fun getAuthHeaders(): Map<String, String>? =
        getSessionKey()?.let {
            mapOf(UserSession.AUTHORIZATION_HEADER to it)
        }

    override fun getSessionKey(): String? =
        getCurrentUserUseCase.getCurrentUser().session?.sessionKey

    override fun getUsername(): String =
        getCurrentUserUseCase.getCurrentUser().username
}
