package com.stt.android.network.interfaces;

import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.util.Pair;
import com.google.gson.JsonParseException;
import com.stt.android.STTApplication;
import com.stt.android.multimedia.sportie.SportieShareSource;
import com.stt.android.network.HttpResponseException;
import com.stt.android.remote.di.BaseUrlConfiguration;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Type;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import okhttp3.MediaType;

public abstract class ANetworkProvider {

    private static final String UTF_8 = "UTF-8";
    public static final Charset UTF_8_CHARSET = Charset.forName(UTF_8);
    private static final String TEXT_PLAIN_CONTENT_TYPE = "text/plain;charset=" + UTF_8;
    public static final MediaType MEDIA_TYPE_TEXT_PLAIN = MediaType.parse(TEXT_PLAIN_CONTENT_TYPE);
    private static final String APPLICATION_JSON_CONTENT_TYPE =
        "application/json;charset=" + UTF_8;
    public static final MediaType MEDIA_TYPE_JSON = MediaType.parse(APPLICATION_JSON_CONTENT_TYPE);
    private static final String OCTET_STREAM_CONTENT_TYPE = "application/octet-stream";
    public static final MediaType MEDIA_TYPE_OCTET_STREAM =
        MediaType.parse(OCTET_STREAM_CONTENT_TYPE);
    public static final MediaType MEDIA_TYPE_APPLICATION_ZIP = MediaType.parse("application/zip");

    private static ConnectivityManager connectivityManager;

    public static String buildSecureBackendUrl(String path) {
        return buildSecureBackendUrl(path, null);
    }

    public static String buildSecureWebWorkoutUrl(String username, String key) {
        return buildSecureWebWorkoutUrl(username, key, SportieShareSource.UNKNOWN);
    }

    public static String buildSecureWebWorkoutUrl(String username, String key, SportieShareSource source) {
        final BaseUrlConfiguration baseUrlConfiguration =
            STTApplication.getComponent().baseUrlConfiguration();
        final String baseUrl = source == SportieShareSource.WORKOUT_SUMMARY ? baseUrlConfiguration.getWorkoutShareSummaryUrl()
            : baseUrlConfiguration.getWorkoutShareUrl();
        return baseUrl + username + "/" + key;
    }

    @NonNull
    public static String buildSecureBackendUrl(String path, String query) {
        return buildBackendUrl(path, query,1);
    }

    @NonNull
    public static String buildSecureBackendUrl(String path, String query, int apiVersion) {
        return buildBackendUrl(path, query, apiVersion);
    }

    @NonNull
    private static String buildBackendUrl(String path, @Nullable String query, int apiVersion) {
        final BaseUrlConfiguration baseUrlConfiguration =
            STTApplication.getComponent().baseUrlConfiguration();
        StringBuilder urlBuilder = new StringBuilder();
        urlBuilder
            .append(baseUrlConfiguration.getAskoUrl())
            .append(String.format(Locale.US, "v%d", apiVersion))
            .append(path);
        if (query != null) {
            urlBuilder.append("?").append(query);
        }
        return urlBuilder.toString().replaceAll("(?<!(http:|https:))//", "/");
    }

    @NonNull
    public static String buildBackendUrlForWww(String host, String path, String query, String
        scheme, int port) {
        try {
            URI uri =
                new URI(scheme, null, host, port, path, query,
                    null);
            return uri.toASCIIString();
        } catch (URISyntaxException e) {
            throw new RuntimeException("Malformed URL. Path: [" + path + "], " +
                "Query [" + query + "]", e);
        }
    }

    public static void setConnectivityManager(ConnectivityManager connectivityManager) {
        ANetworkProvider.connectivityManager = connectivityManager;
    }

    public static boolean isOnline() {
        NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
        return activeNetworkInfo != null && activeNetworkInfo.isConnectedOrConnecting();
    }

    /**
     * @return the URL query string or empty if there were no params to add.
     * @throws UnsupportedEncodingException
     */
    @NonNull
    protected static String constructParams(@NonNull String urlAddr,
        @Nullable List<Pair<?, ?>> params) throws UnsupportedEncodingException {
        if (urlAddr.length() == 0) {
            throw new IllegalArgumentException("Parameter urlAddr can't be empty");
        }
        StringBuilder paramBuilder = new StringBuilder();
        if (params != null && !params.isEmpty()) {
            if (urlAddr.contains("?")) {
                char lastChar = urlAddr.charAt(urlAddr.length() - 1);
                if (lastChar != '&' && lastChar != '?') {
                    paramBuilder.append('&');
                }
            } else {
                paramBuilder.append('?');
            }
            for (Pair<?, ?> param : params) {
                paramBuilder.append(URLEncoder.encode(param.first.toString(), UTF_8));
                paramBuilder.append('=');
                if (param.second != null) {
                    paramBuilder.append(URLEncoder.encode(param.second.toString(), UTF_8));
                }
                paramBuilder.append('&');
            }
            paramBuilder.deleteCharAt(paramBuilder.length() - 1);
        }
        return paramBuilder.toString();
    }

    @SuppressWarnings("TypeParameterUnusedInFormals")
    public abstract <T> T getJson(@NonNull String url, @Nullable Map<String, String> headers,
        @Nullable List<Pair<?, ?>> params, @NonNull Type typeOfT)
        throws IOException, JsonParseException, HttpResponseException;

    @SuppressWarnings("TypeParameterUnusedInFormals")
    public <T> T getJson(@NonNull String url, @Nullable Map<String, String> headers,
        @NonNull Type wrapperType) throws IOException, JsonParseException, HttpResponseException {
        return getJson(url, headers, null, wrapperType);
    }

    public abstract String postJson(@NonNull String url, @Nullable Map<String, String> headers,
        @NonNull Object bodyObject) throws IOException, HttpResponseException;

    /**
     * Performs a HTTP POST to the given URL returning its contents appended to
     * the given StringBuffer
     *
     * @param url the address which to perform a POST
     * @param bodyContent the body content that will be submitted inside the
     * POST
     * @return the response body as string
     * @throws IOException if the post didn't succeed.
     * @throws HttpResponseException Signals a non 2xx HTTP response.
     */
    public abstract String post(@NonNull String url, @Nullable Map<String, String> headers,
        @Nullable List<Pair<?, ?>> params, @Nullable String bodyContent,
        @NonNull MediaType contentType) throws IOException, HttpResponseException;

    public abstract String postFile(@NonNull String url, @Nullable Map<String, String> headers,
        @Nullable List<Pair<?, ?>> params, @NonNull File file, @NonNull MediaType contentType)
        throws IOException, HttpResponseException;

    public abstract String get(@NonNull String url, @Nullable Map<String, String> headers)
        throws IOException, HttpResponseException;

    /**
     * Performs a HTTP PUT to the given URL returning its contents
     *
     * @param url the address which to perform a PUT
     * @param params URL request parameters
     * @return the response body as string
     * @throws IOException if the post didn't succeed.
     * @throws HttpResponseException Signals a non 2xx HTTP response.
     */
    public abstract String put(@NonNull String url, @Nullable Map<String, String> headers,
        @Nullable List<Pair<?, ?>> params) throws IOException, HttpResponseException;

    /**
     * Performs a HTTP PUT to the given URL where the body contains the JSON representation of
     * the {@code bodyContent} parameter
     *
     * @param url the address which to perform a PUT
     * @param headers request headers
     * @param bodyContent the object to serialize to JSON as body
     * @return the response body as string
     * @throws IOException if the post didn't succeed.
     * @throws HttpResponseException Signals a non 2xx HTTP response.
     */
    public abstract String putJson(@NonNull String url, @Nullable Map<String, String> headers,
        @NonNull Object bodyContent) throws IOException, HttpResponseException;

    /**
     * Performs a HTTP PUT to the given URL returning its contents
     *
     * @param url the address where to perform a PUT
     * @param file the file which contains the data to send in the body of the
     * request
     * @param headers request headers
     * @return the response body as string
     * @throws IOException if the post didn't succeed.
     * @throws HttpResponseException Signals a non 2xx HTTP response.
     */
    public abstract String putBinaryFile(@NonNull String url, @Nullable Map<String, String> headers,
        @NonNull File file) throws IOException, HttpResponseException;

    /**
     * Stores the fetched url contents to a file.
     *
     * @throws IOException
     */
    public abstract void getAndSaveFile(@NonNull String url, @Nullable Map<String, String> headers,
        @NonNull File file) throws IOException, HttpResponseException;

    public abstract String delete(@NonNull String url, @Nullable Map<String, String> headers)
        throws IOException, HttpResponseException;
}
