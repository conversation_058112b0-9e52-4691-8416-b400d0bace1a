package com.stt.android.network;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.util.Pair;
import com.google.gson.Gson;
import com.google.gson.JsonParseException;
import com.stt.android.network.interfaces.ANetworkProvider;
import java.io.File;
import java.io.IOException;
import java.io.Reader;
import java.lang.reflect.Type;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import javax.inject.Inject;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okio.BufferedSink;
import okio.Okio;
import timber.log.Timber;

public class OkHttpNetworkProvider extends ANetworkProvider {
    private final OkHttpClient okHttpClient;
    private final Gson gson;

    @Inject
    public OkHttpNetworkProvider(OkHttpClient okHttpClient, Gson gson) {
        this.okHttpClient = okHttpClient;
        this.gson = gson;
    }

    @Override
    public String get(@NonNull String url, @Nullable Map<String, String> headers)
            throws IOException, HttpResponseException {
        Request.Builder builder = new Request.Builder().url(url);
        addHeaders(builder, headers);
        return executeRequest(builder.build());
    }

    private static void addHeaders(@NonNull Request.Builder builder,
        @Nullable Map<String, String> headers) {
        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                builder.addHeader(entry.getKey(), entry.getValue());
            }
        }
    }

    @Nullable
    @Override
    @SuppressWarnings("TypeParameterUnusedInFormals")
    public <T> T getJson(@NonNull String url, @Nullable Map<String, String> headers,
                         @Nullable List<Pair<?, ?>> params, @NonNull Type typeOfT)
            throws IOException, JsonParseException, HttpResponseException {
        url += constructParams(url, params);
        Request.Builder builder = new Request.Builder().url(url);
        addHeaders(builder, headers);
        Reader reader = null;
        try {
            Response response = okHttpClient.newCall(builder.build()).execute();
            if (!response.isSuccessful()) {
                response.body().close();
                throw new HttpResponseException(response.code(), response.headers(),
                        "Unexpected code " + response);
            }

            reader = response.body().charStream();
            return gson.fromJson(reader, typeOfT);
        } catch (SecurityException e) {
            throw new IOException("Missing permission to access Internet", e);
        } catch (IllegalStateException e) {
            throw new IOException("Failed to execute HTTP request", e);
        } finally {
            if (reader != null) {
                reader.close();
            }
        }
    }


    @Override
    public String postJson(@NonNull String url, @Nullable Map<String, String> headers,
                           @NonNull Object bodyObject) throws IOException, HttpResponseException {
        return post(url, headers, null, gson.toJson(bodyObject), MEDIA_TYPE_JSON);
    }

    @Override
    public String post(@NonNull String url, @Nullable Map<String, String> headers,
                       @Nullable List<Pair<?, ?>> params, @Nullable String bodyContent,
                       @NonNull MediaType contentType) throws IOException, HttpResponseException {
        url += constructParams(url, params);
        Request.Builder builder = new Request.Builder().url(url);
        if (bodyContent == null) {
            bodyContent = "";
        }
        builder.post(RequestBody.create(contentType, bodyContent));
        addHeaders(builder, headers);

        return executeRequest(builder.build());
    }

    @Override
    public String postFile(@NonNull String url, @Nullable Map<String, String> headers,
        @Nullable List<Pair<?, ?>> params, @NonNull File file, @NonNull MediaType contentType)
        throws IOException, HttpResponseException {

        url += constructParams(url, params);
        Request.Builder builder = new Request.Builder().url(url);
        builder.post(RequestBody.create(contentType, file));
        addHeaders(builder, headers);

        return executeRequest(builder.build());
    }

    @Override
    public String put(@NonNull String url, @Nullable Map<String, String> headers,
        @Nullable List<Pair<?, ?>> params) throws IOException, HttpResponseException {
        url += constructParams(url, params);

        Request.Builder builder = new Request.Builder().url(url)
            .put(RequestBody.create(MEDIA_TYPE_TEXT_PLAIN, ""));
        addHeaders(builder, headers);
        return executeRequest(builder.build());
    }

    @Override
    public String putJson(@NonNull String url, @Nullable Map<String, String> headers,
        @NonNull Object bodyContent)
        throws IOException, HttpResponseException {
        Request.Builder builder = new Request.Builder().url(url);
        addHeaders(builder, headers);
        builder.put(RequestBody.create(MEDIA_TYPE_JSON, gson.toJson(bodyContent)));

        return executeRequest(builder.build());
    }

    @Override
    public String putBinaryFile(@NonNull String url, @Nullable Map<String, String> headers,
                                @NonNull File file) throws IOException, HttpResponseException {
        Request.Builder builder = new Request.Builder().url(url);
        addHeaders(builder, headers);
        builder.put(RequestBody.create(MEDIA_TYPE_OCTET_STREAM, file));

        return executeRequest(builder.build());
    }

    @Override
    public void getAndSaveFile(@NonNull String url, @Nullable Map<String, String> headers,
                               @NonNull File file) throws IOException, HttpResponseException {
        Request.Builder builder = new Request.Builder().url(url);
        addHeaders(builder, headers);

        Request request = builder.build();
        Response response = null;
        try {
            response = okHttpClient.newCall(request).execute();
            if (!response.isSuccessful()) {
                throw new HttpResponseException(response.code(), response.headers(),
                        "Unexpected code " + response);
            }
            BufferedSink bufferedSink = null;
            try {
                bufferedSink = Okio.buffer(Okio.sink(file));
                bufferedSink.writeAll(response.body().source());
            } finally {
                if (bufferedSink != null) {
                    bufferedSink.close();
                }
            }
        } catch (SecurityException e) {
            throw new IOException("Missing permission to access Internet", e);
        } catch (IllegalStateException e) {
            throw new IOException("Failed to execute HTTP request", e);
        } finally {
            if (response != null) {
                response.body().close();
            }
        }
    }

    @Override
    public String delete(@NonNull String url, @Nullable Map<String, String> headers)
            throws IOException, HttpResponseException {
        Request.Builder builder = new Request.Builder().url(url);
        addHeaders(builder, headers);
        return executeRequest(builder.delete().build());
    }

    private String executeRequest(@NonNull Request request) throws IOException, HttpResponseException {
        Response response = null;
        try {
            response = okHttpClient.newCall(request).execute();
            if (!response.isSuccessful()) {
                String message = String.format(Locale.US, "Unexpected status code: %d", response.code());
                HttpResponseException e = new HttpResponseException(response.code(),
                        response.headers(), message);
                Timber.w(e);
                throw e;
            }
            return response.body().string();
        } catch (SecurityException e) {
            throw new IOException("Missing permission to access Internet", e);
        } catch (IllegalStateException e) {
            throw new IOException("Failed to execute HTTP request", e);
        } catch (IOException e) {
            if (ANetworkProvider.isOnline()) {
                Timber.w(e, "error during OkHttpNetworkProvider.executeRequest");
            }
            throw e;
        } finally {
            if (response != null) {
                response.body().close();
            }
        }
    }
}
