package com.stt.android.workoutsettings.follow;

import com.stt.android.cardlist.FeedCard;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.views.MVPView;
import java.util.List;

public interface TargetWorkoutSelectionView extends MVPView {
    void askEnableGps();

    void onGhostWorkoutSelected(WorkoutHeader workout);

    void onFollowWorkoutSelected(WorkoutHeader workout);

    void onFollowRouteSelected(String routeId);

    void showCards(List<FeedCard> cards);

    void showError();

    void showActionButton();

    void hideActionButton();

    void clearSelectedTarget();

    void showNoWorkoutsView();

    void hideNoWorkoutsView();
}
