package com.stt.android.workoutsettings.autopause

import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.viewModels
import com.stt.android.R
import com.stt.android.cadence.CadenceHelper
import com.stt.android.common.ui.observeNotNull
import com.stt.android.common.viewstate.ViewStateListFragment2
import com.stt.android.databinding.FragmentAutoPauseSelectionListBinding
import com.stt.android.domain.user.ActivityTypeHelper
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workout.ActivityType.Companion.valueOf
import com.stt.android.ui.extensions.requireTheme
import com.stt.android.ui.utils.EpoxyConditionalDividerItemDecoration
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class AutoPauseSelectionListFragment :
    ViewStateListFragment2<AutoPauseSelectionListData, AutoPauseSelectionListViewModel>() {

    override val viewModel: AutoPauseSelectionListViewModel by viewModels()

    override val layoutId = R.layout.fragment_auto_pause_selection_list
    private val binding: FragmentAutoPauseSelectionListBinding get() = requireBinding()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val activityType = valueOf(requireArguments().getInt(KEY_ACTIVITY_TYPE_ID))
        viewModel.loadData(
            activityType = activityType,
            hasKnownCadence = CadenceHelper.hasKnownCadence(requireContext()),
            selectedAutoPause = ActivityTypeHelper.getAutoPause(requireContext(), activityType)
        )

        viewModel.onAutoPauseSelected.observeNotNull(viewLifecycleOwner) { selectedAutoPause ->
            ActivityTypeHelper.saveAutoPause(activity, activityType, selectedAutoPause)
            parentFragmentManager.popBackStack()
        }

        val divider = resources.getDimensionPixelSize(R.dimen.size_divider)
        binding.list.addItemDecoration(
            EpoxyConditionalDividerItemDecoration(
                dividerColor = resources.getColor(R.color.light_grey, requireTheme()),
                drawDividerOver = false,
                dividerHeightBetween = { _, _ -> divider }
            )
        )
        setupActionBar()
    }

    private fun setupActionBar() {
        val activity = activity as? AppCompatActivity
        activity?.supportActionBar?.let { actionBar ->
            actionBar.setTitle(R.string.auto_pause)
            actionBar.setDisplayHomeAsUpEnabled(true)
        }
    }

    companion object {
        const val FRAGMENT_TAG =
            "com.stt.android.ui.fragments.settings.AutoPauseSelectionFragment.FRAGMENT_TAG"
        private const val KEY_ACTIVITY_TYPE_ID = "com.stt.android.KEY_ACTIVITY_TYPE_ID"

        @JvmStatic
        fun newInstance(activityType: ActivityType): AutoPauseSelectionListFragment {
            return AutoPauseSelectionListFragment().apply {
                arguments = Bundle().apply {
                    putInt(KEY_ACTIVITY_TYPE_ID, activityType.id)
                }
            }
        }
    }
}
