package com.stt.android.workoutsettings;

import android.content.ActivityNotFoundException;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.os.PowerManager;
import android.provider.Settings;
import android.text.TextUtils;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.ViewModelProvider;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import com.stt.android.R;
import com.stt.android.analytics.AmplitudeAnalyticsTracker;
import com.stt.android.analytics.AnalyticsEvent;
import com.stt.android.analytics.AnalyticsEventProperty;
import com.stt.android.analytics.AnalyticsProperties;
import com.stt.android.analytics.AnalyticsPropertyValue;
import com.stt.android.analytics.FirebaseAnalyticsTracker;
import com.stt.android.cardlist.FeedCard;
import com.stt.android.common.ui.SimpleDialogFragment;
import com.stt.android.controllers.CurrentUserController;
import com.stt.android.controllers.UserSettingsController;
import com.stt.android.controllers.WorkoutHeaderController;
import com.stt.android.databinding.WorkoutSettingsActivityBinding;
import com.stt.android.domain.routes.GetRouteUseCase;
import com.stt.android.domain.routes.Route;
import com.stt.android.domain.user.ActivityTypeHelper;
import com.stt.android.domain.user.UserWorkoutSummary;
import com.stt.android.domain.user.VoiceFeedbackSettings;
import com.stt.android.domain.user.VoiceFeedbackSettingsHelper;
import com.stt.android.domain.workout.ActivityType;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.exceptions.InternalDataException;
import com.stt.android.home.explore.routes.RouteDetailsNavigator;
import com.stt.android.ui.activities.SensorAwareRecordWorkoutServiceAppCompatActivity;
import com.stt.android.ui.activities.WorkoutActivity;
import com.stt.android.ui.components.workout.WorkoutCardViewModel;
import com.stt.android.ui.map.MapHelper;
import com.stt.android.utils.HarmonyUtils;
import com.stt.android.utils.STTConstants;
import com.stt.android.workouts.TrackingState;
import com.stt.android.workoutsettings.activitytype.ActivityTypeSelectionListFragment;
import com.stt.android.workoutsettings.autopause.AutoPauseSelectionListFragment;
import com.stt.android.workoutsettings.follow.BaseTargetWorkoutSelectionPresenter;
import com.stt.android.workoutsettings.follow.TargetWorkoutSelectionFragment;
import com.stt.android.workoutsettings.follow.TargetWorkoutSelectionPresenter;
import com.stt.android.workoutsettings.follow.TargetWorkoutSelectionView;
import dagger.hilt.android.AndroidEntryPoint;
import io.reactivex.Completable;
import io.reactivex.Single;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;
import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.inject.Inject;
import timber.log.Timber;

@AndroidEntryPoint
public class WorkoutSettingsActivity extends SensorAwareRecordWorkoutServiceAppCompatActivity
    implements BaseWorkoutSettingsFragment.WorkoutSettingsListener,
    BaseWorkoutSettingsFragment.WorkoutTargetProvider, TargetWorkoutSelectionView,
    SimpleDialogFragment.Callback {
    /**
     * Back stack name used to return to main workout settings after workout is selected as ghost or
     * follow.
     */
    private static final String SELECT_TARGET_STACK = "SELECT_TARGET";
    private static final String SELECTION_MODE_ARG = "SELECTION_MODE_ARG";
    private final BroadcastReceiver recordingStateListener = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            // Once the workout has started we can destroy this activity
            TrackingState trackingState =
                (TrackingState) intent.getSerializableExtra(STTConstants.ExtraKeys.RECORDING_STATE);
            if (trackingState == TrackingState.RECORDING) {
                WorkoutSettingsActivity.this.finish();
            }
        }
    };
    private final CompositeDisposable disposable = new CompositeDisposable();

    private Disposable selectedTargetDisposable = null;

    @Inject
    LocalBroadcastManager localBM;

    @Inject
    TargetWorkoutSelectionPresenter targetWorkoutSelectionPresenter;

    @Inject
    UserSettingsController userSettingsController;

    @Inject
    CurrentUserController currentUserController;

    @Inject
    WorkoutHeaderController workoutHeaderController;

    @Inject
    GetRouteUseCase getRouteUseCase;

    @Inject
    FirebaseAnalyticsTracker firebaseAnalyticsTracker;

    @Inject
    AmplitudeAnalyticsTracker amplitudeAnalyticsTracker;

    @BaseTargetWorkoutSelectionPresenter.SelectionMode
    private int selectionMode = BaseTargetWorkoutSelectionPresenter.ACTION_SELECT_NONE;
    private boolean refreshRouteSelection;

    private WorkoutSettingsViewModel viewModel;
    public WorkoutCardViewModel workoutCardViewModel;
    private static final String POWER_SAVE_ISSUE_DIALOG = "powerSaveIssueDialog";

    public static Intent newStartIntent(Context context) {
        return new Intent(context, WorkoutSettingsActivity.class);
    }

    public static Intent newFollowRouteStartIntent(Context context, String routeId) {
        Intent intent = new Intent(context, WorkoutSettingsActivity.class);
        intent.putExtra(STTConstants.ExtraKeys.FOLLOW_ROUTE_ID, routeId);
        return intent;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK
            && requestCode == RouteDetailsNavigator.ROUTE_TARGET_RESULT_CODE) {
            String routeId = data.getStringExtra(STTConstants.ExtraKeys.FOLLOW_ROUTE_ID);
            getIntent().putExtra(STTConstants.ExtraKeys.FOLLOW_ROUTE_ID, routeId);
            // work around for fragment state loss when returning from RouteDetailsActivity
            refreshRouteSelection = true;
        }
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            //noinspection WrongConstant
            this.selectionMode = savedInstanceState.getInt(SELECTION_MODE_ARG, -1);
        }

        super.onCreate(savedInstanceState);

        ViewModelProvider viewModelProvider = new ViewModelProvider(this);
        viewModel = viewModelProvider.get(WorkoutSettingsViewModel.class);
        workoutCardViewModel = viewModelProvider.get(WorkoutCardViewModel.class);

        WorkoutSettingsActivityBinding binding =
            WorkoutSettingsActivityBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setSupportActionBar(binding.workoutSettingsToolbar);

        FragmentManager fragmentManager = getSupportFragmentManager();

        if (savedInstanceState == null) {
            fragmentManager.beginTransaction()
                .add(R.id.fragmentContainer, WorkoutSettingsFragment.Companion.newInstance(),
                    WorkoutSettingsFragment.FRAGMENT_TAG)
                .commit();
        }

        localBM.registerReceiver(recordingStateListener,
            new IntentFilter(STTConstants.BroadcastActions.RECORDING_STATE_CHANGED));
        if (shouldShowPowerSaveModeDialog()) {
            showPowerSaveDialog();
        }

        WorkoutHeader followWorkoutTarget = getFollowWorkoutTarget();
        WorkoutHeader ghostTargetWorkout = getGhostTarget();
        String followRouteId = getFollowRouteId();
        if (followWorkoutTarget != null) {
            viewModel.onFollowWorkoutSelected(followWorkoutTarget);
        } else if (ghostTargetWorkout != null) {
            viewModel.onGhostWorkoutSelected(ghostTargetWorkout);
        } else if (followRouteId != null) {
            viewModel.onFollowRouteSelected(followRouteId);
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.WORKOUT_SETTINGS_SCREEN);
        targetWorkoutSelectionPresenter.takeView(this);
        targetWorkoutSelectionPresenter.reset(selectionMode);
    }

    // work around for fragment state loss when returning from RouteDetailsActivity
    @Override
    protected void onPostResume() {
        super.onPostResume();
        if (refreshRouteSelection) {
            onFollowRouteSelected(getFollowRouteId());
            refreshRouteSelection = false;
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        targetWorkoutSelectionPresenter.dropView();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        disposable.clear();
        /*
         * We need to keep listening after onPause() or onStop() since we want
         * to destroy this activity after user presses Start workout
         */
        localBM.unregisterReceiver(recordingStateListener);
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }

    public void refreshWithTimeout() {
        // Try to restore previously selected target before continuing with loading items to the
        // list
        cancelSelectedTargetLoading();
        selectedTargetDisposable = setSelectedTarget()
            .onErrorComplete()
            .doOnComplete(() -> {
                targetWorkoutSelectionPresenter.refresh();
            })
            .subscribe();
    }

    private Completable setSelectedTarget() {
        Single<Route> followRouteTargetObservable = getFollowRouteTarget();
        if (followRouteTargetObservable != null) {
            return followRouteTargetObservable.flatMapCompletable(
                route -> {
                    targetWorkoutSelectionPresenter.setSelectedRoute(route);
                    return Completable.complete();
                });
        } else {
            return Completable.fromAction(() -> {
                WorkoutHeader followWorkoutTarget = getFollowWorkoutTarget();
                WorkoutHeader ghostTarget = getGhostTarget();
                if (ghostTarget != null) {
                    targetWorkoutSelectionPresenter.setSelectedWorkout(ghostTarget);
                } else if (followWorkoutTarget != null) {
                    targetWorkoutSelectionPresenter.setSelectedWorkout(followWorkoutTarget);
                }
            });
        }
    }

    private void cancelSelectedTargetLoading() {
        if (selectedTargetDisposable != null) {
            selectedTargetDisposable.dispose();
            selectedTargetDisposable = null;
        }
    }

    public void cancelRefresh() {
        cancelSelectedTargetLoading();
    }

    @Override
    public void onActivityTypeSelectionClicked() {
        getSupportFragmentManager().beginTransaction()
            .setCustomAnimations(R.anim.slide_in_right_to_left, R.anim.slide_out_right_to_left,
                R.anim.slide_in_left_to_right, R.anim.slide_out_left_to_right)
            .replace(R.id.fragmentContainer, ActivityTypeSelectionListFragment.newInstance(),
                ActivityTypeSelectionListFragment.FRAGMENT_TAG)
            .addToBackStack(null)
            .commitAllowingStateLoss();
    }

    @Override
    public void onVoiceFeedbackSettingClicked() {
        ActivityType activityType = ActivityTypeHelper.getLastActivity(this);
        getSupportFragmentManager().beginTransaction()
            .setCustomAnimations(R.anim.slide_in_right_to_left, R.anim.slide_out_right_to_left,
                R.anim.slide_in_left_to_right, R.anim.slide_out_left_to_right)
            .replace(R.id.fragmentContainer,
                VoiceFeedbackSettingsFragment.newInstance(activityType.getId()),
                VoiceFeedbackSettingsFragment.FRAGMENT_TAG)
            .addToBackStack(null)
            .commitAllowingStateLoss();
    }

    @Override
    public void onAutoPauseSelectionClicked() {
        ActivityType activityType = ActivityTypeHelper.getLastActivity(this);
        getSupportFragmentManager().beginTransaction()
            .setCustomAnimations(R.anim.slide_in_right_to_left, R.anim.slide_out_right_to_left,
                R.anim.slide_in_left_to_right, R.anim.slide_out_left_to_right)
            .replace(R.id.fragmentContainer,
                AutoPauseSelectionListFragment.newInstance(activityType),
                AutoPauseSelectionListFragment.FRAGMENT_TAG)
            .addToBackStack(null)
            .commitAllowingStateLoss();
    }

    @Override
    public void onSettingsReady(ActivityType selectedActivityType) {
        Context applicationContext = getApplicationContext();
        // Every time the user is about to start a workout let's reset the zoom level.
        MapHelper.resetZoomLevel(applicationContext);
        // and the bearing.
        MapHelper.resetBearing(applicationContext);

        Intent intent;
        // Add a follow workout if necessary or ghost target (can't have both at the same time)
        Intent startIntent = getIntent();
        WorkoutHeader targetWorkout =
            startIntent.getParcelableExtra(STTConstants.ExtraKeys.FOLLOW_WORKOUT_HEADER);
        ArrayMap<String, Object> properties = new ArrayMap<>();
        if (targetWorkout != null) {
            properties.put("Route", getRouteProperty(targetWorkout));
            properties.put("Ghost", "off");
            intent = WorkoutActivity.newStartIntentFollowWorkout(applicationContext,
                selectedActivityType, shouldCheckGps, shouldCheckBluetooth, targetWorkout);
        } else if ((targetWorkout =
            startIntent.getParcelableExtra(STTConstants.ExtraKeys.GHOST_TARGET_WORKOUT_HEADER))
            != null) {
            properties.put("Route", "off");
            properties.put("Ghost", getGhostProperty(targetWorkout));
            intent =
                WorkoutActivity.newStartIntentGhostWorkout(applicationContext, selectedActivityType,
                    shouldCheckGps, shouldCheckBluetooth, targetWorkout);
        } else {
            String followRouteId =
                startIntent.getStringExtra(STTConstants.ExtraKeys.FOLLOW_ROUTE_ID);
            if (followRouteId != null) {
                properties.put("Route", getRouteProperty(followRouteId));
                properties.put("Ghost", "off");
                intent = WorkoutActivity.newStartIntentFollowRoute(applicationContext,
                    selectedActivityType, shouldCheckGps, shouldCheckBluetooth, followRouteId);
            } else {
                intent = WorkoutActivity.newStartIntent(applicationContext, selectedActivityType,
                    shouldCheckGps, shouldCheckBluetooth);
                properties.put("Route", "off");
                properties.put("Ghost", "off");
            }
        }
        properties.put("ActivityType", selectedActivityType.getSimpleName());
        properties.put("VoiceFeedback", getVoiceFeedbackSettings(selectedActivityType));
        properties.put("AutoPause", ActivityTypeHelper.getAutoPause(this, selectedActivityType));
        properties.put("Maps", userSettingsController.getSettings().getSelectedMapType().getName());
        try {
            UserWorkoutSummary summary = workoutHeaderController.getUserWorkoutSummary(
                currentUserController.getUsername());
            if (summary != null) {
                properties.put("PreviousWorkouts", summary.getTotalWorkouts());
            }
        } catch (InternalDataException e) {
            Timber.d("Failed to get workouts summary: %s", e.getMessage());
        }
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.WORKOUT_SETUP_COMPLETE,
            properties);
        trackRouteFollowAnalytics();
        startActivity(intent);
    }

    //we need some horribly complex code just get the route to be followed for analytics :(
    void trackRouteFollowAnalytics() {
        WorkoutHeader targetWorkout =
            getIntent().getParcelableExtra(STTConstants.ExtraKeys.FOLLOW_WORKOUT_HEADER);
        String followRouteId = getIntent().getStringExtra(STTConstants.ExtraKeys.FOLLOW_ROUTE_ID);

        if (targetWorkout != null) {
            boolean ownRoute =
                currentUserController.getUsername().equals(targetWorkout.getUsername());
            long daysSinceCreated = TimeUnit.MILLISECONDS.toDays(
                System.currentTimeMillis() - targetWorkout.getStartTime());
            sendRouteAnalyticsEvent(ownRoute, daysSinceCreated,
                (long) targetWorkout.getTotalTime());
        } else if (followRouteId != null) {
            disposable.add(getRouteUseCase.getRouteRx(followRouteId)
                .subscribeOn(Schedulers.io())
                .subscribe(route -> {
                    boolean ownRoute =
                        currentUserController.getUsername().equals(route.getOwnerUserName());
                    sendRouteAnalyticsEvent(ownRoute, route.getDaysSinceCreated(),
                        route.getDurationEstimation());
                }, Timber::w));
        }
    }

    void sendRouteAnalyticsEvent(boolean ownRoute, long daysSinceCreated, long durationEstimation) {
        AnalyticsProperties analyticsProperties = new AnalyticsProperties()
            .put(AnalyticsEventProperty.ROUTES_OWNER,
                ownRoute ? AnalyticsPropertyValue.OWN : AnalyticsPropertyValue.OTHER)
            .put(AnalyticsEventProperty.DAYS_SINCE_CREATED, daysSinceCreated)
            .put(AnalyticsEventProperty.DURATION_IN_SECONDS, durationEstimation);
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.ROUTE_FOLLOW, analyticsProperties);
        firebaseAnalyticsTracker.trackEvent(AnalyticsEvent.ROUTE_FOLLOW, analyticsProperties);
    }

    private String getVoiceFeedbackSettings(ActivityType selectedActivityType) {
        VoiceFeedbackSettings voiceSettings =
            VoiceFeedbackSettingsHelper.getVoiceFeedbackSettings(this,
                selectedActivityType.getId());
        if (!voiceSettings.enabled) {
            return "off";
        } else {
            return "on";
        }
    }

    private String getRouteProperty(WorkoutHeader header) {
        if (header == null) {
            return "off";
        } else if (currentUserController.getUsername().equals(header.getUsername())) {
            return "own";
        } else {
            return "other";
        }
    }

    private String getRouteProperty(String routeId) {
        if (routeId == null) {
            return "off";
        } else {
            return "on";
        }
    }

    private String getGhostProperty(WorkoutHeader header) {
        if (header == null) {
            return "off";
        } else if (currentUserController.getUsername().equals(header.getUsername())) {
            return "own";
        } else {
            return "other";
        }
    }

    @Override
    @Nullable
    public WorkoutHeader getFollowWorkoutTarget() {
        return getIntent().getParcelableExtra(STTConstants.ExtraKeys.FOLLOW_WORKOUT_HEADER);
    }

    @Override
    @Nullable
    public WorkoutHeader getGhostTarget() {
        return getIntent().getParcelableExtra(STTConstants.ExtraKeys.GHOST_TARGET_WORKOUT_HEADER);
    }

    @Nullable
    @Override
    public Single<Route> getFollowRouteTarget() {
        final String routeId = getFollowRouteId();
        return TextUtils.isEmpty(routeId) ? null : getRouteUseCase.getRouteRx(routeId).toSingle()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread());
    }

    public String getFollowRouteId() {
        return getIntent().getStringExtra(STTConstants.ExtraKeys.FOLLOW_ROUTE_ID);
    }

    @Override
    public void onSelectGhostTargetClicked() {
        onSelectTarget(BaseTargetWorkoutSelectionPresenter.ACTION_SELECT_GHOST_TARGET);
    }

    @Override
    public void onSelectFollowRouteClicked() {
        onSelectTarget(BaseTargetWorkoutSelectionPresenter.ACTION_SELECT_FOLLOW_ROUTE_OR_WORKOUT);
    }

    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putInt(SELECTION_MODE_ARG, selectionMode);
    }

    private void onSelectTarget(int mode) {
        this.selectionMode = mode;
        targetWorkoutSelectionPresenter.reset(mode);
        getSupportFragmentManager().beginTransaction()
            .setCustomAnimations(R.anim.slide_in_right_to_left, R.anim.slide_out_right_to_left,
                R.anim.slide_in_left_to_right, R.anim.slide_out_left_to_right)
            .replace(R.id.fragmentContainer, TargetWorkoutSelectionFragment.newInstance(),
                TargetWorkoutSelectionFragment.FRAGMENT_TAG)
            .addToBackStack(SELECT_TARGET_STACK)
            .commit();
    }

    @Nullable
    private TargetWorkoutSelectionFragment getTargetWorkoutSelectionFragment() {
        return (TargetWorkoutSelectionFragment) getSupportFragmentManager()
            .findFragmentByTag(TargetWorkoutSelectionFragment.FRAGMENT_TAG);
    }

    @Override
    public void onGhostWorkoutSelected(WorkoutHeader workout) {
        Intent intent = getIntent();
        intent.removeExtra(STTConstants.ExtraKeys.FOLLOW_WORKOUT_HEADER);
        intent.removeExtra(STTConstants.ExtraKeys.FOLLOW_ROUTE_ID);
        intent.putExtra(STTConstants.ExtraKeys.GHOST_TARGET_WORKOUT_HEADER, workout);
        getSupportFragmentManager().popBackStack(SELECT_TARGET_STACK,
            FragmentManager.POP_BACK_STACK_INCLUSIVE);
        viewModel.onGhostWorkoutSelected(workout);
    }

    @Override
    public void onFollowWorkoutSelected(WorkoutHeader workout) {
        Intent intent = getIntent();
        intent.removeExtra(STTConstants.ExtraKeys.GHOST_TARGET_WORKOUT_HEADER);
        intent.removeExtra(STTConstants.ExtraKeys.FOLLOW_ROUTE_ID);
        intent.putExtra(STTConstants.ExtraKeys.FOLLOW_WORKOUT_HEADER, workout);
        getSupportFragmentManager().popBackStack(SELECT_TARGET_STACK,
            FragmentManager.POP_BACK_STACK_INCLUSIVE);
        viewModel.onFollowWorkoutSelected(workout);
    }

    @Override
    public void onFollowRouteSelected(String routeId) {
        Intent intent = getIntent();
        intent.removeExtra(STTConstants.ExtraKeys.GHOST_TARGET_WORKOUT_HEADER);
        intent.removeExtra(STTConstants.ExtraKeys.FOLLOW_WORKOUT_HEADER);
        intent.putExtra(STTConstants.ExtraKeys.FOLLOW_ROUTE_ID, routeId);
        getSupportFragmentManager().popBackStack(SELECT_TARGET_STACK,
            FragmentManager.POP_BACK_STACK_INCLUSIVE);
        viewModel.onFollowRouteSelected(routeId);
    }

    @Override
    public void askEnableGps() {
        TargetWorkoutSelectionFragment tsFragment = getTargetWorkoutSelectionFragment();
        if (tsFragment != null) {
            tsFragment.askEnableGps();
        }
    }

    @Override
    public void showCards(List<FeedCard> cards) {
        TargetWorkoutSelectionFragment tsFragment = getTargetWorkoutSelectionFragment();
        if (tsFragment != null) {
            tsFragment.showCards(cards);
        }
    }

    @Override
    public void showError() {
        TargetWorkoutSelectionFragment tsFragment = getTargetWorkoutSelectionFragment();
        if (tsFragment != null) {
            tsFragment.showError();
        }
    }

    @Override
    public void showActionButton() {
        TargetWorkoutSelectionFragment tsFragment = getTargetWorkoutSelectionFragment();
        if (tsFragment != null) {
            tsFragment.showActionButton();
        }
    }

    @Override
    public void hideActionButton() {
        TargetWorkoutSelectionFragment tsFragment = getTargetWorkoutSelectionFragment();
        if (tsFragment != null) {
            tsFragment.hideActionButton();
        }
    }

    @Override
    public void clearSelectedTarget() {
        Intent intent = getIntent();
        intent.removeExtra(STTConstants.ExtraKeys.GHOST_TARGET_WORKOUT_HEADER);
        intent.removeExtra(STTConstants.ExtraKeys.FOLLOW_WORKOUT_HEADER);
        intent.removeExtra(STTConstants.ExtraKeys.FOLLOW_ROUTE_ID);
    }

    @Override
    public void showNoWorkoutsView() {
        TargetWorkoutSelectionFragment tsFragment = getTargetWorkoutSelectionFragment();
        if (tsFragment != null) {
            tsFragment.showNoWorkoutsView();
        }
    }

    @Override
    public void hideNoWorkoutsView() {
        TargetWorkoutSelectionFragment tsFragment = getTargetWorkoutSelectionFragment();
        if (tsFragment != null) {
            tsFragment.hideNoWorkoutsView();
        }
    }

    public int getSelectionMode() {
        return targetWorkoutSelectionPresenter.getSelectionMode();
    }

    public TargetWorkoutSelectionPresenter getPresenter() {
        return targetWorkoutSelectionPresenter;
    }

    private boolean shouldShowPowerSaveModeDialog() {
        PowerManager powerManager = (PowerManager) getSystemService(Context.POWER_SERVICE);
        if (powerManager != null) {
            return powerManager.isPowerSaveMode();
        }
        return false;
    }

    void showPowerSaveDialog() {
        SimpleDialogFragment dialog = SimpleDialogFragment.newInstance(
            getString(R.string.power_save_warning_dialog_message),
            getString(R.string.power_save_warning_dialog_title), getString(R.string.settings),
            getString(R.string.skip_str), false);
        dialog.setCancelable(false);
        dialog.show(getSupportFragmentManager(), POWER_SAVE_ISSUE_DIALOG);
    }

    @Override
    public void onDialogButtonPressed(@Nullable String tag, int which) {
        if (POWER_SAVE_ISSUE_DIALOG.equals(tag)
            && which == DialogInterface.BUTTON_POSITIVE) {
            Intent batterySaverIntent = new Intent();
            // If the API is greater than 22 and HarmonyOS uses the Intent power management
            // instead of the Setting power management
            if (HarmonyUtils.INSTANCE.isHarmonyOs()) {
                batterySaverIntent.setAction(Intent.ACTION_POWER_USAGE_SUMMARY);
            } else {
                batterySaverIntent.setAction(Settings.ACTION_BATTERY_SAVER_SETTINGS);
            }
            try {
                startActivity(batterySaverIntent);
            } catch (ActivityNotFoundException e) {
                Timber.d("Could not open battery saver settings");
            }
        }
    }

    @Override
    public void onDialogDismissed(@Nullable String tag) {
    }
}
