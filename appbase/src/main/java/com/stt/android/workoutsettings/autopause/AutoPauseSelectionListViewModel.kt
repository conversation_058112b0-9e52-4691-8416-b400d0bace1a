package com.stt.android.workoutsettings.autopause

import androidx.lifecycle.LiveData
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.viewstate.LoadingStateViewModel
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workout.AutoPause
import com.stt.android.ui.utils.SingleLiveEvent
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import javax.inject.Inject

@HiltViewModel
class AutoPauseSelectionListViewModel @Inject constructor(
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    coroutinesDispatchers: CoroutinesDispatchers,
    val userSettingsController: UserSettingsController,
) : LoadingStateViewModel<AutoPauseSelectionListData>(
    ioThread,
    mainThread,
    coroutinesDispatchers
) {

    override fun retryLoading() {
        /* no-op */
    }

    fun loadData(
        activityType: ActivityType,
        hasKnownCadence: Boolean,
        selectedAutoPause: AutoPause
    ) {
        notifyLoading()

        val settings = userSettingsController.settings

        val cadenceActivity = activityType == ActivityType.CYCLING ||
            activityType == ActivityType.MOUNTAIN_BIKING ||
            activityType == ActivityType.GRAVEL_CYCLING
        val autoPauseValues = settings.measurementUnit.autoPauseValues
        val supportedAutoPauses =
            autoPauseValues.filterSupportedAutoPauseValues(cadenceActivity, hasKnownCadence)

        val data = AutoPauseSelectionListData(
            autoPauses = supportedAutoPauses,
            selectedAutoPause = selectedAutoPause,
            onAutoPauseClicked = ::onAutoPauseClicked,
        )

        notifyDataLoaded(data)
    }

    private fun Array<AutoPause>.filterSupportedAutoPauseValues(
        cadenceActivity: Boolean,
        hasKnownCadence: Boolean
    ) = filter { autoPause ->
        // "zero" auto pause is only available if the user selects cycling or
        // mountain biking, and the user has known cadence
        val zeroAutoPause =
            autoPause == AutoPause.ZERO_KM_H || autoPause == AutoPause.ZERO_MI_H
        !zeroAutoPause || cadenceActivity && hasKnownCadence
    }

    val onAutoPauseSelected: LiveData<AutoPause> get() = _onAutoPauseSelected
    private val _onAutoPauseSelected = SingleLiveEvent<AutoPause>()

    private fun onAutoPauseClicked(activityType: AutoPause) {
        _onAutoPauseSelected.value = activityType
    }
}
