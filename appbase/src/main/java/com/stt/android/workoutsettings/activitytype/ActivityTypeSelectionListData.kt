package com.stt.android.workoutsettings.activitytype

import com.stt.android.domain.workout.ActivityType

data class ActivityTypeSelectionListData(
    val recentActivities: List<ActivityType>,
    val popularActivities: List<ActivityType>,
    val allActivitiesSortedByLocalName: List<ActivityType>,
    val selectedActivityType: ActivityType? = null,
    val onActivityTypeClicked: (ActivityType) -> Unit
)
