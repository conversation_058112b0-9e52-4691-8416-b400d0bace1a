package com.stt.android.workoutsettings

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.TextView
import androidx.annotation.ColorInt
import androidx.annotation.StringRes
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.withStyledAttributes
import com.airbnb.epoxy.ModelProp
import com.airbnb.epoxy.ModelView
import com.airbnb.epoxy.TextProp
import com.stt.android.R
import com.stt.android.ThemeColors

@ModelView(autoLayout = ModelView.Size.MATCH_WIDTH_WRAP_HEIGHT)
open class WorkoutSettingItem
@JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    protected val titleLabel: TextView by lazy { findViewById(R.id.titleLabel) }
    protected val selectionLabel: TextView by lazy { findViewById(R.id.selectionLabel) }

    protected open val layout: Int
        get() = R.layout.workout_setting_item

    init {
        View.inflate(context, layout, this)

        attrs?.let {
            context.withStyledAttributes(attrs, R.styleable.WorkoutSettingItem) {
                getResourceId(R.styleable.WorkoutSettingItem_workoutSettingItemTitle, -1).let {
                    if (it != -1) {
                        titleLabel.setText(it)
                    }
                }
            }
        }
    }

    override fun setEnabled(enabled: Boolean) {
        super.setEnabled(enabled)
        titleLabel.isEnabled = enabled
    }

    @ModelProp
    open fun setSelectedItem(selected: Boolean) {
        titleLabel.setTextColor(getSelectionStateColor(selected))
    }

    @TextProp
    open fun setTitle(description: CharSequence) {
        titleLabel.text = description
    }

    fun setSelection(@StringRes description: Int) {
        selectionLabel.apply {
            setText(description)
            visibility = View.VISIBLE
        }
    }

    fun setSelection(description: CharSequence) {
        selectionLabel.apply {
            text = description
            visibility = View.VISIBLE
        }
    }

    @ColorInt
    protected fun getSelectionStateColor(selected: Boolean): Int {
        return ThemeColors.resolveColor(
            context,
            if (selected) {
                android.R.attr.colorAccent
            } else {
                android.R.attr.textColorPrimary
            }
        )
    }
}
