package com.stt.android.workoutsettings

import android.content.Context
import android.content.res.ColorStateList
import android.util.AttributeSet
import android.view.View
import android.widget.ImageView
import androidx.annotation.DrawableRes
import androidx.core.widget.ImageViewCompat
import com.airbnb.epoxy.AfterPropsSet
import com.airbnb.epoxy.CallbackProp
import com.airbnb.epoxy.ModelProp
import com.airbnb.epoxy.ModelView
import com.stt.android.R
import com.stt.android.ui.utils.setOnClickListenerThrottled
import com.stt.android.utils.addRequiresPremiumNote
import com.stt.android.utils.removeRequiresPremiumNote

@ModelView(autoLayout = ModelView.Size.MATCH_WIDTH_WRAP_HEIGHT)
class WorkoutSettingImageItem
@JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : WorkoutSettingItem(context, attrs, defStyleAttr) {

    @set:[CallbackProp]
    var onItemClicked: OnClickListener? = null

    private val imageView: ImageView by lazy { findViewById(R.id.imageView) }
    private val checkMark: ImageView by lazy { findViewById(R.id.checkMark) }

    private var showPremiumRequired = false

    override val layout: Int
        get() = R.layout.workout_setting_image_item

    init {
        attrs?.let {
            context.obtainStyledAttributes(attrs, R.styleable.WorkoutSettingImageItem).apply {
                getResourceId(
                    R.styleable.WorkoutSettingImageItem_workoutSettingImageItemImage,
                    -1
                ).let {
                    if (it != -1) {
                        imageView.setImageResource(it)
                        imageView.visibility = View.VISIBLE
                    }
                }

                recycle()
            }
        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val height = context.resources.getDimension(R.dimen.start_workout_setting_with_subtitle_row_height).toInt()
        val newHeightMeasureSpec = MeasureSpec.makeMeasureSpec(height, MeasureSpec.EXACTLY)
        super.onMeasure(widthMeasureSpec, newHeightMeasureSpec)
    }

    override fun setSelectedItem(selected: Boolean) {
        super.setSelectedItem(selected)
        ImageViewCompat.setImageTintList(
            imageView,
            ColorStateList.valueOf(getSelectionStateColor(selected))
        )
        checkMark.visibility = if (selected) View.VISIBLE else View.GONE
    }

    @ModelProp
    fun setImage(@DrawableRes imageResource: Int) {
        this.imageView.setImageResource(imageResource)
        this.imageView.visibility = View.VISIBLE
    }

    @ModelProp
    fun setShowPremiumRequired(show: Boolean) {
        showPremiumRequired = show
        updateShowPremiumRequired()
    }

    override fun setTitle(description: CharSequence) {
        super.setTitle(description)
        updateShowPremiumRequired()
    }

    @AfterPropsSet
    fun onPropsSet() {
        setOnClickListenerThrottled {
            onItemClicked?.onClick(it)
        }
    }

    private fun updateShowPremiumRequired() {
        if (showPremiumRequired) {
            titleLabel.text =
                titleLabel.text.addRequiresPremiumNote(context, tagStyled = true)
        } else {
            titleLabel.text =
                titleLabel.text.removeRequiresPremiumNote(context)
        }
    }
}
