package com.stt.android.workoutsettings;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.res.Resources;
import android.os.Bundle;
import android.speech.tts.TextToSpeech;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.StringRes;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import com.stt.android.R;
import com.stt.android.STTApplication;
import com.stt.android.analytics.AnalyticsPropertyValue;
import com.stt.android.common.ui.SingleChoiceDialogFragment;
import static com.stt.android.common.ui.SingleChoiceDialogFragment.RESULT_OK;
import static com.stt.android.common.ui.SingleChoiceDialogFragment.SELECTED_INDEX;
import com.stt.android.databinding.VoiceFeedbackSettingsFragmentBinding;
import com.stt.android.di.FeatureTogglePreferences;
import com.stt.android.domain.user.MeasurementUnit;
import com.stt.android.domain.user.VoiceFeedbackSettings;
import com.stt.android.domain.user.VoiceFeedbackSettingsHelper;
import com.stt.android.domain.workout.ActivityType;
import com.stt.android.laps.Laps;
import com.stt.android.premium.PremiumRequiredToAccessHandler;
import com.stt.android.ui.fragments.BaseCurrentUserControllerFragment;
import com.stt.android.ui.utils.DialogHelper;
import com.stt.android.ui.utils.TextFormatter;
import com.stt.android.workouts.tts.TextToSpeechHelper;
import static com.stt.android.workoutsettings.VoiceFeedbackSettingDialogFragment.RESULT_DISTANCE_VALUE;
import static com.stt.android.workoutsettings.VoiceFeedbackSettingDialogFragment.RESULT_DURATION_VALUE;
import static com.stt.android.workoutsettings.VoiceFeedbackSettingDialogFragment.RESULT_LAP_ENABLED;
import static com.stt.android.workoutsettings.VoiceFeedbackSettingDialogFragment.RESULT_POSITIVE;
import dagger.hilt.android.AndroidEntryPoint;
import java.util.Locale;
import javax.inject.Inject;

@AndroidEntryPoint
public class VoiceFeedbackSettingsFragment extends BaseCurrentUserControllerFragment
    implements TextToSpeech.OnInitListener {
    public static final String FRAGMENT_TAG =
        "com.stt.android.workoutsettings.VoiceFeedbackSettingsFragment.FRAGMENT_TAG";
    private static final int DURATION_MAX_PROGRESS = 24;
    private static String KEY_ACTIVITY_TYPE_ID = "com.stt.android.KEY_ACTIVITY_TYPE_ID";

    private VoiceFeedbackSettingsFragmentBinding binding;

    private VoiceFeedbackSettings voiceFeedbackSettings;

    private TextToSpeech textToSpeech;

    @Inject
    protected PremiumRequiredToAccessHandler premiumRequiredToAccessHandler;

    @Inject
    @FeatureTogglePreferences
    SharedPreferences featureTogglePreferences;

    private static final int REQUEST_LAP_DIALOG = 1;
    private static final String LAP_DIALOG_TAG = "lapDialog";
    private static final int REQUEST_VOICE_FEEDBACK_DISTANCE_DIALOG = 2;
    private static final int REQUEST_VOICE_FEEDBACK_DURATION_DIALOG = 3;
    private static final int REQUEST_VOICE_FEEDBACK_ENERGY = 4;
    private static final int REQUEST_VOICE_FEEDBACK_CURRENT_SPEED_PACE = 5;
    private static final int REQUEST_VOICE_FEEDBACK_AVERAGE_SPEED_PACE = 6;
    private static final int REQUEST_VOICE_FEEDBACK_CURRENT_HEART_RATE = 7;
    private static final int REQUEST_VOICE_FEEDBACK_AVERAGE_HEART_RATE = 8;
    private static final int REQUEST_VOICE_FEEDBACK_CURRENT_CADENCE = 9;
    private static final int REQUEST_VOICE_FEEDBACK_AVERAGE_CADENCE = 10;
    private static final int REQUEST_VOICE_FEEDBACK_AVERAGE_GHOST = 11;
    private static final String CONTENT_DIALOG_TAG = "contentDialog";

    public static VoiceFeedbackSettingsFragment newInstance(int activityTypeId) {
        VoiceFeedbackSettingsFragment fragment = new VoiceFeedbackSettingsFragment();

        Bundle args = new Bundle();
        args.putInt(KEY_ACTIVITY_TYPE_ID, activityTypeId);
        fragment.setArguments(args);

        return fragment;
    }

    private static String buildDescription(VoiceFeedbackSettings.Frequency frequency,
        Resources resources, MeasurementUnit measurementUnit) {
        StringBuilder description = new StringBuilder();
        if (frequency.perLap) {
            description.append(resources.getString(R.string.lap));
        }
        if (frequency.distance > 0) {
            if (description.length() > 0) {
                description.append(", ");
            }
            description.append(
                String.format(Locale.getDefault(), "%.1f", measurementUnit.toDistanceUnit(frequency.distance)))
                .append(" ")
                .append(resources.getString(measurementUnit.getDistanceUnit()));
        }
        if (frequency.duration > 0) {
            if (description.length() > 0) {
                description.append(", ");
            }
            description.append(
                TextFormatter.formatElapsedTimeWithUnit(resources, frequency.duration));
        }
        return description.length() == 0 ? resources.getString(R.string.never)
            : description.toString();
    }

    private void showSettingDialog(
        final FragmentManager fragmentManager,
        final Fragment targetFragment,
        final MeasurementUnit measurementUnit,
        @StringRes int title,
        final VoiceFeedbackSettings.Frequency frequency,
        int requestCode) {

        Context context = targetFragment.getContext();
        if (context == null) return;

        Fragment settingDialog = fragmentManager.findFragmentByTag(LAP_DIALOG_TAG);
        if (settingDialog instanceof DialogFragment) {
            ((DialogFragment) settingDialog).dismiss();
        }

        final DialogFragment dialog =
            VoiceFeedbackSettingDialogFragment.newInstance(
                context.getString(title),
                frequency.perLap,
                frequency.distance,
                VoiceFeedbackSettingsHelper.MAX_REPEATING_DISTANCE,
                frequency.duration,
                DURATION_MAX_PROGRESS,
                measurementUnit);
        dialog.setTargetFragment(targetFragment, requestCode);
        dialog.show(fragmentManager, CONTENT_DIALOG_TAG);
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // the option menu is created when setHasOptionsMenu() is called on certain devices,
        // so need to get the setting before that, otherwise will crash
        // e.g. https://rink.hockeyapp.net/manage/apps/47264/app_versions/62/crash_reasons/21153437
        voiceFeedbackSettings = VoiceFeedbackSettingsHelper.getVoiceFeedbackSettings(getActivity(),
            getActivityTypeId());
        setHasOptionsMenu(true);

        premiumRequiredToAccessHandler.onCreate(this);
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
        Bundle savedInstanceState) {
        binding = VoiceFeedbackSettingsFragmentBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

        binding.voiceFeedbackAutoPauseEnabled.setChecked(voiceFeedbackSettings.autoPauseEnabled);
        binding.voiceFeedbackAutoPauseEnabled.setOnCheckedChangeListener(
            (button, isChecked) -> {
                voiceFeedbackSettings = voiceFeedbackSettings.updateAutoPauseEnabled(isChecked);
                VoiceFeedbackSettingsHelper.saveVoiceFeedbackSettings(getActivity(),
                    voiceFeedbackSettings);
            });

        int activityTypeId = getActivityTypeId();
        final Resources resources = getResources();
        final MeasurementUnit measurementUnit =
            userSettingsController.getSettings().getMeasurementUnit();
        binding.lapIntervalButton.setSelection(
            voiceFeedbackSettings.lapType.getTitle(resources, measurementUnit));
        binding.lapIntervalButton.setOnClickListener(v -> {
            FragmentManager fm = getFragmentManager();
            if (fm == null) {
                return;
            }
            Fragment lapDialog = fm.findFragmentByTag(LAP_DIALOG_TAG);
            if (lapDialog instanceof DialogFragment) {
                ((DialogFragment) lapDialog).dismiss();
            }

            Laps.Type selectedLapType = voiceFeedbackSettings.lapType;
            Laps.Type[] allLaps = Laps.Type.values();
            String[] texts = new String[allLaps.length];
            int selected = 0;
            int i = 0;
            for (Laps.Type lapType : allLaps) {
                texts[i] = lapType.getTitle(resources, measurementUnit).toString();
                if (selectedLapType == lapType) {
                    selected = i;
                }
                ++i;
            }

            final DialogFragment dialog =
                SingleChoiceDialogFragment.newInstance(getString(R.string.lap), texts, selected);
            dialog.setTargetFragment(this, REQUEST_LAP_DIALOG);
            dialog.show(getFragmentManager(), LAP_DIALOG_TAG);
        });

        binding.voiceFeedbackLapTimeEnabled.setChecked(voiceFeedbackSettings.lapTimeEnabled);
        binding.voiceFeedbackLapTimeEnabled.setOnCheckedChangeListener(
            (button, isChecked) -> {
                voiceFeedbackSettings = voiceFeedbackSettings.updateLapTimeEnabled(isChecked);
                VoiceFeedbackSettingsHelper.saveVoiceFeedbackSettings(getActivity(),
                    voiceFeedbackSettings);
            });

        binding.voiceFeedbackLapSpeedPaceEnabled.setChecked(voiceFeedbackSettings.lapSpeedPaceEnabled);
        binding.voiceFeedbackLapSpeedPaceEnabled.setOnCheckedChangeListener(
            (button, isChecked) -> {
                voiceFeedbackSettings =
                    voiceFeedbackSettings.updateLapSpeedPaceEnabled(isChecked);
                VoiceFeedbackSettingsHelper.saveVoiceFeedbackSettings(getActivity(),
                    voiceFeedbackSettings);
            });

        binding.voiceFeedbackDistance.setSelection(
            buildDescription(voiceFeedbackSettings.distance, resources, measurementUnit));
        binding.voiceFeedbackDistance.setOnClickListener(
            v -> showSettingDialog(
                getFragmentManager(),
                this,
                measurementUnit,
                R.string.distance,
                voiceFeedbackSettings.distance,
                REQUEST_VOICE_FEEDBACK_DISTANCE_DIALOG));

        binding.voiceFeedbackDuration.setSelection(
            buildDescription(voiceFeedbackSettings.duration, resources, measurementUnit));
        binding.voiceFeedbackDuration.setOnClickListener(
            v -> showSettingDialog(
                getFragmentManager(),
                this,
                measurementUnit,
                R.string.duration,
                voiceFeedbackSettings.duration,
                REQUEST_VOICE_FEEDBACK_DURATION_DIALOG));

        binding.voiceFeedbackEnergy.setSelection(
            buildDescription(voiceFeedbackSettings.energy, resources, measurementUnit));
        binding.voiceFeedbackEnergy.setOnClickListener(
            v -> showSettingDialog(
                getFragmentManager(),
                this,
                measurementUnit,
                R.string.energy,
                voiceFeedbackSettings.energy,
                REQUEST_VOICE_FEEDBACK_ENERGY));

        binding.voiceFeedbackCurrentSpeedPace.setSelection(
            buildDescription(voiceFeedbackSettings.currentSpeedPace, resources, measurementUnit));
        binding.voiceFeedbackCurrentSpeedPace.setOnClickListener(
            v -> showSettingDialog(
                getFragmentManager(),
                this, measurementUnit,
                R.string.current_speed_pace,
                voiceFeedbackSettings.currentSpeedPace,
                REQUEST_VOICE_FEEDBACK_CURRENT_SPEED_PACE));

        binding.voiceFeedbackAverageSpeedPace.setSelection(
            buildDescription(voiceFeedbackSettings.averageSpeedPace, resources, measurementUnit));
        binding.voiceFeedbackAverageSpeedPace.setOnClickListener(
            v -> showSettingDialog(
                getFragmentManager(),
                this,
                measurementUnit,
                R.string.avg_speed_pace,
                voiceFeedbackSettings.averageSpeedPace,
                REQUEST_VOICE_FEEDBACK_AVERAGE_SPEED_PACE));

        binding.voiceFeedbackCurrentHeartRate.setSelection(
            buildDescription(voiceFeedbackSettings.currentHeartRate, resources, measurementUnit));
        binding.voiceFeedbackCurrentHeartRate.setOnClickListener(
            v -> showSettingDialog(
                getFragmentManager(),
                this,
                measurementUnit,
                R.string.current_heart_rate,
                voiceFeedbackSettings.currentHeartRate,
                REQUEST_VOICE_FEEDBACK_CURRENT_HEART_RATE));

        binding.voiceFeedbackAverageHeartRate.setSelection(
            buildDescription(voiceFeedbackSettings.averageHeartRate, resources, measurementUnit));
        binding.voiceFeedbackAverageHeartRate.setOnClickListener(
            v -> showSettingDialog(
                getFragmentManager(),
                this,
                measurementUnit,
                R.string.avg_heart_rate,
                voiceFeedbackSettings.averageHeartRate,
                REQUEST_VOICE_FEEDBACK_AVERAGE_HEART_RATE));

        if (activityTypeId == ActivityType.CYCLING.getId()
            || activityTypeId == ActivityType.GRAVEL_CYCLING.getId()
            || activityTypeId == ActivityType.MOUNTAIN_BIKING.getId()) {
            binding.voiceFeedbackCurrentCadence.setSelection(
                buildDescription(voiceFeedbackSettings.currentCadence, resources, measurementUnit));
            binding.voiceFeedbackCurrentCadence.setOnClickListener(
                v -> showSettingDialog(
                    getFragmentManager(),
                    this,
                    measurementUnit,
                    R.string.current_cadence,
                    voiceFeedbackSettings.currentCadence,
                    REQUEST_VOICE_FEEDBACK_CURRENT_CADENCE));

            binding.voiceFeedbackAverageCadence.setSelection(
                buildDescription(voiceFeedbackSettings.averageCadence, resources, measurementUnit));
            binding.voiceFeedbackAverageCadence.setOnClickListener(
                v -> showSettingDialog(
                    getFragmentManager(),
                    this,
                    measurementUnit,
                    R.string.avg_cadence,
                    voiceFeedbackSettings.averageCadence,
                    REQUEST_VOICE_FEEDBACK_AVERAGE_CADENCE));
        } else {
            binding.voiceFeedbackCurrentCadence.setVisibility(View.GONE);
            binding.voiceFeedbackAverageCadence.setVisibility(View.GONE);
        }

        binding.voiceFeedbackGhost.setSelection(
            buildDescription(voiceFeedbackSettings.ghost, resources, measurementUnit));
        binding.voiceFeedbackGhost.setOnClickListener(v -> showSettingDialog(
            getFragmentManager(),
            this,
            measurementUnit,
            R.string.ghost_target,
            voiceFeedbackSettings.ghost,
            REQUEST_VOICE_FEEDBACK_AVERAGE_GHOST));

        binding.voiceFeedbackEnabled.setChecked(voiceFeedbackSettings.enabled);
        binding.voiceFeedbackEnabled.setOnCheckedChangeListener(
            (button, isChecked) -> {
                VoiceFeedbackSettingsHelper.saveVoiceFeedbackSettings(getActivity(),
                    voiceFeedbackSettings.updateEnabled(isChecked));
                setOnVoiceFeedbackEnabled(isChecked);

                if (isChecked) {
                    checkTTS();
                }
            });
        binding.voiceFeedbackEnabled.setSelection(
            ActivityType.getLocalizedNameByActivityId(getResources(), getActivityTypeId()));

        premiumRequiredToAccessHandler.startCheckingForPremiumAccess(
            getViewLifecycleOwner(),
            binding.voiceFeedbackBuyPremiumToGetAccessContainer,
            getString(R.string.buy_premium_popup_voice_feedback_description),
            AnalyticsPropertyValue.BuyPremiumPopupShownSource.WORKOUT_RECORDING_VOICE_FEEDBACK_SETTINGS,
            null
        );
    }

    private int getActivityTypeId() {
        return getArguments().getInt(KEY_ACTIVITY_TYPE_ID);
    }

    private void setOnVoiceFeedbackEnabled(boolean enabled) {
        if (voiceFeedbackSettings.enabled == enabled) {
            return;
        }

        voiceFeedbackSettings = voiceFeedbackSettings.updateEnabled(enabled);
    }

    @Override
    public void onResume() {
        super.onResume();
        ActionBar actionBar = null;
        Activity activity = getActivity();
        if (activity instanceof AppCompatActivity) {
            actionBar = ((AppCompatActivity) activity).getSupportActionBar();
        }
        if (actionBar != null) {
            actionBar.setTitle(R.string.voice_feedback);
            actionBar.setDisplayHomeAsUpEnabled(true);
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == REQUEST_LAP_DIALOG && resultCode == RESULT_OK) {
            Laps.Type selectedLapType1 = Laps.Type.values()[data.getIntExtra(SELECTED_INDEX, 0)];
            voiceFeedbackSettings =
                voiceFeedbackSettings.updateLapType(selectedLapType1);
            VoiceFeedbackSettingsHelper.saveVoiceFeedbackSettings(getActivity(),
                voiceFeedbackSettings);
            binding.lapIntervalButton.setSelection(
                selectedLapType1.getTitle(
                    getResources(),
                    userSettingsController.getSettings().getMeasurementUnit()));
        } else if (resultCode == RESULT_POSITIVE) {

            boolean lapEnabled = data.getBooleanExtra(RESULT_LAP_ENABLED, false);
            int distance = data.getIntExtra(RESULT_DISTANCE_VALUE, 0);
            int duration = data.getIntExtra(RESULT_DURATION_VALUE, 0);
            VoiceFeedbackSettings.Frequency frequency =
                new VoiceFeedbackSettings.Frequency(lapEnabled, distance, duration);
            final MeasurementUnit measurementUnit =
                userSettingsController.getSettings().getMeasurementUnit();

            switch (requestCode) {
                case REQUEST_VOICE_FEEDBACK_DISTANCE_DIALOG:
                    voiceFeedbackSettings = voiceFeedbackSettings.updateDistance(frequency);
                    binding.voiceFeedbackDistance.setSelection(
                        buildDescription(voiceFeedbackSettings.distance, getResources(),
                            measurementUnit));
                    break;
                case REQUEST_VOICE_FEEDBACK_DURATION_DIALOG:
                    voiceFeedbackSettings = voiceFeedbackSettings.updateDuration(frequency);
                    binding.voiceFeedbackDuration.setSelection(
                        buildDescription(
                            voiceFeedbackSettings.duration, getResources(), measurementUnit));
                    break;
                case REQUEST_VOICE_FEEDBACK_ENERGY:
                    voiceFeedbackSettings = voiceFeedbackSettings.updateEnergy(frequency);
                    binding.voiceFeedbackEnergy.setSelection(
                        buildDescription(
                            voiceFeedbackSettings.energy, getResources(), measurementUnit));
                    break;
                case REQUEST_VOICE_FEEDBACK_CURRENT_SPEED_PACE:
                    voiceFeedbackSettings = voiceFeedbackSettings.updateCurrentSpeedPace(frequency);
                    binding.voiceFeedbackCurrentSpeedPace.setSelection(
                        buildDescription(
                            voiceFeedbackSettings.currentSpeedPace, getResources(),
                            measurementUnit));
                    break;
                case REQUEST_VOICE_FEEDBACK_AVERAGE_SPEED_PACE:
                    voiceFeedbackSettings = voiceFeedbackSettings.updateAverageSpeedPace(frequency);
                    binding.voiceFeedbackAverageSpeedPace.setSelection(
                        buildDescription(voiceFeedbackSettings.averageSpeedPace, getResources(),
                            measurementUnit));
                    break;
                case REQUEST_VOICE_FEEDBACK_CURRENT_HEART_RATE:
                    voiceFeedbackSettings = voiceFeedbackSettings.updateCurrentHeartRate(frequency);
                    binding.voiceFeedbackCurrentHeartRate.setSelection(
                        buildDescription(voiceFeedbackSettings.currentHeartRate, getResources(),
                            measurementUnit));
                    break;
                case REQUEST_VOICE_FEEDBACK_AVERAGE_HEART_RATE:
                    voiceFeedbackSettings = voiceFeedbackSettings.updateAverageHeartRate(frequency);
                    binding.voiceFeedbackAverageHeartRate.setSelection(
                        buildDescription(voiceFeedbackSettings.averageHeartRate, getResources(),
                            measurementUnit));
                    break;
                case REQUEST_VOICE_FEEDBACK_CURRENT_CADENCE:
                    voiceFeedbackSettings = voiceFeedbackSettings.updateCurrentCadence(frequency);
                    binding.voiceFeedbackCurrentCadence.setSelection(
                        buildDescription(voiceFeedbackSettings.currentCadence,
                            getResources(), measurementUnit));
                case REQUEST_VOICE_FEEDBACK_AVERAGE_CADENCE:
                    voiceFeedbackSettings = voiceFeedbackSettings.updateAverageCadence(frequency);
                    binding.voiceFeedbackAverageCadence.setSelection(
                        buildDescription(voiceFeedbackSettings.averageCadence,
                            getResources(), measurementUnit));
                case REQUEST_VOICE_FEEDBACK_AVERAGE_GHOST:
                    voiceFeedbackSettings = voiceFeedbackSettings.updateGhost(frequency);
                    binding.voiceFeedbackGhost.setSelection(
                        buildDescription(voiceFeedbackSettings.ghost, getResources(),
                            measurementUnit));
                    break;
            }
            VoiceFeedbackSettingsHelper.saveVoiceFeedbackSettings(getActivity(),
                voiceFeedbackSettings);
        }
    }

    private void checkTTS() {
        if (textToSpeech != null) {
            return;
        }

        textToSpeech = new TextToSpeech(requireActivity().getApplicationContext(), this);
    }

    @Override
    public void onInit(int status) {
        if (textToSpeech == null) {
            // just in case some weird engine won't stop even if TextToSpeech.shutdown() is called
            return;
        }

        if (status != TextToSpeech.SUCCESS) {
            return;
        }

        checkTTSLanguage(getString(R.string.tts_language));
    }

    private void checkTTSLanguage(String language) {
        switch (TextToSpeechHelper.isLanguageSupported(textToSpeech, language, false)) {
            case TextToSpeech.LANG_MISSING_DATA:
                DialogHelper.showDialog(getActivity(), R.string.voice_feedback,
                    R.string.tts_not_installed, (dialog, which) -> {
                        Intent installIntent = new Intent();
                        installIntent.setAction(TextToSpeech.Engine.ACTION_INSTALL_TTS_DATA);
                        startActivity(installIntent);
                    }, null);
                break;
            case TextToSpeech.LANG_NOT_SUPPORTED:
                // falls back to English
                String english = Locale.ENGLISH.getLanguage();
                if (!language.equals(english)) {
                    checkTTSLanguage(english);
                }
                break;
            case TextToSpeech.LANG_AVAILABLE:
                // Some stupid text to speech engines (I'm looking at you Samsung!) fire up a
                // dialog after reading a text
                // asking the user to install a higher quality voice. So for a better user
                // experience we ask the engine to
                // speak " " so the dialog is triggered.
                textToSpeech.speak(" ", TextToSpeech.QUEUE_FLUSH, null);
                break;
        }
    }

    @Override
    public void onDetach() {
        if (textToSpeech != null) {
            textToSpeech.shutdown();
            textToSpeech = null;
        }

        super.onDetach();
    }
}
