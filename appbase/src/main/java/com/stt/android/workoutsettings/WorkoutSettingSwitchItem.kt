package com.stt.android.workoutsettings

import android.content.Context
import android.os.Parcelable
import android.util.AttributeSet
import android.util.SparseArray
import android.widget.CompoundButton
import androidx.appcompat.widget.SwitchCompat
import com.stt.android.R

class WorkoutSettingSwitchItem
@JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : WorkoutSettingItem(context, attrs, defStyleAttr) {

    private val switchButton: SwitchCompat by lazy { findViewById(R.id.switchButton) }

    override val layout: Int
        get() = R.layout.workout_setting_switch_item

    fun setChecked(switchOn: Boolean) {
        switchButton.isChecked = switchOn
    }

    fun setOnCheckedChangeListener(listener: CompoundButton.OnCheckedChangeListener?) {
        switchButton.setOnCheckedChangeListener(listener)
    }

    override fun dispatchSaveInstanceState(container: SparseArray<Parcelable?>?) {
        dispatchFreezeSelfOnly(container)
    }

    override fun dispatchRestoreInstanceState(container: SparseArray<Parcelable?>?) {
        dispatchThawSelfOnly(container)
    }
}
