package com.stt.android.workoutsettings.follow;

import android.content.Context;
import android.location.Location;
import androidx.annotation.IntDef;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.WorkerThread;
import com.stt.android.cardlist.FeedCard;
import com.stt.android.controllers.CurrentUserController;
import com.stt.android.controllers.WorkoutHeaderController;
import com.stt.android.domain.Point;
import com.stt.android.domain.routes.GetRoutesUseCase;
import com.stt.android.domain.routes.Route;
import com.stt.android.domain.user.ActivityTypeHelper;
import com.stt.android.domain.user.User;
import com.stt.android.domain.workout.ActivityType;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.home.explore.routes.RouteUtils;
import com.stt.android.maps.location.SuuntoLocationSource;
import com.stt.android.models.SimilarWorkoutModel;
import io.reactivex.Flowable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import kotlin.Pair;
import kotlin.collections.CollectionsKt;
import timber.log.Timber;

public abstract class BaseTargetWorkoutSelectionPresenter
        extends BaseLocationPresenter<TargetWorkoutSelectionView> implements
    OnAddToWatchToggledListener {
    public static final int ACTION_SELECT_NONE = -1;
    public static final int ACTION_SELECT_GHOST_TARGET = 0;
    public static final int ACTION_SELECT_FOLLOW_ROUTE_OR_WORKOUT = 1;

    protected final Context appContext;
    private final WorkoutHeaderController workoutHeaderController;
    private final SimilarWorkoutModel similarWorkoutModel;
    private final GetRoutesUseCase getRoutesUseCase;
    protected final CurrentUserController currentUserController;

    /**
     * See {@link #ACTION_SELECT_FOLLOW_ROUTE_OR_WORKOUT} and {@link #ACTION_SELECT_GHOST_TARGET}
     */
    @SelectionMode
    private int selectionMode = ACTION_SELECT_NONE;

    private WorkoutHeader selectedWorkout;
    private Route selectedRoute;

    @Nullable
    private List<FeedCard> lastFound;
    private boolean hasRoutes;

    @Nullable
    private Disposable findRoutesAtSubscription;

    protected Point startPosition;

    public BaseTargetWorkoutSelectionPresenter(Context appContext,
        WorkoutHeaderController workoutHeaderController,
        CurrentUserController currentUserController,
        SimilarWorkoutModel similarWorkoutModel,
        GetRoutesUseCase getRoutesUseCase,
        SuuntoLocationSource suuntoLocationSource) {
        super(appContext, suuntoLocationSource);
        this.appContext = appContext;
        this.workoutHeaderController = workoutHeaderController;
        this.currentUserController = currentUserController;
        this.similarWorkoutModel = similarWorkoutModel;
        this.getRoutesUseCase = getRoutesUseCase;
    }

    public void reset(@SelectionMode int selectionMode) {
        this.selectionMode = selectionMode;
        this.lastFound = null;
    }

    public void setSelectedWorkout(WorkoutHeader selectedWorkout) {
        this.selectedWorkout = selectedWorkout;
        this.selectedRoute = null;
    }

    public void setSelectedRoute(Route selectedRoute) {
        this.selectedRoute = selectedRoute;
        this.selectedWorkout = null;
    }

    @Override
    public void onLocationChanged(@Nullable Location location) {
        loadWorkoutsOrRoutes(location);
    }

    private void loadWorkoutsOrRoutes(@Nullable Location location) {
        TargetWorkoutSelectionView v = getView();
        if (v == null) {
            return;
        }
        v.hideNoWorkoutsView();

        ActivityType activityType = ActivityTypeHelper.getLastActivity(appContext);
        startPosition =
                location != null ? new Point(location.getLongitude(), location.getLatitude()) : null;
        switch (selectionMode) {
            case ACTION_SELECT_GHOST_TARGET:
                findWorkoutsAt(activityType, startPosition);
                break;
            case ACTION_SELECT_FOLLOW_ROUTE_OR_WORKOUT:
                findRoutesAt(startPosition);
                break;
            case ACTION_SELECT_NONE:
                break;
        }
    }

    private void findRoutesAt(@Nullable final Point startPosition) {

        if (findRoutesAtSubscription != null) {
            findRoutesAtSubscription.dispose();
        }

        Flowable<List<FeedCard>> routeCardsFlowable;
        if (startPosition != null) {
            routeCardsFlowable =
                RouteUtils.findRoutesWithDistance(getRoutesUseCase.getRoutesRx(true, true), startPosition)
                    .map(routes -> {
                        int size = routes.size();
                        List<FeedCard> feedCards = new ArrayList<>(size);
                        if (size == 0) {
                            feedCards.add(new NoRoutesCard());
                            hasRoutes = false;
                        } else {
                            for (int i = 0; i < size; ++i) {
                                Pair<Route, Double> route = routes.get(i);
                                feedCards.add(RouteCard.builder()
                                    .extractRoute(route.getFirst())
                                    .distanceToCurrentLocation(route.getSecond())
                                    .addToWatchToggledListener(this)
                                    .build());
                            }
                            hasRoutes = true;
                        }
                        return feedCards;
                    });
        } else {
            routeCardsFlowable = getRoutesUseCase.getRoutesRx(true, true)
                .map(routes -> {
                    // Only consider routes where starting point is not (0, 0)
                    List<Route> filteredRoutes = CollectionsKt.filter(routes, route ->
                        !(Double.compare(route.getStartPoint().getLatitude(), 0.0) == 0
                            && Double.compare(route.getStartPoint().getLongitude(), 0.0) == 0)
                    );

                    int size = filteredRoutes.size();
                    List<FeedCard> feedCards = new ArrayList<>(size);
                    if (size == 0) {
                        feedCards.add(new NoRoutesCard());
                        hasRoutes = false;
                    } else {
                        for (int i = 0; i < size; ++i) {
                            feedCards.add(
                                RouteCard.builder()
                                    .extractRoute(filteredRoutes.get(i))
                                    .addToWatchToggledListener(this)
                                    .build());
                        }
                        hasRoutes = true;
                    }
                    return feedCards;
                });
        }

        findRoutesAtSubscription =
            routeCardsFlowable
                .map(routeFeedCards -> {
                    List<FeedCard> feedCards = getInitialFeedCardList();
                    feedCards.addAll(routeFeedCards);
                    return feedCards;
                }).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(routeCards -> {
                        TargetWorkoutSelectionView v = getView();
                        if (v != null) {
                            informContentFound(routeCards);
                            lastFound = routeCards;
                            if (hasRoutes) {
                                showActionButton();
                            }
                        }
                    },
                    (throwable) -> {
                        Timber.w(throwable, "Error finding routes");
                        TargetWorkoutSelectionView v = getView();
                        if (v != null) {
                            v.showError();
                        }
                    });
        getDisposable().add(findRoutesAtSubscription);
    }

    private void showActionButton() {
        TargetWorkoutSelectionView view = getView();
        if (view != null) {
            view.showActionButton();
        }
    }

    @WorkerThread
    private static FollowWorkoutCard buildWorkoutCard(WorkoutHeader workoutHeader, User user) {
        return new FollowWorkoutCard(workoutHeader, user);
    }

    private void findWorkoutsAt(ActivityType activityType, @Nullable Point startPosition) {

        if (findRoutesAtSubscription != null) {
            findRoutesAtSubscription.dispose();
        }

        TargetWorkoutSelectionView view = getView();
        if (view != null) {
            view.hideActionButton();
        }

        Flowable<List<WorkoutHeader>> workouts =
                startPosition != null
                    ? Flowable.fromCallable(() -> similarWorkoutModel.findWorkoutsWithSimilarStartPosition(
                        currentUserController.getUsername(), activityType, startPosition))
                    : Flowable.fromCallable(() -> workoutHeaderController.loadWorkouts(
                            currentUserController.getUsername(), activityType))
                        .flatMapIterable(workoutHeaders -> workoutHeaders)
                        .filter(workoutHeader ->
                                !(workoutHeader.getStartPosition() == null
                                        || workoutHeader.getStartPosition().isOrigin())
                        ).toList()
                        .toFlowable();

        findRoutesAtSubscription = workouts.delay(200, TimeUnit.MILLISECONDS)
                .map(workoutHeaders -> {
                    int count = workoutHeaders.size();
                    List<FollowWorkoutCard> cards = new ArrayList<>(count);
                    User user = currentUserController.getCurrentUser();
                    for (int i = 0; i < count; ++i) {
                        cards.add(buildWorkoutCard(workoutHeaders.get(i), user));
                    }
                    return cards;
                })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(workoutCards -> {
                    TargetWorkoutSelectionView v = getView();
                    if (v == null) {
                        return;
                    }

                    if (workoutCards.isEmpty()) {
                        v.showNoWorkoutsView();
                    } else {
                        List<FeedCard> feedCards = getInitialFeedCardList();
                        feedCards.addAll(workoutCards);
                        lastFound = feedCards;
                        informContentFound(feedCards);
                    }
                }, throwable -> {
                    Timber.w(throwable, "Error finding workouts");
                    TargetWorkoutSelectionView v = getView();
                    if (v != null) {
                        v.showError();
                    }
                });
        getDisposable().add(findRoutesAtSubscription);
    }

    @NonNull
    private List<FeedCard> getInitialFeedCardList() {
        List<FeedCard> feedCards = new ArrayList<>();
        if (selectedWorkout != null) {
            feedCards.add(SelectedFollowCard.builder()
                    .workoutHeader(selectedWorkout)
                    .id(selectedWorkout.hashCode())
                    .build());
        } else if (selectedRoute != null) {
            feedCards.add(SelectedFollowCard.builder()
                    .route(selectedRoute)
                    .id(selectedRoute.hashCode())
                    .build());
        }
        return feedCards;
    }

    @Override
    protected void gpsNotEnabled() {
        TargetWorkoutSelectionView view = getView();
        if (view != null) {
            view.askEnableGps();
        }
    }

    @Override
    protected void locationPermissionMissing() {
        loadWorkoutsOrRoutes(null);
    }

    @Override
    protected void locationNotAvailable() {
        loadWorkoutsOrRoutes(null);
    }

    private void informContentFound(@NonNull List<FeedCard> lastFound) {
        TargetWorkoutSelectionView view = getView();
        if (view != null) {
            view.showCards(lastFound);
        }
    }

    void onWorkoutSelect(WorkoutHeader workout) {
        TargetWorkoutSelectionView view = getView();
        if (view != null) {
            switch (selectionMode) {
                case ACTION_SELECT_FOLLOW_ROUTE_OR_WORKOUT:
                    view.onFollowWorkoutSelected(workout);
                    break;
                case ACTION_SELECT_GHOST_TARGET:
                    view.onGhostWorkoutSelected(workout);
                    break;
                case ACTION_SELECT_NONE:
                    break;
            }
        }
    }

    void onRouteSelected(Route route) {
        TargetWorkoutSelectionView view = getView();
        if (view != null) {
            view.onFollowRouteSelected(route.getId());
        }
    }

    void clearSelectedTarget() {
        TargetWorkoutSelectionView view = getView();
        if (view != null) {
            view.clearSelectedTarget();
        }
        this.selectedRoute = null;
        this.selectedWorkout = null;
        if (lastFound != null && !lastFound.isEmpty()) {
            lastFound.remove(0);
            informContentFound(lastFound);
        }
    }

    @SelectionMode
    public int getSelectionMode() {
        return selectionMode;
    }

    @IntDef({
            ACTION_SELECT_NONE, ACTION_SELECT_GHOST_TARGET, ACTION_SELECT_FOLLOW_ROUTE_OR_WORKOUT
    })
    @Retention(RetentionPolicy.SOURCE)
    public @interface SelectionMode {
    }
}
