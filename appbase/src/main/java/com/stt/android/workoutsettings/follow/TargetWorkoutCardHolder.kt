package com.stt.android.workoutsettings.follow

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import com.stt.android.R
import com.stt.android.cardlist.FeedViewHolder
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.ui.components.workout.WorkoutCard
import com.stt.android.ui.components.workout.WorkoutCardViewModel
import com.stt.android.workoutsettings.WorkoutSettingsActivity

class TargetWorkoutCardHolder(
    activity: WorkoutSettingsActivity,
    inflater: LayoutInflater,
    parent: ViewGroup,
): FeedViewHolder<FollowWorkoutCard> (
    inflater.inflate(R.layout.target_workout_card, parent, false)
) {
    private val presenter: TargetWorkoutSelectionPresenter = activity.presenter
    private val workoutCardViewHolder: WorkoutCardViewModel = activity.workoutCardViewModel

    override fun bind(feedCard: FollowWorkoutCard, mapCacheWidth: Int, mapCacheHeight: Int) {
        (itemView as ComposeView).setContentWithM3Theme {
            WorkoutCard(
                workoutHeader = feedCard.workoutHeader,
                onClick = { presenter.onWorkoutSelect(feedCard.workoutHeader) },
                modifier = Modifier.padding(
                    horizontal = MaterialTheme.spacing.medium,
                    vertical = MaterialTheme.spacing.small,
                ),
                viewModel = workoutCardViewHolder,
                configuration = WorkoutCardViewModel.Configuration(
                    showCoverMap = true,
                ),
            )
        }
    }
}
