package com.stt.android.workoutsettings.follow

import androidx.annotation.WorkerThread
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.google.maps.android.PolyUtil
import com.stt.android.cardlist.FeedCard
import com.stt.android.cardlist.FeedCard.Companion.ROUTE_CARD
import com.stt.android.cardlist.MapCard
import com.stt.android.domain.routes.Route
import com.stt.android.home.explore.routes.RouteUtils.routePointsToLatLngList

data class RouteCard internal constructor(
    val addToWatchToggledListener: OnAddToWatchToggledListener,
    override val bounds: LatLngBounds?,
    val routeData: Route,
    override val route: List<LatLng>?,
    override val activityRoutes: List<List<LatLng>>? = null,
    override val nonActivityRoutes: List<List<LatLng>>? = null,
    val distanceToCurrentLocation: Double,
    override val viewType: Int,
    val polyline: String,
    val syncableWithDeviceData: Boolean,
    val watchRouteListFull: Boolean,
    override var id: Long = 0
) : FeedCard, MapCard {
    override fun calculateDifferences(other: FeedCard?): List<Any>? = null

    override val polylineHashCode: Int = polyline.hashCode()

    @Suppress("LongParameterList")
    class Builder internal constructor(
        private var addToWatchToggledListener: OnAddToWatchToggledListener? = null,
        private var bounds: LatLngBounds? = null,
        private var route: List<LatLng>? = null,
        private var polyline: String? = null,
        private var routeData: Route? = null,
        private var distanceToCurrentLocation: Double = DISTANCE_NOT_SET,
        private var id: Long = 0
    ) {
        fun addToWatchToggledListener(listener: OnAddToWatchToggledListener): Builder =
            apply { this.addToWatchToggledListener = listener }

        fun bounds(value: LatLngBounds): Builder = apply { this.bounds = value }

        fun route(value: List<LatLng>): Builder = apply { this.route = value }

        fun polyline(value: String): Builder = apply { this.polyline = value }

        fun routeData(value: Route): Builder = apply { this.routeData = value }

        fun distanceToCurrentLocation(distanceToCurrentLocation: Double): Builder =
            apply { this.distanceToCurrentLocation = distanceToCurrentLocation }

        private fun autoBuild(): RouteCard =
            RouteCard(
                addToWatchToggledListener = addToWatchToggledListener
                    ?: error("addToWatchToggledListener == null"),
                viewType = ROUTE_CARD,
                bounds = bounds,
                route = route,
                polyline = polyline ?: error("polyline == null"),
                routeData = routeData ?: error("routeData == null"),
                distanceToCurrentLocation = distanceToCurrentLocation,
                syncableWithDeviceData = false,
                watchRouteListFull = false
            )

        fun build(): RouteCard {
            val routeCard = autoBuild()
            if (id == 0L) {
                id = calculateId(routeCard)
            }
            routeCard.id = id
            return routeCard
        }

        private fun calculateId(routeCard: RouteCard): Long {
            val id = routeCard.routeData.id
            val key = routeCard.routeData.key
            return id.hashCode().toLong() + (key.hashCode())
        }

        @WorkerThread
        fun extractRoute(route: Route): Builder {
            val routePoints: List<LatLng> = route.segments
                .map { routePointsToLatLngList(it.routePoints) }
                .flatten()
            if (routePoints.isNotEmpty()) {
                val boundsBuilder = LatLngBounds.builder()
                for (routePoint in routePoints) {
                    boundsBuilder.include(routePoint)
                }
                bounds(boundsBuilder.build())
                polyline(PolyUtil.encode(routePoints))
                route(routePoints)
            }
            routeData(route)
            return this
        }
    }

    companion object {
        const val DISTANCE_NOT_SET: Double = -1.0

        @JvmStatic
        fun builder(): Builder = Builder()
    }
}
