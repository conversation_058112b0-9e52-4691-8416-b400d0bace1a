package com.stt.android.workoutsettings.activitytype

import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.fragment.app.viewModels
import com.stt.android.R
import com.stt.android.common.ui.observeNotNull
import com.stt.android.common.viewstate.ViewStateListFragment2
import com.stt.android.databinding.FragmentActivityTypeSelectionListBinding
import com.stt.android.domain.user.ActivityTypeHelper
import com.stt.android.ui.extensions.requireTheme
import com.stt.android.ui.utils.EpoxyConditionalDividerItemDecoration
import com.stt.android.workouts.RecordWorkoutService
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class ActivityTypeSelectionListFragment :
    ViewStateListFragment2<ActivityTypeSelectionListData, ActivityTypeSelectionListViewModel>() {
    override val viewModel: ActivityTypeSelectionListViewModel by viewModels()
    override val layoutId = R.layout.fragment_activity_type_selection_list

    private val binding: FragmentActivityTypeSelectionListBinding get() = requireBinding()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.loadData(requireContext())

        viewModel.onActivityTypeSelected.observeNotNull(viewLifecycleOwner) { selectedActivityType ->
            ActivityTypeHelper.saveRecentActivity(requireContext(), selectedActivityType)
            ContextCompat.startForegroundService(
                requireContext(),
                RecordWorkoutService.newPrepareWorkoutIntent(requireContext(), selectedActivityType)
            )
            parentFragmentManager.popBackStack()
        }

        val divider = resources.getDimensionPixelSize(R.dimen.size_divider)
        binding.list.addItemDecoration(
            EpoxyConditionalDividerItemDecoration(
                dividerColor = resources.getColor(R.color.light_grey, requireTheme()),
                drawDividerOver = false,
                dividerHeightBetween = { _, _ -> divider }
            )
        )
        setupActionBar()
    }

    private fun setupActionBar() {
        val activity = activity as? AppCompatActivity
        activity?.supportActionBar?.let { actionBar ->
            actionBar.setTitle(R.string.select_activity)
            actionBar.setDisplayHomeAsUpEnabled(true)
        }
    }

    companion object {
        const val FRAGMENT_TAG =
            "com.stt.android.workoutsettings.ActivityTypeSelectionListFragment.FRAGMENT_TAG"

        @JvmStatic
        fun newInstance() = ActivityTypeSelectionListFragment()
    }
}
