package com.stt.android.workoutsettings

import androidx.lifecycle.ViewModel
import com.stt.android.domain.user.VoiceFeedbackSettings
import com.stt.android.domain.workouts.WorkoutHeader

abstract class BaseWorkoutSettingsViewModel : ViewModel() {
    protected var hasVoiceFeedback = false
    protected var hasGhostTarget = false
    protected var hasFollowWorkout = false
    protected var hasFollowRoute = false

    fun onUseVoiceFeedbackSettingsSelected(settings: VoiceFeedbackSettings) {
        hasVoiceFeedback = settings.enabled
        onSettingsUpdated()
    }

    fun onFollowWorkoutSelected(followWorkout: WorkoutHeader?) {
        hasGhostTarget = false
        hasFollowWorkout = followWorkout != null
        hasFollowRoute = false
        onSettingsUpdated()
    }

    fun onGhostWorkoutSelected(ghostWorkout: WorkoutHeader?) {
        hasGhostTarget = ghostWorkout != null
        hasFollowWorkout = false
        hasFollowRoute = false
        onSettingsUpdated()
    }

    fun onFollowRouteSelected(routeId: String?) {
        hasGhostTarget = false
        hasFollowWorkout = false
        hasFollowRoute = routeId != null
        onSettingsUpdated()
    }

    open fun onSettingsUpdated() {
        // do nothing
    }
}
