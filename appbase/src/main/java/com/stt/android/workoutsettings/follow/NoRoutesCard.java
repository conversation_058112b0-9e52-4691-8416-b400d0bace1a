package com.stt.android.workoutsettings.follow;

import androidx.annotation.Nullable;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.LatLngBounds;
import com.stt.android.cardlist.FeedCard;
import java.util.List;

public class NoRoutesCard implements FeedCard {

    private long id;

    public NoRoutesCard() {
        this.id = hashCode();
    }

    @Override
    public int getViewType() {
        return NO_ROUTES_CARD;
    }

    @Override
    public long getId() {
        return id;
    }

    @Nullable
    @Override
    public List<Object> calculateDifferences(FeedCard other) {
        return null;
    }

    @Override
    public void setId(long id) {
        this.id=id;
    }

}
