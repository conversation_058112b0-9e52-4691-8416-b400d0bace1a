package com.stt.android.workoutsettings.follow;

import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.provider.Settings;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AlertDialog;
import androidx.recyclerview.widget.RecyclerView;
import com.stt.android.R;
import com.stt.android.analytics.AnalyticsPropertyValue;
import com.stt.android.cardlist.FeedCard;
import com.stt.android.cardlist.FeedFragment;
import com.stt.android.controllers.CurrentUserController;
import com.stt.android.databinding.TargetWorkoutListBinding;
import com.stt.android.home.explore.routes.RoutePlannerNavigator;
import com.stt.android.premium.PremiumRequiredToAccessHandler;
import com.stt.android.ui.utils.DialogHelper;
import com.stt.android.workoutsettings.WorkoutSettingsActivity;
import dagger.hilt.android.AndroidEntryPoint;
import java.util.List;
import javax.inject.Inject;

/**
 * Expects WorkoutSettingsActivity context
 */
@AndroidEntryPoint
public class TargetWorkoutSelectionFragment extends FeedFragment
    implements View.OnClickListener {
    public static final String FRAGMENT_TAG =
        "com.stt.android.workoutsettings.TargetWorkoutSelectionFragment.FRAGMENT_TAG";

    @Inject
    CurrentUserController currentUserController;

    private TargetWorkoutListBinding binding;

    @SuppressWarnings("WeakerAccess")
    @Inject
    RoutePlannerNavigator routePlannerNavigator;

    @Inject
    PremiumRequiredToAccessHandler premiumRequiredToAccessHandler;

    public static TargetWorkoutSelectionFragment newInstance() {
        return new TargetWorkoutSelectionFragment();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        premiumRequiredToAccessHandler.onCreate(this);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
        Bundle savedInstanceState) {
        binding = TargetWorkoutListBinding.inflate(inflater, container, false);

        int selectionMode = (getActivity() instanceof WorkoutSettingsActivity)
            ? ((WorkoutSettingsActivity)getActivity()).getSelectionMode()
            : BaseTargetWorkoutSelectionPresenter.ACTION_SELECT_NONE;
        int premiumDescriptionResId;
        String premiumAnalyticsSource;
        if (selectionMode == BaseTargetWorkoutSelectionPresenter.ACTION_SELECT_GHOST_TARGET) {
            premiumDescriptionResId = R.string.buy_premium_popup_ghost_target_description;
            premiumAnalyticsSource = AnalyticsPropertyValue.BuyPremiumPopupShownSource.WORKOUT_RECORDING_GHOST_SELECTION;
        } else {
            premiumDescriptionResId = R.string.buy_premium_popup_follow_route_description;
            premiumAnalyticsSource = AnalyticsPropertyValue.BuyPremiumPopupShownSource.WORKOUT_RECORDING_FOLLOW_ROUTE_SELECTION;
        }

        premiumRequiredToAccessHandler.startCheckingForPremiumAccess(
            getViewLifecycleOwner(),
            binding.getRoot(),
            getString(premiumDescriptionResId),
            premiumAnalyticsSource,
            null
        );

        return binding.getRoot();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }

    @Override
    protected RecyclerView getRecyclerView() {
        return binding.targetList;
    }

    @Override
    public void onResume() {
        super.onResume();
        WorkoutSettingsActivity activity = (WorkoutSettingsActivity) getActivity();
        if (activity == null) return;
        activity.refreshWithTimeout();

        int selectionMode = activity.getSelectionMode();
        int title;
        switch (selectionMode) {
            case BaseTargetWorkoutSelectionPresenter.ACTION_SELECT_GHOST_TARGET:
                title = R.string.ghost_target;
                break;
            case BaseTargetWorkoutSelectionPresenter.ACTION_SELECT_FOLLOW_ROUTE_OR_WORKOUT:
                title = R.string.follow_route;
                break;
            default:
                title = R.string.workout_target;
                break;
        }
        ActionBar actionBar = activity.getSupportActionBar();
        if (actionBar != null) {
            actionBar.setTitle(title);
            actionBar.setDisplayHomeAsUpEnabled(true);
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        WorkoutSettingsActivity activity = (WorkoutSettingsActivity) getActivity();
        if (activity == null) return;
        activity.cancelRefresh();
    }

    public void showError() {
        DialogHelper.showDialog(getActivity(), R.string.error_generic,
            new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    // In case you don't love fragments enough, here's yet another example:
                    // https://fabric.io/sporst-tracker/android/apps/com.stt.android/issues/57ff779a0aeb16625b72d393
                    if (!isAdded()) {
                        return;
                    }
                    getFragmentManager().popBackStack();
                }
            });
    }

    public void askEnableGps() {
        if (getContext() != null) {

            DialogInterface.OnClickListener onPositive = (dialog, which) -> startActivity(
                new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS));

            DialogInterface.OnClickListener onNegative =
                (dialog, which) -> getFragmentManager().popBackStack();

            new AlertDialog.Builder(getContext())
                .setCancelable(true)
                .setTitle(R.string.settings)
                .setMessage(R.string.gps_disabled_enable)
                .setPositiveButton(R.string.settings, onPositive)
                .setNegativeButton(R.string.cancel, onNegative)
                .setOnCancelListener(dialog -> getFragmentManager().popBackStack())
                .show();
        }
    }

    public void showCards(List<FeedCard> cards) {
        binding.progressBar.setVisibility(View.GONE);
        setContents(cards);
    }

    public void showActionButton() {
        binding.newRouteBt.setVisibility(View.VISIBLE);
        binding.newRouteBt.setOnClickListener(this);
    }

    public void hideActionButton() {
        binding.newRouteBt.setVisibility(View.GONE);
    }

    public void showNoWorkoutsView() {
        binding.noWorkoutsView.setVisibility(View.VISIBLE);
    }

    public void hideNoWorkoutsView() {
        binding.noWorkoutsView.setVisibility(View.GONE);
    }

    @Override
    public void onClick(View view) {
        routePlannerNavigator.startCreateRouteActivityOrRedirectToLogin(getContext(), currentUserController);
    }

    @Nullable
    @Override
    protected View mapSnapshotterSizeProvider() {
        int selectionMode = (getActivity() instanceof WorkoutSettingsActivity)
            ? ((WorkoutSettingsActivity)getActivity()).getSelectionMode()
            : BaseTargetWorkoutSelectionPresenter.ACTION_SELECT_NONE;
        return binding.getRoot().findViewById(
            selectionMode == BaseTargetWorkoutSelectionPresenter.ACTION_SELECT_GHOST_TARGET
                ? R.id.map_snapshotter
                : R.id.route_snapshotter
        );
    }
}
