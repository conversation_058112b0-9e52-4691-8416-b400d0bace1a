package com.stt.android.workoutsettings.follow

import com.stt.android.cardlist.FeedCard
import com.stt.android.domain.routes.Route
import com.stt.android.domain.workouts.WorkoutHeader
import kotlin.jvm.JvmStatic

data class SelectedFollowCard internal constructor(
    val route: Route?,
    val workoutHeader: WorkoutHeader?,
    override var id: Long
) : FeedCard {

    override val viewType: Int = FeedCard.SELECTED_FOLLOW_CARD

    override fun calculateDifferences(other: FeedCard?): List<Any>? = null

    class Builder internal constructor(
        private var route: Route? = null,
        private var workoutHeader: WorkoutHeader? = null,
        private var id: Long = 0
    ) {
        fun route(value: Route): Builder = apply { this.route = value }

        fun workoutHeader(value: WorkoutHeader): Builder = apply {
            this.workoutHeader = value
        }

        fun id(value: Long): Builder = apply { id = value }

        fun build(): SelectedFollowCard =
            SelectedFollowCard(route = route, workoutHeader = workoutHeader, id = id)
    }

    companion object {
        @JvmStatic
        fun builder(): Builder = Builder()
    }
}
