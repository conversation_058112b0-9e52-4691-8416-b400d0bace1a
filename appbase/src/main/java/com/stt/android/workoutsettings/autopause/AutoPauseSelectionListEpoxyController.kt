package com.stt.android.workoutsettings.autopause

import android.content.Context
import com.stt.android.autoPauseInfoHeader
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.ViewStateEpoxyController
import com.stt.android.ui.components.editors.trackingSettingsCheckboxEditor
import javax.inject.Inject

class AutoPauseSelectionListEpoxyController @Inject constructor(
    private val context: Context
) : ViewStateEpoxyController<AutoPauseSelectionListData?>() {

    override fun buildModels(viewState: ViewState<AutoPauseSelectionListData?>) {
        super.buildModels(viewState)

        val data = viewState.data ?: return

        autoPauseInfoHeader {
            id("header")
        }

        data.autoPauses.forEach { autoPause ->
            trackingSettingsCheckboxEditor {
                id(autoPause.name)
                title(autoPause.getLocalizedName(context.resources))
                checkedInitialValue(autoPause == data.selectedAutoPause)
                onCheckedChangeListener { _ ->
                    data.onAutoPauseClicked(autoPause)
                }
            }
        }
    }
}
