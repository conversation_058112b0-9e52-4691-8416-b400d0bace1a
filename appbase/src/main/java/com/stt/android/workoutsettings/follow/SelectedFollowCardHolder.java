package com.stt.android.workoutsettings.follow;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import com.stt.android.R;
import com.stt.android.cardlist.FeedViewHolder;
import com.stt.android.domain.routes.Route;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.ui.utils.TextFormatter;
import com.stt.android.workoutsettings.WorkoutSettingsActivity;
import java.util.Locale;

public class SelectedFollowCardHolder extends FeedViewHolder<SelectedFollowCard> {

    TextView routeTitle;
    ImageView clearRouteBt;

    final TargetWorkoutSelectionPresenter targetWorkoutSelectionPresenter;

    public SelectedFollowCardHolder(LayoutInflater inflater, ViewGroup parent,
                                    WorkoutSettingsActivity activity) {
        super(inflater.inflate(R.layout.selected_follow_card, parent, false));
        routeTitle = itemView.findViewById(R.id.routeTitle);
        clearRouteBt = itemView.findViewById(R.id.clearRoute);
        targetWorkoutSelectionPresenter = activity.getPresenter();
    }

    @Override
    public void bind(SelectedFollowCard feedCard, int mapCacheWidth, int mapCacheHeight) {
        WorkoutHeader followWorkoutHeader = feedCard.getWorkoutHeader();
        Route followRoute = feedCard.getRoute();
        if (followWorkoutHeader != null) {
            String follow = itemView.getResources().getString(R.string.follow_route);
            routeTitle.setText(String.format(Locale.getDefault(), "%s (%s)", follow,
                TextFormatter.formatDate(itemView.getContext(), followWorkoutHeader.getStartTime())));
        } else if (followRoute != null) {
            routeTitle.setText(followRoute.getName());
        }
        clearRouteBt.setOnClickListener(this::clearRoute);
    }

    public void clearRoute(@SuppressWarnings("unused") View view) {
        targetWorkoutSelectionPresenter.clearSelectedTarget();
    }
}
