package com.stt.android.workoutsettings.follow

import android.app.Activity
import android.view.LayoutInflater
import android.view.ViewGroup
import com.stt.android.cardlist.FeedCard
import com.stt.android.cardlist.FeedViewHolder
import com.stt.android.controllers.CurrentUserController
import com.stt.android.home.explore.routes.FeedCardHolderFactory
import com.stt.android.home.explore.routes.RoutePlannerNavigator
import javax.inject.Inject

class NoRoutesCardHolderFactory
@Inject constructor(
    private val currentUserController: CurrentUserController,
    private val routePlannerNavigator: RoutePlannerNavigator
) : FeedCardHolderFactory {
    override fun create(
        inflater: LayoutInflater,
        parent: ViewGroup,
        activity: Activity
    ): FeedViewHolder<out FeedCard> {
        return NoRoutesCardHolder(inflater, parent, currentUserController, routePlannerNavigator)
    }
}
