package com.stt.android.workoutsettings.activitytype

import androidx.annotation.StringRes
import com.stt.android.R
import com.stt.android.activityTypeSelectionListHeader
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.ViewStateEpoxyController
import com.stt.android.domain.workout.ActivityType
import com.stt.android.workoutsettings.workoutSettingImageItem
import javax.inject.Inject

class ActivityTypeSelectionListEpoxyController @Inject constructor() :
    ViewStateEpoxyController<ActivityTypeSelectionListData?>() {

    override fun buildModels(viewState: ViewState<ActivityTypeSelectionListData?>) {
        super.buildModels(viewState)

        val data = viewState.data ?: return

        buildActivityTypeGroup(
            "recent",
            R.string.latest,
            data.recentActivities,
            data.selectedActivityType,
            data.onActivityTypeClicked
        )
        buildActivityTypeGroup(
            "popular",
            R.string.popular,
            data.popularActivities,
            data.selectedActivityType,
            data.onActivityTypeClicked
        )
        buildActivityTypeGroup(
            "all",
            R.string.goal_summary_all_activities,
            data.allActivitiesSortedByLocalName,
            data.selectedActivityType,
            data.onActivityTypeClicked
        )
    }

    private fun buildActivityTypeGroup(
        groupId: String,
        @StringRes groupTitle: Int,
        activityTypes: List<ActivityType>,
        selectedActivityType: ActivityType?,
        onActivityTypeClicked: (ActivityType) -> Unit
    ) {
        activityTypeSelectionListHeader {
            id(groupId)
            titleTextRes(groupTitle)
        }
        activityTypes.forEach { activityType ->
            workoutSettingImageItem {
                id("$groupId ${activityType.id}")
                title(activityType.localizedStringId)
                selectedItem(activityType == selectedActivityType)
                image(activityType.iconId)
                onItemClicked { _ ->
                    onActivityTypeClicked(activityType)
                }
            }
        }
    }
}
