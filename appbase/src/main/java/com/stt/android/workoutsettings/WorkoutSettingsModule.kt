package com.stt.android.workoutsettings

import com.stt.android.common.viewstate.ViewStateEpoxyController
import com.stt.android.workoutsettings.activitytype.ActivityTypeSelectionListData
import com.stt.android.workoutsettings.activitytype.ActivityTypeSelectionListEpoxyController
import com.stt.android.workoutsettings.autopause.AutoPauseSelectionListData
import com.stt.android.workoutsettings.autopause.AutoPauseSelectionListEpoxyController
import dagger.Binds
import dagger.Module

@Module
abstract class WorkoutSettingsModule {
    @Binds
    abstract fun bindActivityTypeSelectionListController(
        controller: ActivityTypeSelectionListEpoxyController
    ): ViewStateEpoxyController<ActivityTypeSelectionListData?>

    @Binds
    abstract fun bindAutoPauseSelectionListController(
        controller: AutoPauseSelectionListEpoxyController
    ): ViewStateEpoxyController<AutoPauseSelectionListData?>
}
