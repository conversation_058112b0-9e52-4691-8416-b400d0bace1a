package com.stt.android.workoutsettings.follow;

import android.annotation.SuppressLint;
import android.content.Context;
import android.location.Location;
import com.stt.android.location.LocationModel;
import com.stt.android.maps.location.SuuntoLocationSource;
import com.stt.android.presenters.BasePresenter;
import com.stt.android.utils.PermissionUtils;
import com.stt.android.views.MVPView;
import kotlin.Unit;
import pub.devrel.easypermissions.EasyPermissions;
import timber.log.Timber;

public abstract class BaseLocationPresenter<T extends MVPView> extends BasePresenter<T> {

    private final SuuntoLocationSource suuntoLocationSource;

    private final Context appContext;

    public BaseLocationPresenter(Context appContext, SuuntoLocationSource suuntoLocationSource) {
        this.appContext = appContext;
        this.suuntoLocationSource = suuntoLocationSource;
    }

    public void refresh() {
        if (LocationModel.isGPSEnabled(appContext)) {
            refreshLocation();
        } else {
            gpsNotEnabled();
        }
    }

    @SuppressLint("MissingPermission")
    private void refreshLocation() {
        if (!EasyPermissions.hasPermissions(appContext, PermissionUtils.LOCATION_PERMISSIONS)) {
            Timber.w("User has not granted location permission");
            locationPermissionMissing();
            return;
        }
        suuntoLocationSource.getLastKnownLocation(
            location -> {
                onLocationChanged(location);
                return Unit.INSTANCE;
            },
            error -> {
                locationNotAvailable();
                return Unit.INSTANCE;
            }
        );
    }

    protected abstract void gpsNotEnabled();

    protected abstract void locationPermissionMissing();

    protected abstract void locationNotAvailable();

    protected abstract void onLocationChanged(Location location);

}
