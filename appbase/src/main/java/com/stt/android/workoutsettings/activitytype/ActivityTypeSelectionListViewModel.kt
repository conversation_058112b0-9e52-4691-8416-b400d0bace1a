package com.stt.android.workoutsettings.activitytype

import android.content.Context
import android.content.SharedPreferences
import androidx.lifecycle.LiveData
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.viewstate.LoadingStateViewModel
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.user.ActivityTypeHelper
import com.stt.android.domain.workout.ActivityType
import com.stt.android.ui.utils.SingleLiveEvent
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import javax.inject.Inject

@HiltViewModel
class ActivityTypeSelectionListViewModel @Inject constructor(
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    coroutinesDispatchers: CoroutinesDispatchers,
    @FeatureTogglePreferences val featureTogglePreferences: SharedPreferences,
) : LoadingStateViewModel<ActivityTypeSelectionListData>(
    ioThread,
    mainThread,
    coroutinesDispatchers
) {

    override fun retryLoading() {
        /* no-op */
    }

    fun loadData(context: Context) {
        notifyLoading()

        val resources = context.resources
        val data = ActivityTypeSelectionListData(
            recentActivities = ActivityTypeHelper.getRecentActivities(context).toList(),
            popularActivities = ActivityType.getPopularActivitiesSortedByLocalNames(resources)
                .toList(),
            allActivitiesSortedByLocalName = ActivityType.getAllActivitiesSortedByLocalNames(
                resources
            ).toList(),
            selectedActivityType = ActivityTypeHelper.getLastActivity(context),
            onActivityTypeClicked = ::onActivityTypeClicked
        )

        notifyDataLoaded(data)
    }

    val onActivityTypeSelected: LiveData<ActivityType> get() = _onActivityTypeSelected
    private val _onActivityTypeSelected = SingleLiveEvent<ActivityType>()

    private fun onActivityTypeClicked(activityType: ActivityType) {
        _onActivityTypeSelected.value = activityType
    }
}
