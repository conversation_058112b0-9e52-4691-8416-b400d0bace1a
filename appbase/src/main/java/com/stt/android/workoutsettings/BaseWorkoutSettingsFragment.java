package com.stt.android.workoutsettings;

import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.Uri;
import android.os.Bundle;
import android.speech.tts.TextToSpeech;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.preference.PreferenceManager;
import com.github.xizzhu.simpletooltip.ToolTipView;
import com.stt.android.FeatureFlags;
import com.stt.android.R;
import com.stt.android.analytics.EmarsysAnalytics;
import com.stt.android.controllers.CurrentUserController;
import com.stt.android.controllers.UserSettingsController;
import com.stt.android.controllers.WorkoutHeaderController;
import com.stt.android.databinding.WorkoutSettingsFragmentBinding;
import com.stt.android.domain.routes.Route;
import com.stt.android.domain.user.ActivityTypeHelper;
import com.stt.android.domain.user.MeasurementUnit;
import com.stt.android.domain.user.VoiceFeedbackSettingsHelper;
import com.stt.android.domain.workout.ActivityType;
import com.stt.android.domain.workout.AutoPause;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.ui.utils.DialogHelper;
import com.stt.android.ui.utils.TextFormatter;
import com.stt.android.ui.utils.ToolTipHelper;
import com.stt.android.utils.LocationPermissionsKt;
import com.stt.android.utils.StartActivityHelper;
import com.stt.android.workouts.RecordWorkoutService;
import com.stt.android.workouts.tts.TextToSpeechHelper;
import dagger.hilt.android.AndroidEntryPoint;
import io.reactivex.Single;
import io.reactivex.disposables.CompositeDisposable;
import java.util.Locale;
import javax.inject.Inject;
import timber.log.Timber;

@AndroidEntryPoint
public abstract class BaseWorkoutSettingsFragment extends Fragment
    implements TextToSpeech.OnInitListener, SharedPreferences.OnSharedPreferenceChangeListener,
    View.OnClickListener {
    public static final String FRAGMENT_TAG =
        "com.stt.android.workoutsettings.WorkoutSettingsFragment.FRAGMENT_TAG";

    protected WorkoutSettingsFragmentBinding binding;
    ToolTipView toolTipView;

    @Inject
    FeatureFlags featureFlags;

    @Inject
    EmarsysAnalytics emarsysAnalytics;

    @Inject
    WorkoutHeaderController workoutHeaderController;

    @Inject
    protected CurrentUserController currentUserController;

    @Inject
    protected UserSettingsController userSettingsController;

    WorkoutSettingsListener listener;
    private WorkoutTargetProvider provider;
    private TextToSpeech textToSpeech;
    private CompositeDisposable disposable = new CompositeDisposable();

    /**
     * Flag to avoid asking again to install TTS if the user cancelled it
     */
    private volatile boolean alreadyAskedTTS = false;

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        if (!(context instanceof WorkoutSettingsListener)
            || !(context instanceof WorkoutTargetProvider)) {
            throw new IllegalArgumentException("Activity '"
                + context
                + "' must implement WorkoutSettingsListener and WorkoutTargetProvider");
        }

        listener = (WorkoutSettingsListener) context;
        provider = (WorkoutTargetProvider) context;
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
        Bundle savedInstanceState) {
        binding = WorkoutSettingsFragmentBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        setCurrentActivityType();
        setCurrentActivityAutoPauseOrDisable();
        setActionListeners();
        showToolTipsIfNeeded();

        Context context = getActivity();
        if (LocationPermissionsKt.isForegroundLocationPermissionGranted(context)) {
            ContextCompat.startForegroundService(
                context,
                RecordWorkoutService.newPrepareWorkoutIntent(context,
                    ActivityTypeHelper.getLastActivity(context))
            );
        }
        binding.selectGhostTargetButton.setSelection(R.string.none);
        binding.selectFollowRouteButton.setSelection(R.string.none);
    }

    private void setActionListeners() {
        binding.activityTypeSelectionButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                listener.onActivityTypeSelectionClicked();

                if (toolTipView != null) {
                    toolTipView.remove();
                    toolTipView = null;
                }
            }
        });

        binding.selectFollowRouteButton.setOnClickListener(this);
        binding.selectGhostTargetButton.setOnClickListener(this);

        binding.voiceFeedbackButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                listener.onVoiceFeedbackSettingClicked();

                if (toolTipView != null) {
                    toolTipView.remove();
                    toolTipView = null;
                }
            }
        });

        binding.autoPauseButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                BaseWorkoutSettingsFragment.this.listener.onAutoPauseSelectionClicked();
            }
        });

        binding.continueBt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // Disable clicks to avoid multiple clicks in a row. It will be re-enabled onResume
                binding.continueBt.setClickable(false);
                // Notify that the user has confirmed the settings
                ActivityType activityType = ActivityTypeHelper.getLastActivity(getActivity());
                BaseWorkoutSettingsFragment.this.listener.onSettingsReady(activityType);
            }
        });
    }

    @Override
    public void onClick(View v) {
        if (v == binding.selectGhostTargetButton) {
            listener.onSelectGhostTargetClicked();
        } else if (v == binding.selectFollowRouteButton) {
            listener.onSelectFollowRouteClicked();
        }
    }

    @Override
    public void onStart() {
        super.onStart();

        refreshUi();

        // When the selected activity type is changed from Wear devices, it sends a message to
        // WearableListener, which saves the activity type into shared preferences. The other (and
        // more proper) way to monitor this, is to add a listener through DataApi, but we need to
        // check what would happen for users that don't have latest Play Services installed.
        PreferenceManager.getDefaultSharedPreferences(getActivity())
            .registerOnSharedPreferenceChangeListener(this);
    }

    protected void refreshUi() {
        // Check for voice feedback support if enabled
        if (isVoiceFeedbackEnabled() && textToSpeech == null && !alreadyAskedTTS) {
            Timber.d("Starting WorkoutSettingsActivity TTS engine");
            // Start the TTS with default engine. See onInit() for more details
            textToSpeech = new TextToSpeech(requireActivity().getApplicationContext(), this);
        }

        setCurrentActivityType();
        setCurrentActivityAutoPauseOrDisable();
        setWorkoutTargetSelection();
        setCurrentVoiceFeedback();
    }

    private boolean isVoiceFeedbackEnabled() {
        Context context = getActivity();
        return VoiceFeedbackSettingsHelper.getVoiceFeedbackSettings(context,
            ActivityTypeHelper.getLastActivity(context).getId()).enabled;
    }

    private void setWorkoutTargetSelection() {
        ActivityType activityType = ActivityTypeHelper.getLastActivity(getActivity());
        if (activityType.isIndoor()) {
            binding.selectFollowRouteButton.setVisibility(View.GONE);
            binding.selectGhostTargetButton.setVisibility(View.GONE);
            binding.autoPauseButtonDivider.setVisibility(View.GONE);
            binding.selectGhostTargetButtonDivider.setVisibility(View.GONE);
            binding.selectFollowRouteButtonDivider.setVisibility(View.GONE);
            return;
        }

        binding.selectFollowRouteButton.setVisibility(View.VISIBLE);
        binding.selectGhostTargetButton.setVisibility(activityType.isSlopeSki() ? View.GONE : View.VISIBLE);

        // only one of these should exists
        WorkoutHeader workoutTarget = provider.getFollowWorkoutTarget();
        WorkoutHeader ghostTarget = provider.getGhostTarget();
        Single<Route> routeTargetObservable = provider.getFollowRouteTarget();
        final MeasurementUnit unit = userSettingsController.getSettings().getMeasurementUnit();
        if (workoutTarget != null) {
            binding.selectFollowRouteButton.setSelection(
                String.format(Locale.getDefault(), "%s (%s)", getString(R.string.follow_route),
                    TextFormatter.formatRelativeDateSpan(getResources(),
                        workoutTarget.getStartTime())));
            binding.selectGhostTargetButton.setSelection(R.string.none);
        } else if (ghostTarget != null) {
            binding.selectGhostTargetButton.setSelection(
                String.format(Locale.getDefault(), "%s (%s)", getString(R.string.ghost_target),
                    TextFormatter.formatRelativeDateSpan(getResources(),
                        ghostTarget.getStartTime())));
            binding.selectFollowRouteButton.setSelection(R.string.none);
        } else if (routeTargetObservable != null) {
            disposable.add(routeTargetObservable.subscribe(route -> {
                binding.selectFollowRouteButton.setSelection(
                        String.format(Locale.getDefault(), "%s (%s%s)", route.getName(), TextFormatter.formatDistance(
                            unit.toDistanceUnit(route.getTotalDistance())),
                            getString(unit.getDistanceUnit())));
                }, error -> Timber.w(error, "Failed to load route")));
            binding.selectGhostTargetButton.setSelection(R.string.none);
        } else {
            binding.selectGhostTargetButton.setSelection(R.string.none);
            binding.selectFollowRouteButton.setSelection(R.string.none);
        }
    }

    @Override
    public void onResume() {
        super.onResume();

        ActionBar actionBar = null;
        Activity activity = getActivity();
        if (activity instanceof AppCompatActivity) {
            actionBar = ((AppCompatActivity) activity).getSupportActionBar();
        }
        if (actionBar != null) {
            actionBar.setTitle(R.string.new_workout_settings);
            actionBar.setDisplayHomeAsUpEnabled(true);
        }

        // Make sure that we restore the continue button state
        binding.continueBt.setClickable(true);
        setCurrentVoiceFeedback();
    }

    private void setCurrentVoiceFeedback() {
        if (ActivityTypeHelper.getLastActivity(getActivity()).isSlopeSki()) {
            binding.voiceFeedbackButton.setVisibility(View.GONE);
        } else {
            binding.voiceFeedbackButton.setVisibility(View.VISIBLE);
            binding.voiceFeedbackButton.setSelection(
                isVoiceFeedbackEnabled() ? R.string.on : R.string.off);
        }
    }

    @Override
    public void onStop() {
        PreferenceManager.getDefaultSharedPreferences(getActivity())
            .unregisterOnSharedPreferenceChangeListener(this);

        if (textToSpeech != null) {
            textToSpeech.shutdown();
            textToSpeech = null;
        }

        super.onStop();
    }

    /**
     * Updates the UI to show the selected activity type
     */
    private void setCurrentActivityType() {
        ActivityType activityType = ActivityTypeHelper.getLastActivity(getActivity());
        binding.activityTypeSelectionButton.setSelection(activityType.getLocalizedName(getResources()));
        binding.activityTypeSelectionButton.setImage(activityType.getIconId());
    }

    private void setCurrentActivityAutoPauseOrDisable() {
        ActivityType activityType = ActivityTypeHelper.getLastActivity(getActivity());
        if (activityType.isIndoor() || activityType.isSlopeSki()) {
            binding.autoPauseButton.setVisibility(View.GONE);
            binding.autoPauseButtonDivider.setVisibility(View.GONE);
        } else {
            binding.autoPauseButton.setVisibility(View.VISIBLE);


            AutoPause lastSavedAutoPauseFromRecordWorkout =
                ActivityTypeHelper.getAutoPause(requireContext(), activityType);
            AutoPause[] autoPauses =
                userSettingsController.getSettings().getMeasurementUnit().getAutoPauseValues();
            // In case user left the record screen and changed the unit setting
            AutoPause selected = null;
            for (AutoPause autoPause : autoPauses) {
                if (autoPause == lastSavedAutoPauseFromRecordWorkout) {
                    selected = autoPause;
                    break;
                }
            }

            if (selected == null){
                selected = AutoPause.DEFAULT;
                ActivityTypeHelper.saveAutoPause(getActivity(), activityType, selected);
            }

            binding.autoPauseButton.setSelection(selected.getLocalizedName(getResources()));
        }
    }

    @Override
    public void onInit(int status) {
        if (textToSpeech == null) {
            // just in case some weird engine won't stop even if TextToSpeech.shutdown() is called
            return;
        }

        Timber.d("TTS Engine initialized? %d", status);
        if (status != TextToSpeech.SUCCESS) {
            return;
        }

        switch (TextToSpeechHelper.isLanguageSupported(textToSpeech,
            getString(R.string.tts_language), true)) {
            case TextToSpeech.LANG_MISSING_DATA:
                showMissingTextToSpeechEngineAlert();
                break;
            case TextToSpeech.LANG_AVAILABLE:
                // Some stupid text to speech engines (I'm looking at you Samsung!) fire up a
                // dialog after reading a text
                // asking the user to install a higher quality voice. So for a better user
                // experience we ask the engine to
                // speak " " so the dialog is triggered.
                textToSpeech.speak(" ", TextToSpeech.QUEUE_FLUSH, null);
                break;
        }
    }

    private void showMissingTextToSpeechEngineAlert() {
        alreadyAskedTTS = true;
        DialogHelper.showDialog(getActivity(), R.string.voice_feedback, R.string.tts_not_installed,
            new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    Intent installIntent = new Intent();
                    installIntent.setAction(TextToSpeech.Engine.ACTION_INSTALL_TTS_DATA);
                    startActivity(installIntent);
                }
            }, null);
    }

    @Override
    public void onSharedPreferenceChanged(SharedPreferences sharedPreferences, String key) {
        if (!isAdded()) {
            // this might be called after the fragment is detached
            // e.g. https://fabric.io/sporst-tracker/android/apps/com.stt
            // .android/issues/558ec833f505b5ccf046a361
            return;
        }
        refreshUi();
    }

    private void showToolTipsIfNeeded() {
        Context context = getActivity();
        if (!ToolTipHelper.hasShownToolTip(context, ToolTipHelper.KEY_SELECT_ACTIVITY_TOOL_TIP)) {
            toolTipView =
                ToolTipHelper.createToolTipView(context, binding.activityTypeSelectionButton, binding.toolTip,
                    R.string.tool_tip_select_activity);
            toolTipView.show();
            ToolTipHelper.markToolTipAsShown(context, ToolTipHelper.KEY_SELECT_ACTIVITY_TOOL_TIP);
        } else if (!ToolTipHelper.hasShownToolTip(context,
            ToolTipHelper.KEY_VOICE_FEEDBACK_TOOL_TIP)) {
            toolTipView =
                ToolTipHelper.createToolTipView(context, binding.voiceFeedbackButton, binding.toolTip,
                    R.string.tool_tip_custom_voice_feedback);
            toolTipView.show();
            ToolTipHelper.markToolTipAsShown(context, ToolTipHelper.KEY_VOICE_FEEDBACK_TOOL_TIP);
        }
    }

    public interface WorkoutTargetProvider {
        @Nullable
        WorkoutHeader getFollowWorkoutTarget();

        @Nullable
        WorkoutHeader getGhostTarget();

        @Nullable
        Single<Route> getFollowRouteTarget();
    }

    public interface WorkoutSettingsListener {
        void onActivityTypeSelectionClicked();

        void onSelectGhostTargetClicked();

        void onSelectFollowRouteClicked();

        void onVoiceFeedbackSettingClicked();

        void onAutoPauseSelectionClicked();

        /**
         * Called once the user has make it clear he/she wants to continue
         *
         * @param selectedActivityType the selected activity type id
         */
        void onSettingsReady(ActivityType selectedActivityType);
    }

    void showAd() {
        if (binding != null) {
            binding.bannerAd.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!StartActivityHelper.startActivityExternally(getActivity(),
                        Uri.parse(getString(R.string.workout_settings_ad_url)))) {
                        DialogHelper.showDialog(getContext(), R.string.error_0);
                    }
                }
            });
            binding.bannerAd.setVisibility(View.VISIBLE);
        }
    }

    void hideAd() {
        if (binding != null) {
            binding.bannerAd.setOnClickListener(null);
            binding.bannerAd.setVisibility(View.GONE);
        }
    }
}
