package com.stt.android.workoutsettings.follow;

import android.content.res.Resources;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import com.stt.android.R;
import com.stt.android.controllers.UserSettingsController;
import com.stt.android.domain.routes.Route;
import com.stt.android.databinding.ItemRouteBinding;
import com.stt.android.domain.user.MeasurementUnit;
import com.stt.android.mapping.InfoModelFormatter;
import com.stt.android.home.explore.routes.BaseRouteCardHolder;
import com.stt.android.ui.utils.ThrottlingOnClickListener;
import com.stt.android.workoutsettings.WorkoutSettingsActivity;

import javax.inject.Inject;

public abstract class WorkoutSelectionRouteCardHolder extends BaseRouteCardHolder {

    private final TargetWorkoutSelectionPresenter targetWorkoutSelectionPresenter;

    UserSettingsController userSettingsController;
    InfoModelFormatter infoModelFormatter;

    protected final ItemRouteBinding binding;

    public WorkoutSelectionRouteCardHolder(
        LayoutInflater inflater,
        ViewGroup parent,
        WorkoutSettingsActivity activity,
        Resources resources,
        UserSettingsController userSettingsController,
        InfoModelFormatter infoModelFormatter) {
        super(inflater.inflate(R.layout.item_route, parent, false), resources);
        binding = ItemRouteBinding.bind(itemView);
        itemView.setOnClickListener(new ThrottlingOnClickListener(v -> handleClick()));
        targetWorkoutSelectionPresenter = activity.getPresenter();
        this.userSettingsController = userSettingsController;
        this.infoModelFormatter = infoModelFormatter;
    }

    @Override
    protected MeasurementUnit getUserMeasurementUnit() {
        return userSettingsController.getSettings().getMeasurementUnit();
    }

    @Override
    protected String formatEstimatedRouteDuration(long duration) {
        return infoModelFormatter.formatEstimatedRouteDuration(duration);
    }

    private void handleClick() {
        if (routeCard == null) {
            return;
        }
        final Route route = routeCard.getRouteData();
        if (!route.getDeleted()) {
            targetWorkoutSelectionPresenter.onRouteSelected(route);
        }
    }
}
