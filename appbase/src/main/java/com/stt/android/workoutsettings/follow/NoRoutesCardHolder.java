package com.stt.android.workoutsettings.follow;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import com.stt.android.R;
import com.stt.android.cardlist.FeedCard;
import com.stt.android.cardlist.FeedViewHolder;
import com.stt.android.controllers.CurrentUserController;
import com.stt.android.home.explore.routes.RoutePlannerNavigator;

public class NoRoutesCardHolder extends FeedViewHolder implements View.OnClickListener {
    @NonNull private final CurrentUserController currentUserController;
    @NonNull private final RoutePlannerNavigator routePlannerNavigator;

    public NoRoutesCardHolder(
        @NonNull LayoutInflater inflater,
        @NonNull ViewGroup parent,
        @NonNull CurrentUserController currentUserController,
        @NonNull RoutePlannerNavigator routePlannerNavigator) {
        super(inflater.inflate(R.layout.viewholder_route_list_no_routes_card, parent, false));
        itemView.findViewById(R.id.newRouteBt).setOnClickListener(this);
        this.currentUserController = currentUserController;
        this.routePlannerNavigator = routePlannerNavigator;
    }

    @Override
    public void bind(FeedCard feedCard, int mapCacheWidth, int mapCacheHeight) {
        //nothing to do
    }

    @Override
    public void onClick(View view) {
        Context context = view.getContext();
        routePlannerNavigator.startCreateRouteActivityOrRedirectToLogin(context, currentUserController);
    }
}
