package com.stt.android.workoutsettings.follow

import com.stt.android.cardlist.FeedCard
import com.stt.android.domain.user.User
import com.stt.android.domain.workouts.WorkoutHeader
import java.util.Objects

data class FollowWorkoutCard(
    val workoutHeader: WorkoutHeader,
    val user: User,
) : FeedCard {
    override val viewType: Int = FeedCard.FOLLOW_WORKOUT_CARD

    override var id: Long = 0L
        get() = Objects.hash(workoutHeader, user).toLong()
        set(value) {
            field = value
        }

    override fun calculateDifferences(other: FeedCard?): List<Any>? = null
}
