package com.stt.android.workoutsettings

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Dialog
import android.content.DialogInterface
import android.os.Bundle
import android.widget.SeekBar
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.SwitchCompat
import androidx.fragment.app.DialogFragment
import com.stt.android.R
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.user.valueOf
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import dagger.hilt.android.AndroidEntryPoint
import java.util.Locale
import javax.inject.Inject

@AndroidEntryPoint
class VoiceFeedbackSettingDialogFragment : DialogFragment() {
    @Inject
    lateinit var infoModelFormatter: InfoModelFormatter

    @SuppressLint("InflateParams")
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        arguments?.let { bundle ->
            val title = bundle.getString(TITLE)
            val perLapFrequency = bundle.getBoolean(PER_LAP_FREQUENCY)
            val distanceFrequency = bundle.getInt(DISTANCE_FREQUENCY)
            val maxDistance = bundle.getInt(MAX_DISTANCE)
            val durationFrequency = bundle.getInt(DURATION_FREQUENCY)
            val maxDuration = bundle.getInt(MAX_DURATION)
            val measurementUnitKey = bundle.getInt(MEASUREMENT_UNIT)
            val measurementUnit = valueOf(measurementUnitKey)

            val view = layoutInflater.inflate(R.layout.voice_feedback_content_setting_dialog, null)
            val lap = view.findViewById<SwitchCompat>(R.id.voiceFeedbackLapSwitch)
            lap.isChecked = perLapFrequency

            val distanceValue = view.findViewById<TextView>(R.id.distanceValue).apply {
                text = getDistanceText(distanceFrequency, measurementUnit)
            }

            val distanceSeekBar = view.findViewById<SeekBar>(R.id.distanceSeekBar).apply {
                max = maxDistance
                progress = distanceFrequency
                setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
                    override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {
                        distanceValue.text = getDistanceText(progress, measurementUnit)
                    }

                    override fun onStartTrackingTouch(seekBar: SeekBar) {}

                    override fun onStopTrackingTouch(seekBar: SeekBar) {}
                })
            }

            val durationValue = view.findViewById<TextView>(R.id.durationValue).apply {
                text = getDurationText(durationFrequency)
            }

            val durationSeekBar = view.findViewById<SeekBar>(R.id.durationSeekBar).apply {
                max = maxDuration
                progress = <EMAIL>(durationFrequency)
                setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
                    override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {
                        durationValue.text = getDurationText(progressToDuration(progress))
                    }

                    override fun onStartTrackingTouch(seekBar: SeekBar) {}
                    override fun onStopTrackingTouch(seekBar: SeekBar) {}
                })
            }

            val builder = AlertDialog.Builder(requireContext())
                .setPositiveButton(R.string.ok) { _, _ ->
                    activity?.let {
                        val intent = it.intent
                        intent.putExtra(RESULT_LAP_ENABLED, lap.isChecked)
                        intent.putExtra(RESULT_DISTANCE_VALUE, distanceSeekBar.progress)
                        intent.putExtra(RESULT_DURATION_VALUE, progressToDuration(durationSeekBar.progress))

                        targetFragment?.onActivityResult(targetRequestCode, RESULT_POSITIVE, intent)
                    }
                }
                .setView(view)

            title?.let {
                builder.setTitle(it)
            }

            return builder.create()
        }
        throw IllegalStateException("Cannot create dialog, arguments cannot be null")
    }

    override fun onCancel(dialog: DialogInterface) {
        super.onCancel(dialog)
        activity?.let {
            targetFragment?.onActivityResult(targetRequestCode, RESULT_CANCELED, it.intent)
        }
    }

    private fun getDistanceText(
        distanceFrequency: Int,
        measurementUnit: MeasurementUnit
    ): String? {
        return if (distanceFrequency == 0) {
            context?.getString(R.string.never)
        } else {
            String.format(
                Locale.US,
                "%.2f %s",
                measurementUnit.toDistanceUnit(distanceFrequency.toDouble()),
                context?.getString(measurementUnit.distanceUnit)
            )
        }
    }

    private fun getDurationText(durationFrequency: Int): String? = if (durationFrequency == 0) {
        context?.getString(R.string.never)
    } else {
        infoModelFormatter.formatValue(SummaryItem.DURATION, durationFrequency).value
    }

    private fun durationToProgress(duration: Int): Int {
        return when {
            duration <= 60 -> duration / 10
            duration <= 300 -> duration / 30 + 4
            duration <= 600 -> duration / 60 + 9
            duration <= 1800 -> duration / 300 + 17
            else -> 24
        }
    }

    private fun progressToDuration(progress: Int): Int {
        return when {
            progress <= 6 -> progress * 10
            progress <= 14 -> (progress - 4) * 30
            progress <= 19 -> (progress - 9) * 60
            progress <= 23 -> (progress - 17) * 300
            else -> 3600
        }
    }

    companion object {
        private const val TITLE = "title"
        private const val PER_LAP_FREQUENCY = "perLapFrequency"
        private const val DISTANCE_FREQUENCY = "distanceFrequency"
        private const val MAX_DISTANCE = "maxDistance"
        private const val DURATION_FREQUENCY = "durationFrequency"
        private const val MAX_DURATION = "maxDuration"
        private const val MEASUREMENT_UNIT = "measurementUnit"
        const val RESULT_POSITIVE: Int = Activity.RESULT_OK // -1
        const val RESULT_CANCELED: Int = Activity.RESULT_CANCELED // 0
        const val RESULT_LAP_ENABLED: String = "lapEnabled"
        const val RESULT_DURATION_VALUE: String = "durationValue"
        const val RESULT_DISTANCE_VALUE: String = "distanceValue"

        @JvmStatic
        @JvmOverloads
        fun newInstance(
            title: String? = null,
            perLapFrequency: Boolean,
            distanceFrequency: Int,
            maxDistance: Int,
            durationFrequency: Int,
            maxDuration: Int,
            measurementUnit: MeasurementUnit
        ): VoiceFeedbackSettingDialogFragment {
            val dialogFragment = VoiceFeedbackSettingDialogFragment()
            dialogFragment.arguments = Bundle().apply {
                putString(TITLE, title)
                putBoolean(PER_LAP_FREQUENCY, perLapFrequency)
                putInt(DISTANCE_FREQUENCY, distanceFrequency)
                putInt(MAX_DISTANCE, maxDistance)
                putInt(DURATION_FREQUENCY, durationFrequency)
                putInt(MAX_DURATION, maxDuration)
                putInt(MEASUREMENT_UNIT, measurementUnit.key)
            }

            return dialogFragment
        }
    }
}
