package com.stt.android.videoencode

import android.graphics.Bitmap
import android.media.MediaCodec
import android.media.MediaCodecInfo.CodecCapabilities
import android.media.MediaFormat
import android.media.MediaMuxer
import timber.log.Timber
import java.io.IOException

class MP4Encoder : Encoder() {
    private var addedFrameCount = 0
    private var bufferInfo = MediaCodec.BufferInfo()
    private var encodedFrameCount = 0
    private var isMuxerStarted = false
    private var isStarted = false
    private var mediaMuxer: MediaMuxer? = null
    private var trackCount = 0
    private var videoCodec: MediaCodec =
        MediaCodec.createEncoderByType(MediaFormat.MIMETYPE_VIDEO_AVC)
    private var videoTrackIndex = 0

    override fun onStart() {
        isStarted = true
        addedFrameCount = 0
        encodedFrameCount = 0
        try {
            bufferInfo = MediaCodec.BufferInfo()
            videoCodec = MediaCodec.createEncoderByType(MediaFormat.MIMETYPE_VIDEO_AVC)
            val widthAndHeight = getWH()
            val videoFormat =
                MediaFormat.createVideoFormat(
                    MediaFormat.MIMETYPE_VIDEO_AVC,
                    widthAndHeight.width,
                    widthAndHeight.height
                )
            videoFormat.setInteger(MediaFormat.KEY_BIT_RATE, BIT_RATE)
            videoFormat.setInteger(MediaFormat.KEY_FRAME_RATE, FRAME_RATE)
            videoFormat.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, I_FRAME_INTERVAL)
            videoFormat.setInteger(
                MediaFormat.KEY_COLOR_FORMAT,
                CodecCapabilities.COLOR_FormatYUV420Flexible
            )
            videoCodec.configure(videoFormat, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE)
            videoCodec.start()
            mediaMuxer = MediaMuxer(outputFilePath, MediaMuxer.OutputFormat.MUXER_OUTPUT_MPEG_4)
        } catch (ioe: IOException) {
            throw RuntimeException("MediaMuxer creation failed", ioe)
        }
    }

    override fun onStop() {
        if (isStarted) {
            encodeVideo()
            if (this.addedFrameCount > 0) {
                videoCodec.stop()
                videoCodec.release()
                mediaMuxer?.stop()
                mediaMuxer?.release()
            } else {
                Timber.w("don't added any frame")
            }
            isStarted = false
        }
    }

    override fun onAddFrame(bitmap: Bitmap?) {
        if (isStarted && bitmap != null) {
            val inputBufIndex = videoCodec.dequeueInputBuffer(TIMEOUT_US.toLong())
            if (inputBufIndex >= 0) {
                val input = getNV12(bitmap.width, bitmap.height, bitmap)
                val inputBuffer = videoCodec.getInputBuffer(inputBufIndex)
                if (inputBuffer != null) {
                    inputBuffer.clear()
                    inputBuffer.put(input)
                    videoCodec.queueInputBuffer(
                        inputBufIndex, 0, input.size,
                        getPresentationTimeUsec(addedFrameCount), 0
                    )
                }
                addedFrameCount++
            }
            while (addedFrameCount > encodedFrameCount) {
                encodeVideo()
            }
        } else {
            Timber.w("add frame failed: $isStarted $bitmap")
        }
    }

    private fun encodeVideo() {
        val encoderStatus = videoCodec.dequeueOutputBuffer(bufferInfo, TIMEOUT_US.toLong())
        Timber.d(
            ("Video encoderStatus = " + encoderStatus + ", presentationTimeUs = "
                + bufferInfo.presentationTimeUs)
        )
        if (encoderStatus == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED) {
            val videoFormat = videoCodec.outputFormat
            Timber.d("output format changed. video format: %s", videoFormat.toString())
            videoTrackIndex = mediaMuxer?.addTrack(videoFormat) ?: 0
            trackCount++
            if (trackCount == 1) {
                Timber.i("started media muxer.")
                mediaMuxer?.start()
                isMuxerStarted = true
            }
        } else if (encoderStatus == MediaCodec.INFO_TRY_AGAIN_LATER) {
            Timber.d("no output from video encoder available")
        } else {
            val encodedData = videoCodec.getOutputBuffer(encoderStatus)
            if (encodedData != null) {
                encodedData.position(bufferInfo.offset)
                encodedData.limit(bufferInfo.offset + bufferInfo.size)
                if (isMuxerStarted) {
                    mediaMuxer?.writeSampleData(videoTrackIndex, encodedData, bufferInfo)
                }
                videoCodec.releaseOutputBuffer(encoderStatus, false)
                encodedFrameCount++
            }
            Timber.d("encoderOutputBuffer $encoderStatus was null")
        }
    }

    private fun getNV12(inputWidth: Int, inputHeight: Int, scaled: Bitmap): ByteArray {
        val argb = IntArray((inputWidth * inputHeight))
        scaled.getPixels(argb, 0, inputWidth, 0, 0, inputWidth, inputHeight)
        val yuv = ByteArray((((inputWidth * inputHeight) * 3) / 2))
        encodeYUV420SP(yuv, argb, inputWidth, inputHeight)
        scaled.recycle()
        return yuv
    }

    private fun encodeYUV420SP(yuv420sp: ByteArray, argb: IntArray, width: Int, height: Int) {
        var yIndex = 0
        var uvIndex = width * height
        var index = 0
        var j = 0
        while (j < height) {
            var uvIndex2: Int
            var yIndex2: Int
            var i = 0
            while (true) {
                uvIndex2 = uvIndex
                yIndex2 = yIndex
                if (i >= width) {
                    break
                }
                val R = (argb[index] and 0xFF0000) shr 16
                val G = (argb[index] and 0xFF00) shr 8
                val B = argb[index] and 0x0000FF
                val Y = ((((R * 77) + (G * 150)) + (B * 29)) + 128) shr 8
                val V = (((((R * -43) - (G * 84)) + (B * 127)) + 128) shr 8) + 128
                val U = (((((R * 127) - (G * 106)) - (B * 21)) + 128) shr 8) + 128
                yIndex = yIndex2 + 1
                yuv420sp[yIndex2] = Y.toByte()
                if (j % 2 == 0 && index % 2 == 0) {
                    uvIndex = uvIndex2 + 1
                    yuv420sp[uvIndex2] = V.toByte()
                    uvIndex2 = uvIndex + 1
                    yuv420sp[uvIndex] = U.toByte()
                }
                uvIndex = uvIndex2
                index++
                i++
            }
            j++
        }
    }

    companion object {
        private const val BIT_RATE = 2000000
        private const val FRAME_RATE = 30
        private const val I_FRAME_INTERVAL = 5
        private const val ONE_SEC: Long = 1000000
        private const val TIMEOUT_US = 10000
        private fun getPresentationTimeUsec(frameIndex: Int): Long {
            return ((frameIndex.toLong()) * ONE_SEC) / 20
        }
    }
}
