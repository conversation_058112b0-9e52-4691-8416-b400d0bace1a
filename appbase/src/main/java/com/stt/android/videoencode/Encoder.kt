package com.stt.android.videoencode

import android.graphics.Bitmap
import android.graphics.Rect
import android.util.Size
import timber.log.Timber
import java.util.Collections
import kotlin.math.roundToInt

abstract class Encoder {
    private var bitmapQueue: MutableList<Bitmap> = Collections.synchronizedList(ArrayList())
    private var encodeFinishListener: EncodeFinishListener? = null
    private var encodingThread: Thread? = null
    protected var outputFilePath = ""
    private var state = STATE_IDLE
    private var outputSizeRect: Rect = Rect()

    /**
     * limit the maximum width to 1080
     * make the width/height become 2's multiple
     */
    fun getWH(): Size {
        val width = outputSizeRect.width()
        val zoomFactor = if (width > 1080) width / 1080f else 1f
        return Size(
            (width / zoomFactor).roundToInt() / 2 * 2,
            (outputSizeRect.height() / zoomFactor).roundToInt() / 2 * 2
        )
    }

    private val mRunnableEncoder = Runnable {
        while (true) {
            if (state != STATE_RECORDING && bitmapQueue.isEmpty()) {
                break
            } else if (bitmapQueue.isNotEmpty()) {
                var bitmap: Bitmap? = null
                try {
                    bitmap = bitmapQueue.removeAt(0)
                } catch (e: IndexOutOfBoundsException) {
                    Timber.e(e)
                }
                if (bitmap != null) {
                    try {
                        onAddFrame(bitmap)
                    } catch (e: Exception) {
                        Timber.e(e)
                    }
                    bitmap.recycle()
                }
                if (state == STATE_RECORDING_UNTIL_LAST_FRAME && bitmapQueue.isEmpty()) {
                    Timber.d("Last frame added")
                    break
                }
            }
        }
        Timber.d("add Frame finished")
        onStop()
        notifyEncodeFinish()
    }

    fun init(outputFilePath: String, outputSizeRect: Rect) {
        this.outputFilePath = outputFilePath
        this.outputSizeRect = outputSizeRect
    }

    fun getOutputSize() = outputSizeRect

    fun startEncode() {
        bitmapQueue.clear()
        onStart()
        setState(STATE_RECORDING)
        encodingThread = Thread(mRunnableEncoder)
        encodingThread?.name = "EncodeThread"
        encodingThread?.start()
    }

    private fun notifyEncodeFinish() {
        encodingThread?.interrupt()
        encodeFinishListener?.onEncodeFinished()
    }

    fun stopEncode() {
        notifyLastFrameAdded()
        setState(STATE_IDLE)
    }

    fun addFrame(bitmap: Bitmap) {
        if (state == STATE_RECORDING) {
            bitmapQueue.add(bitmap)
        }
    }

    fun setEncodeFinishListener(listener: EncodeFinishListener) {
        encodeFinishListener = listener
    }

    /**
     * Reserved for gif encoder
     */
    private fun notifyLastFrameAdded() {
        setState(STATE_RECORDING_UNTIL_LAST_FRAME)
    }

    private fun setState(state: Int) {
        this.state = state
    }

    protected abstract fun onAddFrame(bitmap: Bitmap?)

    protected abstract fun onStart()

    protected abstract fun onStop()

    companion object {
        private const val STATE_IDLE: Int = 0
        private const val STATE_RECORDING: Int = 1
        private const val STATE_RECORDING_UNTIL_LAST_FRAME: Int = 2
    }
}

interface EncodeFinishListener {
    fun onEncodeFinished()
}
