package com.stt.android.videoencode

import android.annotation.SuppressLint
import android.content.res.AssetFileDescriptor
import android.media.MediaCodec
import android.media.MediaExtractor
import android.media.MediaFormat
import android.media.MediaMuxer
import androidx.annotation.WorkerThread
import java.nio.ByteBuffer

object MergeAudioAndVideo {
    private const val VIDEO_MIMETYPE = "video/"
    private const val AUDIO_MIMETYPE = "audio/"

    @WorkerThread
    @SuppressLint("WrongConstant")
    fun merge(videoPath: String, audioFileDescriptor: AssetFileDescriptor, outputPath: String) {
        val videoExtractor = MediaExtractor()
        videoExtractor.setDataSource(videoPath)
        val videoTrackIndex =
            findTrackIndex(videoExtractor, VIDEO_MIMETYPE) ?: throw Exception("no video track")
        videoExtractor.selectTrack(videoTrackIndex.first)
        val audioExtractor = MediaExtractor()
        audioExtractor.setDataSource(audioFileDescriptor)
        val audioTrackIndex =
            findTrackIndex(audioExtractor, AUDIO_MIMETYPE) ?: throw Exception("no audio track")
        audioExtractor.selectTrack(audioTrackIndex.first)
        val mediaMuxer = MediaMuxer(outputPath, MediaMuxer.OutputFormat.MUXER_OUTPUT_MPEG_4)
        val writeVideoTrackIndex = mediaMuxer.addTrack(videoTrackIndex.second)
        val writeAudioTrackIndex = mediaMuxer.addTrack(audioTrackIndex.second)
        mediaMuxer.start()
        val videoBufferInfo = MediaCodec.BufferInfo()
        val byteBuffer = ByteBuffer.allocate(500 * 1024)
        while (true) {
            val readVideoSampleSize = videoExtractor.readSampleData(byteBuffer, 0)
            if (readVideoSampleSize < 0) {
                break
            }

            videoBufferInfo.size = readVideoSampleSize
            videoBufferInfo.presentationTimeUs = videoExtractor.sampleTime
            videoBufferInfo.offset = 0
            videoBufferInfo.flags = videoExtractor.sampleFlags
            mediaMuxer.writeSampleData(writeVideoTrackIndex, byteBuffer, videoBufferInfo)
            videoExtractor.advance()
        }
        val audioBufferInfo = MediaCodec.BufferInfo()
        while (true) {
            val readAudioSampleSize = audioExtractor.readSampleData(byteBuffer, 0)
            if (readAudioSampleSize < 0) {
                if (audioBufferInfo.presentationTimeUs < videoBufferInfo.presentationTimeUs) {
                    audioExtractor.unselectTrack(audioTrackIndex.first)
                    audioExtractor.selectTrack(audioTrackIndex.first)
                    continue
                } else
                    break
            }

            audioBufferInfo.size = readAudioSampleSize
            audioBufferInfo.presentationTimeUs = audioExtractor.sampleTime
            audioBufferInfo.offset = 0
            audioBufferInfo.flags = audioExtractor.sampleFlags
            if (audioBufferInfo.presentationTimeUs > videoBufferInfo.presentationTimeUs) {
                break
            }
            mediaMuxer.writeSampleData(writeAudioTrackIndex, byteBuffer, audioBufferInfo)
            audioExtractor.advance()
        }
        mediaMuxer.stop()
        mediaMuxer.release()
        videoExtractor.release()
        audioExtractor.release()
    }

    private fun findTrackIndex(
        mediaExtractor: MediaExtractor,
        srcMimeType: String
    ): Pair<Int, MediaFormat>? {
        val trackCount = mediaExtractor.trackCount
        for (index in 0..trackCount) {
            val trackFormat = mediaExtractor.getTrackFormat(index)
            val mimeType = trackFormat.getString(MediaFormat.KEY_MIME)
            if (mimeType?.startsWith(srcMimeType) == true) return index to trackFormat
        }
        return null
    }
}
