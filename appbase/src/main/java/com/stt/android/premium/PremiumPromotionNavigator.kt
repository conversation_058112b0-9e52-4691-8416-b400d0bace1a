package com.stt.android.premium

import android.content.Context
import android.content.Intent
import androidx.fragment.app.FragmentManager
import javax.inject.Inject

interface PremiumPromotionNavigator {
    fun newPremiumFeaturePromotionIntent(
        context: Context,
        analyticsSource: String,
        buyPremiumPopupShownAnalyticsReason: String? = null
    ): Intent?

    fun navigateToPremiumFeaturePromotion(
        context: Context,
        analyticsSource: String,
        buyPremiumPopupShownAnalyticsReason: String? = null
    )

    fun newPurchaseSubscriptionIntent(
        context: Context,
        analyticsSource: String,
        buyPremiumPopupShownAnalyticsReason: String? = null
    ): Intent?

    fun navigateToPurchaseSubscription(
        context: Context,
        analyticsSource: String,
        buyPremiumPopupShownAnalyticsReason: String? = null
    )

    fun newInDepthPremiumDescriptionIntent(
        context: Context,
        analyticsSource: String,
        buyPremiumPopupShownAnalyticsReason: String? = null
    ): Intent?

    fun navigateToInDepthPremiumDescription(
        context: Context,
        analyticsSource: String,
        buyPremiumPopupShownAnalyticsReason: String? = null
    )

    fun openWorkoutPlaybackPromotionDialog(
        fragmentManager: FragmentManager,
        analyticsSource: String,
    )

    fun openSuuntoTagsPromotionDialog(
        fragmentManager: FragmentManager,
        analyticsSource: String,
    )
}

class PremiumPromotionNavigatorNoOp @Inject constructor() : PremiumPromotionNavigator {
    override fun newPremiumFeaturePromotionIntent(
        context: Context,
        analyticsSource: String,
        buyPremiumPopupShownAnalyticsReason: String?
    ): Intent? = null

    override fun navigateToPremiumFeaturePromotion(
        context: Context,
        analyticsSource: String,
        buyPremiumPopupShownAnalyticsReason: String?
    ) {
        // Do nothing
    }

    override fun newPurchaseSubscriptionIntent(
        context: Context,
        analyticsSource: String,
        buyPremiumPopupShownAnalyticsReason: String?
    ): Intent? = null

    override fun navigateToPurchaseSubscription(
        context: Context,
        analyticsSource: String,
        buyPremiumPopupShownAnalyticsReason: String?
    ) {
        // Do nothing
    }

    override fun newInDepthPremiumDescriptionIntent(
        context: Context,
        analyticsSource: String,
        buyPremiumPopupShownAnalyticsReason: String?
    ): Intent? = null

    override fun navigateToInDepthPremiumDescription(
        context: Context,
        analyticsSource: String,
        buyPremiumPopupShownAnalyticsReason: String?
    ) {
        // Do nothing
    }

    override fun openWorkoutPlaybackPromotionDialog(
        fragmentManager: FragmentManager,
        analyticsSource: String
    ) {
        // Do nothing
    }

    override fun openSuuntoTagsPromotionDialog(
        fragmentManager: FragmentManager,
        analyticsSource: String
    ) {
        // Do nothing
    }
}
