package com.stt.android.premium

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.RelativeLayout
import androidx.annotation.Dimension
import androidx.annotation.StringRes
import androidx.core.view.doOnLayout
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import com.stt.android.R
import com.stt.android.databinding.PremiumBuyToGetAccessBinding
import kotlin.math.roundToInt

class BuyPremiumToGetAccessView(context: Context) : RelativeLayout(context) {
    private val binding = PremiumBuyToGetAccessBinding.inflate(LayoutInflater.from(context), this, true)

    init {
        doOnLayout {
            val totalHeight = binding.root.height
            val popupHeight = binding.premiumBuyToGetAccessContent.height
            val popupTotalVerticalMargin = 2 * context.resources.getDimensionPixelSize(R.dimen.size_spacing_medium)

            if (totalHeight <= popupHeight + popupTotalVerticalMargin) {
                // popup has used all vertical space available, the title and button(s) might
                // not have enough room to fit nicely. Reduce margins to give them bit more room.
                fun multiplyTopMargin(view: View, factor: Double) = view.updateLayoutParams<MarginLayoutParams> {
                    topMargin = (topMargin * factor).roundToInt()
                }

                multiplyTopMargin(binding.premiumBuyToGetAccessTitle, 0.5)
                multiplyTopMargin(binding.premiumBuyToGetAccessDescription, 0.75)
                multiplyTopMargin(binding.premiumBuyToGetAccessTryForFree, 0.75)
                multiplyTopMargin(binding.premiumBuyToGetAccessBuyButton, 0.75)
            }
        }
    }

    fun setBuyButtonClickListener(listener: OnClickListener?) {
        binding.premiumBuyToGetAccessBuyButton.setOnClickListener(listener)
    }

    fun setBuyButtonHeight(@Dimension heightPx: Int) {
        binding.premiumBuyToGetAccessBuyButton.height = heightPx
    }

    fun setCloseButtonClickListener(listener: OnClickListener?) {
        binding.premiumBuyToGetAccessCloseButton.setOnClickListener(listener)
    }

    fun setClickOutsideListener(listener: OnClickListener?) {
        binding.root.setOnClickListener(listener)
    }

    fun setClickDeclineButtonListener(listener: OnClickListener?) {
        binding.premiumBuyToGetAccessDeclineButton.setOnClickListener(listener)
    }

    fun setDescription(@StringRes stringRes: Int) {
        setDescription(context.getString(stringRes))
    }

    fun setDescription(text: String) {
        binding.premiumBuyToGetAccessDescription.text = text
    }

    fun showCloseButton(show: Boolean) {
        binding.premiumBuyToGetAccessCloseButton.isVisible = show
    }

    fun showDeclineButton(show: Boolean) {
        binding.premiumBuyToGetAccessDeclineButton.isVisible = show
    }

    fun setBuyButtonText(text: String) {
        binding.premiumBuyToGetAccessBuyButton.text = text
    }
}
