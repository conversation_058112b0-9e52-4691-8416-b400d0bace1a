package com.stt.android.premium

import android.view.ViewGroup
import androidx.activity.ComponentActivity
import androidx.activity.OnBackPressedCallback
import androidx.activity.OnBackPressedDispatcher
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleOwner
import javax.inject.Inject

/**
 * Wrapper and extra helper for [PremiumRequiredToAccessHandler] that handles clearing
 * selected premium map features on back press or when the Activity / Fragment is destroyed for good.
 * In Suunto app this interface is bound to a no-op implementation, so it can be used in shared code
 * without separate app flavor checks.
 *
 * Registers an [OnBackPressedCallback] to the Activity, so the Activity's back handling logic needs
 * to be based on [OnBackPressedDispatcher] rather than overriding [ComponentActivity.onBackPressed]
 * for this to work as intended.
 */
interface PremiumMapFeaturesAccessHandler {
    /**
     * Call sometime during onCreate. Internally registers a [OnBackPressedCallback] that is
     * enabled when the buy premium popup is shown, so in most cases you want to register other
     * such callbacks before calling this.
     */
    fun onCreate(
        activity: ComponentActivity,
        analyticsSource: String
    )

    /**
     * Call sometime during onCreate. Internally registers a [OnBackPressedCallback] that is
     * enabled when the buy premium popup is shown, so in most cases you want to register other
     * such callbacks before calling this.
     */
    fun onCreate(
        fragment: Fragment,
        analyticsSource: String
    )

    /**
     * Call during onDestroy. If the Activity is exiting, clears premium map selections if the buy
     * premium popup is being shown to allow next map screen to be shown without need to show the
     * popup when entering the screen.
     */
    fun onDestroy(activity: ComponentActivity?)

    /**
     * Calls [PremiumRequiredToAccessHandler.startCheckingForPremiumAccess] and starts
     * enabling & disabling the [OnBackPressedCallback] registered in [onCreate].
     *
     * @param accessStateChangeListener - works like in [PremiumRequiredToAccessHandler.startCheckingForPremiumAccess],
     *   and is called after this handler's onBackPressedCallback enabled status has been updated
     */
    fun startCheckingForPremiumAccess(
        viewLifecycleOwner: LifecycleOwner,
        containerView: ViewGroup,
        accessStateChangeListener: ((hasAccess: Boolean) -> Unit)?
    )

    /**
     * Calls [PremiumRequiredToAccessHandler.startCheckingForPremiumAccess] and starts
     * enabling & disabling the [OnBackPressedCallback] registered in [onCreate]
     */
    fun startCheckingForPremiumAccess(
        viewLifecycleOwner: LifecycleOwner,
        containerView: ViewGroup
    ) = startCheckingForPremiumAccess(
        viewLifecycleOwner = viewLifecycleOwner,
        containerView = containerView,
        accessStateChangeListener = null
    )
}

class PremiumMapFeaturesAccessHandlerNoOp @Inject constructor() : PremiumMapFeaturesAccessHandler {
    override fun onCreate(
        activity: ComponentActivity,
        analyticsSource: String
    ) {
        // Do nothing
    }

    override fun onCreate(
        fragment: Fragment,
        analyticsSource: String
    ) {
        // Do nothing
    }

    override fun onDestroy(activity: ComponentActivity?) {
        // Do nothing
    }

    override fun startCheckingForPremiumAccess(
        viewLifecycleOwner: LifecycleOwner,
        containerView: ViewGroup,
        accessStateChangeListener: ((hasAccess: Boolean) -> Unit)?
    ) {
        // Do nothing
    }
}
