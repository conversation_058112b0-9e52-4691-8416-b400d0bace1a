package com.stt.android.premium

import android.content.Context
import androidx.activity.ComponentActivity
import androidx.fragment.app.Fragment
import javax.inject.Inject

interface PremiumPurchaseFlowLauncher {
    fun register(fragment: Fragment, callback: ((didPurchase: Boolean) -> Unit)? = null)
    fun register(activity: ComponentActivity, callback: ((didPurchase: Boolean) -> Unit)? = null)

    fun launchFeaturePromotionAndAskAboutAppReset(
        context: Context,
        analyticsSource: String,
        buyPremiumPopupShownAnalyticsReason: String? = null
    )

    fun launchFeaturePromotionAndResetAppAutomatically(
        context: Context,
        analyticsSource: String,
        buyPremiumPopupShownAnalyticsReason: String? = null
    )

    fun launchInDepthPremiumDescriptionAndAskAboutAppReset(
        context: Context,
        analyticsSource: String,
        buyPremiumPopupShownAnalyticsReason: String? = null
    )

    fun launchInDepthPremiumDescriptionAndResetAppAutomatically(
        context: Context,
        analyticsSource: String,
        buyPremiumPopupShownAnalyticsReason: String? = null
    )
}

class PremiumPurchaseFlowLauncherNoOp @Inject constructor() : PremiumPurchaseFlowLauncher {
    override fun register(fragment: Fragment, callback: ((didPurchase: Boolean) -> Unit)?) {
        // Do nothing
    }

    override fun register(activity: ComponentActivity, callback: ((didPurchase: Boolean) -> Unit)?) {
        // Do nothing
    }

    override fun launchFeaturePromotionAndAskAboutAppReset(
        context: Context,
        analyticsSource: String,
        buyPremiumPopupShownAnalyticsReason: String?
    ) {
        // Do nothing
    }

    override fun launchFeaturePromotionAndResetAppAutomatically(
        context: Context,
        analyticsSource: String,
        buyPremiumPopupShownAnalyticsReason: String?
    ) {
        // Do nothing
    }

    override fun launchInDepthPremiumDescriptionAndAskAboutAppReset(
        context: Context,
        analyticsSource: String,
        buyPremiumPopupShownAnalyticsReason: String?
    ) {
        // Do nothing
    }

    override fun launchInDepthPremiumDescriptionAndResetAppAutomatically(
        context: Context,
        analyticsSource: String,
        buyPremiumPopupShownAnalyticsReason: String?
    ) {
        // Do nothing
    }
}
