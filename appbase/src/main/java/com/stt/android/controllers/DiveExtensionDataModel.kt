package com.stt.android.controllers

import com.stt.android.data.source.local.diveextension.LocalDiveExtension
import com.stt.android.domain.workouts.extensions.DiveExtension
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.remote.extensions.ExtensionsRemoteApi
import com.stt.android.remote.extensions.RemoteWorkoutExtension
import com.stt.android.utils.firstOfType
import javax.inject.Inject

class DiveExtensionDataModel @Inject constructor(
    workoutHeaderController: WorkoutHeaderController,
    diveExtensionDataAccess: ExtensionDataAccessRoomDb<LocalDiveExtension, DiveExtension>,
    extensionsRemoteApi: ExtensionsRemoteApi,
    extensionDataAccessMap: Map<Class<out WorkoutExtension>, ExtensionDataAccess<out WorkoutExtension>>,
) : ExtensionDataModel<DiveExtension>(
    diveExtensionDataAccess,
    workoutHeader<PERSON>ontroller,
    extensionsRemote<PERSON><PERSON>,
    extensionDataAccessMap,
) {
    override fun getBackendExtensionType(): String = RemoteWorkoutExtension.Type.DIVE_HEADER_EXTENSION.value

    override fun findExtension(extensions: List<WorkoutExtension>): DiveExtension? =
        extensions.firstOfType()
}
