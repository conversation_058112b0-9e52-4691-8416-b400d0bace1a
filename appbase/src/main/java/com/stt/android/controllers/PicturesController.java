package com.stt.android.controllers;

import android.text.TextUtils;
import androidx.annotation.NonNull;
import androidx.annotation.WorkerThread;
import com.j256.ormlite.dao.Dao;
import com.j256.ormlite.stmt.QueryBuilder;
import com.stt.android.domain.UserSession;
import com.stt.android.domain.database.DatabaseHelper;
import com.stt.android.domain.user.ImageInformation;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.exceptions.BackendException;
import com.stt.android.exceptions.InternalDataException;
import com.stt.android.utils.FileUtils;
import com.stt.android.utils.STTConstants;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;
import javax.inject.Inject;
import javax.inject.Singleton;
import rx.Observable;
import rx.functions.Func1;
import timber.log.Timber;

@Singleton
public class PicturesController {
    final Dao<ImageInformation, Integer> dao;
    final CurrentUserController currentUserController;
    final BackendController backendController;

    @Inject
    FileUtils fileUtils;

    @Inject
    public PicturesController(DatabaseHelper helper,
        CurrentUserController currentUserController, BackendController backendController) {
        try {
            this.dao = helper.getDao(ImageInformation.class);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        this.currentUserController = currentUserController;
        this.backendController = backendController;
    }

    @WorkerThread
    public ImageInformation store(ImageInformation picture) throws InternalDataException {
        try {
            if (TextUtils.isEmpty(picture.getFileName())) {
                ImageInformation localImage = dao.queryForSameId(picture);
                if (localImage != null) {
                    boolean isLocallyChanged = picture.isLocallyChanged();
                    if (!isLocallyChanged) {
                        picture = picture.synced();
                    }
                }
            }
            dao.createOrUpdate(picture);
            return picture;
        } catch (SQLException e) {
            throw new InternalDataException("Unable to store picture metadata to local database",
                e);
        }
    }

    @WorkerThread
    public void store(final List<ImageInformation> pictures) throws InternalDataException {
        try {
            dao.callBatchTasks(new Callable<Void>() {
                @Override
                public Void call() throws Exception {
                    for (ImageInformation imgInfo : pictures) {
                        store(imgInfo);
                    }
                    return null;
                }
            });
        } catch (Exception e) {
            throw new InternalDataException("Unable to store picture metadata to local database",
                e);
        }
    }

    /**
     * @return all the pictures data in the database that have been locally changed (waiting to be
     * synced) and that have a valid workout key (so the backend can find their workout).
     * @throws InternalDataException
     */
    @WorkerThread
    @NonNull
    public List<ImageInformation> findUnsyncedPictures(String userName) throws InternalDataException {
        try {
            QueryBuilder<ImageInformation, Integer> qb = dao.queryBuilder();
            qb.orderBy(ImageInformation.DbFields.TIMESTAMP, true)
                .where()
                .eq(ImageInformation.DbFields.LOCALLY_CHANGED, true)
                .and()
                .isNotNull(ImageInformation.DbFields.WORKOUT_KEY)
                .and()
                .isNotNull(ImageInformation.DbFields.WORKOUT_ID)
                .and()
                .eq(ImageInformation.DbFields.USER_NAME, userName);
            return Collections.unmodifiableList(qb.query());
        } catch (SQLException e) {
            throw new InternalDataException("Unable to find picture metadata from local database",
                e);
        }
    }

    /**
     * Returns a map from workout IDs to all images of that workout.
     */
    @NonNull
    @WorkerThread
    public Map<Integer, List<ImageInformation>> findByWorkoutIds(@NonNull Collection<Integer> workoutIds) throws InternalDataException {
        if (workoutIds.isEmpty()) {
            return Collections.emptyMap();
        }

        try {
            QueryBuilder<ImageInformation, Integer> qb = dao.queryBuilder();
            qb.orderBy(ImageInformation.DbFields.TIMESTAMP, true)
                .where()
                .in(ImageInformation.DbFields.WORKOUT_ID, workoutIds);
            return dao.query(qb.prepare())
                .stream()
                .collect(Collectors.groupingBy(ImageInformation::getWorkoutId));
        } catch (SQLException e) {
            throw new InternalDataException("Failed to find picture metadata from local database", e);
        }
    }

    @NonNull
    @WorkerThread
    public List<ImageInformation> findByWorkoutId(int workoutId) throws InternalDataException {
        List<ImageInformation> images = findByWorkoutIds(Collections.singleton(workoutId))
            .get(workoutId);
        return images != null ? images : Collections.emptyList();
    }

    /**
     * Find all images belonging to the user with the specified user name, sorted by timestamp in
     * descending order. If count is 0 or negative, return all images of this user.
     */
    @WorkerThread
    @NonNull
    public List<ImageInformation> findByUserName(String userName, long count) throws InternalDataException {
        try {
            QueryBuilder<ImageInformation, Integer> qb = dao.queryBuilder();
            if (count > 0L) {
                qb.limit(count);
            }
            qb.orderBy(ImageInformation.DbFields.TIMESTAMP, false)
                .where()
                .eq(ImageInformation.DbFields.USER_NAME, userName);
            return dao.query(qb.prepare());
        } catch (SQLException e) {
            throw new InternalDataException("Failed ot find images by user name", e);
        }
    }

    @WorkerThread
    void removeSynced() {
        try {
            remove(dao.queryForEq(ImageInformation.DbFields.LOCALLY_CHANGED, false));
        } catch (Exception e) {
            Timber.w(e, "Error while removing synced pictures");

            try {
                dao.deleteBuilder().delete();
            } catch (Exception e2) {
                Timber.w(e2, "Error while removing synced pictures");
            }
        }
    }

    /**
     * Removes picture metadata from the database.
     */
    @WorkerThread
    void remove(final List<ImageInformation> picturesToBeDeleted) throws InternalDataException {
        try {
            dao.callBatchTasks((Callable<Void>) () -> {
                for (ImageInformation pictureToBeDeleted : picturesToBeDeleted) {
                    remove(pictureToBeDeleted);
                }
                return null;
            });
        } catch (Exception e) {
            throw new InternalDataException("Unable to delete picture metadata from local database",
                e);
        }
    }

    /**
     * Removes picture metadata from the database.
     */
    @WorkerThread
    public void remove(ImageInformation pictureToBeDeleted) throws InternalDataException {
        try {
            dao.delete(pictureToBeDeleted);
        } catch (SQLException e) {
            throw new InternalDataException("Unable to delete picture metadata from local database",
                e);
        }
    }

    /**
     * Removes picture metadata, local copy, and also informs server about the removal.
     */
    @WorkerThread
    public void deleteAndPushPicture(ImageInformation imageToDelete)
        throws BackendException, InternalDataException {
        // backend uses key as picture identifier, if trying to delete a picture without key
        // check local DB for picture with same ID for more up-to-date version that might have the key.
        // This can prevent locally deleted picture reappearing after sync if the imageToDelete
        // was fetched between adding the image locally and uploading it
        ImageInformation imageToDeleteWithKey = imageToDelete;
        if (TextUtils.isEmpty(imageToDeleteWithKey.getKey())) {
            try {
                ImageInformation storedImage = dao.queryForSameId(imageToDeleteWithKey);
                if (storedImage != null) {
                    imageToDeleteWithKey = storedImage;
                }
            } catch (SQLException exception) {
                // ignore
            }
        }

        if (!TextUtils.isEmpty(imageToDeleteWithKey.getKey()) && currentUserController.getUsername()
            .equals(imageToDeleteWithKey.getUsername())) {
            // the image has been synced, let's inform the backend
            UserSession userSession = currentUserController.getSession();
            if (!backendController.pushDeletedImage(userSession, imageToDeleteWithKey)) {
                throw new BackendException("Failed to push deleted image to backend");
            }
        }

        remove(imageToDeleteWithKey);
        deletePicture(imageToDeleteWithKey);
    }

    /**
     * Removes picture metadata belonging to the given workout from the database, and deletes local
     * copies of the picture.
     */
    @WorkerThread
    public void deleteByWorkout(WorkoutHeader workoutHeader) throws InternalDataException {
        List<ImageInformation> workoutImages = findByWorkoutId(workoutHeader.getId());
        if (workoutImages.isEmpty()) {
            return;
        }

        for (ImageInformation image : workoutImages) {
            try {
                deletePicture(image);
            } catch (InternalDataException ignored) {
            }
        }

        remove(workoutImages);
    }

    /**
     * Deletes local copies of the picture.
     */
    @WorkerThread
    public void deletePicture(ImageInformation workoutPicture) throws InternalDataException {
        String fileName = workoutPicture.getFileName();
        if (TextUtils.isEmpty(fileName)) {
            return;
        }

        // first tries to delete from internal file folder
        boolean deleted = false;
        try {
            deleted = FileUtils.delete(
                fileUtils.getInternalFilePath(STTConstants.DIRECTORY_PICTURES, fileName));
        } catch (Exception e) {
            // falls through
            Timber.w("file %s could not be deleted: %s", fileName, e.getMessage());
        }
        if (!deleted) {
            try {
                // now tries to delete from cache
                deleted = FileUtils.delete(
                    fileUtils.getCachedFilePath(STTConstants.DIRECTORY_PICTURES, fileName));
                if (!deleted) {
                    Timber.w("Could not delete file %s", fileName);
                }
            } catch (Exception e) {
                throw new InternalDataException("Unable to delete cached pictures", e);
            }
        }
    }

    public Observable<List<ImageInformation>> loadImages(final WorkoutHeader workoutHeader) {
        Func1<Throwable, Observable<? extends List<ImageInformation>>> nullOnException =
            throwable -> Observable.just(null);
        Observable<List<ImageInformation>> local =
            loadImagesFromLocal(workoutHeader).onErrorResumeNext(nullOnException);
        Observable<List<ImageInformation>> backend =
            loadImagesFromBackend(workoutHeader).onErrorResumeNext(nullOnException);
        return Observable.concat(local, backend)
            .filter(images -> images != null && !images.isEmpty())
            .defaultIfEmpty(Collections.emptyList())
            .onBackpressureBuffer();
    }

    private Observable<List<ImageInformation>> loadImagesFromLocal(
        final WorkoutHeader workoutHeader) {
        return Observable.fromCallable(() -> findByWorkoutId(workoutHeader.getId()));
    }

    private Observable<List<ImageInformation>> loadImagesFromBackend(
        final WorkoutHeader workoutHeader) {
        return Observable.defer(() -> {
            String workoutKey = workoutHeader.getKey();
            if (TextUtils.isEmpty(workoutKey)) {
                return Observable.empty();
            }

            try {
                UserSession session = currentUserController.getSession();
                List<ImageInformation> images =
                    backendController.fetchWorkoutPictures(session, workoutKey);
                QueryBuilder<ImageInformation, Integer> qb = dao.queryBuilder();
                qb.orderBy(ImageInformation.DbFields.TIMESTAMP, true)
                    .where()
                    .eq(ImageInformation.DbFields.WORKOUT_KEY, workoutKey)
                    .and()
                    .isNull(ImageInformation.DbFields.KEY);
                List<ImageInformation> unsynced = dao.query(qb.prepare());

                List<ImageInformation> all =
                    new ArrayList<>(images.size() + unsynced.size());
                all.addAll(unsynced);

                int workoutId = workoutHeader.getId();
                for (ImageInformation image : images) {
                    all.add(image.linkWithWorkout(workoutId));
                }

                all.sort(Comparator.comparingLong(ImageInformation::getTimestamp));
                return Observable.just(all);
            } catch (Exception e) {
                return Observable.error(e);
            }
        }).doOnNext(images -> {
            // TODO how about my friends?
            if (currentUserController.getUsername().equals(workoutHeader.getUsername())) {
                try {
                    store(images);
                } catch (Exception ignored) {
                }
            }
        });
    }
}
