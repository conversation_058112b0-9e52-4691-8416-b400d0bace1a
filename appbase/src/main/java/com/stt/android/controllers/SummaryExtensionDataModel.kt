package com.stt.android.controllers

import com.stt.android.data.source.local.summaryextension.LocalSummaryExtension
import com.stt.android.domain.user.workoutextension.BackendWorkoutSummaryExtension
import com.stt.android.domain.workouts.extensions.SummaryExtension
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.remote.extensions.ExtensionsRemoteApi
import com.stt.android.utils.firstOfType
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SummaryExtensionDataModel @Inject constructor(
    workoutHeaderController: WorkoutHeaderController,
    summaryExtensionExtensionDataAccess: ExtensionDataAccessRoomDb<LocalSummaryExtension, SummaryExtension>,
    extensionsRemoteApi: ExtensionsRemoteApi,
    extensionDataAccessMap: Map<Class<out WorkoutExtension>, ExtensionDataAccess<out WorkoutExtension>>,
) : ExtensionDataModel<SummaryExtension>(
    summaryExtensionExtensionDataAccess,
    workoutHeaderController,
    extensionsRemoteApi,
    extensionDataAccessMap,
) {
    override fun getBackendExtensionType(): String = BackendWorkoutSummaryExtension.TYPE

    override fun findExtension(extensions: List<WorkoutExtension>): SummaryExtension? =
        extensions.firstOfType<SummaryExtension>()
}
