package com.stt.android.controllers

import com.stt.android.data.source.local.fitnessextension.LocalFitnessExtension
import com.stt.android.domain.user.workoutextension.BackendFitnessExtension
import com.stt.android.domain.user.workoutextension.FitnessExtension
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.remote.extensions.ExtensionsRemoteApi
import com.stt.android.utils.firstOfType
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class FitnessExtensionDataModel @Inject constructor(
    workoutHeaderController: WorkoutHeaderController,
    fitnessExtensionExtensionDataAccess: ExtensionDataAccessRoomDb<LocalFitnessExtension, FitnessExtension>,
    extensionsRemoteApi: ExtensionsRemoteApi,
    extensionDataAccessMap: Map<Class<out WorkoutExtension>, ExtensionDataAccess<out WorkoutExtension>>,
) : ExtensionDataModel<FitnessExtension>(
    fitnessExtensionExtensionDataAccess,
    workoutHeaderController,
    extensionsRemoteApi,
    extensionDataAccessMap,
) {
    override fun getBackendExtensionType(): String = BackendFitnessExtension.TYPE

    override fun findExtension(extensions: List<WorkoutExtension>): FitnessExtension? =
        extensions.firstOfType<FitnessExtension>()
}
