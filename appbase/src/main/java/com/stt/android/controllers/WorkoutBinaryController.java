package com.stt.android.controllers;

import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.domain.workout.Workout;
import com.stt.android.domain.workout.WorkoutData;
import com.stt.android.exceptions.InternalDataException;
import com.stt.android.tracker.compat.serialization.DeserializationFailedException;
import com.stt.android.tracker.compat.serialization.HeaderSerializer;
import com.stt.android.tracker.compat.serialization.LegacyWorkoutSerializer;
import com.stt.android.tracker.compat.serialization.SerializationFailedException;
import com.stt.android.tracker.compat.serialization.ServiceHeaderSerializer;
import com.stt.android.tracker.model.LegacyHeader;
import com.stt.android.tracker.model.LegacyServiceHeader;
import com.stt.android.tracker.model.LegacyWorkout;
import com.stt.android.tracker.model.LegacyWorkoutDataContainer;
import com.stt.android.utils.FileUtils;
import com.stt.android.utils.STTConstants;
import java.io.BufferedInputStream;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import javax.inject.Inject;
import javax.inject.Singleton;
import timber.log.Timber;

@Singleton
public class WorkoutBinaryController {
    private final FileUtils fileUtils;

    @Inject
    public WorkoutBinaryController(FileUtils fileUtils) {
        this.fileUtils = fileUtils;
    }

    /**
     * Currently saves the workout using the legacy binary format
     *
     * @throws InternalDataException
     */
    public void saveWorkoutBinary(Workout workout, String filename, int userWeight, int machineId,
        int applicationId) throws InternalDataException {
        LegacyWorkoutDataContainer data =
            new LegacyWorkoutDataContainer(workout, machineId, applicationId);
        LegacyWorkout legacyWorkout = data.getLegacyWorkout();
        LegacyHeader legacyHeader = data.getLegacyHeader();
        LegacyServiceHeader legacyServiceHeader = data.getLegacyServiceHeader();
        saveLegacyWorkoutBinary(legacyWorkout, legacyHeader, legacyServiceHeader, filename,
            userWeight);
    }

    /**
     * Reads a legacy binary workout from disk, same as calling readLegacyWorkoutBinary(file,
     * false).
     *
     * @throws InternalDataException
     */
    public static WorkoutData readLegacyWorkoutBinary(File file) throws InternalDataException {
        return readLegacyWorkoutBinary(file, false).getWorkoutData();
    }

    /**
     * Reads a legacy binary workout from disk.
     *
     * @param onlyHeaders true if the file does only contain workout header information
     * @throws InternalDataException
     */
    public static LegacyWorkoutDataContainer readLegacyWorkoutBinary(File file, boolean onlyHeaders)
        throws InternalDataException {
        DataInputStream dataInputStream = null;
        try {
            dataInputStream = new DataInputStream(new BufferedInputStream(new FileInputStream(file)));
            LegacyHeader header = HeaderSerializer.read(dataInputStream);
            LegacyServiceHeader legacyServiceHeader = ServiceHeaderSerializer.read(dataInputStream);
            LegacyWorkout legacyWorkout;
            if (onlyHeaders) {
                legacyWorkout = LegacyWorkout.createDummy();
            } else {
                legacyWorkout = LegacyWorkoutSerializer.read(dataInputStream);
            }
            return new LegacyWorkoutDataContainer(header, legacyWorkout, legacyServiceHeader);
        } catch (FileNotFoundException | DeserializationFailedException e) {
            throw new InternalDataException("Workout data could not be read", e);
        } finally {
            if (dataInputStream != null) {
                try {
                    dataInputStream.close();
                } catch (IOException e) {
                    // Not much we can do
                }
            }
        }
    }

    /**
     * Do not use this method when storing updated workouts as some values will be overwritten and
     * not from the original workout (e.g.
     * version number, application ID...)
     *
     * @throws InternalDataException
     */
    private void saveLegacyWorkoutBinary(LegacyWorkout legacyWorkout, LegacyHeader legacyHeader,
        LegacyServiceHeader legacyServiceHeader, String filename, int userWeight)
        throws InternalDataException {
        DataOutputStream stream = null;
        try {
            File file = fileUtils.getInternalFilePath(STTConstants.DIRECTORY_WORKOUTS, filename);
            Timber.d("Storing workout to file %s", file);
            stream = new DataOutputStream(new FileOutputStream(file));
            HeaderSerializer.write(stream, legacyHeader, userWeight);
            ServiceHeaderSerializer.write(stream, legacyServiceHeader);
            LegacyWorkoutSerializer.write(stream, legacyWorkout);
        } catch (FileNotFoundException | SerializationFailedException e) {
            throw new InternalDataException("Unable to serialize legacy data.", e);
        } finally {
            if (stream != null) {
                try {
                    stream.close();
                } catch (IOException e) {
                    // not much we can do in here
                }
            }
        }
    }

    /**
     * Moves a workout binary file name stored in the internal file path to the cache.
     * <p>
     * <em>NOTE:</em> This method accesses the disk so it must be called from a
     * background thread.
     * </p>
     *
     * @throws java.io.FileNotFoundException if oldFileName can't be found
     * @throws java.io.IOException if the file could not be moved
     */
    public void moveInternalBinaryWorkoutToCache(String oldFileName, String newFileName)
        throws IOException {
        File oldFile = fileUtils.getInternalFilePath(STTConstants.DIRECTORY_WORKOUTS, oldFileName);
        File newFile = fileUtils.getCachedFilePath(STTConstants.DIRECTORY_WORKOUTS, newFileName);
        Timber.d("Moving workout from file %s to file %s", oldFile, newFile);
        FileUtils.move(oldFile, newFile, true);
    }

    public void removeNonCachedBinary(WorkoutHeader workout) throws InternalDataException {
        try {
            File binaryFile = fileUtils.getInternalFilePath(STTConstants.DIRECTORY_WORKOUTS,
                workout.getFilename());
            if (!FileUtils.delete(binaryFile)) {
                throw new InternalDataException("Unable to delete binary " + workout.getFilename());
            }
        } catch (FileNotFoundException e) {
            Timber.w(e, "Workout binary file name null. There's no binary to delete.");
        }
    }
}
