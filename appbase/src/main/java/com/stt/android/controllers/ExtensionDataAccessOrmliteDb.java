package com.stt.android.controllers;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.j256.ormlite.dao.Dao;
import com.j256.ormlite.stmt.DeleteBuilder;
import com.j256.ormlite.stmt.QueryBuilder;
import com.stt.android.domain.database.DatabaseHelper;
import com.stt.android.domain.workouts.extensions.WorkoutExtension;
import com.stt.android.exceptions.InternalDataException;
import java.sql.SQLException;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.Callable;

public class ExtensionDataAccessOrmliteDb<T extends WorkoutExtension>
    implements ExtensionDataAccess<T> {

    final Dao<T, Integer> dao;
    final String workoutIdFieldName;

    public ExtensionDataAccessOrmliteDb(Class<T> type, DatabaseHelper helper, String workoutIdFieldName) {
        this.workoutIdFieldName = workoutIdFieldName;
        try {
            this.dao = helper.getDao(type);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void store(@NonNull T extension) throws InternalDataException {
        try {
            dao.createOrUpdate(extension);
        } catch (SQLException e) {
            throw new InternalDataException("Error writing to local database", e);
        }
    }

    @NonNull
    @Override
    public List<T> findAll() throws InternalDataException {
        try {
            return dao.queryForAll();
        } catch (SQLException e) {
            throw new InternalDataException("Error finding all from local database", e);
        }
    }

    @Override
    public void store(@NonNull List<? extends T> extensions) throws InternalDataException {
        try {
            dao.callBatchTasks((Callable<Void>) () -> {
                for (T extension : extensions) {
                    dao.createOrUpdate(extension);
                }
                return null;
            });
        } catch (Exception e) {
            throw new InternalDataException("Error writing to local database", e);
        }
    }

    @Override
    public void removeByWorkoutId(int workoutId) throws InternalDataException {
        try {
            DeleteBuilder<T, Integer> db = dao.deleteBuilder();
            db.where().eq(workoutIdFieldName, workoutId);
            dao.delete(db.prepare());
        } catch (SQLException e) {
            throw new InternalDataException("Error deleting from local database", e);
        }
    }

    @Override
    public void removeByWorkoutIds(@NonNull Collection<Integer> workoutIds) throws InternalDataException {
        try {
            DeleteBuilder<T, Integer> db = dao.deleteBuilder();
            db.where().in(workoutIdFieldName, workoutIds);
            dao.delete(db.prepare());
        } catch (Exception e) {
            throw new InternalDataException("Error deleting from local database", e);
        }
    }

    @Nullable
    @Override
    public T findByWorkoutId(int workoutId) throws InternalDataException {
        try {
            QueryBuilder<T, Integer> qb = dao.queryBuilder();
            qb.where().eq(workoutIdFieldName, workoutId);
            return qb.queryForFirst();
        } catch (Throwable e) {
            throw new InternalDataException("Error finding from local database", e);
        }
    }

    @NonNull
    @Override
    public List<T> findByWorkoutIds(@NonNull Collection<Integer> workoutIds) throws InternalDataException {
        try {
            QueryBuilder<T, Integer> qb = dao.queryBuilder();
            qb.where()
                .in(workoutIdFieldName, workoutIds);
            return dao.query(qb.prepare());
        } catch (Throwable e) {
            throw new InternalDataException("Error finding from local database", e);
        }
    }
}
