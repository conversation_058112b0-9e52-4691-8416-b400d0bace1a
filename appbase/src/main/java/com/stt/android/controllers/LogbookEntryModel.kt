package com.stt.android.controllers

import androidx.annotation.WorkerThread
import com.j256.ormlite.dao.Dao
import com.j256.ormlite.field.DataType
import com.j256.ormlite.table.TableUtils
import com.stt.android.TestOpen
import com.stt.android.domain.database.DatabaseHelper
import com.stt.android.domain.user.workout.SuuntoLogbookEntry
import com.stt.android.exceptions.InternalDataException
import io.reactivex.Completable
import io.reactivex.schedulers.Schedulers
import java.sql.SQLException
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
@TestOpen
class LogbookEntryModel
@Inject constructor(
    helper: DatabaseHelper
) {
    private val dao: Dao<SuuntoLogbookEntry, Int>

    init {
        try {
            this.dao = helper.getDao(SuuntoLogbookEntry::class.java)
        } catch (e: SQLException) {
            throw RuntimeException(e)
        }
    }

    @WorkerThread
    @Throws(InternalDataException::class)
    fun queryEntryIds(): List<Long> = doGetEntryIds().first

    @WorkerThread
    @Throws(InternalDataException::class)
    fun queryWorkoutVsEntryIdMap(): Map<Int, Pair<Long, String?>> = doGetEntryIds().second

    @WorkerThread
    @Throws(InternalDataException::class)
    fun store(logbookEntry: SuuntoLogbookEntry) {
        try {
            dao.createOrUpdate(logbookEntry)
        } catch (e: SQLException) {
            throw InternalDataException("Error writing to local database", e)
        }
    }

    @WorkerThread
    @Throws(InternalDataException::class)
    fun findByWorkoutId(workoutId: Int): SuuntoLogbookEntry? {
        try {
            return dao.queryBuilder()
                .where()
                .eq(SuuntoLogbookEntry.DbFields.WORKOUT_ID, workoutId)
                .query()
                ?.firstOrNull()
        } catch (e: SQLException) {
            throw InternalDataException("Error finding logbook entry for workout ID: $workoutId")
        }
    }

    @WorkerThread
    @Throws(InternalDataException::class)
    fun doGetEntryIds(): Pair<List<Long>, Map<Int, Pair<Long, String?>>> {
        val workoutVsEntryIdMap = mutableMapOf<Int, Pair<Long, String?>>()
        try {
            val queryBuiler = StringBuilder().apply {
                append("SELECT")
                append(" ")
                append(SuuntoLogbookEntry.DbFields.ENTRY_ID)
                append(",")
                append(SuuntoLogbookEntry.DbFields.WORKOUT_ID)
                append(",")
                append(SuuntoLogbookEntry.DbFields.SERIAL_NUMBER)
                append(" ")
                append("FROM")
                append(" ")
                append(SuuntoLogbookEntry.TABLE_NAME)
            }

            val columnType = arrayOf(DataType.LONG_OBJ, DataType.INTEGER_OBJ, DataType.STRING)

            val results = dao.queryRaw(queryBuiler.toString(), columnType).results
            val count = results.size
            if (count == 0) {
                return emptyList<Long>() to workoutVsEntryIdMap
            }

            val entryIds = ArrayList<Long>(count)
            for (result in results) {
                entryIds.add(result[0] as Long)
                workoutVsEntryIdMap[result[1] as Int] = Pair(result[0] as Long, result[2] as? String)
            }
            return entryIds to workoutVsEntryIdMap
        } catch (e: SQLException) {
            throw InternalDataException(
                "Unable to fetch entryIds from the local database: " + e.message,
                e
            )
        }
    }

    @WorkerThread
    private fun doClearModel() {
        TableUtils.clearTable(dao.connectionSource, SuuntoLogbookEntry::class.java)
    }

    fun clearModel(): Completable {
        return Completable.fromAction {
            doClearModel()
        }
            .subscribeOn(Schedulers.io())
    }
}
