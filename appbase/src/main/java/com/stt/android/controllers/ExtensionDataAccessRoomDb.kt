package com.stt.android.controllers

import androidx.annotation.WorkerThread
import com.stt.android.data.EntityMapper
import com.stt.android.data.source.local.workoutextension.LocalWorkoutExtension
import com.stt.android.data.source.local.workoutextension.WorkoutExtensionDao
import com.stt.android.data.workout.ExtensionDataFetcher
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.exceptions.InternalDataException
import javax.inject.Inject

class ExtensionDataAccessRoomDb<T : LocalWorkoutExtension, Z : WorkoutExtension> @Inject constructor(
    private val workoutExtensionDao: WorkoutExtensionDao<T>,
    private val extensionDataFetcher: ExtensionDataFetcher<T>,
    private val mapper: EntityMapper<T, Z>,
) : ExtensionDataAccess<Z> {
    @WorkerThread
    @Throws(InternalDataException::class)
    override fun store(extension: Z) {
        val local = mapper.toDataEntity()(extension)
        workoutExtensionDao.insert(local)
    }

    @WorkerThread
    @Throws(InternalDataException::class)
    override fun findAll(): List<Z> {
        return extensionDataFetcher.fetchAll()
            .map(mapper.toDomainEntityList())
            .blockingGet()
    }

    @WorkerThread
    @Throws(InternalDataException::class)
    override fun store(extensions: List<Z>) {
        val local = mapper.toDataEntityList()(extensions)
        workoutExtensionDao.insertList(local)
    }

    @WorkerThread
    @Throws(InternalDataException::class)
    override fun removeByWorkoutId(workoutId: Int) {
        extensionDataFetcher.fetchById(workoutId)
            .blockingGet()
            ?.let(workoutExtensionDao::removeExtension)
    }

    @WorkerThread
    @Throws(InternalDataException::class)
    override fun removeByWorkoutIds(workoutIds: Collection<Int>) {
        for (id in workoutIds) {
            removeByWorkoutId(id)
        }
    }

    @WorkerThread
    @Throws(InternalDataException::class)
    override fun findByWorkoutId(workoutId: Int): Z? {
        return extensionDataFetcher.fetchById(workoutId)
            .map(mapper.toDomainEntity())
            .blockingGet()
    }

    @WorkerThread
    @Throws(InternalDataException::class)
    override fun findByWorkoutIds(workoutIds: Collection<Int>): List<Z> =
        extensionDataFetcher.fetchByIds(workoutIds)
            .map { mapper.toDomainEntity()(it) }
}
