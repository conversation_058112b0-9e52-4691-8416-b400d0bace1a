package com.stt.android.controllers

import androidx.annotation.WorkerThread
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.exceptions.InternalDataException

interface ExtensionDataAccess<T : WorkoutExtension> {
    @WorkerThread
    @Throws(InternalDataException::class)
    fun store(extension: T)

    @WorkerThread
    @Throws(InternalDataException::class)
    fun findAll(): List<T>

    @WorkerThread
    @Throws(InternalDataException::class)
    fun store(extensions: List<T>)

    @WorkerThread
    @Throws(InternalDataException::class)
    fun removeByWorkoutId(workoutId: Int)

    @WorkerThread
    @Throws(InternalDataException::class)
    fun removeByWorkoutIds(workoutIds: Collection<Int>)

    @WorkerThread
    @Throws(InternalDataException::class)
    fun findByWorkoutId(workoutId: Int): T?

    @WorkerThread
    @Throws(InternalDataException::class)
    fun findByWorkoutIds(workoutIds: Collection<Int>): List<T>
}
