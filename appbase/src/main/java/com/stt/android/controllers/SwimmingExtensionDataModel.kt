package com.stt.android.controllers

import com.stt.android.data.source.local.swimmingextension.LocalSwimmingExtension
import com.stt.android.domain.user.workoutextension.BackendSwimmingHeaderExtension
import com.stt.android.domain.workouts.extensions.SwimmingExtension
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.remote.extensions.ExtensionsRemoteApi
import com.stt.android.utils.firstOfType
import javax.inject.Inject

class SwimmingExtensionDataModel @Inject constructor(
    workoutHeaderController: WorkoutHeaderController,
    swimmingExtensionDataAccess: ExtensionDataAccessRoomDb<LocalSwimmingExtension, SwimmingExtension>,
    extensionsRemoteApi: ExtensionsRemoteApi,
    extensionDataAccessMap: Map<Class<out WorkoutExtension>, ExtensionDataAccess<out WorkoutExtension>>
) : ExtensionDataModel<SwimmingExtension>(
    swimmingExtensionDataAccess,
    workoutHeaderController,
    extensionsRemoteApi,
    extensionDataAccessMap
) {

    override fun getBackendExtensionType(): String? = BackendSwimmingHeaderExtension.TYPE

    override fun findExtension(extensions: List<WorkoutExtension>): SwimmingExtension? =
        extensions.firstOfType()
}
