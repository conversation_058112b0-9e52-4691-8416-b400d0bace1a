package com.stt.android.controllers

import com.stt.android.TestOpen
import com.stt.android.domain.user.workoutextension.FitnessExtension
import com.stt.android.domain.user.workoutextension.IntensityExtension
import com.stt.android.domain.user.workoutextension.SlopeSkiSummary
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.DiveExtension
import com.stt.android.domain.workouts.extensions.JumpRopeExtension
import com.stt.android.domain.workouts.extensions.SummaryExtension
import com.stt.android.domain.workouts.extensions.SwimmingExtension
import com.stt.android.domain.workouts.extensions.WeatherExtension
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.exceptions.InternalDataException
import com.stt.android.ski.SlopeSkiCalculator
import timber.log.Timber
import javax.inject.Inject

/**
 * This class encapsulates all operations done on workout extensions in [SessionController]
 */
@TestOpen
class WorkoutExtensionDataModels
@Inject
constructor(
    val slopeSkiDataModel: SlopeSkiDataModel,
    val summaryExtensionDataModel: SummaryExtensionDataModel,
    val fitnessExtensionDataModel: FitnessExtensionDataModel,
    val intensityExtensionDataModel: IntensityExtensionDataModel,
    val diveExtensionDataModel: DiveExtensionDataModel,
    val swimmingExtensionDataModel: SwimmingExtensionDataModel,
    val weatherExtensionDataModel: WeatherExtensionDataModel,
    val jumpRopeExtensionDataModel: JumpRopeExtensionDataModel,
) {

    @Throws(InternalDataException::class)
    fun buildExtensions(
        workoutHeader: WorkoutHeader
    ): List<WorkoutExtension> {
        return listOfNotNull(
            slopeSkiDataModel.findByWorkoutId(workoutHeader.id),
            summaryExtensionDataModel.findByWorkoutId(workoutHeader.id),
            fitnessExtensionDataModel.findByWorkoutId(workoutHeader.id),
            intensityExtensionDataModel.findByWorkoutId(workoutHeader.id),
            diveExtensionDataModel.findByWorkoutId(workoutHeader.id),
            swimmingExtensionDataModel.findByWorkoutId(workoutHeader.id),
            weatherExtensionDataModel.findByWorkoutId(workoutHeader.id),
            jumpRopeExtensionDataModel.findByWorkoutId(workoutHeader.id)
        )
    }

    @Throws(InternalDataException::class)
    fun buildExtensions(
        workoutHeader: WorkoutHeader,
        geopoints: List<WorkoutGeoPoint>?
    ): List<WorkoutExtension> {
        val extensions = mutableListOf<WorkoutExtension>()
        // Manual workouts don't have extensions at the moment
        if (!workoutHeader.manuallyAdded) {
            if (workoutHeader.activityType.isSlopeSki) {
                try {
                    slopeSkiDataModel.findByWorkoutId(workoutHeader.id)?.let {
                        extensions.add(it)
                    }
                } catch (e: InternalDataException) {
                    Timber.w(e, "Did not find SlopeSkiExtension for workout")
                }
                val hasSlopeSkiExtension =
                    extensions.any { extension -> extension is SlopeSkiSummary }
                if (!hasSlopeSkiExtension) {
                    val slopeSkiCalculator = SlopeSkiCalculator()
                    geopoints?.let {
                        it.forEach { geoPoint ->
                            slopeSkiCalculator.addObservation(
                                geoPoint.millisecondsInWorkout,
                                geoPoint.totalDistance,
                                geoPoint.altitude,
                                geoPoint.speedMetersPerSecond.toDouble()
                            )
                        }
                        extensions.add(
                            slopeSkiCalculator.calculateRuns().toSlopeSkiSummary(workoutHeader.id)
                        )
                    }
                }
            }

            try {
                summaryExtensionDataModel.findByWorkoutId(workoutHeader.id)?.let {
                    extensions.add(it)
                }
            } catch (e: InternalDataException) {
                Timber.w(e, "Did not find SummaryExtension for workout")
            }

            try {
                fitnessExtensionDataModel.findByWorkoutId(workoutHeader.id)?.let {
                    extensions.add(it)
                }
            } catch (e: InternalDataException) {
                Timber.w(e, "Did not find FitnessExtension for workout")
            }

            try {
                intensityExtensionDataModel.findByWorkoutId(workoutHeader.id)?.let {
                    extensions.add(it)
                }
            } catch (e: InternalDataException) {
                Timber.w(e, "Did not find IntensityExtension for workout")
            }

            try {
                diveExtensionDataModel.findByWorkoutId(workoutHeader.id)?.let {
                    extensions.add(it)
                }
            } catch (e: InternalDataException) {
                Timber.w(e, "Did not find DiveExtension for workout")
            }

            try {
                swimmingExtensionDataModel.findByWorkoutId(workoutHeader.id)?.let {
                    extensions.add(it)
                }
            } catch (e: InternalDataException) {
                Timber.w(e, "Did not find SwimmingExtension for workout")
            }

            try {
                weatherExtensionDataModel.findByWorkoutId(workoutHeader.id)?.let {
                    extensions.add(it)
                }
            } catch (e: InternalDataException) {
                Timber.w(e, "Did not find WeatherExtension for workout")
            }

            try {
                jumpRopeExtensionDataModel.findByWorkoutId(workoutHeader.id)?.let {
                    extensions.add(it)
                }
            } catch (e: InternalDataException) {
                Timber.w(e, "Did not find JumpRopeExtension for workout")
            }
        }
        return extensions
    }

    @Throws(InternalDataException::class)
    fun storeExtensions(extensions: List<WorkoutExtension>) {
        if (extensions.isNotEmpty()) {
            val slopeSkiSummaries = mutableListOf<SlopeSkiSummary>()
            val summaryExtensions = mutableListOf<SummaryExtension>()
            val fitnessExtensions = mutableListOf<FitnessExtension>()
            val intensityExtensions = mutableListOf<IntensityExtension>()
            val diveExtensions = mutableListOf<DiveExtension>()
            val swimmingExtensions = mutableListOf<SwimmingExtension>()
            val weatherExtensions = mutableListOf<WeatherExtension>()
            val jumpRopeExtensions = mutableListOf<JumpRopeExtension>()
            extensions.forEach { extension ->
                when (extension.type) {
                    WorkoutExtension.TYPE_SLOPE_SKI -> slopeSkiSummaries.add(extension as SlopeSkiSummary)
                    WorkoutExtension.TYPE_WORKOUT_SUMMARY -> summaryExtensions.add(extension as SummaryExtension)
                    WorkoutExtension.TYPE_FITNESS -> fitnessExtensions.add(extension as FitnessExtension)
                    WorkoutExtension.TYPE_WORKOUT_INTENSITY -> intensityExtensions.add(extension as IntensityExtension)
                    WorkoutExtension.TYPE_DIVE -> diveExtensions.add(extension as DiveExtension)
                    WorkoutExtension.TYPE_SWIMMING -> swimmingExtensions.add(extension as SwimmingExtension)
                    WorkoutExtension.TYPE_WEATHER -> weatherExtensions.add(extension as WeatherExtension)
                    WorkoutExtension.TYPE_JUMP_ROPE -> jumpRopeExtensions.add(extension as JumpRopeExtension)
                }
            }
            slopeSkiDataModel.store(slopeSkiSummaries)
            summaryExtensionDataModel.store(summaryExtensions)
            fitnessExtensionDataModel.store(fitnessExtensions)
            intensityExtensionDataModel.store(intensityExtensions)
            diveExtensionDataModel.store(diveExtensions)
            swimmingExtensionDataModel.store(swimmingExtensions)
            weatherExtensionDataModel.store(weatherExtensions)
            jumpRopeExtensionDataModel.store(jumpRopeExtensions)
        }
    }

    /**
     * @param forceDeleteNotPrefetched forces deletion of workout extensions that are not pre-fetched in backend sync
     */
    @Throws(InternalDataException::class)
    fun removeExtensionsByWorkoutIds(
        workoutIds: Collection<Int>,
        forceDeleteNotPrefetched: Boolean
    ) {
        slopeSkiDataModel.removeByWorkoutIds(workoutIds)
        summaryExtensionDataModel.removeByWorkoutIds(workoutIds)
        fitnessExtensionDataModel.removeByWorkoutIds(workoutIds)
        intensityExtensionDataModel.removeByWorkoutIds(workoutIds)
        if (forceDeleteNotPrefetched) {
            diveExtensionDataModel.removeByWorkoutIds(workoutIds)
            swimmingExtensionDataModel.removeByWorkoutIds(workoutIds)
        }
        weatherExtensionDataModel.removeByWorkoutIds(workoutIds)
        jumpRopeExtensionDataModel.removeByWorkoutIds(workoutIds)
    }
}
