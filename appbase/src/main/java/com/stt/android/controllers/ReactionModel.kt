package com.stt.android.controllers

import androidx.annotation.WorkerThread
import com.j256.ormlite.dao.Dao
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.database.DatabaseHelper
import com.stt.android.domain.user.Reaction
import com.stt.android.domain.user.ReactionSummary
import com.stt.android.domain.user.UserDataSource
import com.stt.android.domain.user.follow.IsFolloweeUseCase
import com.stt.android.exceptions.InternalDataException
import io.reactivex.Completable
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.sql.SQLException
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ReactionModel @Inject constructor(
    helper: <PERSON><PERSON><PERSON><PERSON>,
    private val currentUserController: <PERSON><PERSON>ser<PERSON>ontroller,
    private val backendController: Backend<PERSON>ontroller,
    private val userDataSource: UserDataSource,
    private val workoutHeaderController: WorkoutHeaderController,
    private val isFolloweeUseCase: IsFolloweeUseCase,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) {
    val summaryDao: Dao<ReactionSummary, Long>
    val reactionDao: Dao<Reaction, Long>

    init {
        try {
            summaryDao = helper.getDao(ReactionSummary::class.java)
            reactionDao = helper.getDao(Reaction::class.java)
        } catch (e: SQLException) {
            throw RuntimeException(e)
        }
    }

    @WorkerThread
    @Throws(InternalDataException::class)
    fun store(reactions: List<ReactionSummary>) {
        try {
            summaryDao.callBatchTasks {
                for (reaction in reactions) {
                    summaryDao.createOrUpdate(reaction)
                }
            }
        } catch (e: Exception) {
            throw InternalDataException("Unable to store reactions to local database", e)
        }
    }

    @WorkerThread
    @Throws(InternalDataException::class)
    fun removeSynced() {
        try {
            val db = reactionDao.deleteBuilder()
            db.where()
                .isNotNull(Reaction.DbFields.KEY)
                .and()
                .eq(Reaction.DbFields.LOCALLY_CHANGED, false)
            reactionDao.delete(db.prepare())
            summaryDao.deleteBuilder().delete()
        } catch (e: SQLException) {
            throw InternalDataException("Unable to delete reactions from local database", e)
        }
    }

    @WorkerThread
    @Throws(InternalDataException::class)
    fun storeReactions(reactions: List<Reaction>) {
        try {
            reactionDao.callBatchTasks {
                for (reaction in reactions) {
                    reactionDao.createOrUpdate(reaction)
                }
            }
        } catch (e: Exception) {
            throw InternalDataException("Unable to store reactions to local database", e)
        }
    }

    @WorkerThread
    @Throws(InternalDataException::class)
    fun remove(reactions: Collection<Reaction>) {
        try {
            reactionDao.delete(reactions)
        } catch (e: SQLException) {
            throw InternalDataException("Unable to delete reactions from local database", e)
        }
    }

    @WorkerThread
    @Throws(InternalDataException::class)
    fun removeDuplicates(reactions: Collection<Reaction>) {
        val duplicateReactions = reactions.filter { isDuplicate(it) }
        remove(duplicateReactions)
    }

    @WorkerThread
    @Throws(InternalDataException::class)
    fun removeSyncedReactionsByWorkoutKey(
        workoutKey: String,
        @ReactionSummary.ReactionType reactionType: String,
    ) {
        try {
            val db = reactionDao.deleteBuilder()
            db.where()
                .eq(Reaction.DbFields.WORKOUT_KEY, workoutKey)
                .and()
                .eq(Reaction.DbFields.REACTION, reactionType)
                .and()
                .eq(Reaction.DbFields.LOCALLY_CHANGED, false)
            reactionDao.delete(db.prepare())
        } catch (e: SQLException) {
            throw InternalDataException("Unable to delete reactions from local database", e)
        }
    }

    @WorkerThread
    @Throws(InternalDataException::class)
    fun findDeletedReactions(userName: String): List<Reaction> {
        return try {
            val qb = reactionDao.queryBuilder()
            qb.where()
                .eq(Reaction.DbFields.DELETED, true)
                .and()
                .eq(Reaction.DbFields.USER_NAME, userName)
            reactionDao.query(qb.prepare())
        } catch (e: Exception) {
            throw InternalDataException("Unable to find reactions from local database", e)
        }
    }

    @WorkerThread
    @Throws(InternalDataException::class)
    fun findUnsyncedNotDeletedReactionsByUserName(userName: String): List<Reaction> {
        return try {
            val qb = reactionDao.queryBuilder()
            qb.where()
                .isNull(Reaction.DbFields.KEY)
                .and()
                .eq(Reaction.DbFields.USER_NAME, userName)
                .and()
                .eq(Reaction.DbFields.DELETED, false)
            reactionDao.query(qb.prepare())
        } catch (e: Exception) {
            throw InternalDataException("Unable to find reactions from local database", e)
        }
    }

    private fun findUnsyncedReactionsInWorkoutByUserName(
        workoutKey: String,
        @ReactionSummary.ReactionType reactionType: String,
        username: String,
        deletedFieldValue: Boolean
    ): List<Reaction> {
        if (workoutKey.isEmpty()) {
            return emptyList()
        }
        return try {
            val qb = reactionDao.queryBuilder()
            qb.where()
                .eq(Reaction.DbFields.WORKOUT_KEY, workoutKey)
                .and()
                .eq(Reaction.DbFields.REACTION, reactionType)
                .and()
                .eq(Reaction.DbFields.LOCALLY_CHANGED, true)
                .and()
                .eq(Reaction.DbFields.USER_NAME, username)
                .and()
                .eq(Reaction.DbFields.DELETED, deletedFieldValue)
            reactionDao.query(qb.prepare())
        } catch (_: Exception) {
            emptyList()
        }
    }

    private fun findUnsyncedDeletedReactionsInWorkoutByUserName(
        workoutKey: String,
        @ReactionSummary.ReactionType reactionType: String,
        username: String
    ): List<Reaction> {
        return findUnsyncedReactionsInWorkoutByUserName(workoutKey, reactionType, username, true)
    }

    private fun findUnsyncedAddedReactionsInWorkoutByUserName(
        workoutKey: String,
        @ReactionSummary.ReactionType reactionType: String,
        username: String
    ): List<Reaction> {
        return findUnsyncedReactionsInWorkoutByUserName(workoutKey, reactionType, username, false)
    }

    /**
     * Find if there is ReactionSummaries in which the user has reacted already and the reaction
     * is the same as in compared reaction
     */
    @WorkerThread
    @Throws(InternalDataException::class)
    fun isDuplicate(reaction: Reaction): Boolean {
        if (!reaction.isDeleted) {
            return try {
                val qb = summaryDao.queryBuilder()
                qb.where()
                    .eq(ReactionSummary.DbFields.USER_REACTED, true)
                    .and()
                    .eq(ReactionSummary.DbFields.WORKOUT_KEY, reaction.workoutKey)
                    .and()
                    .eq(ReactionSummary.DbFields.REACTION, reaction.reaction)
                qb.setCountOf(true)
                summaryDao.countOf(qb.prepare()) > 0
            } catch (e: Exception) {
                throw InternalDataException(
                    "Unable to find duplicate reactions from local database",
                    e
                )
            }
        }
        return false
    }

    @WorkerThread
    @Throws(InternalDataException::class)
    fun findSummary(
        workoutKey: String,
        @ReactionSummary.ReactionType reactionType: String
    ): ReactionSummary? {
        if (workoutKey.isEmpty()) {
            return null
        }
        return try {
            val qb = summaryDao.queryBuilder()
            qb.where()
                .eq(ReactionSummary.DbFields.WORKOUT_KEY, workoutKey)
                .and()
                .eq(ReactionSummary.DbFields.REACTION, reactionType)
            summaryDao.queryForFirst(qb.prepare())
        } catch (e: Exception) {
            throw InternalDataException("Unable to load reactions from local database", e)
        }
    }

    /**
     * Returns a map from workout keys to the reaction summary of that workout.
     */
    suspend fun findSummary(
        workoutKeys: List<String>,
        @ReactionSummary.ReactionType reactionType: String
    ): Map<String, ReactionSummary> = withContext(coroutinesDispatchers.io) {
        if (workoutKeys.isEmpty()) {
            return@withContext emptyMap()
        }

        val qb = summaryDao.queryBuilder()
        qb.where()
            .`in`(ReactionSummary.DbFields.WORKOUT_KEY, workoutKeys)
            .and()
            .eq(ReactionSummary.DbFields.REACTION, reactionType)
        summaryDao.query(qb.prepare())
            .associateBy(ReactionSummary::getWorkoutKey)
    }

    fun addReaction(reactionSummary: ReactionSummary): Completable = Completable.fromCallable {
        addReactionBlocking(reactionSummary)
    }

    @Throws(Exception::class)
    fun addReactionBlocking(reactionSummary: ReactionSummary): ReactionSummary =
        reactionDao.callBatchTasks {
            val currentUser =
                currentUserController.currentUser
            val newReaction =
                Reaction.local(
                    reactionSummary.workoutKey,
                    reactionSummary.reaction,
                    currentUser.username,
                    currentUser.realNameOrUsername,
                    currentUser.profileImageUrl ?: "",
                    System.currentTimeMillis(),
                    false
                )
            reactionDao.createOrUpdate(newReaction)
            val updatedReactionSummary = reactionSummary.toBuilder()
                .userReacted(true)
                .count(reactionSummary.count + 1)
                .build()
            summaryDao.createOrUpdate(updatedReactionSummary)
            updatedReactionSummary
        }

    fun removeReaction(reactionSummary: ReactionSummary): Completable = Completable.fromCallable {
        removeReactionBlocking(reactionSummary)
    }

    @Throws(Exception::class)
    fun removeReactionBlocking(reactionSummary: ReactionSummary): ReactionSummary =
        reactionDao.callBatchTasks {
            val currentUser =
                currentUserController.currentUser
            val newReaction =
                Reaction.local(
                    reactionSummary.workoutKey,
                    reactionSummary.reaction,
                    currentUser.username,
                    currentUser.realNameOrUsername,
                    currentUser.profileImageUrl ?: "",
                    System.currentTimeMillis(),
                    true
                )
            reactionDao.createOrUpdate(newReaction)
            val updatedReactionSummary = reactionSummary.toBuilder()
                .userReacted(false)
                .count(reactionSummary.count - 1)
                .build()
            summaryDao.createOrUpdate(updatedReactionSummary)
            updatedReactionSummary
        }

    fun loadReactions(
        workoutKey: String,
        @ReactionSummary.ReactionType reactionType: String,
    ): Flow<List<Reaction>> = flow {
        if (workoutKey.isEmpty()) {
            emit(emptyList())
            return@flow
        }

        runSuspendCatching {
            emit(loadReactionsFromLocal(workoutKey, reactionType))
        }.onFailure { e ->
            Timber.w(e, "Failed to load reactions from local")
        }

        runSuspendCatching {
            emit(loadReactionsFromRemote(workoutKey, reactionType))
        }.onFailure { e ->
            Timber.w(e, "Failed to load reactions from remote")
            emit(emptyList())
        }
    }

    suspend fun loadReactionsFromLocal(
        workoutKey: String,
        @ReactionSummary.ReactionType reactionType: String,
    ): List<Reaction> = withContext(coroutinesDispatchers.io) {
        if (workoutKey.isEmpty()) {
            return@withContext emptyList()
        }

        val qb = reactionDao.queryBuilder()
        qb.orderBy(Reaction.DbFields.TIMESTAMP, false)
            .where()
            .eq(Reaction.DbFields.WORKOUT_KEY, workoutKey)
            .and()
            .eq(Reaction.DbFields.REACTION, reactionType)
            .and()
            .eq(Reaction.DbFields.DELETED, false)
        reactionDao.query(qb.prepare())
            .combineWithLocalUsers()
    }

    // Make sure we have a consistent list of user real name and profile pictures in the reaction list.
    private suspend fun List<Reaction>.combineWithLocalUsers(): List<Reaction> =  withContext(coroutinesDispatchers.io) {
        val users = userDataSource.findLocalUsers(usernames = map(Reaction::getUserName).toSet())
        map { reaction ->
            users[reaction.userName]?.let { user ->
                reaction.toBuilder()
                    .userRealName(user.realNameOrUsername)
                    .userProfilePictureUrl(user.profileImageUrl)
                    .build()
            } ?: reaction
        }
    }

    suspend fun loadReactionsFromRemote(
        workoutKey: String,
        @ReactionSummary.ReactionType reactionType: String,
    ): List<Reaction> = withContext(coroutinesDispatchers.io) {
        if (workoutKey.isEmpty()) {
            return@withContext emptyList()
        }

        val isOwnOrFriendsWorkoutAsync = async {
            val workoutOwnerUsername = workoutHeaderController.find(workoutKey)
                ?.username
                ?: return@async false
            currentUserController.username == workoutOwnerUsername ||
                isFolloweeUseCase.isFollowee(workoutOwnerUsername)
        }

        val fetchedReactions = backendController.fetchWorkoutReactions(currentUserController.session, workoutKey)
            .filter { it.reaction == reactionType }
            .combineWithLocalUsers()
            .sortedByDescending { it.timestamp }

        val isOwnOrFriendsWorkout = isOwnOrFriendsWorkoutAsync.await()
        if (isOwnOrFriendsWorkout) {
            removeSyncedReactionsByWorkoutKey(workoutKey, reactionType)
            filterUnsyncedDeletedReactionByUser(fetchedReactions, workoutKey, reactionType)
                .let { filtered -> fillUnsyncedAddedReactionByUser(filtered, workoutKey, reactionType) }
                .let { filtered -> storeReactions(filtered) }
        }

        return@withContext fetchedReactions
    }

    @WorkerThread
    private fun filterUnsyncedDeletedReactionByUser(
        reactions: List<Reaction>,
        workoutKey: String,
        @ReactionSummary.ReactionType reactionType: String
    ): List<Reaction> {
        val unsyncedDeletedByUser = findUnsyncedDeletedReactionsInWorkoutByUserName(
            workoutKey,
            reactionType,
            currentUserController.username,
        )

        return if (unsyncedDeletedByUser.isEmpty()) {
            // There were no deleted, locally changed reactions from the current user
            reactions
        } else {
            // There was a deleted, locally changed reaction. Since we only support one reaction
            // type and one user can only have a single reaction for a workout, we can filter
            // out any reactions by the current user from the list received from backend.
            reactions.filter {
                it.userName != currentUserController.username
            }
        }
    }

    @WorkerThread
    private fun fillUnsyncedAddedReactionByUser(
        reactions: List<Reaction>,
        workoutKey: String,
        @ReactionSummary.ReactionType reactionType: String
    ): List<Reaction> {
        val unsyncedAddedByUser = findUnsyncedAddedReactionsInWorkoutByUserName(
            workoutKey,
            reactionType,
            currentUserController.username,
        )

        return if (unsyncedAddedByUser.isEmpty()) {
            // No locally added reactions, use list from backend from such
            reactions
        } else {
            // Add locally added reaction to list and filter out reactions from current user
            // from given list to avoid duplicates.
            unsyncedAddedByUser + reactions.filter {
                it.userName != currentUserController.username
            }
        }
    }
}
