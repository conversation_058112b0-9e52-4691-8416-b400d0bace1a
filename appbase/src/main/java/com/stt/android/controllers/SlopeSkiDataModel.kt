package com.stt.android.controllers

import com.stt.android.domain.user.workoutextension.BackendSlopeSkiWorkoutExtension
import com.stt.android.domain.user.workoutextension.SlopeSkiSummary
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.remote.extensions.ExtensionsRemoteApi
import com.stt.android.utils.firstOfType
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SlopeSkiDataModel @Inject constructor(
    workoutHeaderController: WorkoutHeaderController,
    slopeSkiSummaryExtensionDataAccess: ExtensionDataAccessOrmliteDb<SlopeSkiSummary>,
    extensionsRemoteApi: ExtensionsRemoteApi,
    extensionDataAccessMap: Map<Class<out WorkoutExtension>, ExtensionDataAccess<out WorkoutExtension>>,
) : ExtensionDataModel<SlopeSkiSummary>(
    slopeSkiSummaryExtensionDataAccess,
    workoutHeaderController,
    extensionsRemoteApi,
    extensionDataAccessMap,
) {
    override fun getBackendExtensionType(): String = BackendSlopeSkiWorkoutExtension.TYPE

    override fun findExtension(extensions: List<WorkoutExtension>): SlopeSkiSummary? =
        extensions.firstOfType()
}
