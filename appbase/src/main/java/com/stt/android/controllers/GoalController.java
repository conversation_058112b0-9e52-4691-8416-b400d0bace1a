package com.stt.android.controllers;

import androidx.annotation.NonNull;
import androidx.annotation.WorkerThread;
import com.stt.android.data.TimeUtilsKt;
import com.stt.android.data.goaldefinition.GoalDefinitionExtKt;
import com.stt.android.data.source.local.workout.LocalWorkoutHeader;
import com.stt.android.data.source.local.workout.WorkoutHeaderDao;
import com.stt.android.domain.goaldefinition.GoalDefinition;
import com.stt.android.domain.user.Goal;
import com.stt.android.domain.user.GoalSummary;
import com.stt.android.domain.workout.ActivityType;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.exceptions.InternalDataException;
import com.stt.android.utils.CalendarProvider;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashSet;
import java.util.List;
import javax.inject.Inject;

public class GoalController {

    private final WorkoutHeaderDao dao;
    private final WorkoutHeaderController workoutHeaderController;
    private final CalendarProvider calendarProvider;

    @Inject
    public GoalController(WorkoutHeaderDao dao,
        WorkoutHeaderController workoutHeaderController,
        CalendarProvider calendarProvider) {
        this.dao = dao;
        this.workoutHeaderController = workoutHeaderController;
        this.calendarProvider = calendarProvider;
    }

    @WorkerThread
    @NonNull
    public Goal loadCurrentGoal(GoalDefinition goalDefinition) {
        return fillGoalData(GoalDefinitionExtKt.createGoal(
            goalDefinition, System.currentTimeMillis(), calendarProvider));
    }

    private Goal fillGoalData(Goal goal) {
        int achieved = 0;
        int totalTime = 0;
        List<ActivityType> activities;
        List<Integer> workoutIds = new ArrayList<>();
        HashSet<Integer> activityIds = new HashSet<>();
        GoalDefinition goalDefinition = goal.goalDefinition;

        List<LocalWorkoutHeader> localWorkoutHeaders = dao.loadLatestWorkoutsOfActivityTypes(
            goalDefinition.getUserName(),
            goalDefinition.getActivityIds().isEmpty() ? null : goalDefinition.getActivityIds(),
            -1,
            null,
            goal.getStartTime(),
            goal.getEndTime()
        );
        for (LocalWorkoutHeader localWorkoutHeader : localWorkoutHeaders) {
            workoutIds.add(localWorkoutHeader.getId());
            activityIds.add(localWorkoutHeader.getActivityId());
            switch (goalDefinition.getType()) {
                case DURATION:
                    achieved += localWorkoutHeader.getTotalTime();
                    break;
                case DISTANCE:
                    achieved += localWorkoutHeader.getTotalDistance();
                    break;
                case WORKOUTS:
                    totalTime += localWorkoutHeader.getTotalTime();
                    achieved++;
                    break;
                case ENERGY:
                    achieved += localWorkoutHeader.getEnergyConsumption();
                    break;
                default:
                    throw new IllegalStateException("Illegal goal type.");
            }
        }
        activities = new ArrayList<>(activityIds.size());
        for (Integer activityId : activityIds) {
            activities.add(ActivityType.valueOf(activityId));
        }

        return goal.update(achieved, totalTime, activities, workoutIds);
    }

    @NonNull
    @WorkerThread
    public GoalSummary loadGoalSummary(final GoalDefinition goalDefinition)
        throws InternalDataException {
        long periodEndTime = GoalDefinitionExtKt.getEndTimeOrMaxValue(goalDefinition);
        long now = System.currentTimeMillis();
        if (periodEndTime > now) {
            periodEndTime = now;
        }
        long goalDefinitionStartTime = goalDefinition.getStartTime();
        ArrayList<Goal> goals = new ArrayList<>();
        List<Goal> goalsBeforeTimeBegins;
        if (goalDefinition.getPeriod() == GoalDefinition.Period.CUSTOM) {
            // when fetching goal summary, the custom goal is split into weeks
            if (periodEndTime > goalDefinitionStartTime) {
                Calendar calendar = calendarProvider.getCalendar();
                calendar.setTimeInMillis(periodEndTime);
                calendar.set(Calendar.DAY_OF_WEEK, calendar.getFirstDayOfWeek());
                while (periodEndTime > goalDefinitionStartTime) {
                    long periodStartTime = calendar.getTimeInMillis();
                    Goal goal = GoalDefinitionExtKt.createGoal(goalDefinition, periodStartTime, periodEndTime, calendarProvider);
                    goals.add(fillGoalData(goal));

                    // makes sure the end time is the previous day
                    periodEndTime = goal.getStartTime() - 1;
                    calendar.add(Calendar.WEEK_OF_YEAR, -1);
                }
            }

            goalsBeforeTimeBegins =
                new ArrayList<>(); // custom goals don't have pre-history data
        } else {
            while (periodEndTime > goalDefinitionStartTime) {
                Goal goal = fillGoalData(GoalDefinitionExtKt.createGoal(goalDefinition, periodEndTime, calendarProvider));
                goals.add(goal);
                periodEndTime =
                    goal.getStartTime() - 1; // makes sure the end time is the previous day
            }

            goalsBeforeTimeBegins = new ArrayList<>();
            WorkoutHeader oldestWorkout =
                workoutHeaderController.findOldestNotDeletedWorkout(
                    goalDefinition.getUserName(), TimeUtilsKt.getReleasedUTCMilli());
            long firstWorkoutTimestamp = oldestWorkout == null ? System.currentTimeMillis()
                : oldestWorkout.getStartTime();
            while (periodEndTime >= firstWorkoutTimestamp) {
                Goal goal =
                    fillGoalData(GoalDefinitionExtKt.createGoalBeforeTimeBegins(goalDefinition, periodEndTime, calendarProvider));
                goalsBeforeTimeBegins.add(goal);
                periodEndTime = goal.getStartTime() - 1; // makes sure the end time is the
                // previous day
            }
        }
        return new GoalSummary(goalDefinition, goals, goalsBeforeTimeBegins);
    }
}
