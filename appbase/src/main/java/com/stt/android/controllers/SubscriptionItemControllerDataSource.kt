package com.stt.android.controllers

import androidx.annotation.WorkerThread
import com.stt.android.domain.UserSession
import com.stt.android.domain.user.SubscriptionItem
import com.stt.android.domain.user.UserSubscription
import com.stt.android.exceptions.InternalDataException
import kotlinx.coroutines.flow.Flow

interface SubscriptionItemControllerDataSource {
    suspend fun store(subscriptionItem: SubscriptionItem)

    suspend fun loadAllUserSubscriptions(): List<UserSubscription>
    suspend fun loadValidSubscription(): UserSubscription?
    fun validSubscriptionFlow(): Flow<UserSubscription?>

    suspend fun empty()

    suspend fun refreshUserSubscriptions(session: UserSession)

    suspend fun refreshUserSubscriptions()
}
