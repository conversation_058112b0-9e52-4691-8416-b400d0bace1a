package com.stt.android.controllers

import com.stt.android.TestOpen
import com.stt.android.data.source.local.intensityextension.LocalIntensityExtension
import com.stt.android.domain.user.workoutextension.BackendIntensityExtension
import com.stt.android.domain.user.workoutextension.IntensityExtension
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.remote.extensions.ExtensionsRemoteApi
import com.stt.android.utils.firstOfType
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class IntensityExtensionDataModel @Inject constructor(
    workoutHeaderController: WorkoutHeaderController,
    intensityExtensionDataAccess: ExtensionDataAccessRoomDb<LocalIntensityExtension, IntensityExtension>,
    extensionsRemoteApi: ExtensionsRemoteApi,
    extensionDataAccessMap: Map<Class<out WorkoutExtension>, ExtensionDataAccess<out WorkoutExtension>>,
) : ExtensionDataModel<IntensityExtension>(
    intensityExtensionDataAccess,
    workoutHeaderController,
    extensionsRemoteApi,
    extensionDataAccessMap
) {
    override fun getBackendExtensionType(): String? = BackendIntensityExtension.TYPE

    override fun findExtension(extensions: List<WorkoutExtension>): IntensityExtension? {
        return extensions.firstOfType()
    }
}
