package com.stt.android.controllers

import androidx.annotation.WorkerThread
import com.stt.android.data.usersettings.FcmTokenSynchronizer
import com.stt.android.data.usersettings.UserSettingsSynchronizer
import kotlinx.coroutines.runBlocking
import timber.log.Timber

/**
 * Syncs user settings and fcm token with the server
 *
 * This method is meant to be used only from SessionController as a bridge to Java.
 * Must be called from a worker thread.
 */
@WorkerThread
internal fun syncUserSettingsAndFcmToken(
    fcmTokenSynchronizer: FcmTokenSynchronizer,
    userSettingsSynchronizer: UserSettingsSynchronizer,
    isLoggingOut: Boolean = false
) = runBlocking {
    try {
        if (isLoggingOut) {
            Timber.d("Syncing user settings and unregistering fcm token")
            fcmTokenSynchronizer.removeFcmToken()
            userSettingsSynchronizer.syncUserSettings()
        } else {
            Timber.d("Fetching user settings and registering fcm token")
            fcmTokenSynchronizer.saveFcmToken()
            userSettingsSynchronizer.fetchAndStoreUserSettings()
        }
    } catch (ex: Exception) {
        Timber.e(ex, "Fatal error: could not sync user settings and fcm token")
    }
}
