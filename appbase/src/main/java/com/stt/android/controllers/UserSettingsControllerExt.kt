package com.stt.android.controllers

import com.stt.android.domain.user.UserSettings
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow

fun UserSettingsController.userSettings(): Flow<UserSettings> = callbackFlow {
    val listener = UserSettingsController.UpdateListener {
        trySend(settings)
    }
    addUpdateListener(listener)

    send(settings)

    awaitClose {
        removeUpdateListener(listener)
    }
}
