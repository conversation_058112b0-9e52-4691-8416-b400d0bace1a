package com.stt.android.controllers;

import android.text.TextUtils;
import androidx.annotation.NonNull;
import androidx.annotation.WorkerThread;
import com.j256.ormlite.dao.Dao;
import com.j256.ormlite.stmt.DeleteBuilder;
import com.j256.ormlite.stmt.QueryBuilder;
import com.stt.android.domain.database.DatabaseHelper;
import com.stt.android.exceptions.InternalDataException;
import com.stt.android.workoutdetail.comments.WorkoutComment;
import java.sql.SQLException;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;
import javax.inject.Inject;
import javax.inject.Singleton;
import rx.Completable;
import rx.Observable;
import rx.Single;
import rx.functions.Func1;
import timber.log.Timber;

@Singleton
public class WorkoutCommentController {
    private final Dao<WorkoutComment, Integer> dao;
    final BackendController backendController;
    final CurrentUserController currentUserController;

    @Inject
    public WorkoutCommentController(DatabaseHelper helper,
        BackendController backendController, CurrentUserController currentUserController) {
        try {
            this.dao = helper.getDao(WorkoutComment.class);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        this.backendController = backendController;
        this.currentUserController = currentUserController;
    }

    public Observable<List<WorkoutComment>> load(final String workoutKey) {
        if (TextUtils.isEmpty(workoutKey)) {
            // workout is not synced,there's no workout
            return Observable.just(null);
        }

        // We need to skip exceptions otherwise Observable.concat() would only call the first one.
        Func1<Throwable, Single<? extends List<WorkoutComment>>> nullOnException =
            throwable -> {
                Timber.w(throwable, "Error while retrieving workout comments: %s", workoutKey);
                return Single.just(null);
            };
        Single<List<WorkoutComment>> db =
            loadFromLocalCache(workoutKey).onErrorResumeNext(nullOnException);
        Single<List<WorkoutComment>> network =
            loadFromBackend(workoutKey).onErrorResumeNext(nullOnException);
        return Single
            // Emit from DB then network
            .concat(db, network)
            // And we don't want to emit nulls so let's filter them out
            .filter(new Func1<List<WorkoutComment>, Boolean>() {
                @Override
                public Boolean call(List<WorkoutComment> comments) {
                    return comments != null;
                }
            });
    }

    private Single<List<WorkoutComment>> loadFromLocalCache(final String workoutKey) {
        return Single.fromCallable(() -> find(workoutKey));
    }

    private Single<List<WorkoutComment>> loadFromBackend(final String workoutKey) {
        return Single.fromCallable(() -> {
            List<WorkoutComment> comments = backendController.fetchWorkoutComments(
                currentUserController.getSession(), workoutKey);
            comments.sort(Comparator.comparingLong(WorkoutComment::getTimestamp));
            return comments;
        }).doOnSuccess(comments -> {
            try {
                removeByWorkoutKey(workoutKey);
                store(comments);
            } catch (InternalDataException e) {
                Timber.e(e, "Failed to store workout comments to local database");
            }
        });
    }

    public Completable sendComment(final WorkoutComment workoutComment) {
        return Completable.create(subscriber -> {
            try {
                if (!currentUserController.isLoggedIn()) {
                    throw new IllegalStateException("No one's logged in");
                }
                backendController.pushNewWorkoutComment(
                    currentUserController.getSession(), workoutComment);
                store(Collections.singletonList(workoutComment));
                subscriber.onCompleted();
            } catch (Exception e) {
                subscriber.onError(e);
            }
        });
    }

    @NonNull
    @WorkerThread
    public List<WorkoutComment> find(String workoutKey) throws InternalDataException {
        if (TextUtils.isEmpty(workoutKey)) {
            return Collections.emptyList();
        }

        try {
            QueryBuilder<WorkoutComment, Integer> qb = dao.queryBuilder();
            qb.orderBy(WorkoutComment.DbFields.TIMESTAMP, true)
                .where()
                .eq(WorkoutComment.DbFields.WORKOUT_KEY, workoutKey);
            return dao.query(qb.prepare());
        } catch (Throwable e) {
            throw new InternalDataException("Unable to find workout comments from local database", e);
        }
    }

    /**
     * Returns a map from workout keys to all comments of that workout.
     */
    @NonNull
    @WorkerThread
    public Map<String, List<WorkoutComment>> find(@NonNull List<String> workoutKeys) throws InternalDataException {
        if (workoutKeys.isEmpty()) {
            return Collections.emptyMap();
        }
        try {
            QueryBuilder<WorkoutComment, Integer> qb = dao.queryBuilder();
            qb.orderBy(WorkoutComment.DbFields.TIMESTAMP, true)
                .where()
                .in(WorkoutComment.DbFields.WORKOUT_KEY, workoutKeys);
            return dao.query(qb.prepare())
                .stream()
                .collect(Collectors.groupingBy(WorkoutComment::getWorkoutKey));
        } catch (Throwable e) {
            throw new InternalDataException("Unable to find workout comments from local database", e);
        }
    }

    @WorkerThread
    public void store(final List<WorkoutComment> workoutComments) throws InternalDataException {
        try {
            dao.callBatchTasks(new Callable<Void>() {
                @Override
                public Void call() throws Exception {
                    for (WorkoutComment workoutComment : workoutComments) {
                        dao.createOrUpdate(workoutComment);
                    }
                    return null;
                }
            });
        } catch (Exception e) {
            throw new InternalDataException("Unable to store workout comments to local database",
                e);
        }
    }

    @WorkerThread
    public int removeByWorkoutKey(String workoutKey) throws InternalDataException {
        try {
            DeleteBuilder<WorkoutComment, Integer> db = dao.deleteBuilder();
            db.where().eq(WorkoutComment.DbFields.WORKOUT_KEY, workoutKey);
            return dao.delete(db.prepare());
        } catch (SQLException e) {
            throw new InternalDataException("Unable to delete workout comment from local database",
                e);
        }
    }

    @WorkerThread
    public int removeByCommentKey(String commentKey) throws InternalDataException {
        if (commentKey == null) {
            return 0;
        }

        try {
            DeleteBuilder<WorkoutComment, Integer> db = dao.deleteBuilder();
            db.where().eq(WorkoutComment.DbFields.KEY, commentKey);
            return dao.delete(db.prepare());
        } catch (SQLException e) {
            throw new InternalDataException("Unable to delete workout comment from local database",
                e);
        }
    }

    @WorkerThread
    public void empty() throws InternalDataException {
        try {
            dao.deleteBuilder().delete();
        } catch (SQLException e) {
            throw new InternalDataException("Unable to empty workout comments from local database",
                e);
        }
    }
}
