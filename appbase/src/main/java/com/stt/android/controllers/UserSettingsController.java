package com.stt.android.controllers;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.preference.PreferenceManager;
import com.squareup.moshi.JsonAdapter;
import com.squareup.moshi.Moshi;
import com.squareup.moshi.Types;
import com.stt.android.domain.user.UserSettings;
import com.stt.android.exceptions.InternalDataException;
import com.stt.android.sharedpreference.mapping.SharedPreference;
import com.stt.android.utils.FlavorUtils;
import com.stt.android.utils.STTConstants;
import dagger.hilt.android.qualifiers.ApplicationContext;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.locks.ReadWriteLock;
import javax.inject.Inject;
import javax.inject.Singleton;
import timber.log.Timber;

@Singleton
public class UserSettingsController {
    public static final String COUNTRY_CHINA = "CN";

    private static final Set<String> userSettingsSharedPrefsKeys = new HashSet<>();

    static {
        Field[] fields = UserSettings.class.getDeclaredFields();
        for (Field field : fields) {
            SharedPreference preference = field.getAnnotation(SharedPreference.class);
            // if no @SharedPreference, do nothing
            if (preference != null) {
                userSettingsSharedPrefsKeys.add(preference.value());
            }
        }
    }

    @NonNull
    private final Context appContext;
    @NonNull
    private final SharedPreferences sharedPreferences;
    @NonNull
    private final ReadWriteLock sessionLock;
    @NonNull
    private final Moshi moshi;
    @NonNull
    private final LocalBroadcastManager localBroadcastManager;

    @NonNull
    private UserSettings settings;

    private final CopyOnWriteArraySet<UpdateListener> updateListeners = new CopyOnWriteArraySet<>();

    @Inject
    public UserSettingsController(
        @ApplicationContext @NonNull Context appContext,
        @NonNull SharedPreferences sharedPreferences,
        @NonNull ReadWriteLock sessionLock,
        @NonNull Moshi moshi,
        @NonNull LocalBroadcastManager localBroadcastManager
    ) {
        this.appContext = appContext;
        this.sharedPreferences = sharedPreferences;
        this.sessionLock = sessionLock;
        this.moshi = moshi;
        this.localBroadcastManager = localBroadcastManager;

        boolean existingSettingsReadSuccess = false;
        try {
            this.sessionLock.writeLock().lock();

            UserSettingInitialization userSettingInitialization = initializeUserSetting();
            this.settings = userSettingInitialization.userSettings;
            if (userSettingInitialization.shouldStoreUserSettings) {
                this.settings = storeSettings(userSettingInitialization.userSettings);
            }
        } catch (InternalDataException e) {
            throw new RuntimeException(
                String.format(
                    "Failed to initialize UserSettingsController, successfully read previous settings: %b",
                    existingSettingsReadSuccess
                ),
                e
            );
        } finally {
            this.sessionLock.writeLock().unlock();
        }
    }

    @NonNull
    private UserSettingInitialization initializeUserSetting() throws InternalDataException {
        boolean shouldStoreUserSettings = false;
        UserSettings userSettings = readFromSharedPreferences();
        if (userSettings == null) {
            userSettings = UserSettings.defaults(appContext);
            shouldStoreUserSettings = true;
        }

        // in china flavor there is only china country
        // War Is Peace. Freedom Is Slavery. Ignorance Is Strength.
        String userCountry = userSettings.getCountry().toUpperCase(Locale.ROOT);
        if (FlavorUtils.INSTANCE.isSuuntoAppChina() && !COUNTRY_CHINA.equals(userCountry)) {
            userSettings = userSettings.setCountry(COUNTRY_CHINA);
            shouldStoreUserSettings = true;
        }
        // in the rest of the world there is no china, it is only a hoax
        if (!FlavorUtils.INSTANCE.isSuuntoAppChina() && COUNTRY_CHINA.equals(userCountry)) {
            userSettings = userSettings.setCountry("");
        }
        // setting default country
        if (userSettings.getCountry().isEmpty()) {
            userSettings = userSettings.setDefaultCountry(appContext);
            if (!userSettings.getCountry().isEmpty()) {
                shouldStoreUserSettings = true;
            }
        }

        // set default language if changed
        if (userSettings.getLanguage().isEmpty() ||
            !userSettings.getLanguage().equals(UserSettings.getDefaultLanguage())) {
            userSettings = userSettings.setDefaultLanguage();
            shouldStoreUserSettings = true;
        }

        // set default first day of the week if not in preferences
        if (!sharedPreferences.contains(UserSettings.FIRST_DAY_OF_THE_WEEK_KEY)) {
            userSettings = userSettings
                .setFirstDayOfTheWeek(UserSettings.getDefaultFirstDayOfTheWeek(appContext));
            shouldStoreUserSettings = true;
        }

        return new UserSettingInitialization(userSettings, shouldStoreUserSettings);
    }

    public void addUpdateListener(@NonNull UpdateListener updateListener) {
        updateListeners.add(updateListener);
    }

    public void removeUpdateListener(@NonNull UpdateListener updateListener) {
        updateListeners.remove(updateListener);
    }

    private static void markUserSettingsAsLocallyChanged(SharedPreferences sharedPreferences) {
        sharedPreferences.edit().putBoolean(UserSettings.LOCALLY_CHANGED_KEY, true).apply();
    }

    private static void saveUserProfileChangedTimestamp(SharedPreferences sharedPreferences, String key) {
        Timber.d("saveUserProfileChangedTimestamp, key:%s", key);
        String timestampKey = switch (key) {
            case UserSettings.BIRTH_DATE -> UserSettings.USER_SETTINGS_LAST_AGE_SAVED_TIMESTAMP_KEY;
            case UserSettings.GENDER -> UserSettings.USER_SETTINGS_LAST_GENDER_SAVED_TIMESTAMP_KEY;
            case UserSettings.WEIGHT -> UserSettings.USER_SETTINGS_LAST_WEIGHT_SAVED_TIMESTAMP_KEY;
            case UserSettings.HEIGHT -> UserSettings.USER_SETTINGS_LAST_HEIGHT_SAVED_TIMESTAMP_KEY;
            case UserSettings.HR_MAXIMUM_KEY ->
                UserSettings.USER_SETTINGS_LAST_MAX_HR_SAVED_TIMESTAMP_KEY;
            case UserSettings.HR_REST_KEY ->
                UserSettings.USER_SETTINGS_LAST_REST_HR_SAVED_TIMESTAMP_KEY;
            default -> null;
        };

        if (timestampKey != null) {
            sharedPreferences.edit().putLong(timestampKey, System.currentTimeMillis()).apply();
        }
    }

    @NonNull
    public UserSettings storeSettings(@NonNull UserSettings settings) throws InternalDataException {
        sessionLock.writeLock().lock();
        boolean dayOfWeekChanged = this.settings.getFirstDayOfTheWeek() != settings.getFirstDayOfTheWeek();
        try {
            this.settings = storeToSharedPreferences(settings);
            notifySettingsChanged(settings.isLocallyChanged(), dayOfWeekChanged);
            return this.settings;
        } finally {
            sessionLock.writeLock().unlock();
        }
    }

    private void notifySettingsChanged(boolean didLocalChanges, boolean dayOfWeekChanged) {
        for (UpdateListener listener : updateListeners) {
            listener.onSettingsStoredToPreferences(didLocalChanges);
        }
        if (dayOfWeekChanged) {
            localBroadcastManager.sendBroadcast(new Intent(STTConstants.BroadcastActions.USER_SETTINGS_FIRST_DAY_OF_WEEK_CHANGED));
        }
    }

    /**
     * Populates the UserSettings to shared preferences.
     *
     * @throws InternalDataException
     */
    private UserSettings storeToSharedPreferences(UserSettings settings)
        throws InternalDataException {
        try {
            Editor editor = sharedPreferences.edit();
            Field[] fields = UserSettings.class.getDeclaredFields();
            for (Field field : fields) {
                SharedPreference preference = field.getAnnotation(SharedPreference.class);
                Class<?> type = field.getType();
                // if no @SharedPreference, do nothing
                if (preference != null) {
                    field.setAccessible(true);
                    Object value = field.get(settings);
                    field.setAccessible(false);

                    if (value != null) {
                        Timber.v("Setting preference %s with value %s", preference.value(),
                            value.toString());
                        // enums stored as its name
                        if (type.isEnum()) {
                            Method name = type.getMethod("name");
                            value = ((String) name.invoke(value)).toLowerCase(Locale.US);
                        }
                        Class<?> valueType = value.getClass();
                        if (preference.storeAsString()) {
                            editor.putString(preference.value(), value.toString());
                        } else if (valueType == Boolean.class) {
                            editor.putBoolean(preference.value(), (Boolean) value);
                        } else if (valueType == Float.class) {
                            editor.putFloat(preference.value(), (Float) value);
                        } else if (valueType == Integer.class) {
                            editor.putInt(preference.value(), (Integer) value);
                        } else if (valueType == Long.class) {
                            editor.putLong(preference.value(), (Long) value);
                        } else if (valueType == String.class) {
                            editor.putString(preference.value(), (String) value);
                        } else if (valueType == String[].class) {
                            editor.putString(
                                preference.value(),
                                moshi.adapter(String[].class).toJson((String[]) value)
                            );
                        } else if (valueType == int[].class) {
                            editor.putString(
                                preference.value(),
                                moshi.adapter(int[].class).toJson((int[]) value)
                            );
                        } else if (preference.mapKeyType() != void.class && preference.mapValueType() != void.class) {
                            Type mapType = Types.newParameterizedType(Map.class, preference.mapKeyType(), preference.mapValueType());
                            editor.putString(preference.value(), moshi.adapter(mapType).toJson(value));
                        } else if (preference.jsonObjectType() != void.class) {
                            @SuppressWarnings("unchecked")
                            JsonAdapter<Object> adapter = (JsonAdapter<Object>) moshi.adapter(preference.jsonObjectType());
                            editor.putString(preference.value(),
                                adapter.toJson(value));
                        } else {
                            throw new InternalDataException("Invalid data type " + valueType);
                        }
                    }
                }
            }
            editor.putBoolean(UserSettings.USER_SETTINGS_SAVED_KEY, true);
            editor.putLong(UserSettings.USER_SETTINGS_LAST_SAVED_TIMESTAMP_KEY, System.currentTimeMillis());
            boolean result = editor.commit();
            if (!result) {
                throw new InternalDataException("Failed to commit settings to SharedPreferences");
            }
            return settings;
        } catch (IllegalArgumentException | InvocationTargetException | NoSuchMethodException |
            IllegalAccessException e) {
            throw new InternalDataException("Unable to store settings to SharedPreferences", e);
        }
    }

    /**
     * Checks if the provided key is a field in {@link UserSettings} annotated with {@link SharedPreference}
     * @param key The shared preference key
     * @return True if this key is a field in {@link UserSettings} annotated with {@link SharedPreference}
     */
    boolean isUserSettingsKey(String key) {
        return userSettingsSharedPrefsKeys.contains(key);
    }

    /**
     * Reads all the shared preferences related to user settings and constructs
     * a new UserSettings instance with the values read.
     *
     * @return a new UserSettings instance with the values read or null if any of the values can't be found
     * @throws InternalDataException if the type of the shared preference is invalid
     */
    @Nullable
    private UserSettings readFromSharedPreferences() throws InternalDataException {
        try {
            if (!sharedPreferences.getBoolean(UserSettings.USER_SETTINGS_SAVED_KEY, false)) {
                return null;
            }
            Map<String, ?> allPreferences = sharedPreferences.getAll();
            UserSettings settings = new UserSettings();
            Field[] fields = UserSettings.class.getDeclaredFields();
            for (Field field : fields) {
                SharedPreference preference = field.getAnnotation(SharedPreference.class);
                // if no @SharedPreference, do nothing
                if (preference != null) {
                    Object value = allPreferences.get(preference.value());
                    String defaultValue = preference.defaultValue();
                    field.setAccessible(true);
                    if (value != null) {
                        Class<?> type = field.getType();
                        Class<?> valueType = value.getClass();
                        // enums stored as its name, call valueOf with name
                        if (type.isEnum()) {
                            try {
                                value = type.getMethod("valueOf", String.class)
                                    .invoke(null, ((String) value).toUpperCase(Locale.US));
                            } catch (IllegalArgumentException e) {
                                Timber.w(e, "Using default value");
                                value = type.getMethod("valueOf", String.class)
                                    .invoke(null, (defaultValue).toUpperCase(Locale.US));
                            }
                        } else if (type == Boolean.class || type == boolean.class) {
                            try {
                                if (valueType != Boolean.class && preference.storeAsString()) {
                                    value = Boolean.valueOf((String) value);
                                }
                            } catch (NumberFormatException e) {
                                Timber.w(e, "Using default value");
                                value = Boolean.valueOf(defaultValue);
                            }
                        } else if (type == Float.class || type == float.class) {
                            try {
                                if (valueType != Float.class && preference.storeAsString()) {
                                    value = Float.valueOf((String) value);
                                }
                            } catch (NumberFormatException e) {
                                Timber.w(e, "Using default value");
                                value = Float.valueOf(defaultValue);
                            }
                        } else if (type == Double.class || type == double.class) {
                            try {
                                if (valueType != Double.class && preference.storeAsString()) {
                                    value = Double.valueOf((String) value);
                                }
                            } catch (NumberFormatException e) {
                                Timber.w(e, "Using default value");
                                value = Double.valueOf(defaultValue);
                            }
                        } else if (type == Integer.class || type == int.class) {
                            try {
                                if (valueType != Integer.class && preference.storeAsString()) {
                                    value = Integer.valueOf((String) value);
                                }
                            } catch (NumberFormatException e) {
                                Timber.w(e, "Using default value");
                                value = Integer.valueOf(defaultValue);
                            }
                        } else if (type == Long.class || type == long.class) {
                            try {
                                if (valueType != Long.class && preference.storeAsString()) {
                                    value = Long.valueOf((String) value);
                                }
                            } catch (NumberFormatException e) {
                                Timber.w(e, "Using default value");
                                value = Long.valueOf(defaultValue);
                            }
                        } else if (type == String[].class) {
                            try {
                                value = moshi.adapter(type).fromJson((String) value);
                            } catch (IOException e) {
                                Timber.w(e, "Using default value");
                                value = new String[]{};
                            }
                        } else if (type == int[].class) {
                            try {
                                value = moshi.adapter(type).fromJson((String) value);
                            } catch (IOException e) {
                                Timber.w(e, "Using default value");
                                value = new int[]{};
                            }
                        }  else if (preference.mapValueType() != void.class && preference.mapKeyType() != void.class) {
                            try {
                                Type mapType = Types.newParameterizedType(Map.class, preference.mapKeyType(), preference.mapValueType());
                                value = moshi.adapter(mapType).fromJson((String)value);
                            } catch (IOException e) {
                                Timber.w("Using default value");
                                value = new HashMap<>();
                            }
                        } else if (preference.jsonObjectType() != void.class) {
                            try {
                                value = moshi.adapter(preference.jsonObjectType())
                                    .fromJson((String) value);
                            } catch (IOException e) {
                                Timber.w("Using default value");
                                try {
                                    value = moshi.adapter(preference.jsonObjectType())
                                        .fromJson(preference.defaultValue());
                                } catch (IOException e1) {
                                    value = null;
                                }
                            }

                        }  else if (type != String.class) {
                            throw new InternalDataException("Invalid data type " + type);
                        }

                        // set field with the resolved value
                        field.set(settings, value);
                    } else {
                        Timber.w("Couldn't find user setting %s value in shared preferences.",
                            preference.value());
                    }

                    field.setAccessible(false);
                }
            }
            return settings;
        } catch (NoSuchMethodException | InvocationTargetException | IllegalAccessException |
            IllegalArgumentException e) {
            throw new InternalDataException("Unable to fetch settings from shared preferences", e);
        }
    }

    /**
     * <i>Non-Blocking method</i>
     */
    @NonNull
    public UserSettings getSettings() {
        return settings;
    }

    private void reloadFromSharedPrefs() throws InternalDataException {
        UserSettings userSettings = readFromSharedPreferences();
        if (userSettings != null) {
            storeSettings(userSettings);
        }
    }

    @NonNull
    public UserSettingsUpdater registerUserSettingsUpdater(Context context) {
        SharedPreferences defaultSharedPreferences =
            PreferenceManager.getDefaultSharedPreferences(context);
        UserSettingsUpdater userSettingsUpdater =
            new UserSettingsUpdater(sessionLock, this);
        defaultSharedPreferences.registerOnSharedPreferenceChangeListener(userSettingsUpdater);
        return userSettingsUpdater;
    }

    public void unregisterUserSettingsUpdater(Context context,
        @Nullable UserSettingsUpdater userSettingsUpdater) {
        if (userSettingsUpdater != null) {
            SharedPreferences defaultSharedPreferences =
                PreferenceManager.getDefaultSharedPreferences(context);
            defaultSharedPreferences.unregisterOnSharedPreferenceChangeListener(
                userSettingsUpdater);
        }
    }

    /**
     * @return timestamp at which these settings were saved
     */
    public long getSettingsLastSavedTimestamp() {
        return sharedPreferences.getLong(UserSettings.USER_SETTINGS_LAST_SAVED_TIMESTAMP_KEY, 0);
    }

    public static class UserSettingsUpdater implements SharedPreferences.OnSharedPreferenceChangeListener {
        @NonNull
        private final ReadWriteLock sessionLock;

        @NonNull
        private final UserSettingsController userSettingsController;

        UserSettingsUpdater(@NonNull ReadWriteLock sessionLock,
            @NonNull UserSettingsController userSettingsController) {
            this.sessionLock = sessionLock;
            this.userSettingsController = userSettingsController;
        }

        @Override
        public void onSharedPreferenceChanged(SharedPreferences sharedPreferences, String key) {
            if (UserSettings.LOCALLY_CHANGED_KEY.equals(key) ||
                !userSettingsController.isUserSettingsKey(key)) {
                return;
            }
            Timber.d("onSharedPreferenceChanged(): [ key = '%s' ] changed", key);
            sessionLock.writeLock().lock();
            sharedPreferences.unregisterOnSharedPreferenceChangeListener(this);
            try {
                UserSettingsController.markUserSettingsAsLocallyChanged(sharedPreferences);
                UserSettingsController.saveUserProfileChangedTimestamp(sharedPreferences, key);
                if (UserSettings.ALTITUDE_SOURCE.equals(key)) {
                    sharedPreferences.edit()
                        .putBoolean(UserSettings.ALTITUDE_SOURCE_SET_BY_USER, true)
                        .apply();
                }
                userSettingsController.reloadFromSharedPrefs();
                Timber.d("Updated user settings");
            } catch (InternalDataException e) {
                Timber.e(e, "Could not update user settings");
            } finally {
                sharedPreferences.registerOnSharedPreferenceChangeListener(this);
                sessionLock.writeLock().unlock();
            }
        }
    }

    public interface UpdateListener {
        /**
         * @param didLocalChanges If the changes were made locally or not. If false, the settings
         *                        were from backend and settings might have not actually changed
         */
        void onSettingsStoredToPreferences(boolean didLocalChanges);
    }

    private record UserSettingInitialization(
        @NonNull UserSettings userSettings, boolean shouldStoreUserSettings) {
    }
}
