package com.stt.android.controllers

import timber.log.Timber
import javax.inject.Inject

class TakeOwnershipOfUnsyncedWorkoutsUseCase @Inject constructor(
    private val workoutHeaderController: WorkoutHeaderController,
) {
    operator fun invoke(username: String) {
        // Take ownership of any unsynced workouts. These should be then synced to backend with the
        // new session.
        try {
            workoutHeaderController.takeOwnershipOfUnsyncedWorkouts(username)
        } catch (e: Exception) {
            Timber.w(e, "Failed to take ownership of unsynced workouts")
        }
    }
}
