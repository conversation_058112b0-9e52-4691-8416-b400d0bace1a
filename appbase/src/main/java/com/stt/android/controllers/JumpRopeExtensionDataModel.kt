package com.stt.android.controllers

import com.stt.android.data.source.local.jumpropeextension.LocalJumpRopeExtension
import com.stt.android.domain.workouts.extensions.JumpRopeExtension
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.remote.extensions.ExtensionsRemoteApi
import com.stt.android.remote.extensions.RemoteWorkoutExtension
import com.stt.android.utils.firstOfType
import javax.inject.Inject

class JumpRopeExtensionDataModel @Inject constructor(
    workoutHeaderController: WorkoutHeaderController,
    jumpRopeExtensionDataAccess: ExtensionDataAccessRoomDb<LocalJumpRopeExtension, JumpRopeExtension>,
    extensionsRemoteApi: ExtensionsRemoteApi,
    extensionDataAccessMap: Map<Class<out WorkoutExtension>, ExtensionDataAccess<out WorkoutExtension>>,
) : ExtensionDataModel<JumpRopeExtension>(
    jumpRopeExtensionDataAccess,
    workoutHeaderController,
    extensionsRemoteApi,
    extensionDataAccessMap
) {
    override fun getBackendExtensionType(): String = RemoteWorkoutExtension.Type.JUMP_ROPE_EXTENSION.value

    override fun findExtension(extensions: List<WorkoutExtension>): JumpRopeExtension? =
        extensions.firstOfType()
}
