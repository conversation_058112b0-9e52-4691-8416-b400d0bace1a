package com.stt.android.controllers

import com.stt.android.common.coroutines.ORMLITE
import com.stt.android.domain.user.workoutextension.SlopeSkiSummary
import com.stt.android.domain.workouts.extensions.SlopeSkiExtensionDataSource
import kotlinx.coroutines.withContext
import javax.inject.Inject

class SlopeSkiExtensionRepository
@Inject constructor(
    private val slopeSkiSummaryExtensionDataAccess: ExtensionDataAccessOrmliteDb<SlopeSkiSummary>
) : SlopeSkiExtensionDataSource {
    override suspend fun findByWorkoutId(workoutId: Int): SlopeSkiSummary? = withContext(ORMLITE) {
        slopeSkiSummaryExtensionDataAccess.findByWorkoutId(workoutId)
    }
}
