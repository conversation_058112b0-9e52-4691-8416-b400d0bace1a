package com.stt.android.controllers;

import android.text.TextUtils;
import androidx.annotation.NonNull;
import androidx.annotation.WorkerThread;
import com.j256.ormlite.dao.Dao;
import com.j256.ormlite.stmt.DeleteBuilder;
import com.j256.ormlite.stmt.QueryBuilder;
import com.stt.android.domain.database.DatabaseHelper;
import com.stt.android.domain.user.VideoInformation;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.exceptions.BackendException;
import com.stt.android.exceptions.InternalDataException;
import com.stt.android.utils.FileUtils;
import com.stt.android.utils.STTConstants;
import java.sql.SQLException;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;
import javax.inject.Inject;
import javax.inject.Singleton;
import timber.log.Timber;

@Singleton
public class VideoModel {
    final Dao<VideoInformation, Integer> dao;
    private final CurrentUserController currentUserController;
    private final BackendController backendController;
    private final FileUtils fileUtils;

    @Inject
    public VideoModel(DatabaseHelper helper,
        CurrentUserController currentUserController, BackendController backendController,
        FileUtils fileUtils) {
        try {
            this.dao = helper.getDao(VideoInformation.class);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        this.currentUserController = currentUserController;
        this.backendController = backendController;
        this.fileUtils = fileUtils;
    }

    @WorkerThread
    public void storeMetaData(@NonNull List<VideoInformation> videos) throws InternalDataException {
        try {
            dao.callBatchTasks((Callable<Void>) () -> {
                for (VideoInformation video : videos) {
                    storeMetaData(video);
                }
                return null;
            });
        } catch (Exception e) {
            throw new InternalDataException("Unable to store video metadata to local database", e);
        }
    }

    @WorkerThread
    public void storeMetaData(@NonNull VideoInformation videoInfo) throws InternalDataException {
        try {
            if (TextUtils.isEmpty(videoInfo.getFileName())) {
                // Let's check for local filename so we can keep some reference
                VideoInformation localVideo = dao.queryForSameId(videoInfo);
                if (localVideo != null) {
                    boolean isLocallyChanged = videoInfo.isLocallyChanged();
                    videoInfo = videoInfo.updateFileName(localVideo.getFileName())
                        .updateThumbnailFileName(localVideo.getThumbnailFileName());
                    if (!isLocallyChanged) {
                        videoInfo = videoInfo.synced();
                    }
                }
            }
            dao.createOrUpdate(videoInfo);
        } catch (SQLException e) {
            throw new InternalDataException("Unable to store video metadata to local database", e);
        }
    }

    @WorkerThread
    void removeSynced() throws InternalDataException {
        try {
            DeleteBuilder<VideoInformation, Integer> db = dao.deleteBuilder();
            db.where()
                .isNotNull(VideoInformation.DbFields.KEY)
                .and()
                .eq(VideoInformation.DbFields.LOCALLY_CHANGED, false);
            dao.delete(db.prepare());
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to delete synced video metadata from local database", e);
        }
    }

    @WorkerThread
    public void deleteAndPushVideo(VideoInformation video, Boolean deleteLocalFile)
        throws BackendException, InternalDataException {
        // backend uses key as video identifier, if trying to delete a video without key
        // check local DB for video with same ID for more up-to-date version that might have the key.
        // This can prevent locally deleted video reappearing after sync if the video param
        // was fetched between adding the video locally and uploading it
        VideoInformation videoWithKey = video;
        if (TextUtils.isEmpty(videoWithKey.getKey())) {
            try {
                VideoInformation storedVideo = dao.queryForSameId(videoWithKey);
                if (storedVideo != null) {
                    videoWithKey = storedVideo;
                }
            } catch (SQLException exception) {
                // ignore
            }
        }

        if (!TextUtils.isEmpty(videoWithKey.getKey())) {
            // the video has been synced, let's first inform the backend
            if (!backendController.pushDeletedVideo(currentUserController.getSession(),
                videoWithKey)) {
                throw new BackendException("Failed to push deleted video to backend");
            }
        }

        if (deleteLocalFile) {
            try {
                deleteVideo(videoWithKey);
            } catch (InternalDataException e) {
                Timber.e(e, "Failed to delete video file.");
            }
        }

        removeMetaData(videoWithKey);
    }

    @WorkerThread
    public void delete(VideoInformation videoInformation) throws InternalDataException {
        deleteVideo(videoInformation);
        removeMetaData(videoInformation);
    }

    @WorkerThread
    public void delete(WorkoutHeader workoutHeader) throws InternalDataException {
        final List<VideoInformation> videos = findByWorkoutId(workoutHeader.getId());
        if (videos.isEmpty()) {
            return;
        }

        for (VideoInformation video : videos) {
            try {
                deleteVideo(video);
            } catch (InternalDataException ignored) {
                // do nothing
            }
        }

        try {
            dao.callBatchTasks((Callable<Void>) () -> {
                for (VideoInformation video : videos) {
                    removeMetaData(video);
                }
                return null;
            });
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to delete video metadata from local database", e);
        }
    }

    @WorkerThread
    void removeMetaData(VideoInformation videoInfo) throws InternalDataException {
        try {
            dao.delete(videoInfo);
        } catch (SQLException e) {
            throw new InternalDataException("Unable to delete video metadata from local database",
                e);
        }
    }

    @WorkerThread
    private void deleteVideo(VideoInformation videoInfo) throws InternalDataException {
        String fileName = videoInfo.getFileName();
        if (TextUtils.isEmpty(fileName)) {
            return;
        }

        // first tries to delete from internal file folder
        boolean deleted = false;
        try {
            deleted = FileUtils.delete(
                fileUtils.getInternalFilePath(STTConstants.DIRECTORY_VIDEOS, fileName));
        } catch (Exception e) {
            // falls through
            Timber.w("File %s could not be deleted: %s", fileName, e.getMessage());
        }
        if (!deleted) {
            try {
                // now tries to delete from cache
                deleted = FileUtils.delete(
                    fileUtils.getCachedFilePath(STTConstants.DIRECTORY_VIDEOS, fileName));
                if (!deleted) {
                    Timber.w("Could not delete file %s", fileName);
                }
            } catch (Exception e) {
                throw new InternalDataException("Unable to delete cached videos", e);
            }
        }
    }

    @WorkerThread
    @NonNull
    public List<VideoInformation> findUnsyncedVideos(String userName) throws InternalDataException {
        try {
            QueryBuilder<VideoInformation, Integer> qb = dao.queryBuilder();
            qb.orderBy(VideoInformation.DbFields.TIMESTAMP, true)
                .where()
                .eq(VideoInformation.DbFields.LOCALLY_CHANGED, true)
                .and()
                .isNotNull(VideoInformation.DbFields.WORKOUT_KEY)
                .and()
                .isNotNull(VideoInformation.DbFields.WORKOUT_ID)
                .and()
                .eq(VideoInformation.DbFields.USERNAME, userName);
            return Collections.unmodifiableList(qb.query());
        } catch (SQLException e) {
            throw new InternalDataException("Unable to find video metadata from local database", e);
        }
    }

    /**
     * Returns a map from workout IDs to all videos of that workout.
     */
    @NonNull
    @WorkerThread
    public Map<Integer, List<VideoInformation>> findByWorkoutIds(@NonNull Collection<Integer> workoutIds) throws InternalDataException {
        if (workoutIds.isEmpty()) {
            return Collections.emptyMap();
        }

        try {
            QueryBuilder<VideoInformation, Integer> qb = dao.queryBuilder();
            qb.orderBy(VideoInformation.DbFields.TIMESTAMP, true)
                .where()
                .in(VideoInformation.DbFields.WORKOUT_ID, workoutIds);
            return dao.query(qb.prepare())
                .stream()
                .collect(Collectors.groupingBy(VideoInformation::getWorkoutId));
        } catch (SQLException e) {
            throw new InternalDataException("Failed to find picture metadata from local database", e);
        }
    }

    @NonNull
    @WorkerThread
    public List<VideoInformation> findByWorkoutId(int workoutId) throws InternalDataException {
        List<VideoInformation> videos = findByWorkoutIds(Collections.singleton(workoutId))
            .get(workoutId);
        return videos != null ? videos : Collections.emptyList();
    }

    @WorkerThread
    public long count(int workoutId) throws InternalDataException {
        try {
            return dao.queryBuilder()
                .where()
                .eq(VideoInformation.DbFields.WORKOUT_ID, workoutId)
                .countOf();
        } catch (SQLException e) {
            throw new InternalDataException("Unable to count video from local database", e);
        }
    }
}
