package com.stt.android.controllers;

import com.stt.android.domain.UserSession;
import com.stt.android.exceptions.BackendException;
import com.stt.android.network.HttpResponseException;
import com.stt.android.network.interfaces.ANetworkProvider;
import java.io.IOException;
import javax.inject.Inject;
import javax.inject.Singleton;

import timber.log.Timber;

@Deprecated
@Singleton
public class LoginController {

    private final ANetworkProvider networkProvider;

    @Inject
    public LoginController(ANetworkProvider networkProvider) {
        this.networkProvider = networkProvider;
    }

    public void logout(UserSession session) throws BackendException {
        String workoutsURL = ANetworkProvider.buildSecureBackendUrl("/logout");
        try {
            networkProvider.get(workoutsURL, session.getAuthorizationHeaders());
        } catch (IOException | HttpResponseException e) {
            String message = "Unable to logout";
            Timber.e(e, message);
            throw new BackendException(message, e);
        }
    }
}
