package com.stt.android.controllers

import com.soy.algorithms.intensity.IntensityZones
import com.soy.algorithms.intensity.IntensityZonesData
import com.stt.android.data.workout.toDomainType
import com.stt.android.domain.user.workoutextension.FitnessExtension
import com.stt.android.domain.user.workoutextension.IntensityExtension
import com.stt.android.domain.user.workoutextension.SlopeSkiSummary
import com.stt.android.domain.workouts.extensions.DiveExtension
import com.stt.android.domain.workouts.extensions.JumpRopeExtension
import com.stt.android.domain.workouts.extensions.SummaryExtension
import com.stt.android.domain.workouts.extensions.SwimmingExtension
import com.stt.android.domain.workouts.extensions.WeatherExtension
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.remote.extensions.RemoteIntensityExtensionZones
import com.stt.android.remote.extensions.RemoteWorkoutExtension
import timber.log.Timber

/**
 * todo We can get rid of this once the whole ExtensionDataModel is moved to new architecture
 */
object ExtensionRemoteMapper {

    @JvmStatic
    fun convertRemoteExtensions(
        workoutId: Int,
        remoteWorkoutExtensions: List<RemoteWorkoutExtension>
    ): List<WorkoutExtension> = remoteWorkoutExtensions.mapNotNull { convertRemoteExtension(workoutId, it) }

    @JvmStatic
    fun convertRemoteExtension(workoutId: Int, remoteExtension: RemoteWorkoutExtension): WorkoutExtension? {
        return when (remoteExtension) {
            is RemoteWorkoutExtension.RemoteSummaryExtension -> remoteExtension.convertToSummaryExtension(workoutId)
            is RemoteWorkoutExtension.RemoteDiveHeaderExtension -> remoteExtension.convertToDiveHeaderExtension(workoutId)
            is RemoteWorkoutExtension.RemoteFitnessExtension -> remoteExtension.convertToFitnessExtension(workoutId)
            is RemoteWorkoutExtension.RemoteIntensityExtension -> remoteExtension.convertToIntensityExtension(workoutId)
            is RemoteWorkoutExtension.RemoteSlopeSkiSummaryExtension -> remoteExtension.convertToSlopeSkiSummaryExtension(workoutId)
            is RemoteWorkoutExtension.RemoteSwimmingHeaderExtension -> remoteExtension.convertToSwimmingHeaderExtension(workoutId)
            is RemoteWorkoutExtension.RemoteWeatherExtension -> remoteExtension.convertToWeatherExtension(workoutId)
            is RemoteWorkoutExtension.RemoteJumpRopeExtension -> remoteExtension.convertToJumpRopeExtension(workoutId)
            else -> {
                Timber.w("Cannot convert extension ${remoteExtension.javaClass}")
                null
            }
        }
    }
}

private fun RemoteWorkoutExtension.RemoteSlopeSkiSummaryExtension.convertToSlopeSkiSummaryExtension(workoutId: Int) =
    SlopeSkiSummary(
        workoutId,
        statistics.numberOfRuns,
        statistics.descentDurationSeconds * 1000L,
        statistics.descentMeters,
        statistics.descentDistanceMeters,
        statistics.maxSpeed
    )

internal fun RemoteWorkoutExtension.RemoteIntensityExtension.convertToIntensityExtension(workoutId: Int) =
    IntensityExtension(
        workoutId = workoutId,
        intensityZones = IntensityZones(
            hr = this.zones?.heartRate?.toIntensityZoneData()?.bpmToHz(),
            speed = this.zones?.speed?.toIntensityZoneData(),
            power = this.zones?.power?.toIntensityZoneData(),
        ),
    )

private fun RemoteWorkoutExtension.RemoteSwimmingHeaderExtension.convertToSwimmingHeaderExtension(
    workoutId: Int
): SwimmingExtension? {
    val isValid =
        listOf(
            avgSwolf,
            avgStrokeRate,
            breaststrokeGlideTime,
            ventilationFrequency,
            avgFreestyleBreathAngle,
            maxFreestyleBreathAngle,
            freestyleGlideAngle,
            avgBreaststrokeBreathAngle,
            maxBreaststrokeBreathAngle,
            freestyleDuration,
            breaststrokeDuration,
            freestylePercentage,
            breaststrokePercentage,
            breaststrokeHeadAngle,
        )
            .any { it != null }
    return if (isValid) {
        SwimmingExtension(
            workoutId = workoutId,
            avgSwolf = avgSwolf ?: 0,
            avgStrokeRate = avgStrokeRate ?: 0.0f,
            breathingRate = ventilationFrequency,
            breaststrokeDuration = breaststrokeDuration,
            breaststrokePercentage = breaststrokePercentage,
            breaststrokeGlideTime = breaststrokeGlideTime,
            breaststrokeMaxBreathAngle = maxBreaststrokeBreathAngle,
            breaststrokeAvgBreathAngle = avgBreaststrokeBreathAngle,
            freestyleDuration = freestyleDuration,
            freestylePercentage = freestylePercentage,
            freestyleMaxBreathAngle = maxFreestyleBreathAngle,
            freestyleAvgBreathAngle = avgFreestyleBreathAngle,
            freestylePitchAngle = freestyleGlideAngle,
            breaststrokeHeadAngle = breaststrokeHeadAngle
        )
    } else {
        // invalid swimming extension, returning null
        null
    }
}

private fun RemoteWorkoutExtension.RemoteWeatherExtension.convertToWeatherExtension(workoutId: Int) =
    if (weatherIcon != null || temperature != null || windSpeed != null) {
        WeatherExtension(
            workoutId = workoutId,
            airPressure = airPressure,
            cloudiness = cloudiness,
            groundLevelAirPressure = groundLevelAirPressure,
            humidity = humidity,
            rainVolume1h = rainVolume1h,
            rainVolume3h = rainVolume3h,
            seaLevelAirPressure = seaLevelAirPressure,
            snowVolume1h = snowVolume1h,
            snowVolume3h = snowVolume3h,
            temperature = temperature,
            weatherIcon = weatherIcon,
            windDirection = windDirection,
            windSpeed = windSpeed
        )
    } else {
        Timber.w("Useless weather extension for workoutId=$workoutId (no icon, temp, or wind info)")
        null
    }

private fun RemoteIntensityExtensionZones.toIntensityZoneData() =
    IntensityZonesData(
        zone1Duration = zone1.totalTime,
        zone2Duration = zone2.totalTime,
        zone3Duration = zone3.totalTime,
        zone4Duration = zone4.totalTime,
        zone5Duration = zone5.totalTime,
        zone2LowerLimit = zone2.lowerLimit,
        zone3LowerLimit = zone3.lowerLimit,
        zone4LowerLimit = zone4.lowerLimit,
        zone5LowerLimit = zone5.lowerLimit
    )

private fun RemoteWorkoutExtension.RemoteFitnessExtension.convertToFitnessExtension(workoutId: Int) =
    maxHeartRate?.let {
        FitnessExtension(
            workoutId = workoutId,
            maxHeartRate = it,
            vo2Max = vo2Max ?: 0.0f,
            fitnessAge = fitnessAge,
        )
    }

private fun RemoteWorkoutExtension.RemoteDiveHeaderExtension.convertToDiveHeaderExtension(workoutId: Int) =
    DiveExtension(
        workoutId,
        maxDepth = maxDepth,
        algorithm = algorithm,
        personalSetting = personalSetting,
        diveNumberInSeries = diveNumberInSeries,
        cns = cns,
        algorithmLock = algorithmLock,
        diveMode = diveMode,
        otu = otu,
        pauseDuration = pauseDuration,
        gasConsumption = gasConsumption,
        altitudeSetting = altitudeSetting,
        gasQuantities = gasQuantities ?: mapOf(),
        surfaceTime = surfaceTime,
        diveTime = diveTime,
        gasesUsed = gasesUsed ?: listOf(),
        maxDepthTemperature = maxDepthTemperature,
        avgDepth = avgDepth,
        minGF = minGF,
        maxGF = maxGF
    )

private fun RemoteWorkoutExtension.RemoteSummaryExtension.convertToSummaryExtension(workoutId: Int) =
    SummaryExtension(
        workoutId = workoutId,
        pte = pte,
        feeling = feeling,
        avgTemperature = avgTemperature,
        peakEpoc = peakEpoc,
        avgPower = avgPower,
        avgCadence = avgCadence,
        avgSpeed = avgSpeed,
        ascentTime = ascentTime,
        descentTime = descentTime,
        performanceLevel = performanceLevel,
        recoveryTime = recoveryTime,
        ascent = ascent,
        descent = descent,
        deviceHardwareVersion = gear?.deviceHardwareVersion,
        deviceSoftwareVersion = gear?.deviceSoftwareVersion,
        productType = gear?.productType,
        displayName = gear?.displayName,
        deviceName = gear?.deviceName,
        deviceSerialNumber = gear?.deviceSerialNumber,
        deviceManufacturer = gear?.deviceManufacturer,
        exerciseId = exerciseId,
        zapps = zapps.toDomainType(),
        maxCadence = maxCadence,
        repetitionCount = repetitionCount,
        avgStrideLength = avgStrideLength,
        fatConsumption = fatConsumption,
        carbohydrateConsumption = carbohydrateConsumption,
        avgGroundContactTime = avgGroundContactTime,
        avgVerticalOscillation = avgVerticalOscillation,
        avgLeftGroundContactBalance = avgLeftGroundContactBalance,
        avgRightGroundContactBalance = avgRightGroundContactBalance,
        lacticThHr = lacticThHr,
        lacticThPace = lacticThPace,
        avgAscentSpeed = avgAscentSpeed,
        maxAscentSpeed = maxAscentSpeed,
        avgDescentSpeed = avgDescentSpeed,
        maxDescentSpeed = maxDescentSpeed,
        avgDistancePerStroke = avgDistancePerStroke,
    )

private fun RemoteWorkoutExtension.RemoteJumpRopeExtension.convertToJumpRopeExtension(workoutId: Int) =
    JumpRopeExtension(
        workoutId = workoutId,
        rounds = rounds,
        avgSkipsPerRound = avgSkipsPerRound,
        maxConsecutiveSkips = maxConsecutiveSkips
    )
