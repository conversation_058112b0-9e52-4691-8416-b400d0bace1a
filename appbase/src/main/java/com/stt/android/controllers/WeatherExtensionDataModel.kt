package com.stt.android.controllers

import com.stt.android.data.source.local.weatherextension.LocalWeatherExtension
import com.stt.android.domain.workouts.extensions.WeatherExtension
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.remote.extensions.ExtensionsRemoteApi
import com.stt.android.remote.extensions.RemoteWorkoutExtension
import com.stt.android.utils.firstOfType
import javax.inject.Inject

class WeatherExtensionDataModel @Inject constructor(
    workoutHeaderController: WorkoutHeaderController,
    weatherExtensionDataAccess: ExtensionDataAccessRoomDb<LocalWeatherExtension, WeatherExtension>,
    extensionsRemoteApi: ExtensionsRemoteApi,
    extensionDataAccessMap: Map<Class<out WorkoutExtension>, ExtensionDataAccess<out WorkoutExtension>>
) : ExtensionDataModel<WeatherExtension>(
    weatherExtensionDataAccess,
    workoutHeaderController,
    extensionsRemoteApi,
    extensionDataAccessMap
) {

    override fun getBackendExtensionType(): String? = RemoteWorkoutExtension.Type.WEATHER_EXTENSION.value

    override fun findExtension(extensions: List<WorkoutExtension>): WeatherExtension? =
        extensions.firstOfType()
}
