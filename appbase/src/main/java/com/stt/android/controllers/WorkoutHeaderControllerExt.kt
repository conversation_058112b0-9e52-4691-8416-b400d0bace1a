package com.stt.android.controllers

import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.utils.toV1
import com.stt.android.utils.toV2
import io.reactivex.BackpressureStrategy
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.rx2.asFlow
import kotlinx.coroutines.rx2.asObservable
import rx.Observable
import timber.log.Timber
import kotlin.time.Duration.Companion.milliseconds

@OptIn(FlowPreview::class, ExperimentalCoroutinesApi::class)
val WorkoutHeaderController.currentUserWorkoutUpdated: Flow<Unit> get() = currentUserWorkoutUpdatesAsObservable
    .toV2()
    .asFlow()
    .debounce(100.milliseconds)
    .mapLatest { }

@OptIn(FlowPreview::class, ExperimentalCoroutinesApi::class)
val WorkoutHeaderController.workoutUpdated: Flow<Unit> get() = workoutUpdatesAsObservable
    .toV2()
    .asFlow()
    .debounce(100.milliseconds)
    .mapLatest { }

fun WorkoutHeaderController.loadWorkoutsRx(username: String): Observable<List<WorkoutHeader>> =
    loadWorkouts(username).asObservable().toV1(BackpressureStrategy.LATEST)

fun WorkoutHeaderController.loadWorkouts(username: String): Flow<List<WorkoutHeader>> = flow {
    val isCurrentUser = currentUserController.username == username
    val local = findAllWhereOwner(username, false)
    if (isCurrentUser || local.isNotEmpty()) {
        emit(local.sortedByDescending(WorkoutHeader::startTime))
    }
    if (isCurrentUser) {
        // For current user, all workouts are locally available, so no need to fetch from server.
        return@flow
    }

    val session = currentUserController.session ?: return@flow
    val fetchedWorkouts = backendController.fetchWorkouts(session, username)
        .getResult()
    val fetchedWorkoutHeaders = fetchedWorkouts
        .map { workout -> workout.getWorkoutHeader(this@loadWorkouts) }
    val workoutHeadersToEmit = merge(local, fetchedWorkoutHeaders)
        .sortedByDescending(WorkoutHeader::startTime)
    emit(workoutHeadersToEmit)

    val isFollowee = isFolloweeUseCase.isFollowee(username)
    if (!isFollowee) {
        return@flow
    }
    fetchedWorkouts.forEach { workout ->
        picturesController.store(workout.images)
        videoModel.storeMetaData(workout.videos)
    }
    store(fetchedWorkoutHeaders)
}.catch { e ->
    Timber.w(e, "Error while loading workouts")
}.flowOn(Dispatchers.IO)

private fun merge(
    workoutHeaders1: List<WorkoutHeader>,
    workoutHeaders2: List<WorkoutHeader>,
): Collection<WorkoutHeader> {
    if (workoutHeaders1.isEmpty()) {
        return workoutHeaders2
    }
    if (workoutHeaders2.isEmpty()) {
        return workoutHeaders1
    }

    val workoutHeadersByKey = workoutHeaders1.associateBy(WorkoutHeader::key)
        .toMutableMap()
    workoutHeaders2.associateByTo(workoutHeadersByKey, WorkoutHeader::key)
    return workoutHeadersByKey.values
}
