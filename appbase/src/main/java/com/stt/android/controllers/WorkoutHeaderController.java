package com.stt.android.controllers;

import android.text.TextUtils;
import android.util.SparseIntArray;
import androidx.annotation.IntDef;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.WorkerThread;
import com.stt.android.analytics.AnalyticsUserProperty;
import com.stt.android.analytics.AnalyticsWorkoutsSummary;
import com.stt.android.data.TimeUtilsKt;
import com.stt.android.data.source.local.tags.LocalUserTag;
import com.stt.android.data.source.local.workout.LocalUserWorkoutSummary;
import com.stt.android.data.source.local.workout.LocalUserWorkoutTotals;
import com.stt.android.data.source.local.workout.LocalWorkoutHeader;
import com.stt.android.data.source.local.workout.WorkoutHeaderDao;
import com.stt.android.data.workout.WorkoutMappersKt;
import static com.stt.android.data.workout.WorkoutMappersKt.toDomain;
import static com.stt.android.data.workout.WorkoutMappersKt.toLocal;
import com.stt.android.domain.UserSession;
import com.stt.android.domain.user.BackendWorkout;
import com.stt.android.domain.user.User;
import com.stt.android.domain.user.UserWorkoutSummary;
import com.stt.android.domain.user.follow.IsFolloweeUseCase;
import com.stt.android.domain.user.workout.tss.TSSMappersKt;
import com.stt.android.domain.workout.ActivityType;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.domain.workouts.tss.WorkoutTSSSummary;
import com.stt.android.exceptions.BackendException;
import com.stt.android.exceptions.InternalDataException;
import com.stt.android.ui.extensions.WorkoutHeaderExtensionsKt;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.sql.SQLException;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.Year;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.inject.Inject;
import javax.inject.Singleton;
import rx.BackpressureOverflow;
import rx.Observable;
import rx.subjects.PublishSubject;
import rx.subjects.SerializedSubject;
import timber.log.Timber;

/**
 * todo refactor away from this class to clean architecture use cases,
 * and move DB access to persistence module
 */
@Singleton
public class WorkoutHeaderController {
    private static final int MAX_UPDATES_BUFFER = 40;
    public static final int WORKOUTS_PAGE_SIZE = 30;
    final WorkoutHeaderDao dao;
    final CurrentUserController currentUserController;
    @SuppressWarnings("WeakerAccess")
    final IsFolloweeUseCase isFolloweeUseCase;
    final BackendController backendController;
    final PicturesController picturesController;
    final VideoModel videoModel;
    final UserSettingsController userSettingsController;

    /**
     * Used to notify changes in the current user workout headers
     */
    @SuppressWarnings("WeakerAccess")
    final SerializedSubject<WorkoutUpdate, WorkoutUpdate> currentUserWorkoutUpdatesSubject =
        PublishSubject.<WorkoutUpdate>create().toSerialized();

    private final SerializedSubject<Object, Object> workoutUpdatesSubject =
        PublishSubject.<Object>create().toSerialized();

    @Inject
    public WorkoutHeaderController(
        CurrentUserController currentUserController,
        BackendController backendController,
        PicturesController picturesController,
        VideoModel videoModel,
        IsFolloweeUseCase isFolloweeUseCase,
        WorkoutHeaderDao dao,
        UserSettingsController userSettingsController
    ) {
        this.dao = dao;
        this.currentUserController = currentUserController;
        this.isFolloweeUseCase = isFolloweeUseCase;
        this.backendController = backendController;
        this.picturesController = picturesController;
        this.videoModel = videoModel;
        this.userSettingsController = userSettingsController;
    }

    @WorkerThread
    public WorkoutHeader store(WorkoutHeader workoutHeader)
        throws InternalDataException {
        store(Collections.singletonList(workoutHeader));
        return workoutHeader;
    }

    /**
     * Stores the given workouts. It might lead to partial storing if one of the
     * workouts fails to be stored.
     *
     * @throws InternalDataException
     */
    void store(@NonNull final List<WorkoutHeader> workouts)
        throws InternalDataException {
        if (workouts.isEmpty()) {
            return;
        }

        final String currentUsername = currentUserController.getUsername();
        final List<WorkoutUpdate> currentUserWorkoutUpdates = new ArrayList<>();
        try {
            for (WorkoutHeader workout : workouts) {
                boolean isNewInsertion = dao.upsertSync(toLocal(workout));
                if (workout.getUsername().equals(currentUsername)) {
                    int op = isNewInsertion ? WorkoutUpdate.CREATED : WorkoutUpdate.UPDATED;
                    currentUserWorkoutUpdates.add(new WorkoutUpdate(op, workout));
                }
            }
            for (WorkoutUpdate update : currentUserWorkoutUpdates) {
                currentUserWorkoutUpdatesSubject.onNext(update);
            }
            workoutUpdatesSubject.onNext(new Object());
        } catch (Exception e) {
            throw new InternalDataException("Unable to store workouts", e);
        }
    }

    public void remove(@NonNull final List<WorkoutHeader> workouts)
        throws InternalDataException {
        final String currentUsername = currentUserController.getUsername();
        final List<WorkoutUpdate> currentUserWorkoutUpdates = new ArrayList<>(workouts.size());
        boolean hasRemoved = false;
        try {
            for (WorkoutHeader workout : workouts) {
                if (dao.deleteSync(toLocal(workout)) > 0) {
                    hasRemoved = true;
                }

                if (workout.getUsername().equals(currentUsername)) {
                    currentUserWorkoutUpdates.add(new WorkoutUpdate(WorkoutUpdate.DELETED, workout));
                }
            }
            for (WorkoutUpdate update : currentUserWorkoutUpdates) {
                currentUserWorkoutUpdatesSubject.onNext(update);
            }
            if (hasRemoved) {
                workoutUpdatesSubject.onNext(new Object());
            }
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to remove all workouts. Some might have been deleted.", e);
        }
    }

    public void remove(@NonNull WorkoutHeader workoutHeader) throws InternalDataException {
        try {
            boolean hasRemoved = dao.deleteSync(toLocal(workoutHeader)) > 0;
            if (hasRemoved) {
                if (workoutHeader.getUsername().equals(currentUserController.getUsername())) {
                    currentUserWorkoutUpdatesSubject.onNext(
                        new WorkoutUpdate(WorkoutUpdate.DELETED, workoutHeader));
                }
                workoutUpdatesSubject.onNext(new Object());
            }
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to delete workout from local database: " + e.getMessage(), e);
        }
    }

    public boolean hasWorkout(int id) throws InternalDataException {
        try {
            return dao.findByIdSync(id) != null;
        } catch (Exception e) {
            throw new InternalDataException("Unabled to check if workout ID exists", e);
        }
    }

    @WorkerThread
    @NonNull
    public List<WorkoutHeader> find(@NonNull List<Integer> workoutIds) {
        if (workoutIds.isEmpty()) {
            return Collections.emptyList();
        }

        return dao.findAllByIdSync(workoutIds)
            .stream()
            .map(WorkoutMappersKt::toDomain)
            .collect(Collectors.toList());
    }

    /**
     * @param workoutKey the key for the workout to retrieve
     * @return the workout header (from local DB or from network) or null if not found or error occurs
     */
    @WorkerThread
    @Nullable
    public WorkoutHeader getOrFetch(final String workoutKey) throws BackendException {
        WorkoutHeader local = find(workoutKey);
        if (local != null) {
            return local;
        }

        return fetchWorkoutHeader(workoutKey);
    }

    /**
     * @param workoutKey the key for the workout to get from local DB
     * @return workout header or null if not found.
     */
    @WorkerThread
    @Nullable
    public WorkoutHeader find(final String workoutKey) {
        LocalWorkoutHeader localWorkoutHeader = dao.findByKeySync(workoutKey);
        return localWorkoutHeader != null ? WorkoutMappersKt.toDomain(localWorkoutHeader) : null;
    }

    /**
     * @param workoutKey the key for the workout to fetch from backend
     * @return the workout header or null if not found in the backend.
     */
    private WorkoutHeader fetchWorkoutHeader(final String workoutKey) throws BackendException {
        UserSession session = currentUserController.getSession();
        BackendWorkout backendResult = backendController.fetchWorkoutHeader(session, workoutKey);
        return backendResult.getWorkoutHeader(this);
    }

    @Nullable
    @WorkerThread
    public WorkoutHeader findLatestNotDeletedWorkout(@NonNull String userName) {
        LocalWorkoutHeader localWorkoutHeader = dao.findLatestNotDeletedWorkout(userName);
        return localWorkoutHeader != null ? WorkoutMappersKt.toDomain(localWorkoutHeader) : null;
    }

    /**
     * Finds the latest not deleted workout that can be shared with a photo or a map.
     *
     * @param userName the username whose workouts we want to find
     * @return shareable workout or null
     */
    @WorkerThread
    @Nullable
    public WorkoutHeader findLatestNotDeletedShareableWorkout(final String userName) {
        LocalWorkoutHeader localWorkoutHeader = dao.findLatestNotDeletedShareableWorkout(userName);
        return localWorkoutHeader != null ? WorkoutMappersKt.toDomain(localWorkoutHeader) : null;
    }

    /**
     * Finds the latest not deleted commute workout that can be shared with a photo or a map.
     */
    @Nullable
    public WorkoutHeader findLatestNotDeletedShareableCommuteWorkout(final String userName) {
        LocalWorkoutHeader localWorkoutHeader =
            dao.findLatestNotDeletedShareableCommuteWorkout(userName);
        return localWorkoutHeader != null ? WorkoutMappersKt.toDomain(localWorkoutHeader) : null;
    }

    public WorkoutHeader findOldestNotDeletedWorkout(String userName, long since) throws InternalDataException {
        try {
            LocalWorkoutHeader localWorkoutHeader = dao.findOldestNotDeletedWorkout(userName, since);
            return localWorkoutHeader != null ? WorkoutMappersKt.toDomain(localWorkoutHeader) : null;
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to fetch oldest workout from local database: " + e.getMessage(), e);
        }
    }

    /**
     * @param username             the user from whom to fetch the workouts
     * @param includeDeleted       set to true if deleted workouts should be fetched
     *                             also.
     * @return an unmodifiable list of workouts linked to the given user
     * @throws InternalDataException if we can't fetch workouts from local
     *                               database
     */
    @WorkerThread
    @NonNull
    public List<WorkoutHeader> findAllWhereOwner(String username,
                                                 boolean includeDeleted
    ) throws InternalDataException {
        return findAllWhereOwner(username, includeDeleted, null);
    }

    @NonNull
    public List<WorkoutHeader> findAllWhereOwner(String username,
                                                 boolean includeDeleted,
                                                 ActivityType activityType
    ) throws InternalDataException {

        return findAllWhereOwnerPaged(
                username,
                includeDeleted,
                activityType,
                0);
    }

    @NonNull
    public List<WorkoutHeader> findAllWhereOwnerPaged(
        String username,
        boolean includeDeleted,
        ActivityType activityType,
        int page
    ) throws InternalDataException {
        return findAllPagedOfType(
            username,
            includeDeleted,
            activityType != null ? activityType.getId() : null,
            page
        );
    }

    @NonNull
    public List<WorkoutHeader> findAllPagedOfType(String username,
        boolean includeDeleted,
        Integer activityTypeId,
        int page
    ) throws InternalDataException {

        try {
            return dao.findAllPagedOfType(username, includeDeleted, activityTypeId, page)
                .stream().map(WorkoutMappersKt::toDomain).collect(Collectors.toList());
        } catch (Throwable e) {
            throw new InternalDataException(
                "Unable to fetch workouts from local database: " + e.getMessage(), e);
        }
    }

    @NonNull
    public List<WorkoutHeader> findAllPagedExcludingTypes(String username,
        boolean includeDeleted,
        Set<Integer> excludedTypes,
        int page
    ) throws InternalDataException {
        try {
            return dao.findAllPagedExcludingTypes(username, includeDeleted, excludedTypes, page)
                .stream().map(WorkoutMappersKt::toDomain).collect(Collectors.toList());
        } catch (Throwable e) {
            throw new InternalDataException(
                "Unable to fetch workouts from local database: " + e.getMessage(), e);
        }
    }

    /**
     * Find WorkoutHeaders for current user based on list of workoutIds
     */
    public List<WorkoutHeader> findByIdsForCurrentUser(final Collection<Integer> workoutIds)
        throws InternalDataException {
        try {
            return dao.findByIdsForUser(currentUserController.getUsername(), workoutIds)
                .stream().map(WorkoutMappersKt::toDomain).collect(Collectors.toList());
        } catch (Throwable e) {
            throw new InternalDataException(
                "Unable to fetch workouts from local database: " + e.getMessage(), e);
        }
    }

    @WorkerThread
    public boolean isOwnWorkout(@NonNull String workoutKey) throws InternalDataException {
        try {
            return dao.isWorkoutForUser(workoutKey, currentUserController.getUsername()) > 0;
        } catch (Throwable e) {
            throw new InternalDataException(
                "Unable to fetch workouts from local database: " + e.getMessage(), e);
        }
    }

    public List<WorkoutHeader> findLatestNotDeletedWorkouts(String userName,
        ActivityType activityType, long till, long limit) throws InternalDataException {
        try {
            List<LocalWorkoutHeader> workoutHeaders;
            if (limit > 0L) {
                workoutHeaders = dao.findLatestNotDeletedWorkoutsWithLimit(userName, till,
                    activityType.getId(), (int) limit);
            } else {
                workoutHeaders = dao.findLatestNotDeletedWorkouts(userName, till, activityType.getId());
            }
            return workoutHeaders.stream().map(WorkoutMappersKt::toDomain).collect(Collectors.toList());
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to fetch latest workout from local database: " + e.getMessage(), e);
        }
    }

    /**
     * Return the stop time of the latest workout for the given user, or 0 if no workout exists.
     */
    public long getLatestWorkoutStopTime(@NonNull List<String> usernames) throws InternalDataException {
        try {
            return dao.getLatestWorkoutStopTime(usernames);
        } catch (Exception e) {
            throw new InternalDataException("Unable to get latest workout from local database", e);
        }
    }

    public List<WorkoutHeader> getLatestNotDeletedWorkoutHeadersAndMatchingUserTags(String userName, long limit)
        throws InternalDataException {
        try {
            Map<LocalWorkoutHeader, List<LocalUserTag>> result = dao.getLatestNotDeletedWorkoutHeadersAndMatchingUserTags(userName, (int) limit);

            if (result.isEmpty()) {
                return Collections.emptyList();
            }

            return result
                .entrySet()
                .stream()
                .map(entry -> WorkoutMappersKt.toDomain(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to fetch latest workout from local database: " + e.getMessage(), e);
        }
    }

    public List<WorkoutHeader> findNotDeletedWorkoutHeaders(
        String userName,
        @Nullable ActivityType activityType,
        long since,
        long till
    ) throws InternalDataException {
        return findWorkoutHeaders(
            userName,
            activityType != null ? activityType.getId() : null,
            since, till);
    }

    public List<WorkoutHeader> findWorkoutHeaders(
        String userName,
        Integer activityTypeId,
        long since,
        long till
    ) throws InternalDataException {
        try {
            return dao.getNotDeletedWorkoutHeaders(userName, since, till, activityTypeId)
                .stream().map(WorkoutMappersKt::toDomain).collect(Collectors.toList());
        } catch (Exception e) {
            throw new InternalDataException("Unable to find recent workout from local database", e);
        }
    }

    /**
     * @return a summary of all the workouts from the given user
     * @throws InternalDataException
     */
    @WorkerThread
    public UserWorkoutSummary getUserWorkoutSummary(String username) throws InternalDataException {
        try {
            LocalUserWorkoutSummary localUserWorkoutSummary = dao.loadUserWorkoutSummary(username);
            return new UserWorkoutSummary(localUserWorkoutSummary.getTotalWorkouts(),
                localUserWorkoutSummary.getTotalDistance(), localUserWorkoutSummary.getTotalDuration(),
                localUserWorkoutSummary.getTotalEnergyKCal());
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to fetch summary information from local database: " + e.getMessage(), e);
        }
    }

    @Nullable
    public UserWorkoutSummary getUserWorkoutSummary(String userName, ActivityType activityType,
        long since, long till) {
        try {
            LocalUserWorkoutSummary localUserWorkoutSummary = dao.loadUserWorkoutSummary(
                userName, activityType.getId(), since, till);
            return new UserWorkoutSummary(localUserWorkoutSummary.getTotalWorkouts(),
                localUserWorkoutSummary.getTotalDistance(), localUserWorkoutSummary.getTotalDuration(),
                localUserWorkoutSummary.getTotalEnergyKCal());
        } catch (Exception e) {
            Timber.w(e, "Unable to fetch workout summary from the local database");
            return null;
        }
    }

    /**
     * @return Summary of all workouts for analytics of given user
     * @throws InternalDataException
     */
    @WorkerThread
    public AnalyticsWorkoutsSummary getUserAnalyticsWorkoutSummary(String username)
        throws InternalDataException {
        try {
            LocalUserWorkoutTotals localUserWorkoutTotals = dao.loadUserWorkoutTotals(username);
            int totalWorkouts = localUserWorkoutTotals.getTotalWorkouts();
            double totalDurationInSeconds = localUserWorkoutTotals.getTotalTime();
            int totalPictures = localUserWorkoutTotals.getPictureCount();
            int totalComments = localUserWorkoutTotals.getCommentCount();
            int totalLikes = localUserWorkoutTotals.getReactionCount();
            int totalPrivateWorkouts = dao.countPrivateWorkouts(username);
            int totalPublicWorkouts = dao.countPublicWorkouts(username);
            int totalForFollowers = dao.countWorkoutsForFollowers(username);
            int totalWorkoutsWithDescription = dao.countWorkoutsWithDescription(username);

            // Current year
            final Year currentYear = Year.now();
            final long startOfCurrentYear = TimeUtilsKt.toStartOfYearMilli(currentYear);
            final long endOfCurrentYear = TimeUtilsKt.toEndOfYearMilli(currentYear);
            int totalWorkoutsCurrentYear =
                getWorkoutCountForTime(username, startOfCurrentYear, endOfCurrentYear);
            int totalWorkoutDurationCurrentYear =
                getWorkoutDurationForTime(username, startOfCurrentYear, endOfCurrentYear);

            // Previous year
            final Year previousYear = Year.now().minusYears(1);
            final long startOfPreviousYear = TimeUtilsKt.toStartOfYearMilli(previousYear);
            final long endOfPreviousYear = TimeUtilsKt.toEndOfYearMilli(previousYear);
            int totalWorkoutsPreviousYear =
                getWorkoutCountForTime(username, startOfPreviousYear, endOfPreviousYear);
            int totalWorkoutDurationPreviousYear =
                getWorkoutDurationForTime(username, startOfPreviousYear, endOfPreviousYear);

            // Last 3, 7, 14, 28, 30, and 31 days
            SparseIntArray workoutCountsInPeriods = new SparseIntArray();
            ZoneId zoneId = ZoneId.systemDefault();
            final long endOfPeriod = LocalDate.now().plusDays(1).atStartOfDay(zoneId)
                .toInstant().toEpochMilli();
            for (int numDays : AnalyticsUserProperty.TOTAL_WORKOUTS_WITHIN_LAST_DAYS_PERIODS) {
                final long startOfPeriod = LocalDate.now().minusDays(numDays).atStartOfDay(zoneId)
                    .toInstant().toEpochMilli();

                int totalWorkoutsInPeriod = getWorkoutCountForTime(username, startOfPeriod, endOfPeriod);
                workoutCountsInPeriods.put(numDays, totalWorkoutsInPeriod);
            }
            DayOfWeek firstDayOfTheWeek = userSettingsController.getSettings().getFirstDayOfTheWeek();
            long currentWeekStartOfPeriod = LocalDate.now()
                .with(TemporalAdjusters.previousOrSame(firstDayOfTheWeek))
                .atStartOfDay(zoneId)
                .toInstant()
                .toEpochMilli();

            int totalWorkoutsCurrentWeek = getWorkoutCountForTime(username, currentWeekStartOfPeriod, endOfPeriod);
            long currentMonthStartOfPeriod = LocalDate.now()
                .with(TemporalAdjusters.firstDayOfMonth())
                .atStartOfDay(zoneId)
                .toInstant()
                .toEpochMilli();
            int totalWorkoutCurrentMonth = getWorkoutCountForTime(username, currentMonthStartOfPeriod, endOfPeriod);

            return AnalyticsWorkoutsSummary.builder()
                .currentYear(currentYear.getValue())
                .previousYear(previousYear.getValue())
                .totalWorkouts(totalWorkouts)
                .totalWorkoutsCurrentYear(totalWorkoutsCurrentYear)
                .totalWorkoutsPreviousYear(totalWorkoutsPreviousYear)
                .totalDurationInMinutes((int) (totalDurationInSeconds / 60))
                .totalDurationInMinutesCurrentYear(totalWorkoutDurationCurrentYear)
                .totalDurationInMinutesPreviousYear(totalWorkoutDurationPreviousYear)
                .totalPictures(totalPictures)
                .totalComments(totalComments)
                .totalLikes(totalLikes)
                .totalPrivate(totalPrivateWorkouts)
                .totalForFollowers(totalForFollowers)
                .totalPublic(totalPublicWorkouts)
                .totalWithDescription(totalWorkoutsWithDescription)
                .workoutCountsForPreviousDays(workoutCountsInPeriods)
                .totalWorkoutCurrentWeek(totalWorkoutsCurrentWeek)
                .totalWorkoutCurrentMonth(totalWorkoutCurrentMonth)
                .build();
        } catch (SQLException e) {
            throw new InternalDataException(
                "Unable to fetch workout analytics summary from the local database: "
                    + e.getMessage(), e);
        }
    }

    private int getWorkoutCountForTime(String username, long from, long until)
    throws SQLException {
        return dao.countWorkoutsBetween(username, from, until);
    }

    private int getWorkoutDurationForTime(String username, long from, long until)
        throws SQLException {
        double totalDurationInSeconds = dao.getWorkoutDurationForTime(username, from, until);
        return (int) (totalDurationInSeconds / 60);
    }

    @WorkerThread
    public List<WorkoutHeader> findAllWhereOwner(List<User> ownerList, long limit)
        throws InternalDataException {
        try {
            List<String> owners = new ArrayList<>(ownerList.size());
            for (User user : ownerList) {
                owners.add(user.getUsername());
            }
            List<LocalWorkoutHeader> workoutHeaders;
            if (limit > 0L) {
                workoutHeaders = dao.findAllWhereOwner(owners, (int) limit);
            } else {
                workoutHeaders = dao.findAllWhereOwner(owners);
            }
            return workoutHeaders.stream().map(WorkoutMappersKt::toDomain).collect(Collectors.toList());
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to fetch workouts from local database: " + e.getMessage(), e);
        }
    }

    /**
     * @return all workout headers that have been changed locally (excluding deleted) .
     * @throws InternalDataException
     */
    public List<WorkoutHeader> findSyncedButLocallyChanged(String ownerUsername)
        throws InternalDataException {
        try {
            return dao.findSyncedButLocallyChanged(ownerUsername).stream()
                .map(WorkoutMappersKt::toDomain).collect(Collectors.toList());
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to fetch workouts from local database: " + e.getMessage(), e);
        }
    }

    public boolean markExtensionsFetched(int workoutId) throws InternalDataException {
        try {
            return dao.markExtensionsFetched(workoutId) > 0;
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to mark extensions as fetched: " + e.getMessage(), e);
        }
    }

    /**
     * @return all workout headers that have been marked as deleted
     * @throws InternalDataException
     */
    public List<WorkoutHeader> findAllDeleted(String ownerUsername) throws InternalDataException {
        try {
            return dao.findAllDeleted(ownerUsername).stream()
                .map(WorkoutMappersKt::toDomain).collect(Collectors.toList());
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to fetch workouts from local database: " + e.getMessage(), e);
        }
    }

    /**
     * @return all workout headers that have never been synced to backend.
     * @throws InternalDataException
     */
    public List<WorkoutHeader> findNeverSyncedWorkoutsForUser(String ownerUsername)
        throws InternalDataException {
        try {
            return dao.findNeverSyncedWorkoutsForUser(ownerUsername).stream()
                .map(WorkoutMappersKt::toDomain).collect(Collectors.toList());
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to fetch workouts from local database: " + e.getMessage(), e);
        }
    }

    /**
     * @return all workout headers that have already been synced to the backend.
     * @throws InternalDataException
     */
    public List<WorkoutHeader> findAllSynced() throws InternalDataException {
        try {
            return dao.findAllSynced().stream()
                .map(WorkoutMappersKt::toDomain).collect(Collectors.toList());
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to fetch workouts from local database: " + e.getMessage(), e);
        }
    }

    public List<WorkoutHeader> findManuallyCreatedWorkouts(String ownerUsername)
        throws InternalDataException {
        try {
            return dao.findManuallyCreatedWorkouts(ownerUsername).stream()
                .map(WorkoutMappersKt::toDomain).collect(Collectors.toList());
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to fetch workouts from local database: " + e.getMessage(), e);
        }
    }

    public List<WorkoutHeader> findByUserAndStartTime(String ownerUsername,
        long from, long till) throws InternalDataException {
        return findWorkoutHeaders(ownerUsername, null, from, till);
    }


    /**
     * Marks the given workout header as deleted
     *
     * @throws InternalDataException
     */
    @NonNull
    public WorkoutHeader markAsDeleted(@NonNull WorkoutHeader workoutHeader) throws InternalDataException {
        WorkoutHeader updated = workoutHeader.toBuilder().deleted().locallyChanged(true).build();

        try {
            LocalWorkoutHeader local = toLocal(updated);
            dao.upsertSync(local);
            if (updated.getUsername().equals(currentUserController.getUsername())) {
                currentUserWorkoutUpdatesSubject.onNext(new WorkoutUpdate(WorkoutUpdate.DELETED, updated));
            }
            workoutUpdatesSubject.onNext(new Object());
        } catch (Exception e) {
            Timber.w(e, "Failed to mark workout as deleted");
            throw new InternalDataException("Unable to store workout to local database: " + e.getMessage(), e);
        }

        return updated;
    }

    /**
     * Updates all the workout headers "locallyChanged" column to false for the
     * given workout keys
     *
     * @param updatedWorkoutKeys the workout to update
     * @throws InternalDataException
     */
    public void markAsSynced(List<String> updatedWorkoutKeys) throws InternalDataException {
        try {
            dao.markAsSynced(updatedWorkoutKeys);
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to mark workouts as synced from local database: " + e.getMessage(), e);
        }
    }

    public void markAsSeen(String workoutKey) throws InternalDataException {
        try {
            dao.markAsSeen(workoutKey);
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to mark workout as seen in local database: " + e.getMessage(), e);
        }
    }

    /**
     * Reassign all unsynced workouts to the given username
     *
     * @throws InternalDataException
     */
    public void takeOwnershipOfUnsyncedWorkouts(@NonNull String newUsername)
        throws InternalDataException {
        try {
            dao.takeOwnershipOfUnsyncedWorkouts(newUsername);
        } catch (Exception e) {
            throw new InternalDataException("Unable to update ownership from local database",
                e);
        }
    }

    /**
     * Returns a list of workouts which share the following properties with the reference workout:
     * <ul>
     * <li>same user</li>
     * <li>same activity type</li>
     * <li>total distance is within 10%</li>
     * <li>center point is similar (see
     * {@link WorkoutHeaderController.SimilarWorkoutsDistanceThreshold#centerPointTolerance}
     * )</li>
     * <li>stop point is similar (see
     * {@link WorkoutHeaderController.SimilarWorkoutsDistanceThreshold#stopPointTolerance}
     * )</li>
     * </ul>
     *
     * @param referenceWorkout the workout used as reference when searching for similar ones
     * @return the list of similar workouts based on the route properties sorted by duration
     * (shortest first)
     */
    @NonNull
    public List<WorkoutHeader> findWithSimilarRoute(@NonNull WorkoutHeader referenceWorkout)
        throws InternalDataException {
        Timber.d("WorkoutHeaderController.findWithSimilarRoute(%d)", referenceWorkout.getId());

        List<WorkoutHeader> similarWorkouts = findWithSimilarRoute(Collections.singletonList(referenceWorkout))
            .get(referenceWorkout);
        if (similarWorkouts == null) {
            similarWorkouts = Collections.emptyList();
        }

        Timber.d("WorkoutHeaderController.findWithSimilarRoute() Found %d", similarWorkouts.size());
        return similarWorkouts;
    }

    /**
     * Returns workouts which share the following properties with the reference workout:
     * <ul>
     * <li>same user</li>
     * <li>same activity type</li>
     * <li>total distance is within 10%</li>
     * <li>center point is similar (see
     * {@link WorkoutHeaderController.SimilarWorkoutsDistanceThreshold#centerPointTolerance}
     * )</li>
     * <li>stop point is similar (see
     * {@link WorkoutHeaderController.SimilarWorkoutsDistanceThreshold#stopPointTolerance}
     * )</li>
     * </ul>
     *
     * @param referenceWorkouts the workout used as reference when searching for similar ones
     * @return a map from the reference workout to a list of similar workouts based on the route
     * properties sorted by duration in ascending order (i.e. first one has shortest duration).
     */
    @NonNull
    public Map<WorkoutHeader, List<WorkoutHeader>> findWithSimilarRoute(
        @NonNull Collection<WorkoutHeader> referenceWorkouts) throws InternalDataException {
        Timber.d("WorkoutHeaderController.findWithSimilarRoute() - %d workout(s)", referenceWorkouts.size());
        List<WorkoutHeader> filteredReferenceWorkouts = referenceWorkouts.stream()
            .filter(referenceWorkout ->
                !referenceWorkout.getActivityType().isIndoor() &&
                    referenceWorkout.getTotalDistance() > 0.0 &&
                    !referenceWorkout.isPolylineEmpty() &&
                    referenceWorkout.getStartPosition() != null &&
                    referenceWorkout.getCenterPosition() != null &&
                    referenceWorkout.getStopPosition() != null
            ).collect(Collectors.toUnmodifiableList());
        return findWithSimilarDistance(filteredReferenceWorkouts).entrySet()
            .stream()
            .collect(
                Collectors.toUnmodifiableMap(
                    Map.Entry::getKey,
                    e -> {
                        WorkoutHeader referenceWorkout = e.getKey();
                        SimilarWorkoutsDistanceThreshold threshold =
                            SimilarWorkoutsDistanceThreshold.getByDistance(referenceWorkout.getTotalDistance());
                        return e.getValue()
                            .stream()
                            .filter(candidate -> WorkoutHeaderExtensionsKt.isSimilarRouteWorkout(candidate, referenceWorkout, threshold))
                            .collect(Collectors.toUnmodifiableList());
                    }
                )
            );
    }

    /**
     * Find all workouts with the same user, same activity type and total distance within 10% of
     * the reference workout.
     *
     * @return the list of similar workouts sorted by duration in ascending order (i.e. first one
     * has shortest duration).
     * @throws InternalDataException
     */
    @NonNull
    public List<WorkoutHeader> findWithSimilarDistance(@NonNull WorkoutHeader referenceWorkout) throws InternalDataException {
        Timber.d("WorkoutHeaderController.findWithSimilarDistance(%d)", referenceWorkout.getId());

        List<WorkoutHeader> similarWorkouts = findWithSimilarDistance(Collections.singletonList(referenceWorkout))
            .get(referenceWorkout);
        return similarWorkouts != null ? similarWorkouts : Collections.emptyList();
    }

    /**
     * Find all workouts with the same user, same activity type and total distance within 10% of
     * the reference workout.
     *
     * @return a map from the reference workout to the list of similar workouts sorted by duration
     * in ascending order (i.e. first one has shortest duration).
     * @throws InternalDataException
     */
    @NonNull
    private Map<WorkoutHeader, List<WorkoutHeader>> findWithSimilarDistance(
        @NonNull Collection<WorkoutHeader> referenceWorkouts) throws InternalDataException {
        Timber.d("WorkoutHeaderController.findWithSimilarDistance() - %d workout(s)", referenceWorkouts.size());
        if (referenceWorkouts.isEmpty()) {
            return Collections.emptyMap();
        }

        try {
            Set<String> usernames = new HashSet<>();
            Set<Integer> activityTypeIds = new HashSet<>();
            double minDistance = Double.MAX_VALUE;
            double maxDistance = 0.0;
            for (WorkoutHeader workout : referenceWorkouts) {
                usernames.add(workout.getUsername());
                activityTypeIds.add(workout.getActivityTypeId());
                minDistance = Math.min(workout.getTotalDistance(), minDistance);
                maxDistance = Math.max(workout.getTotalDistance(), maxDistance);
            }
            if (minDistance <= 0.0 || maxDistance <= 0.0) {
                return Collections.emptyMap();
            }

            minDistance *= 0.9;
            maxDistance *= 1.1;
            Map<WorkoutHeader, List<WorkoutHeader>> results = new HashMap<>();
            List<LocalWorkoutHeader> localWorkoutHeaders =
                dao.findWithDistanceBetween(usernames, activityTypeIds, minDistance, maxDistance);
            for (LocalWorkoutHeader localWorkoutHeader : localWorkoutHeaders) {
                WorkoutHeader workoutHeader = WorkoutMappersKt.toDomain(localWorkoutHeader);
                referenceWorkouts.stream()
                    .filter(referenceWorkout ->
                        referenceWorkout.getActivityTypeId() == workoutHeader.getActivityTypeId() &&
                            TextUtils.equals(referenceWorkout.getUsername(), workoutHeader.getUsername()) &&
                            referenceWorkout.getTotalDistance() * 0.9 <= workoutHeader.getTotalDistance() &&
                            referenceWorkout.getTotalDistance() * 1.1 >= workoutHeader.getTotalDistance()
                    ).forEach(reference ->
                        results.computeIfAbsent(reference, k -> new ArrayList<>())
                            .add(workoutHeader)
                    );
            }
            return results;
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to fetch similar distance workouts from local database: " + e.getMessage(),
                e);
        }
    }

    /**
     * Find whether there is a workout with the same user, same activity type and total distance
     * and timestamp
     * @param activityId - activity type id
     * @param stamp - timestamp of the start date
     * @return whether do we have such workout
     * @throws InternalDataException
     */
    public boolean isWorkoutSyncedWithWatch(@Nullable Integer activityId, @Nullable Long stamp)
        throws InternalDataException {
        try {
            if (activityId == null || stamp == null) return false;
            return dao.findWithStartTime(activityId, stamp).size() > 0;
        } catch (Exception ignored) {
            return false;
        }
    }

    /**
     * @param activityType null to find all non-deleted workouts
     */
    @WorkerThread
    @NonNull
    public List<WorkoutHeader> findNotDeletedWorkouts(
        @NonNull String username, @NonNull final ActivityType activityType) throws InternalDataException {
        try {
            return dao.findNotDeletedWorkouts(username, activityType.getId())
                .stream()
                .map(WorkoutMappersKt::toDomain)
                .collect(Collectors.toList());
        } catch (Exception e) {
            throw new InternalDataException("Unable to find not deleted workouts from local database", e);
        }
    }

    @WorkerThread
    public long count(String username) throws InternalDataException {
        try {
            return dao.count(username);
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to fetch workouts from local database: " + e.getMessage(), e);
        }
    }

    @WorkerThread
    public long syncedWorkoutsCount(String username) throws InternalDataException {
        try {
            return dao.syncedWorkoutsCount(username);
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to fetch workouts from local database: " + e.getMessage(), e);
        }
    }

    @WorkerThread
    public boolean hasAny(String username) throws InternalDataException {
        try {
            return dao.any(username) != null;
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to fetch workouts from local database: " + e.getMessage(), e);
        }
    }

    @WorkerThread
    @NonNull
    public List<WorkoutHeader> loadWorkouts(final String username, final ActivityType activityType) {
        return dao.loadWorkouts(username, activityType.getId())
            .stream()
            .map(WorkoutMappersKt::toDomain)
            .collect(Collectors.toList());
    }

    @NonNull
    public List<WorkoutTSSSummary> loadWorkoutTSSSummaries(final String username, final long since)
        throws InternalDataException {
        try {
            List<WorkoutTSSSummary> summaries = dao.loadWorkoutsTSSSummary(username, since)
                .stream().map(local -> new WorkoutTSSSummary(
                    local.getId(), local.getKey(), local.getUsername(), local.getStartTime(),
                    local.getEndTime(), TSSMappersKt.toDomain(local.getTss()), local.getActivityId()
                )).collect(Collectors.toList());

            // Filter unwanted 0 TSSs and errors we've mapped to 0 TSS
            // RawRowMapper's docs say that we should be able to do this by returning null in the mapper
            // above, but that causes ORMLite to throw errors that cause the whole load to fail
            Iterator<WorkoutTSSSummary> summaryIterator = summaries.iterator();
            while (summaryIterator.hasNext()) {
                WorkoutTSSSummary summary = summaryIterator.next();
                if (summary.getTss().getTrainingStressScore() == 0f) {
                    summaryIterator.remove();
                }
            }

            return summaries;
        } catch (Throwable e) {
            throw new InternalDataException(
                "Unable to fetch workout TSS summaries from local database: " + e.getMessage(), e);
        }
    }

    /**
     * Subscribe to this observable to receive notifications about changes in current user
     * workout headers.
     */
    @NonNull
    public Observable<WorkoutUpdate> getCurrentUserWorkoutUpdatesAsObservable() {
        return currentUserWorkoutUpdatesSubject.asObservable()
            .onBackpressureBuffer(
                MAX_UPDATES_BUFFER,
                () -> Timber.w("Overflowing workout updates!!! dropping oldest to avoid backpressure exception"),
                BackpressureOverflow.ON_OVERFLOW_DROP_OLDEST);
    }

    @NonNull
    public Observable<Object> getWorkoutUpdatesAsObservable() {
        return workoutUpdatesSubject.asObservable();
    }

    @WorkerThread
    public int loadUnseenWorkoutsCount(final String username) {
        return dao.countUnseenWorkouts(username);
    }

    /**
     * Loads local DB workout ID for workout key if available. This method is only meant to be used on
     * workout headers that were loaded from the server.
     *
     * @param fetchedWorkoutHeader {@link WorkoutHeader} that <em>must</em> contain a workout key.
     * @return A new {@link WorkoutHeader} instance with its existing local DB ID, if such workout key
     * was found.
     *
     * @throws IllegalStateException in case the provided {@link WorkoutHeader#getKey()} is null
     */
    @NonNull
    public WorkoutHeader loadExistingLocalWorkoutIdForFetchedWorkout(@NonNull WorkoutHeader fetchedWorkoutHeader) {
        String key = fetchedWorkoutHeader.getKey();
        if (key == null) {
            throw new IllegalStateException("Cannot load local workout ID because workout key is null");
        }
        WorkoutHeader existingWorkoutHeader = find(key);
        int workoutId;
        if (existingWorkoutHeader != null) {
            workoutId = existingWorkoutHeader.getId();
        } else {
            workoutId = fetchedWorkoutHeader.getId();
        }

        return fetchedWorkoutHeader.toBuilder()
            .id(workoutId)
            .build();
    }

    public long loadActivityTypeCount(int activityType) throws InternalDataException {
        try {
            return dao.countOfActivityId(currentUserController.getUsername(), activityType);
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to fetch activitytype count from local database: " + e.getMessage(), e);
        }
    }

    public WorkoutHeader loadFastestOfActivityTypeInPeriod(int activityType, long since, long till)
        throws InternalDataException {
        try {
            LocalWorkoutHeader localWorkoutHeader = dao.loadFastestOfActivityTypeInPeriod(
                currentUserController.getUsername(), activityType, since, till);
            return localWorkoutHeader != null ? toDomain(localWorkoutHeader) : null;
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to fetch fastest workout of activitytype from local database: "
                    + e.getMessage(), e);
        }
    }

    @Nullable
    public WorkoutHeader loadShortestTimeOfActivityTypeInPeriodWithinDistance(int activityType, long since, long till, double minDistance, double maxDistance)
        throws InternalDataException {
        try {
            LocalWorkoutHeader localWorkoutHeader = dao.loadShortestTimeOfActivityTypeInPeriodWithDistanceRange(
                currentUserController.getUsername(), activityType, minDistance, maxDistance, since, till);
            return localWorkoutHeader != null ? toDomain(localWorkoutHeader) : null;
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to fetch fastest workout of activitytype from local database: "
                    + e.getMessage(), e);
        }
    }

    public WorkoutHeader loadFarthestOfActivityTypeInPeriod(int activityType, long since, long till)
        throws InternalDataException {
        try {
            LocalWorkoutHeader localWorkoutHeader = dao.loadFarthestOfActivityTypeInPeriod(
                currentUserController.getUsername(), activityType, since, till);
            return localWorkoutHeader != null ? toDomain(localWorkoutHeader) : null;
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to fetch farthest workout of activitytype from local database: "
                    + e.getMessage(), e);
        }
    }

    @Nullable
    public WorkoutHeader loadLongestOfActivityTypeInPeriod(int activityType, long since, long till)
        throws InternalDataException {
        try {
            LocalWorkoutHeader localWorkoutHeader = dao.loadLongestOfActivityTypeInPeriod(
                currentUserController.getUsername(), activityType, since, till);
            return localWorkoutHeader != null ? toDomain(localWorkoutHeader) : null;
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to fetch longest workout of activitytype from local database: "
                    + e.getMessage(), e);
        }
    }

    @Nullable
    public WorkoutHeader loadHighestAltitudeOfActivityTypeInPeriod(int activityType, long since, long till)
        throws InternalDataException {
        try {
            LocalWorkoutHeader localWorkoutHeader = dao.loadHighestAltitudeOfActivityTypeInPeriod(
                currentUserController.getUsername(), activityType, since, till);
            return localWorkoutHeader != null ? toDomain(localWorkoutHeader) : null;
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to fetch highest altitude workout of activitytype from local database: "
                    + e.getMessage(), e);
        }
    }

    @Nullable
    public WorkoutHeader loadHighestClimbOfActivityTypeInPeriod(int activityType, long since, long till)
        throws InternalDataException {
        try {
            LocalWorkoutHeader localWorkoutHeader = dao.loadHighestClimbOfActivityTypeInPeriod(
                currentUserController.getUsername(), activityType, since, till);
            return localWorkoutHeader != null ? toDomain(localWorkoutHeader) : null;
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to fetch highest climb workout of activitytype from local database: "
                    + e.getMessage(), e);
        }
    }

    @Nullable
    public WorkoutHeader loadFastestPaceOfActivityTypeInPeriod(int activityType, long since, long till)
        throws InternalDataException {
        try {
            LocalWorkoutHeader localWorkoutHeader = dao.loadFastestPaceOfActivityTypeInPeriod(
                currentUserController.getUsername(), activityType, since, till);
            return localWorkoutHeader != null ? toDomain(localWorkoutHeader) : null;
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to fetch fastest cadence workout of activitytype from local database: "
                    + e.getMessage(), e);
        }
    }

    public long loadActivityCountInPeriod(long since, long till) throws InternalDataException {
        try {
            return dao.countActivitiesInPeriod(currentUserController.getUsername(), since, till, null);
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to fetch activity count in period from local database: " + e.getMessage(), e);
        }
    }

    public long loadActivityTypeCountInPeriod(int activityType, long since, long till)
        throws InternalDataException {
        try {
            return dao.countActivitiesInPeriod(currentUserController.getUsername(), since, till, activityType);
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to fetch activitytype count in period from local database: "
                    + e.getMessage(), e);
        }
    }

    public WorkoutHeader loadLatestOfActivityType(int activityType, long till)
        throws InternalDataException {
        try {
            LocalWorkoutHeader localWorkoutHeader = dao.loadLatestOfActivityType(
                currentUserController.getUsername(), activityType, till);
            return localWorkoutHeader != null ? toDomain(localWorkoutHeader) : null;
        } catch (Exception e) {
            throw new InternalDataException(
                "Unable to fetch latest workout of activitytype from local database: "
                    + e.getMessage(), e);
        }
    }

    @Nullable
    @WorkerThread
    public WorkoutHeader findNotDeletedWithUserTags(int workoutId) {
        Map<LocalWorkoutHeader, List<LocalUserTag>> result =
            dao.findNotDeletedWorkoutHeadersAndMatchingUserTagsById(workoutId);

        if (result.isEmpty() || !result.keySet().stream().findFirst().isPresent()) {
            return null;
        }

        LocalWorkoutHeader localWorkoutHeader = result.keySet().stream().findFirst().get();
        List<LocalUserTag> localUserTags = result.getOrDefault(localWorkoutHeader, Collections.emptyList());
        return WorkoutMappersKt.toDomain(localWorkoutHeader, localUserTags);
    }

    @NonNull
    public List<WorkoutHeader> findAllWorkoutHeadersAndMatchingUserTags(String username) throws InternalDataException {
        try {
            return dao.findAllWorkoutHeadersAndMatchingUserTags(username)
                .entrySet()
                .stream()
                .map(entry -> WorkoutMappersKt.toDomain(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());
        } catch (Throwable e) {
            throw new InternalDataException(
                "Unable to fetch workouts from local database: " + e.getMessage(), e);
        }
    }

    @NonNull
    public List<WorkoutHeader> findWorkoutsWithUnsyncedUserTags() throws InternalDataException {
        try {
            return dao.findWorkoutsWithUnsyncedUserTags()
                .entrySet()
                .stream()
                .map(entry -> WorkoutMappersKt.toDomain(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());
        } catch (Throwable e) {
            throw new InternalDataException(
                "Unable to fetch workouts with unsynced user tags from local database: " + e.getMessage(), e);
        }
    }

    public enum SimilarWorkoutsDistanceThreshold {
        THRESHOLD_0(0, 100, 150),
        THRESHOLD_1(5000, 100, 200),
        THRESHOLD_2(10000, 150, 400),
        THRESHOLD_3(40000, 200, 800),
        THRESHOLD_4(80000, 400, 1200),
        THRESHOLD_5(150000, 2000, 1500);

        private final int centerPointTolerance;
        private final int stopPointTolerance;
        private final int distanceThreshold;

        public int getCenterPointTolerance() {
            return centerPointTolerance;
        }

        public int getStopPointTolerance() {
            return stopPointTolerance;
        }

        /**
         * @param distanceThreshold minimum distance in meters for which this threshold applies.
         */
        SimilarWorkoutsDistanceThreshold(int distanceThreshold, int centerPointTolerance,
            int stopPointTolerance) {
            this.distanceThreshold = distanceThreshold;
            this.centerPointTolerance = centerPointTolerance;
            this.stopPointTolerance = stopPointTolerance;
        }

        /**
         * Checks which bucket the given distance belongs to. That is, if distance is between 5000
         * and 10000
         * (THRESHOLD_1 and THRESHOLD_2) then THRESHOLD_1 will be returned.
         *
         * @return the threshold that matches the given distance.
         */
        public static SimilarWorkoutsDistanceThreshold getByDistance(double distance) {
            if (distance <= THRESHOLD_1.distanceThreshold) {
                return THRESHOLD_0;
            } else if (distance > THRESHOLD_1.distanceThreshold
                && distance <= THRESHOLD_2.distanceThreshold) {
                return THRESHOLD_1;
            } else if (distance > THRESHOLD_2.distanceThreshold
                && distance <= THRESHOLD_3.distanceThreshold) {
                return THRESHOLD_2;
            } else if (distance > THRESHOLD_3.distanceThreshold
                && distance <= THRESHOLD_4.distanceThreshold) {
                return THRESHOLD_3;
            } else if (distance > THRESHOLD_4.distanceThreshold
                && distance <= THRESHOLD_5.distanceThreshold) {
                return THRESHOLD_4;
            } else {
                return THRESHOLD_5;
            }
        }
    }

    public static class WorkoutUpdate {
        public static final int CREATED = 0;
        public static final int UPDATED = 1;
        public static final int DELETED = 2;
        /**
         * One of {@link Operation}
         */
        @Operation
        public final int operation;
        @NonNull
        public final WorkoutHeader workoutHeader;

        public WorkoutUpdate(@Operation int operation, @NonNull WorkoutHeader workoutHeader) {
            this.operation = operation;
            this.workoutHeader = workoutHeader;
        }

        @Override
        public String toString() {
            return "WorkoutUpdate{"
                + "operation="
                + operation
                + ", workoutHeader="
                + workoutHeader
                + '}';
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;

            WorkoutUpdate that = (WorkoutUpdate) o;

            if (operation != that.operation) return false;
            return workoutHeader.equals(that.workoutHeader);
        }

        @Override
        public int hashCode() {
            int result = operation;
            result = 31 * result + workoutHeader.hashCode();
            return result;
        }

        @Retention(RetentionPolicy.SOURCE)
        @IntDef({ CREATED, UPDATED, DELETED })
        public @interface Operation {
        }
    }
    @NonNull
    public List<WorkoutHeader> findPagedByTimeRange(
        String username,
        long sinceMs,
        long untilMs,
        Integer includeActivityTypeId,
        Set<Integer> excludeActivityTypeIds,
        int page,
        int firstPageSize,
        int pageSize) throws InternalDataException {
        try {
            return dao.findPagedByTimeRange(
                    username,
                    sinceMs,
                    untilMs,
                    includeActivityTypeId,
                    excludeActivityTypeIds,
                    page,
                    firstPageSize,
                    pageSize)
                .stream().map(WorkoutMappersKt::toDomain).collect(Collectors.toList());
        } catch (Throwable e) {
            throw new InternalDataException(
                "Unable to fetch workouts from local database: " + e.getMessage(), e);
        }
    }
}
