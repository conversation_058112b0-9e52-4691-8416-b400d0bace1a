package com.stt.android.controllers

import com.google.android.gms.maps.model.LatLngBounds
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.domain.user.BackendUserWorkoutPair
import com.stt.android.domain.workouts.WorkoutHeader
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ExploreController @Inject constructor(
    private val workoutHeaderController: WorkoutHeaderController,
    private val currentUserController: CurrentUserController,
    private val backendController: BackendController,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) {
    suspend fun findWorkouts(bounds: LatLngBounds): List<WorkoutHeader> =
        withContext(coroutinesDispatchers.io) {
            backendController.findWorkouts(
                currentUserController.session,
                bounds.southwest.latitude,
                bounds.southwest.longitude,
                bounds.northeast.latitude,
                bounds.northeast.longitude,
                EXPLORE_LIMIT
            ).mapNotNull(::toWorkoutCardInfo)
        }

    private fun toWorkoutCardInfo(userWorkoutPair: BackendUserWorkoutPair): WorkoutHeader? = try {
        userWorkoutPair.backendWorkout
            .getWorkoutHeader(workoutHeaderController)
    } catch (e: NullPointerException) {
        // there are cases when it crashes for null pointer exceptions, let's try to figure out
        // what's happening
        // https://www.fabric.io/sporst-tracker/android/apps/com.stt.android/issues/55f04138f5d3a7f76be5eb59
        Timber.w(e, "Failed to convert workout to workout card info")
        null
    }

    companion object {
        private const val EXPLORE_LIMIT = 50
    }
}
