package com.stt.android.refreshable

import android.content.SharedPreferences
import androidx.core.content.edit
import com.stt.android.utils.STTConstants
import javax.inject.Inject

interface RefreshablesInitialRunDoneStore {
    fun hasDoneInitialRefreshForLogin(): Boolean
    fun setInitialRefreshForLoginDone()
}

class SharedPrefsRefreshablesInitialRunDoneStore
@Inject constructor(
    private val sharedPreferences: SharedPreferences
) : RefreshablesInitialRunDoneStore {
    override fun hasDoneInitialRefreshForLogin(): Boolean {
        return sharedPreferences.getBoolean(
            STTConstants.DefaultPreferences.KEY_HAS_RUN_INITIAL_REFRESH,
            false
        )
    }

    override fun setInitialRefreshForLoginDone() {
        sharedPreferences
            .edit {
                putBoolean(STTConstants.DefaultPreferences.KEY_HAS_RUN_INITIAL_REFRESH, true)
            }
    }
}
