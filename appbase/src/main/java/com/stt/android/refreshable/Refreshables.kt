package com.stt.android.refreshable

import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.SessionController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.refreshable.Refreshable
import com.stt.android.refreshable.Refreshables.Companion.MIN_TIME_BETWEEN_RUNS
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.yield
import timber.log.Timber
import java.time.Clock
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * A launch point for all [Refreshable]s. Only one refresh can run at one time, other requests
 * will be ignored if a refresh is already running. If you need to make sure that something gets
 * synced, use [SyncRequestHandler] directly to queue up work.
 *
 * This class is injected with all [Refreshable]s and executes all of them
 * sequentially in a predefined order specified in [RefreshablesModule].
 *
 * As most [Refreshable]s handle syncing user data, a check for user being logged in
 * is done before running each of the [Refreshable]s and executing them is stopped if
 * there isn't active user session or log out is in progress.
 *
 * By default limits the rate how often the Refreshables are run, but can be given a flag
 * to skip the rate limiting check.
 */
@Singleton
class Refreshables
@Inject constructor(
    private val refreshables: Set<@JvmSuppressWildcards Refreshable>,
    private val clock: Clock,
    private val sessionController: SessionController,
    private val currentUserController: CurrentUserController,
    private val initialRunDoneStore: RefreshablesInitialRunDoneStore
) {
    private val mutex = Mutex()
    private var lastRunTimestampMs = 0L

    private val _isRefreshing = MutableStateFlow(false)
    val isRefreshing: StateFlow<Boolean> = _isRefreshing.asStateFlow()

    suspend fun refresh(skipRateLimiting: Boolean = false) {
        if (!shouldRunNow(skipRateLimiting)) {
            return
        }

        if (!mutex.tryLock()) {
            return
        }

        try {
            _isRefreshing.value = true

            for (refreshable in refreshables) {
                if (sessionController.isLoggingOut || !currentUserController.isLoggedIn) {
                    break
                }

                runSuspendCatching {
                    refreshable.refresh()
                }.onFailure { e ->
                    Timber.w(e, "Failed to refresh $refreshable")
                }

                yield() // Cancellation check
            }

            lastRunTimestampMs = clock.millis()
            initialRunDoneStore.setInitialRefreshForLoginDone()
        } finally {
            _isRefreshing.value = false
            mutex.unlock()
        }
    }

    private fun shouldRunNow(skipRateLimiting: Boolean): Boolean {
        return if (mutex.isLocked) {
            false
        } else {
            skipRateLimiting || hasSurpassedRatelimitTimeframe() || !initialRunDoneStore.hasDoneInitialRefreshForLogin()
        }
    }

    /**
     * Checks if there's been over [MIN_TIME_BETWEEN_RUNS] milliseconds since last run refresh
     */
    private fun hasSurpassedRatelimitTimeframe(): Boolean {
        return lastRunTimestampMs == 0L || clock.millis() - lastRunTimestampMs > MIN_TIME_BETWEEN_RUNS
    }

    companion object {
        private val MIN_TIME_BETWEEN_RUNS = TimeUnit.MINUTES.toMillis(10)
    }
}
