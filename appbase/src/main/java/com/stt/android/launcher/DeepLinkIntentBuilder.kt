package com.stt.android.launcher

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.text.TextUtils
import androidx.core.net.toUri
import com.stt.android.R
import com.stt.android.controllers.CurrentUserController

const val SETTINGS = "settings"
const val ACCOUNT = "account"
const val COUNTRY = "country"
const val USER_SETTINGS = "usersettings"
const val SCREEN_BACKLIGHT = "screenbacklight"
const val ALTITUDE_SOURCE = "altitudesource"
const val ALTITUDE_OFFSET = "altitudeoffset"
const val NOTIFICATIONS = "notifications"
const val FOLLOWER_APPROVAL = "followerapproval"
const val EXPLORE = "explore"
const val ROUTES = "routes"
const val MAP = "map"
const val MAP_STYLE = "style"
const val MAPS = "maps"
const val PEOPLE = "people"
const val WORKOUT = "workout"
const val LATEST_WORKOUT = "latest"
const val SHARE_LATEST_WORKOUT = "sharelatest"
const val SHARE_LATEST_COMMUTE_WORKOUT = "sharelatestcommute"
const val FEED = "feed"
const val MANUAL_WORKOUT = "manualworkout"
const val WEEKLY_GOAL = "weeklygoal"
const val CO2_REDUCED = "co2reduced"
const val MARKETINGINBOX = "marketinginbox"
const val PHONE_SETTINGS = "phonesettings"
const val BATTERY_SETTINGS = "batterysettings"
const val POWER_MANAGEMENT_SETTINGS = "powermanagementsettings"
const val PRIVACY_SETTINGS = "privacysettings"
const val WEEK_CALENDAR = "weekview"
const val MONTH_CALENDAR = "monthview"
const val YEAR_CALENDAR = "yearview"
const val SUBSCRIBE_MARKETING_CONSENT = "subscribemarketingconsent"
const val SHARE_YEAR = "shareyear"
const val SHARE_YEAR_DATE = "sharesummaryyear"
const val SHARE_MONTH = "sharesummarymonth"
const val SHARE_30_DAYS = "sharesummary30d"
const val SHARE_WEEK = "sharesummaryweek"
const val DIARY = "diary"
const val PROGRESS = "progress"
const val SUMMARY = "summary"
const val RECOVERY = "recovery"
const val QUESTIONNAIRE = "questionnaire"
const val QUESTIONNAIRE_MOTIVATIONS = "motivations"
const val MENSTRUAL_CYCLE = "menstrualcycle"
const val LOG = "log"
const val ONBOARDING = "onboarding"
const val AI_PROGRAMS = "myplan"

/**
 * Interface for building Intent for deeplinks
 */
interface DeepLinkIntentBuilder {
    /**
     * Match the uri fragment parts to supported deeplinks.
     * If a match is found, an Intent instance is returned otherwise this methods returns null.
     * @param context Context
     * @param currentUserController [CurrentUserController] instance
     * @param fragmentOrPathParts the URI broken into parts
     * @param type the type of the deeplink
     * @return Intent instance or null
     */
    fun getDeepLinkIntent(
        context: Context,
        uri: Uri,
        currentUserController: CurrentUserController,
        fragmentOrPathParts: Array<String>,
        type: String,
    ): Intent?

    fun getAuthIntent(context: Context, type: String, uri: Uri): Intent?

    fun getTestIntent(context: Context, type: String): Intent?

    fun getOtaIntent(context: Context, uri: Uri): Intent?

    fun getUserIntent(context: Context, uri: Uri): Intent

    fun getWorkoutIntent(context: Context, uri: Uri): Intent

    fun getFollowersIntent(context: Context): Intent

    fun getDeepLinkIntentFromInstallReferral(
        context: Context,
        installReferrer: String,
        currentUserController: CurrentUserController
    ): Intent? {
        val uriString = "${context.getString(R.string.app_deeplink_url_scheme)}://$installReferrer"
        val parts = installReferrer.split("/")
        val type = parts.getOrNull(1) ?: ""
        return getDeepLinkIntent(
            context = context,
            uri = uriString.toUri(),
            currentUserController = currentUserController,
            fragmentOrPathParts = parts.toTypedArray(),
            type = type
        )
    }

    fun getFragmentsOrPathParts(uri: Uri): Array<String> {
        var fragmentOrPath = uri.fragment
        if (TextUtils.isEmpty(fragmentOrPath)) {
            fragmentOrPath = uri.path
        }
        return fragmentOrPath?.split("/")?.toTypedArray() ?: emptyArray()
    }
}
