package com.stt.android.launcher

import androidx.lifecycle.ViewModel
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.domain.workouts.WorkoutHeader
import kotlinx.coroutines.withContext

abstract class BaseProxyViewModel(
    private val workoutHeaderController: WorkoutHeaderController,
    private val currentUserController: CurrentUserController,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : ViewModel() {
    suspend fun findLatestShareableWorkout(): WorkoutHeader? = withContext(coroutinesDispatchers.io) {
        workoutHeaderController.findLatestNotDeletedShareableWorkout(currentUserController.username)
    }

    suspend fun findLatestShareableCommuteWorkout(): WorkoutHeader? = withContext(coroutinesDispatchers.io) {
        workoutHeaderController.findLatestNotDeletedShareableCommute<PERSON>orkout(currentUserController.username)
    }

    suspend fun findLatestWorkout(): WorkoutHeader? = withContext(coroutinesDispatchers.io) {
        workoutHeaderController.findLatestNotDeletedWorkout(currentUserController.username)
    }
}
