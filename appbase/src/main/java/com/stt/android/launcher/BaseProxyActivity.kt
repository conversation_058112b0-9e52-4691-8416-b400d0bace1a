package com.stt.android.launcher

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.os.BuildCompat
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.lifecycle.lifecycleScope
import androidx.tracing.trace
import com.helpshift.support.ApiConfig
import com.helpshift.support.Metadata
import com.helpshift.support.Support
import com.stt.android.R
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue.AppOpenSourceType.APP_ICON
import com.stt.android.analytics.AnalyticsPropertyValue.AppOpenSourceType.DEEP_LINK
import com.stt.android.analytics.AppOpenAnalytics
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.di.AppVersionNumberForSupport
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator
import com.stt.android.help.HelpshiftHelper
import com.stt.android.help.LoadSupportMetadataUseCase
import com.stt.android.home.HomeActivityNavigator
import com.stt.android.infomodel.SummaryItem
import com.stt.android.multimedia.sportie.SportieShareSource
import com.stt.android.session.SignInFlowHook
import com.stt.android.social.userprofile.UserProfileActivity
import com.stt.android.ui.tasks.LogoutTask
import com.stt.android.utils.BrandCampaignTracker
import com.stt.android.utils.STTConstants
import com.stt.android.workouts.sharepreview.WorkoutSharePreviewActivity
import dagger.Lazy
import kotlinx.coroutines.launch
import kotlinx.coroutines.rx2.await
import timber.log.Timber
import java.util.Locale
import javax.inject.Inject
import javax.inject.Named

/**
 * This activity is responsible to decide what activity to show to the user depending on different
 * scenarios (e.g. user opened a sports tracker link from somewhere else in android)
 */
abstract class BaseProxyActivity : AppCompatActivity() {

    @Inject
    internal lateinit var currentUserController: CurrentUserController

    @Inject
    internal lateinit var userSettingsController: UserSettingsController

    @Inject
    internal lateinit var workoutHeaderController: WorkoutHeaderController

    @Inject
    internal lateinit var intentBuilder: DeepLinkIntentBuilder

    @Inject
    internal lateinit var brandCampaignTracker: BrandCampaignTracker

    @Inject
    lateinit var signInFlowHook: SignInFlowHook

    @Inject
    lateinit var logoutTask: Lazy<LogoutTask>

    internal val viewModel: ProxyViewModel by viewModels()

    @Inject
    @AppVersionNumberForSupport
    internal lateinit var appVersionNumberForSupport: String

    @JvmField
    @Inject
    @Named("GooglePlayServicesAvailable")
    var isGooglePlayServicesAvailable = false

    @Inject
    lateinit var homeActivityNavigator: HomeActivityNavigator

    @Inject
    lateinit var rewriteNavigator: WorkoutDetailsRewriteNavigator

    @Inject
    lateinit var loadSupportMetadataUseCase: LoadSupportMetadataUseCase

    @Inject
    lateinit var amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker

    @Inject
    lateinit var appOpenAnalytics: AppOpenAnalytics

    private lateinit var startIntent: Intent

    private var uri: Uri? = null

    override fun onCreate(savedInstanceState: Bundle?) = trace("BaseProxyActivity.onCreate") {
        // Keep the Android 12 OS provided splash screen visible instead of showing
        // this Activity's windowBackground based splash.
        if (BuildCompat.isAtLeastS()) {
            installSplashScreen().apply {
                setKeepOnScreenCondition { true }
            }
        }

        super.onCreate(savedInstanceState)

        trackOpenedFromAnalyticsEvent(intent)
        trackAppOpen(intent)

        startIntent = homeActivityNavigator.newStartIntent(
            this,
            fromNewMapNotification = false,
            newRoute = false,
            showHeartRateSetting = false
        )

        if (currentUserController.currentUser.isSessionKeyMissing) {
            // Force logout in case deserializing UserSession from a blob in OrmLite had failed
            // during migration
            forceLogout()
            return@trace
        }

        if (currentUserController.isLoggedIn && isLoginToFacebookNeeded()) {
            if (loginToFacebook()) {
                return@trace
            }
        }

        // Parse the URL and open the correct activity or fall back to HomeActivity
        uri = intent.data
        uri?.let { existingUri ->
            val intentBasedOnUri = processUri(existingUri)
            intentBasedOnUri?.let {
                startIntent = it
                startIntent.flags = when {
                    existingUri.isUserUri() || existingUri.isWorkoutUri() || existingUri.isFollowersUri() -> 0
                    existingUri.isAuthUri() ->
                        // We're showing this view from a link, so let's clear the task stack in
                        // case if it is not an auth request
                        Intent.FLAG_ACTIVITY_CLEAR_TOP
                    else ->
                        // By default clear the back stack and start a new task
                        (
                            Intent.FLAG_ACTIVITY_NEW_TASK
                                or Intent.FLAG_ACTIVITY_CLEAR_TASK
                                or Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS
                            )
                }
            }
        }

        // Some deeplinks require to open for example a workout that fulfills a condition e.g. the latest. To navigate
        // to the right view we need to first obtain user's data.
        // If user is logged in, we do rx calls in viewmodel and after receiving data act accordingly.
        // Otherwise a new instance of this activity is launched after succesful login to obtain data etc.
        val deepLinkExtra: String? = startIntent.getStringExtra(STTConstants.ExtraKeys.DEEPLINK)

        if (uri?.isAppleAuthUri() == true) {
            // If this is Apple auth URI to sign in the user, do that regardless if the user was
            // logged in or not. Other auth URIs are use cases where the user needs to be logged
            // in already.
            openDeepLinkActivity(uri, startIntent)
        } else if (userIsNotLoggedIn()) {
            // Check if the user is logged in or not and force login if not

            // If user's data is needed before resolving activity to start, startIntent does not contain information
            // about activity to start and we need to launch fallback activity.
            val userDataNeeded = (deepLinkExtra != null)
            if (userDataNeeded) {
                startIntent = newStartIntentClearStack(this).apply { data = uri }
            }
            // We don't want the user to come back here so clear the task stack on login successful
            startIntent.apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            }
            openDeepLinkActivity(
                uri,
                signInFlowHook.newStartIntent(
                    this,
                    startIntent
                )
            )
        } else {
            when (deepLinkExtra) {
                LATEST_WORKOUT -> {
                    lifecycleScope.launch {
                        val latestWorkout = viewModel.findLatestWorkout()
                            ?: return@launch
                        val intent = rewriteNavigator.createIntent(
                            context = this@BaseProxyActivity,
                            username = latestWorkout.username,
                            workoutId = latestWorkout.id,
                            workoutKey = latestWorkout.key,
                        )
                        openDeepLinkActivity(uri, intent)
                    }
                }
                SHARE_LATEST_WORKOUT -> {
                    lifecycleScope.launch {
                        val latestShareableWorkout = viewModel.findLatestShareableWorkout()
                            ?: return@launch
                        val (intent, _) = WorkoutSharePreviewActivity.newStartIntent(
                            latestShareableWorkout,
                            this@BaseProxyActivity,
                            0,
                            SportieShareSource.UNKNOWN
                        )
                        openDeepLinkActivity(uri, intent)
                    }
                }
                SHARE_LATEST_COMMUTE_WORKOUT -> {
                    lifecycleScope.launch {
                        val workoutHeader = viewModel.findLatestShareableCommuteWorkout()
                        val intent = if (workoutHeader != null) {
                            WorkoutSharePreviewActivity.newStartIntent(
                                workoutHeader = workoutHeader,
                                context = this@BaseProxyActivity,
                                itemIndex = 0,
                                source = SportieShareSource.UNKNOWN,
                                defaultSummaryItems = listOf(SummaryItem.CO2EMISSIONSREDUCED)
                            ).first
                        } else {
                            // Fallback to home if no shareable commute workout is found
                            homeActivityNavigator.newStartIntent(
                                this@BaseProxyActivity,
                                fromNewMapNotification = false,
                                newRoute = false,
                                showHeartRateSetting = false
                            )
                        }

                        openDeepLinkActivity(uri, intent)
                    }
                }
                else -> openDeepLinkActivity(uri, startIntent)
            }
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        trackAppOpen(intent)
    }

    protected open fun userIsNotLoggedIn() = !currentUserController.isLoggedIn

    protected abstract fun isLoginToFacebookNeeded(): Boolean
    protected abstract fun loginToFacebook(): Boolean

    private fun openDeepLinkActivity(uri: Uri?, intent: Intent) {
        startActivity(intent)

        // we open the HelpShift activity on top of whatever activity we should open, as a hack
        // to get the "back" stack working
        if (HELPSHIFT_URL == uri?.authority) {
            val pid = uri.getQueryParameter("pid")
            val helper = HelpshiftHelper()
            val apiConfigBuilder = ApiConfig.Builder()
                .setCustomMetadata(Metadata(helper.addMetaData(this, appVersionNumberForSupport), null))
            if (TextUtils.isEmpty(pid)) {
                helper.getTagFilter(this)?.let {
                    apiConfigBuilder.setWithTagsMatching(it)
                }
                Support.showFAQs(this, apiConfigBuilder.build())
            } else {
                Support.showSingleFAQ(this, pid!!, apiConfigBuilder.build())
            }
        }

        finish()
        /* don't show any animation for the activity being started, otherwise the user will see
        two activity started animations (one for the current ProxyActivity, and other one for the
         activity being started)
         */
        overridePendingTransition(0, 0)
    }

    private fun processUri(uri: Uri): Intent? {
        Timber.d("Got URI: %s", uri.toString())
        try {
            // Helpshift related deep link is handled in onCreate()
            if (HELPSHIFT_URL == uri.authority) {
                return null
            }

            val supportsCustomInbox = resources.getBoolean(R.bool.supportsCustomInbox)
            if (supportsCustomInbox && uri.host == BrandCampaignTracker.URI_HOST) {
                handleCustomInboxUri(uri)
                return null
            }

            val fragmentOrPathParts = intentBuilder.getFragmentsOrPathParts(uri)
                .takeUnless(Array<*>::isEmpty)
                ?: return null
            val type = fragmentOrPathParts.getOrNull(1)?.lowercase(Locale.ROOT) ?: ""
            return when {
                uri.isDeeplinkUri() -> {
                    // Check if this a helpshift related url
                    if (uri.isHelpshiftPath()) {
                        handleHelpshiftUri(uri)
                        return null
                    }

                    // Else try to match the uri to other known deeplinks
                    getDeeplinkIntent(uri, fragmentOrPathParts, type)
                }
                uri.isAuthUri() -> intentBuilder.getAuthIntent(this, type, uri)
                uri.isTestUri() -> intentBuilder.getTestIntent(this, type)
                uri.isOtaUri() -> intentBuilder.getOtaIntent(this, uri)
                uri.isUserUri() -> intentBuilder.getUserIntent(this, uri)
                uri.isWorkoutUri() -> intentBuilder.getWorkoutIntent(this, uri)
                uri.isFollowersUri() -> intentBuilder.getFollowersIntent(this)
                else -> getStartIntent(uri, fragmentOrPathParts, type)
            }
        } catch (e: Throwable) {
            // illegal URL for deep link, just fall through and opens HomeActivity
            Timber.e(e, "Illegal URL for deep link: %s", uri.toString())
        }

        return null
    }

    private fun getDeeplinkIntent(
        uri: Uri,
        fragmentOrPathParts: Array<String>,
        type: String
    ): Intent? = intentBuilder.getDeepLinkIntent(
        this,
        uri,
        currentUserController,
        fragmentOrPathParts,
        type
    )

    /**
     * Try to handle given [uri] as a custom inbox opt-in or opt-out URI. Returns true if the URI was handled.
     */
    protected fun handleCustomInboxUri(uri: Uri): Boolean {
        val fragmentOrPathParts = intentBuilder.getFragmentsOrPathParts(uri)
        val campaignName: String
        val optInSetting: String

        if (fragmentOrPathParts.size > 2) {
            campaignName = fragmentOrPathParts[1]
            optInSetting = fragmentOrPathParts[2]
        } else {
            return false
        }

        when (optInSetting) {
            BrandCampaignTracker.OPT_IN -> {
                brandCampaignTracker.optInCustomInbox(campaignName)
                return true
            }
            BrandCampaignTracker.OPT_OUT -> {
                brandCampaignTracker.optOutCustomInbox()
                return true
            }
        }

        return false
    }

    protected fun handleInstallReferrerUri(uri: Uri) {
        val paths = uri.pathSegments
        if (paths.size >= 3 && paths[0].lowercase(Locale.US) == BrandCampaignTracker.CAMPAIGN_PATH_PREFIX) {
            brandCampaignTracker.handleInstallReferrerData(campaign = paths[1], brand = paths[2])
        }
    }

    /**
     * Handle deeplinks for Helpshift
     */
    private fun handleHelpshiftUri(uri: Uri) {
        // Extract uri path segments, i.e. parts that come after the host.
        val paths = uri.pathSegments
        if (paths.isEmpty()) {
            return
        }

        lifecycleScope.launch {
            runSuspendCatching {
                val apiConfig = loadSupportMetadataUseCase.run()

                // Try to match the uri to supported Helpshift uris:
                // com.sports-tracker[.suunto]://campaign/feedback
                // com.sports-tracker[.suunto]://campaign/faq...
                when (paths.first()) {
                    FEEDBACK -> Support.showConversation(this@BaseProxyActivity, apiConfig)
                    FAQ -> parseHelpshiftFaqLinks(paths, apiConfig)
                }
            }.onFailure { e ->
                Timber.e(e, "Failed to handle Helpshift deep link")
            }
        }
    }

    /**
     * Parse helpShift faq deeplinks and open matching part of Helpshift
     * @param paths Uri path segments like {"faq", "section", "1"}
     * @param apiConfig Helpshift Apiconfig instance
     */
    private fun parseHelpshiftFaqLinks(paths: List<String>, apiConfig: ApiConfig) {
        // Try to match the path against supported faq uri
        // com.sports-tracker[.suunto]://campaign/faq
        // com.sports-tracker[.suunto]://campaign/faq/section/{sectionID}
        // com.sports-tracker[.suunto]://campaign/faq/item/{itemID}
        when {
            paths.size == 1 -> Support.showFAQs(this, apiConfig)
            paths.size >= 3 -> when (paths[1]) {
                SECTION -> Support.showFAQSection(this, paths[2], apiConfig)
                ITEM -> Support.showSingleFAQ(this, paths[2], apiConfig)
            }
            else -> throw IllegalArgumentException("Helpshift faq uri not supported")
        }
    }

    private fun trackOpenedFromAnalyticsEvent(intent: Intent) {
        val extras = intent.extras ?: return
        if (extras.containsKey(STTConstants.ExtraKeys.OPENED_FROM_SYSTEM_WIDGET_TYPE)) {
            val type = extras.getString(STTConstants.ExtraKeys.OPENED_FROM_SYSTEM_WIDGET_TYPE, null)
                ?: return

            amplitudeAnalyticsTracker.trackEvent(
                AnalyticsEvent.SYSTEM_WIDGET_OPEN_MOBILE_APP,
                AnalyticsProperties().apply {
                    put(AnalyticsEventProperty.SYSTEM_WIDGET_TYPE, type)
                }
            )
        }
    }

    private fun trackAppOpen(intent: Intent) {
        val (sourceType, sourceTypeDetail) = when {
            intent.action == Intent.ACTION_MAIN && intent.hasCategory(Intent.CATEGORY_LAUNCHER) -> {
                APP_ICON to ""
            }

            intent.action == Intent.ACTION_VIEW
                && (intent.hasCategory(Intent.CATEGORY_DEFAULT) || intent.hasCategory(Intent.CATEGORY_BROWSABLE)) -> {
                val uri = intent.data?.toString()?.takeIf { it.isNotBlank() } ?: return
                DEEP_LINK to uri
            }

            else -> return
        }
        appOpenAnalytics.trackEvent(sourceType, sourceTypeDetail)
    }

    private fun getStartIntent(uri: Uri, fragmentOrPathParts: Array<String>, type: String): Intent? {
        return when (type) {
            WORKOUT, MOVE -> getWorkoutStartIntent(fragmentOrPathParts)
            VIEW_PROFILE -> getProfileStartIntent(fragmentOrPathParts)
            FRIENDS ->
                // For the legacy friends model related
                getFriendsStartIntent(fragmentOrPathParts)
            DIARY -> homeActivityNavigator.newStartIntentToDiaryWorkoutList(this)
            // ideally here we could fallback to deeplink intent recognition... but I limit it to
            // the only usecase which we need ("partners" and "suuntoplusstore") just to be safe of
            // not causing side effects
            PARTNERS, STORE_PATH ->
                // this is a deeplink which uses traditional URL and not `com.sports-tracker[.suunto]://campaign`
                getDeeplinkIntent(uri, fragmentOrPathParts, type)
            else -> null
        }
    }

    // Note! Let's default all friends related links from the legacy friends model to following page
    private fun getFriendsStartIntent(fragmentOrPathParts: Array<String>): Intent? {
        return if (fragmentOrPathParts.size >= 3 && fragmentOrPathParts[2] == "manage") {
            homeActivityNavigator.newStartIntentToPeopleTab(
                this,
                fromFollowNotification = false,
                showPendingRequests = false,
                showFollowingTab = true
            )
        } else {
            null
        }
    }

    private fun getProfileStartIntent(fragmentOrPathParts: Array<String>): Intent? {
        return if (fragmentOrPathParts.size >= 3) {
            UserProfileActivity.newStartIntent(this, fragmentOrPathParts[2], false)
        } else {
            null
        }
    }

    private fun getWorkoutStartIntent(fragmentOrPathParts: Array<String>): Intent? {
        // If we've a workout id let's open the workout directly
        // (e.g. URL to workout
        // http://www.sports-tracker.com/workout/rudo3/58ea0d2cf175720d166e2adb
        // or to comments in a workout
        // http://www.sports-tracker.com/workout/rudo3/58ea0d2cf175720d166e2adb/comments)
        if (fragmentOrPathParts.size >= 4) {
            val username = fragmentOrPathParts[2]
            val workoutKey = fragmentOrPathParts[3]
            return rewriteNavigator.createIntent(
                context = this,
                username = username,
                workoutId = null,
                workoutKey = workoutKey
            )
        }
        return null
    }

    private fun forceLogout() {
        Timber.w("User '${currentUserController.currentUser.username}' logged in but session key missing: forcing logout")
        lifecycleScope.launch {
            runCatching {
                logoutTask.get()
                    .logoutWithProgressDialog(this@BaseProxyActivity, supportFragmentManager)
                    .await()
            }.onFailure {
                Timber.w(it, "Failed to log out")
            }

            startIntent.apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            }

            startActivity(
                signInFlowHook.newStartIntent(
                    this@BaseProxyActivity,
                    startIntent
                )
            )
        }
    }

    private fun Uri.isDeeplinkUri(): Boolean {
        return DEEPLINK_HOST == this.host
    }

    private fun Uri.isAuthUri(): Boolean {
        return AUTH_HOST == this.host
    }

    private fun Uri.isTestUri(): Boolean {
        return TEST_HOST == this.host
    }

    private fun Uri.isOtaUri(): Boolean {
        return OTA_HOST == this.host
    }

    private fun Uri.isUserUri(): Boolean = USER_HOST == this.host

    private fun Uri.isWorkoutUri(): Boolean = WORKOUT_HOST == this.host

    private fun Uri.isFollowersUri(): Boolean = FOLLOWERS_HOST == this.host

    private fun Uri.isAppleAuthUri(): Boolean {
        return pathSegments?.firstOrNull() == AUTH_PATH_APPLE
    }

    private fun Uri.isHelpshiftPath(): Boolean {
        val path = this.pathSegments.firstOrNull()
        return path != null && (FEEDBACK == path || FAQ == path)
    }

    companion object {
        private const val HELPSHIFT_URL = "sports-tracker.helpshift.com"
        private const val DEEPLINK_HOST = "campaign"
        private const val AUTH_HOST = "auth"
        private const val TEST_HOST = "test"
        private const val OTA_HOST = "ota"
        private const val USER_HOST = "user"
        private const val WORKOUT_HOST = "workout"
        private const val FOLLOWERS_HOST = "followers"

        private const val AUTH_PATH_APPLE = "apple"

        private const val FEEDBACK = "feedback"
        private const val FAQ = "faq"
        private const val SECTION = "section"
        private const val ITEM = "item"
        private const val MOVE = "move"
        private const val VIEW_PROFILE = "view_profile"
        private const val FRIENDS = "friends"
        private const val DIARY = "diary"
        const val PARTNERS = "partners"
        const val STORE_PATH = "suuntoplusstore"

        /**
         * Creates a new intent to start this activity with new task and clear task flags set. That
         * is, the user won't be able to come back to the activity she was before
         * *NOTE: You might want to call [Activity.overridePendingTransition]
         * with zeros to avoid any transition*
         */
        @JvmStatic
        fun newStartIntentClearStack(context: Context): Intent {
            return Intent(context, ProxyActivity::class.java).setFlags(
                Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            )
        }

        @JvmStatic
        fun newStartIntentClearStackWithOpenFromSystemWidgetAnalytics(
            context: Context,
            widgetType: String
        ): Intent {
            return newStartIntentClearStack(context).apply {
                putExtra(STTConstants.ExtraKeys.OPENED_FROM_SYSTEM_WIDGET_TYPE, widgetType)
            }
        }
    }
}
