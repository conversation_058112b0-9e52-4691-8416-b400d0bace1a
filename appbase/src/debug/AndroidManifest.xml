<?xml version="1.0" encoding="utf-8"?>

<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.ACCESS_MOCK_LOCATION"
        tools:ignore="ProtectedPermissions" />

    <application android:networkSecurityConfig="@xml/ssl_proxy_config"
        tools:targetApi="n">

        <activity
            android:name=".launcher.ProxyActivity"
            android:exported="true">
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:host="api-test.sports-tracker.com" />
                <data android:pathPrefix="/workout" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
        </activity>

    </application>

</manifest>
