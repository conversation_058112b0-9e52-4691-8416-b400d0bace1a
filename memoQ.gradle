import com.stt.gradle.memoq.ApplyMemoQTranslationsTask
import com.stt.gradle.memoq.CollectReferenceStringsForMemoQTask

def remoteMemoQPath = '/android'
def localMemoQPath = "${getLayout().getBuildDirectory().get()}/memoQ"
def translatedDir = 'translated'
def referenceDir = "reference"

tasks.register('clearLocalMemoQDirectories', Delete) {
    if (file(localMemoQPath).exists()) {
        delete file(localMemoQPath).listFiles()
    }
}

tasks.register('pullTranslationsFromMemoQ') {
    dependsOn clearLocalMemoQDirectories
    doFirst {
        mkdir(localMemoQPath)
        ssh.run {
            session(remotes.memoQcontentConnector) {
                get from: "$remoteMemoQPath/$translatedDir", into: localMemoQPath
            }
        }
    }
}

tasks.register('applyMemoQTranslations', ApplyMemoQTranslationsTask) {
    dependsOn pullTranslationsFromMemoQ
    performGitAdd.set(true)
    translatedFiles.set(fileTree("$localMemoQPath/$translatedDir"))
    reference.set(
        fileTree(".").matching {
            include("**/values/strings.xml")
            include("**/values/voice_feedback.xml")
            exclude("maps/MapsSampleApp/**")
            exclude("SCSampleApp/**")
        }
    )
}

tasks.register('commitTranslationsFromMemoQ', Exec) {
    dependsOn applyMemoQTranslations
    description "Commit version change"
    commandLine 'git', 'commit', '-m', "Update translations (${new Date().format('yyyy-MM-dd')})"
}

tasks.register('collectReferenceStringsForMemoQ', CollectReferenceStringsForMemoQTask) {
    dependsOn dependsOn: clearLocalMemoQDirectories
    input.set(
        fileTree(".").matching {
            include("**/values/strings.xml")
            include("**/values/voice_feedback.xml")
            exclude("maps/MapsSampleApp/**")
            exclude("SCSampleApp/**")
        }
    )
    doFirst {
        mkdir("$localMemoQPath/$referenceDir")
    }
    if (!project.hasProperty("skipWritingOutput")) {
        output.set(file("$localMemoQPath/$referenceDir/strings.xml"))
    }
}

project.ext {
    mergeTranslationsTasks = []
}

// Merge translation tasks for each language
// The merged translations are not needed in the weekly localization process, but may be required
// in ad hoc tasks related to memoQ translation memory synchronization, etc.
rootProject.ext.supportedLocales.each { lang ->
    def taskName = "collectReferenceStringsForMemoQ$lang"
    mergeTranslationsTasks.add(taskName)
    tasks.register(taskName, CollectReferenceStringsForMemoQTask) {
        input.set(fileTree(".").matching {
            include("**/values-$lang/strings.xml")
            include("**/values-$lang/voice_feedback.xml")
            exclude("maps/MapsSampleApp/**")
            exclude("SCSampleApp/**")
        })
        output.set(file("$localMemoQPath/$referenceDir/android-merged-$lang-strings.xml"))
    }
}

tasks.register('mergeCurrentTranslationsToOneXmlPerLanguage') {
    dependsOn dependsOn: collectReferenceStringsForMemoQ
    dependsOn dependsOn: mergeTranslationsTasks

    doLast {
        ant.move(file: "$localMemoQPath/$referenceDir/strings.xml", tofile: "$localMemoQPath/$referenceDir/android-merged-strings.xml")
    }
}

remotes {
    memoQcontentConnector {
        host = 'memoq.suunto.com'
        user = 'ssh-memoq'
        port = 6022
        identity = file('id_rsa_memoqssh')
    }
}

ssh.settings {
    knownHosts = file('.ssh_known_hosts')
}

tasks.register('pushReferenceStringsToMemoQ') {
    dependsOn collectReferenceStringsForMemoQ
    doLast {
        ssh.run {
            session(remotes.memoQcontentConnector) {
                try {
                    remove("$remoteMemoQPath/$referenceDir")
                } catch (Exception ignored) {
                }

                put from: "$localMemoQPath/$referenceDir", into: remoteMemoQPath
                println("Succesfully uploaded content from $localMemoQPath/$referenceDir")
            }
        }
    }
}
