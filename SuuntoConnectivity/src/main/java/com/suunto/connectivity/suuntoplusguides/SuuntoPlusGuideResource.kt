package com.suunto.connectivity.suuntoplusguides

import android.content.Context
import com.stt.android.utils.toV1
import com.stt.android.utils.traceCompletableV2
import com.suunto.connectivity.capabilities.SuuntoWatchCapabilities
import com.suunto.connectivity.capabilities.augmentedWithUIScreenSize
import com.suunto.connectivity.repository.SyncResult
import com.suunto.connectivity.suuntoplusguide.SuuntoPlusGuideSyncLogicResult
import com.suunto.connectivity.suuntoplusguide.SuuntoPlusGuideWatchSyncTrigger
import com.suunto.connectivity.suuntoplusguide.WatchBusyStateProvider
import com.suunto.connectivity.sync.SyncState
import com.suunto.connectivity.watch.SpartanSyncResult
import com.suunto.connectivity.watch.SuuntoPlusGuidesSyncResult
import kotlinx.coroutines.rx2.rxCompletable
import rx.Completable
import timber.log.Timber
import javax.inject.Inject

class SuuntoPlusGuideResource
@Inject constructor(
    private val syncTrigger: SuuntoPlusGuideWatchSyncTrigger,
    appContext: Context
) {
    private val suuntoPlusWatchSyncDisabled: Boolean =
        appContext.getSharedPreferences("FEATURE_TOGGLE_SHARED_PREFS", Context.MODE_PRIVATE)
            .getBoolean("KEY_DISABLE_SUUNTO_PLUS_WATCH_SYNC", false)

    fun sync(
        serial: String,
        variant: String, // watch variant like Ibiza or Dolphin
        capabilities: SuuntoWatchCapabilities,
        watchBusyStateProvider: WatchBusyStateProvider,
        builder: SpartanSyncResult.Builder,
    ): Completable {
        var syncStartTimestamp = 0L
        return rxCompletable {
            syncStartTimestamp = System.currentTimeMillis()
            val result = if (!suuntoPlusWatchSyncDisabled) {
                // Run plug-in sync. This may be aborted in case watch becomes busy during sync.
                syncTrigger.syncWatchPlugins(
                    serial = serial,
                    capabilities = capabilities.augmentedWithUIScreenSize(variant),
                    watchBusyStateProvider = watchBusyStateProvider
                )
            } else {
                Timber.d("SuuntoPlus™ plug-in watch sync skipped")
                SuuntoPlusGuideSyncLogicResult.NoNewData
            }
            builder.suuntoPlusGuidesResult(result.toSyncResult(syncStartTimestamp))
        }
            .onErrorComplete { e ->
                Timber.w(e, "SuuntoPlus guide sync failed")
                builder.suuntoPlusGuidesResult(
                    SuuntoPlusGuidesSyncResult(
                        syncResult = SyncResult.failed(e),
                        syncDuration = System.currentTimeMillis() - syncStartTimestamp
                    )
                )
                return@onErrorComplete true
            }
            .traceCompletableV2("SyncSuuntoPlusGuides")
            .toV1()
    }

    fun getSyncState(): SyncState = SyncState(SyncState.SYNCING_SUUNTO_PLUS_GUIDES)

    private companion object {
        fun SuuntoPlusGuideSyncLogicResult.toSyncResult(
            syncStartTimestamp: Long
        ): SuuntoPlusGuidesSyncResult = when (this) {
            is SuuntoPlusGuideSyncLogicResult.Success ->
                SuuntoPlusGuidesSyncResult(
                    syncResult = SyncResult.success(),
                    shouldTriggerBackendSync = triggerBackendSync,
                    syncDuration = System.currentTimeMillis() - syncStartTimestamp,
                )

            is SuuntoPlusGuideSyncLogicResult.Failure ->
                SuuntoPlusGuidesSyncResult(
                    syncResult = SyncResult.failed(message),
                    shouldTriggerBackendSync = triggerBackendSync,
                    syncDuration = System.currentTimeMillis() - syncStartTimestamp,
                )

            SuuntoPlusGuideSyncLogicResult.NoNewData ->
                SuuntoPlusGuidesSyncResult(
                    syncResult = SyncResult.alreadySynced(),
                    syncDuration = System.currentTimeMillis() - syncStartTimestamp,
                )
        }
    }
}
